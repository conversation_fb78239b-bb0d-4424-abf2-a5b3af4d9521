/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

guac-client-statistics {
    font-size: 13px;
    color: white;
    background: #111;
}

guac-client-statistics dl.client-statistics {
    display: table;
    margin: 0;
    padding: 0.2em;
    min-height: 30px;
}

guac-client-statistics dl.client-statistics dt,
guac-client-statistics dl.client-statistics dd {
    display: table-cell;
    padding: 0.25em;
}

guac-client-statistics dl.client-statistics dt {
    padding-right: 0.5em;
    padding-left: 1em;
    white-space: nowrap;
    overflow: hidden;
}

guac-client-statistics dl.client-statistics dt:first-child {
    padding-left: 0.5em;
}

guac-client-statistics dl.client-statistics dd {
    min-width: 6em;
    border: 1px solid rgba(255, 255, 255, 0.125);
    border-radius: 3px;
    background: black;
}

guac-client-statistics dl.client-statistics dd.no-value::before {
    color: #888;
    content: '-';
}
