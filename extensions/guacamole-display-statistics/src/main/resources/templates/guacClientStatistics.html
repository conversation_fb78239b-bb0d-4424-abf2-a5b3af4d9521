<dl class="client-statistics">

    <dt class="client-statistic desktop-fps">
        {{ 'CLIENT.FIELD_HEADER_DESKTOP_FRAMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.desktopFps) }">
        <span ng-show="hasValue(statistics.desktopFps)"
            translate="CLIENT.INFO_FRAMERATE"
            translate-values="{ VALUE : round(statistics.desktopFps) }"></span>
    </dd>

    <dt class="client-statistic server-fps" ng-if="!$root.isEnableH264">
        {{ 'CLIENT.FIELD_HEADER_SERVER_FRAMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.serverFps) }" ng-if="!$root.isEnableH264">
        <span ng-show="hasValue(statistics.serverFps)"
            translate="CLIENT.INFO_FRAMERATE"
            translate-values="{ VALUE : round(statistics.serverFps) }"></span>
    </dd>

    <dt class="client-statistic client-fps" ng-if="!$root.isEnableH264">
        {{ 'CLIENT.FIELD_HEADER_CLIENT_FRAMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.clientFps) }" ng-if="!$root.isEnableH264">
        <span ng-show="hasValue(statistics.clientFps)"
            translate="CLIENT.INFO_FRAMERATE"
            translate-values="{ VALUE : round(statistics.clientFps) }"></span>
    </dd>

    <dt class="client-statistic drop-rate" ng-if="!$root.isEnableH264">
        {{ 'CLIENT.FIELD_HEADER_DROP_FRAMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.dropRate) }" ng-if="!$root.isEnableH264">
        <span ng-show="hasValue(statistics.dropRate)"
            translate="CLIENT.INFO_FRAMERATE"
            translate-values="{ VALUE : round(statistics.dropRate) }"></span>
    </dd>

    <dt class="client-statistic guacSyncLag" ng-if="$root.isEnableH264">
        {{ 'CLIENT.FIELD_HEADER_DELAY_TIMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.guacSyncLag) }" ng-if="$root.isEnableH264">
        <span ng-show="hasValue(statistics.guacSyncLag)"
            translate="CLIENT.INFO_TIME"
            translate-values="{ VALUE : round(statistics.guacSyncLag) }"></span>
    </dd>

    <dt class="client-statistic decodingTime" ng-if="$root.isEnableH264">
        {{ 'CLIENT.FIELD_HEADER_DECODING_TIMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.decodingTime) }" ng-if="$root.isEnableH264">
        <span ng-show="hasValue(statistics.decodingTime)"
            translate="CLIENT.INFO_TIME"
            translate-values="{ VALUE : round(statistics.decodingTime) }"></span>
    </dd>

    <dt class="client-statistic renderingTime" ng-if="$root.isEnableH264">
        {{ 'CLIENT.FIELD_HEADER_RENDERING_TIMERATE' | translate }}
    </dt>
    <dd ng-class="{ 'no-value' : !hasValue(statistics.renderingTime) }" ng-if="$root.isEnableH264">
        <span ng-show="hasValue(statistics.renderingTime)"
            translate="CLIENT.INFO_TIME"
            translate-values="{ VALUE : round(statistics.renderingTime) }"></span>
    </dd>

</dl>