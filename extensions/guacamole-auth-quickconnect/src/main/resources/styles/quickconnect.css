/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */


.quickconnect-container {
    margin: 0.25em 0;
    width: 100%;
    margin-left: 0.50em;
}

.quickconnect-container .quickconnect-field {
    background-image: url('images/protocol-icons/guac-text.png');
    background-repeat: no-repeat;
    background-size: 1.50em;
    background-position: 0.25em center;
    background-color: transparent;
    padding: 0.25em;
    padding-left: 2.50em;
    width: 100%;
    max-width: none;
    border: 0;
    box-sizing: border-box;
}

.quickconnect-button {
    clear: both;
    float: right;
    margin-right: 0.75em;
    font-size: 0.75em;
    padding: 0.25em;
}

.quickconnect-list-item {
    vertical-align: middle;
    align-content: center;
    display: flex
}
