<meta name="before" content=".all-connections" />
<div class="quickconnect-list-item list-item" ng-controller="quickconnectController">
    <div class="quickconnect-container">
        <form ng-submit="quickConnect()">
            <input type=text class="quickconnect-field" placeholder="{{'QUICKCONNECT.FIELD_PLACEHOLDER_URI' |translate}}" ng-model="uri" />
        </form>
    </div>
    <div class="action-buttons">
        <button class="quickconnect-button" ng-click="quickConnect()">{{'QUICKCONNECT.ACTION_CONNECT' | translate}}</button>
    </div>
</div>
