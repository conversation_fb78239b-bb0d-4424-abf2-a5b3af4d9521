/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */


.duo-signature-response-field-container {
    height: 100%;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    display: table;
    background: white;
}

.duo-signature-response-field {
    width: 100%;
    display: table-cell;
    vertical-align: middle;
}

.duo-signature-response-field input[type="submit"] {
    display: none !important;
}

.duo-signature-response-field iframe {
    width: 100%;
    max-width: 620px;
    height: 330px;
    border: none;
    box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
    display: block;
    margin: 1.5em auto;
}

.duo-signature-response-field iframe {
    opacity: 1;
    -webkit-transition: opacity 0.125s;
    -moz-transition:    opacity 0.125s;
    -ms-transition:     opacity 0.125s;
    -o-transition:      opacity 0.125s;
    transition:         opacity 0.125s;
}

.duo-signature-response-field.loading iframe {
    opacity: 0;
}
