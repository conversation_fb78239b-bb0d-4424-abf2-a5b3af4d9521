/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.auth.duo.conf;

import com.google.inject.Inject;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.properties.StringGuacamoleProperty;

/**
 * Service for retrieving configuration information regarding the Duo
 * authentication extension.
 */
public class ConfigurationService {

    /**
     * The Guacamole server environment.
     */
    @Inject
    private Environment environment;

    /**
     * The property within guacamole.properties which defines the hostname
     * of the Duo API endpoint to be used to verify user identities. This will
     * usually be in the form "api-XXXXXXXX.duosecurity.com", where "XXXXXXXX"
     * is some arbitrary alphanumeric value assigned by Duo and specific to
     * your organization.
     */
    private static final StringGuacamoleProperty DUO_API_HOSTNAME =
            new StringGuacamoleProperty() {

        @Override
        public String getName() { return "duo-api-hostname"; }

    };

    /**
     * The property within guacamole.properties which defines the integration
     * key received from Duo for verifying Guacamole users. This value MUST be
     * exactly 20 characters.
     */
    private static final StringGuacamoleProperty DUO_INTEGRATION_KEY =
            new StringGuacamoleProperty() {

        @Override
        public String getName() { return "duo-integration-key"; }

    };

    /**
     * The property within guacamole.properties which defines the secret key
     * received from Duo for verifying Guacamole users. This value MUST be
     * exactly 40 characters.
     */
    private static final StringGuacamoleProperty DUO_SECRET_KEY =
            new StringGuacamoleProperty() {

        @Override
        public String getName() { return "duo-secret-key"; }

    };

    /**
     * The property within guacamole.properties which defines the arbitrary
     * random key which was generated for Guacamole. Note that this value is not
     * provided by Duo, but is expected to be generated by the administrator of
     * the system hosting Guacamole. This value MUST be at least 40 characters.
     */
    private static final StringGuacamoleProperty DUO_APPLICATION_KEY =
            new StringGuacamoleProperty() {

        @Override
        public String getName() { return "duo-application-key"; }

    };

    /**
     * Returns the hostname of the Duo API endpoint to be used to verify user
     * identities, as defined in guacamole.properties by the "duo-api-hostname"
     * property. This will usually be in the form
     * "api-XXXXXXXX.duosecurity.com", where "XXXXXXXX" is some arbitrary
     * alphanumeric value assigned by Duo and specific to your organization.
     *
     * @return
     *     The hostname of the Duo API endpoint to be used to verify user
     *     identities.
     *
     * @throws GuacamoleException
     *     If the associated property within guacamole.properties is missing.
     */
    public String getAPIHostname() throws GuacamoleException {
        return environment.getRequiredProperty(DUO_API_HOSTNAME);
    }

    /**
     * Returns the integration key received from Duo for verifying Guacamole
     * users, as defined in guacamole.properties by the "duo-integration-key"
     * property. This value MUST be exactly 20 characters.
     *
     * @return
     *     The integration key received from Duo for verifying Guacamole
     *     users.
     *
     * @throws GuacamoleException
     *     If the associated property within guacamole.properties is missing.
     */
    public String getIntegrationKey() throws GuacamoleException {
        return environment.getRequiredProperty(DUO_INTEGRATION_KEY);
    }

    /**
     * Returns the secret key received from Duo for verifying Guacamole users,
     * as defined in guacamole.properties by the "duo-secret-key" property. This
     * value MUST be exactly 20 characters.
     *
     * @return
     *     The secret key received from Duo for verifying Guacamole users.
     *
     * @throws GuacamoleException
     *     If the associated property within guacamole.properties is missing.
     */
    public String getSecretKey() throws GuacamoleException {
        return environment.getRequiredProperty(DUO_SECRET_KEY);
    }

    /**
     * Returns the arbitrary random key which was generated for Guacamole, as
     * defined in guacamole.properties by the "duo-application-key" property.
     * Note that this value is not provided by Duo, but is expected to be
     * generated by the administrator of the system hosting Guacamole. This
     * value MUST be at least 40 characters.
     *
     * @return
     *     The arbitrary random key which was generated for Guacamole.
     *
     * @throws GuacamoleException
     *     If the associated property within guacamole.properties is missing.
     */
    public String getApplicationKey() throws GuacamoleException {
        return environment.getRequiredProperty(DUO_APPLICATION_KEY);
    }

}
