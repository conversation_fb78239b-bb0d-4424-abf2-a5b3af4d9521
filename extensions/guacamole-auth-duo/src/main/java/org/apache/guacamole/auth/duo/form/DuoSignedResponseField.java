/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.auth.duo.form;

import org.apache.guacamole.form.Field;
import org.codehaus.jackson.annotate.JsonProperty;

/**
 * A custom field type which uses the DuoWeb API to produce a signed response
 * for a particular user. The signed response serves as an additional
 * authentication factor, as it cryptographically verifies possession of the
 * physical device associated with that user's Duo account.
 */
public class DuoSignedResponseField extends Field {

    /**
     * The name of the HTTP parameter which an instance of this field will
     * populate within a user's credentials.
     */
    public static final String PARAMETER_NAME = "guac-duo-signed-response";

    /**
     * The unique name associated with this field type.
     */
    private static final String FIELD_TYPE_NAME = "GUAC_DUO_SIGNED_RESPONSE";

    /**
     * The hostname of the DuoWeb API endpoint.
     */
    private final String apiHost;

    /**
     * The signed request generated by a call to DuoWeb.signRequest().
     */
    private final String signedRequest;

    /**
     * Creates a new field which uses the DuoWeb API to prompt the user for
     * additional credentials. The provided credentials, if valid, will
     * ultimately be verified by Duo's service, resulting in a signed response
     * which can be cryptographically verified.
     *
     * @param apiHost
     *     The hostname of the DuoWeb API endpoint.
     *
     * @param signedRequest
     *     A signed request generated for the user in question by a call to
     *     DuoWeb.signRequest().
     */
    public DuoSignedResponseField(String apiHost, String signedRequest) {

        // Init base field type properties
        super(PARAMETER_NAME, FIELD_TYPE_NAME);

        // Init Duo-specific properties
        this.apiHost = apiHost;
        this.signedRequest = signedRequest;

    }

    /**
     * Returns the hostname of the DuoWeb API endpoint.
     *
     * @return
     *     The hostname of the DuoWeb API endpoint.
     */
    @JsonProperty("apiHost")
    public String getAPIHost() {
        return apiHost;
    }

    /**
     * Returns the signed request string, which must have been generated by a
     * call to DuoWeb.signRequest().
     *
     * @return
     *     The signed request generated by a call to DuoWeb.signRequest().
     */
    public String getSignedRequest() {
        return signedRequest;
    }

}
