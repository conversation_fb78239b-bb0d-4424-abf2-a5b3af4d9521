<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.base.EntityMapper" >

    <!--
      * SQL fragment which tests whether the value of the given column matches
      * the given entity ID. If group identifiers are provided, the IDs of the
      * entities for all groups having those identifiers are tested, as well.
      * Disabled groups are ignored.
      *
      * @param column
      *     The name of the column to test. This column MUST contain an entity
      *     ID (a foreign key into the guacamole_entity table).
      *
      * @param entityID
      *     The ID of the specific entity to test the column against.
      *
      * @param groups
      *     A collection of group identifiers to additionally test the column
      *     against. Though this functionality is optional, a collection must
      *     always be given, even if that collection is empty.
      -->
    <sql id="isRelatedEntity">
        (
            ${column} = ${entityID}
            <if test="!${groups}.isEmpty()">
                OR ${column} IN (
                    SELECT guacamole_entity.entity_id
                    FROM guacamole_entity
                    JOIN guacamole_user_group ON guacamole_user_group.entity_id = guacamole_entity.entity_id
                    WHERE
                        type = 'USER_GROUP'
                        AND name IN
                            <foreach collection="${groups}" item="effectiveGroup"
                                     open="(" separator="," close=")">
                                #{effectiveGroup,jdbcType=VARCHAR}
                            </foreach>
                        AND disabled = false
                )
            </if>
        )
    </sql>

    <!-- Select names of all effective groups (including inherited) -->
    <select id="selectEffectiveGroupIdentifiers" resultType="string">

        <if test="!recursive">
            SELECT
                guacamole_entity.name
            FROM guacamole_user_group
            JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
            JOIN guacamole_user_group_member ON guacamole_user_group.user_group_id = guacamole_user_group_member.user_group_id
            WHERE
                guacamole_user_group.disabled = false
                AND guacamole_user_group_member.member_entity_id = #{entity.entityID}
            <if test="!effectiveGroups.isEmpty()">
                UNION SELECT
                    guacamole_entity.name
                FROM guacamole_user_group
                JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
                JOIN guacamole_user_group_member ON guacamole_user_group.user_group_id = guacamole_user_group_member.user_group_id
                JOIN guacamole_entity member_entity ON guacamole_user_group_member.member_entity_id = member_entity.entity_id
                WHERE
                    guacamole_user_group.disabled = false
                    AND member_entity.type = 'USER_GROUP' AND member_entity.name IN
                        <foreach collection="effectiveGroups" item="effectiveGroup"
                                 open="(" separator="," close=")">
                            #{effectiveGroup,jdbcType=VARCHAR}
                        </foreach>
                UNION SELECT
                    guacamole_entity.name
                FROM guacamole_user_group
                JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
                WHERE type = 'USER_GROUP' AND name IN
                    <foreach collection="effectiveGroups" item="effectiveGroup"
                             open="(" separator="," close=")">
                        #{effectiveGroup,jdbcType=VARCHAR}
                    </foreach>
            </if>
        </if>

        <if test="recursive">
            WITH RECURSIVE related_entity(entity_id) AS (
                    SELECT
                        guacamole_user_group.entity_id
                    FROM guacamole_user_group
                    JOIN guacamole_user_group_member ON guacamole_user_group.user_group_id = guacamole_user_group_member.user_group_id
                    WHERE
                        guacamole_user_group_member.member_entity_id = #{entity.entityID}
                        AND guacamole_user_group.disabled = false
                <if test="!effectiveGroups.isEmpty()">
                    UNION
                        SELECT
                            guacamole_entity.entity_id
                        FROM guacamole_entity
                        JOIN guacamole_user_group ON guacamole_user_group.entity_id = guacamole_entity.entity_id
                        WHERE
                            type = 'USER_GROUP'
                            AND name IN
                                <foreach collection="effectiveGroups" item="effectiveGroup"
                                         open="(" separator="," close=")">
                                    #{effectiveGroup,jdbcType=VARCHAR}
                                </foreach>
                            AND guacamole_user_group.disabled = false
                </if>
                UNION
                    SELECT
                        guacamole_user_group.entity_id
                    FROM related_entity
                    JOIN guacamole_user_group_member ON related_entity.entity_id = guacamole_user_group_member.member_entity_id
                    JOIN guacamole_user_group ON guacamole_user_group.user_group_id = guacamole_user_group_member.user_group_id
                    WHERE
                        guacamole_user_group.disabled = false
            )
            SELECT name
            FROM related_entity
            JOIN guacamole_entity ON related_entity.entity_id = guacamole_entity.entity_id
            WHERE
                guacamole_entity.type = 'USER_GROUP';
        </if>

    </select>

    <!-- Insert single entity -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="entity.entityID"
            parameterType="org.apache.guacamole.auth.jdbc.base.EntityModel">

        INSERT INTO guacamole_entity (
            name,
            type
        )
        VALUES (
            #{entity.identifier,jdbcType=VARCHAR},
            #{entity.entityType,jdbcType=VARCHAR}
        )

    </insert>

</mapper>
