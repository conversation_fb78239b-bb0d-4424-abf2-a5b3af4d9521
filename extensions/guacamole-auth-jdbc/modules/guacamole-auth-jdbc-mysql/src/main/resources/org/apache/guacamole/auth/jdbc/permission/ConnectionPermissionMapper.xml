<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.permission.ConnectionPermissionMapper" >

    <!-- Result mapper for connection permissions -->
    <resultMap id="ConnectionPermissionResultMap" type="org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel">
        <result column="entity_id"     property="entityID"         jdbcType="INTEGER"/>
        <result column="permission"    property="type"             jdbcType="VARCHAR"
                javaType="org.apache.guacamole.net.auth.permission.ObjectPermission$Type"/>
        <result column="connection_id" property="objectIdentifier" jdbcType="INTEGER"/>
    </resultMap>

    <!-- Select all permissions for a given entity -->
    <select id="select" resultMap="ConnectionPermissionResultMap">

        SELECT
            #{entity.entityID,jdbcType=INTEGER} AS entity_id,
            permission,
            connection_id
        FROM guacamole_connection_permission
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>

    </select>

    <!-- Select the single permission matching the given criteria -->
    <select id="selectOne" resultMap="ConnectionPermissionResultMap">

        SELECT DISTINCT
            #{entity.entityID,jdbcType=INTEGER} AS entity_id,
            permission,
            connection_id
        FROM guacamole_connection_permission
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = #{type,jdbcType=VARCHAR}
            AND connection_id = #{identifier,jdbcType=VARCHAR}

    </select>

    <!-- Select identifiers accessible by the given entity for the given permissions -->
    <select id="selectAccessibleIdentifiers" resultType="string">

        SELECT DISTINCT connection_id 
        FROM guacamole_connection_permission
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND connection_id IN
                <foreach collection="identifiers" item="identifier"
                         open="(" separator="," close=")">
                    #{identifier,jdbcType=VARCHAR}
                </foreach>
            AND permission IN
                <foreach collection="permissions" item="permission"
                         open="(" separator="," close=")">
                    #{permission,jdbcType=VARCHAR}
                </foreach>

    </select>

    <!-- Delete all given permissions -->
    <delete id="delete" parameterType="org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel">

        DELETE FROM guacamole_connection_permission
        WHERE (entity_id, permission, connection_id) IN
            <foreach collection="permissions" item="permission"
                     open="(" separator="," close=")">
                (#{permission.entityID,jdbcType=INTEGER},
                 #{permission.type,jdbcType=VARCHAR},
                 #{permission.objectIdentifier,jdbcType=VARCHAR})
            </foreach>

    </delete>

    <!-- Insert all given permissions -->
    <insert id="insert" parameterType="org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel">

        INSERT IGNORE INTO guacamole_connection_permission (
            entity_id,
            permission,
            connection_id
        )
        VALUES
            <foreach collection="permissions" item="permission" separator=",">
                (#{permission.entityID,jdbcType=INTEGER},
                 #{permission.type,jdbcType=VARCHAR},
                 #{permission.objectIdentifier,jdbcType=VARCHAR})
            </foreach>

    </insert>

</mapper>
