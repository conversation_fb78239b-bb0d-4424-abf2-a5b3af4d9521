<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.user.PasswordRecordMapper" >

    <!-- Result mapper for system permissions -->
    <resultMap id="PasswordRecordResultMap" type="org.apache.guacamole.auth.jdbc.user.PasswordRecordModel">
        <result column="user_id"       property="userID"       jdbcType="INTEGER"/>
        <result column="password_hash" property="passwordHash" jdbcType="BINARY"/>
        <result column="password_salt" property="passwordSalt" jdbcType="BINARY"/>
        <result column="password_date" property="passwordDate" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- Select all password records for a given user -->
    <select id="select" resultMap="PasswordRecordResultMap">

        SELECT
            guacamole_user_password_history.user_id,
            guacamole_user_password_history.password_hash,
            guacamole_user_password_history.password_salt,
            guacamole_user_password_history.password_date
        FROM guacamole_user_password_history
        JOIN guacamole_user ON guacamole_user_password_history.user_id = guacamole_user.user_id
        JOIN guacamole_entity ON guacamole_user.entity_id = guacamole_entity.entity_id
        WHERE
            guacamole_entity.name = #{username,jdbcType=VARCHAR}
        ORDER BY
            guacamole_user_password_history.password_date DESC
        LIMIT #{maxHistorySize}

    </select>

    <!-- Insert the given password record -->
    <insert id="insert" parameterType="org.apache.guacamole.auth.jdbc.user.PasswordRecordModel">

        INSERT INTO guacamole_user_password_history (
            user_id,
            password_hash,
            password_salt,
            password_date
        )
        VALUES (
            #{record.userID,jdbcType=INTEGER},
            #{record.passwordHash,jdbcType=BINARY},
            #{record.passwordSalt,jdbcType=BINARY},
            #{record.passwordDate,jdbcType=TIMESTAMP}
        );

        DELETE FROM guacamole_user_password_history
        WHERE password_history_id &lt;= (
            SELECT password_history_id
            FROM (
                SELECT password_history_id
                FROM guacamole_user_password_history
                WHERE user_id = #{record.userID,jdbcType=INTEGER}
                ORDER BY password_date DESC
                LIMIT 1 OFFSET #{maxHistorySize}
            ) old_password_record
        );

    </insert>

</mapper>
