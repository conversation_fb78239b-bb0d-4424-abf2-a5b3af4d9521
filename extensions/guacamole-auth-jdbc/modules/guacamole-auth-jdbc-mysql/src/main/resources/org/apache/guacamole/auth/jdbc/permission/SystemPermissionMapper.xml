<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.permission.SystemPermissionMapper" >

    <!-- Result mapper for system permissions -->
    <resultMap id="SystemPermissionResultMap" type="org.apache.guacamole.auth.jdbc.permission.SystemPermissionModel">
        <result column="entity_id"  property="entityID" jdbcType="INTEGER"/>
        <result column="permission" property="type"     jdbcType="VARCHAR"
                javaType="org.apache.guacamole.net.auth.permission.SystemPermission$Type"/>
    </resultMap>

    <!-- Select all permissions for a given entity -->
    <select id="select" resultMap="SystemPermissionResultMap">

        SELECT DISTINCT
            #{entity.entityID} AS entity_id,
            permission
        FROM guacamole_system_permission
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>

    </select>

    <!-- Select the single permission matching the given criteria -->
    <select id="selectOne" resultMap="SystemPermissionResultMap">

        SELECT DISTINCT
            #{entity.entityID} AS entity_id,
            permission
        FROM guacamole_system_permission
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = #{type,jdbcType=VARCHAR}

    </select>

    <!-- Delete all given permissions -->
    <delete id="delete" parameterType="org.apache.guacamole.auth.jdbc.permission.SystemPermissionModel">

        DELETE FROM guacamole_system_permission
        WHERE (entity_id, permission) IN
            <foreach collection="permissions" item="permission"
                     open="(" separator="," close=")">
                (#{permission.entityID,jdbcType=INTEGER},
                 #{permission.type,jdbcType=VARCHAR})
            </foreach>

    </delete>

    <!-- Insert all given permissions -->
    <insert id="insert" parameterType="org.apache.guacamole.auth.jdbc.permission.SystemPermissionModel">

        INSERT IGNORE INTO guacamole_system_permission (
            entity_id,
            permission
        )
        VALUES
            <foreach collection="permissions" item="permission" separator=",">
                (#{permission.entityID,jdbcType=INTEGER},
                 #{permission.type,jdbcType=VARCHAR})
            </foreach>

    </insert>

</mapper>
