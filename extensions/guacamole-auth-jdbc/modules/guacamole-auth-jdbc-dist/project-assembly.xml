<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<assembly
    xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">

    <id>dist</id>
    <baseDirectory>${project.parent.artifactId}-${project.parent.version}</baseDirectory>

    <!-- Output .tar.gz -->
    <formats>
        <format>tar.gz</format>
    </formats>

    <!-- Include extension .jars -->
    <dependencySets>

        <!-- MySQL extension .jar -->
        <dependencySet>
            <outputDirectory>mysql</outputDirectory>
            <includes>
                <include>org.apache.guacamole:guacamole-auth-jdbc-mysql</include>
            </includes>
        </dependencySet>

        <!-- PostgreSQL extension .jar -->
        <dependencySet>
            <outputDirectory>postgresql</outputDirectory>
            <includes>
                <include>org.apache.guacamole:guacamole-auth-jdbc-postgresql</include>
            </includes>
        </dependencySet>

        <!-- SQL Server extension .jar -->
        <dependencySet>
            <outputDirectory>sqlserver</outputDirectory>
            <includes>
                <include>org.apache.guacamole:guacamole-auth-jdbc-sqlserver</include>
            </includes>
        </dependencySet>

    </dependencySets>

    <!-- Include extension schema scripts -->
    <fileSets>

        <!-- Licenses -->
        <fileSet>
            <outputDirectory></outputDirectory>
            <directory>src/licenses</directory>
        </fileSet>

        <!-- MySQL schema scripts -->
        <fileSet>
            <outputDirectory>mysql/schema</outputDirectory>
            <directory>../guacamole-auth-jdbc-mysql/schema</directory>
        </fileSet>

        <!-- PostgreSQL schema scripts -->
        <fileSet>
            <outputDirectory>postgresql/schema</outputDirectory>
            <directory>../guacamole-auth-jdbc-postgresql/schema</directory>
        </fileSet>

        <!-- SQL Server schema scripts -->
        <fileSet>
            <outputDirectory>sqlserver/schema</outputDirectory>
            <directory>../guacamole-auth-jdbc-sqlserver/schema</directory>
        </fileSet>

    </fileSets>

</assembly>
