<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.connection.ConnectionMapper" >

    <!-- Result mapper for connection objects -->
    <resultMap id="ConnectionResultMap" type="org.apache.guacamole.auth.jdbc.connection.ConnectionModel" >

        <!-- Connection properties -->
        <id     column="connection_id"            property="objectID"              jdbcType="INTEGER"/>
        <result column="connection_name"          property="name"                  jdbcType="VARCHAR"/>
        <result column="parent_id"                property="parentIdentifier"      jdbcType="INTEGER"/>
        <result column="protocol"                 property="protocol"              jdbcType="VARCHAR"/>
        <result column="max_connections"          property="maxConnections"        jdbcType="INTEGER"/>
        <result column="max_connections_per_user" property="maxConnectionsPerUser" jdbcType="INTEGER"/>
        <result column="proxy_hostname"           property="proxyHostname"         jdbcType="VARCHAR"/>
        <result column="proxy_port"               property="proxyPort"             jdbcType="INTEGER"/>
        <result column="proxy_encryption_method"  property="proxyEncryptionMethod" jdbcType="VARCHAR"
                javaType="org.apache.guacamole.net.auth.GuacamoleProxyConfiguration$EncryptionMethod"/>
        <result column="connection_weight"        property="connectionWeight"      jdbcType="INTEGER"/>
        <result column="failover_only"            property="failoverOnly"          jdbcType="BOOLEAN"/>
        <result column="last_active"              property="lastActive"            jdbcType="TIMESTAMP"/>

        <!-- Associated sharing profiles -->
        <collection property="sharingProfileIdentifiers" resultSet="sharingProfiles" ofType="java.lang.String"
                    column="connection_id" foreignColumn="primary_connection_id">
            <result column="sharing_profile_id"/>
        </collection>

        <!-- Arbitrary attributes -->
        <collection property="arbitraryAttributes" resultSet="arbitraryAttributes"
                    ofType="org.apache.guacamole.auth.jdbc.base.ArbitraryAttributeModel"
                    column="connection_id" foreignColumn="connection_id">
            <result property="name"     column="attribute_name"  jdbcType="VARCHAR"/>
            <result property="value"    column="attribute_value" jdbcType="VARCHAR"/>
        </collection>

    </resultMap>

    <!-- Select all connection identifiers -->
    <select id="selectIdentifiers" resultType="string">
        SELECT connection_id 
        FROM [guacamole_connection]
    </select>

    <!-- Select identifiers of all readable connections -->
    <select id="selectReadableIdentifiers" resultType="string">
        SELECT connection_id
        FROM [guacamole_connection_permission]
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ'
    </select>

    <!-- Select all connection identifiers within a particular connection group -->
    <select id="selectIdentifiersWithin" resultType="string">
        SELECT connection_id 
        FROM [guacamole_connection]
        WHERE
            <if test="parentIdentifier != null">parent_id = #{parentIdentifier,jdbcType=INTEGER}</if>
            <if test="parentIdentifier == null">parent_id IS NULL</if>
    </select>

    <!-- Select identifiers of all readable connections within a particular connection group -->
    <select id="selectReadableIdentifiersWithin" resultType="string">
        SELECT [guacamole_connection].connection_id
        FROM [guacamole_connection]
        JOIN [guacamole_connection_permission] ON [guacamole_connection_permission].connection_id = [guacamole_connection].connection_id
        WHERE
            <if test="parentIdentifier != null">parent_id = #{parentIdentifier,jdbcType=INTEGER}</if>
            <if test="parentIdentifier == null">parent_id IS NULL</if>
            AND <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ'
    </select>

    <!-- Select multiple connections by identifier -->
    <select id="select" resultMap="ConnectionResultMap"
            resultSets="connections,sharingProfiles,arbitraryAttributes">

        SELECT
            [guacamole_connection].connection_id,
            [guacamole_connection].connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user,
            proxy_hostname,
            proxy_port,
            proxy_encryption_method,
            connection_weight,
            failover_only,
            (
                SELECT MAX(start_date)
                FROM [guacamole_connection_history]
                WHERE [guacamole_connection_history].connection_id = [guacamole_connection].connection_id
            ) AS last_active
        FROM [guacamole_connection]
        WHERE [guacamole_connection].connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=INTEGER}
            </foreach>;

        SELECT primary_connection_id, sharing_profile_id
        FROM [guacamole_sharing_profile]
        WHERE primary_connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=INTEGER}
            </foreach>;

        SELECT
            connection_id,
            attribute_name,
            attribute_value
        FROM [guacamole_connection_attribute]
        WHERE connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=INTEGER}
            </foreach>;

    </select>

    <!-- Select multiple connections by identifier only if readable -->
    <select id="selectReadable" resultMap="ConnectionResultMap"
            resultSets="connections,sharingProfiles,arbitraryAttributes">

        SELECT
            [guacamole_connection].connection_id,
            [guacamole_connection].connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user,
            proxy_hostname,
            proxy_port,
            proxy_encryption_method,
            connection_weight,
            failover_only,
            (
                SELECT MAX(start_date)
                FROM [guacamole_connection_history]
                WHERE [guacamole_connection_history].connection_id = [guacamole_connection].connection_id
            ) AS last_active
        FROM [guacamole_connection]
        JOIN [guacamole_connection_permission] ON [guacamole_connection_permission].connection_id = [guacamole_connection].connection_id
        WHERE [guacamole_connection].connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=INTEGER}
            </foreach>
            AND <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="[guacamole_connection_permission].entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ';

        SELECT primary_connection_id, [guacamole_sharing_profile].sharing_profile_id
        FROM [guacamole_sharing_profile]
        JOIN [guacamole_sharing_profile_permission] ON [guacamole_sharing_profile_permission].sharing_profile_id = [guacamole_sharing_profile].sharing_profile_id
        WHERE primary_connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=INTEGER}
            </foreach>
            AND <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ';

        SELECT
            [guacamole_connection_attribute].connection_id,
            attribute_name,
            attribute_value
        FROM [guacamole_connection_attribute]
        JOIN [guacamole_connection_permission] ON [guacamole_connection_permission].connection_id = [guacamole_connection_attribute].connection_id
        WHERE [guacamole_connection_attribute].connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=INTEGER}
            </foreach>
            AND <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ';

    </select>

    <!-- Select single connection by name -->
    <select id="selectOneByName" resultMap="ConnectionResultMap">

        SELECT
            [guacamole_connection].connection_id,
            [guacamole_connection].connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user,
            proxy_hostname,
            proxy_port,
            proxy_encryption_method,
            connection_weight,
            failover_only,
            (
                SELECT MAX(start_date)
                FROM [guacamole_connection_history]
                WHERE [guacamole_connection_history].connection_id = [guacamole_connection].connection_id
            ) AS last_active
        FROM [guacamole_connection]
        WHERE 
            <if test="parentIdentifier != null">parent_id = #{parentIdentifier,jdbcType=INTEGER}</if>
            <if test="parentIdentifier == null">parent_id IS NULL</if>
            AND [guacamole_connection].connection_name = #{name,jdbcType=VARCHAR}

    </select>

    <!-- Delete single connection by identifier -->
    <delete id="delete">
        DELETE FROM [guacamole_connection]
        WHERE connection_id = #{identifier,jdbcType=INTEGER}
    </delete>

    <!-- Insert single connection -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="object.objectID"
            parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionModel">

        INSERT INTO [guacamole_connection] (
            connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user,
            proxy_hostname,
            proxy_port,
            proxy_encryption_method,
            connection_weight,
            failover_only
        )
        VALUES (
            #{object.name,jdbcType=VARCHAR},
            #{object.parentIdentifier,jdbcType=INTEGER},
            #{object.protocol,jdbcType=VARCHAR},
            #{object.maxConnections,jdbcType=INTEGER},
            #{object.maxConnectionsPerUser,jdbcType=INTEGER},
            #{object.proxyHostname,jdbcType=VARCHAR},
            #{object.proxyPort,jdbcType=INTEGER},
            #{object.proxyEncryptionMethod,jdbcType=VARCHAR},
            #{object.connectionWeight,jdbcType=INTEGER},
            #{object.failoverOnly,jdbcType=INTEGER}
        )

    </insert>

    <!-- Update single connection -->
    <update id="update" parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionModel">
        UPDATE [guacamole_connection]
        SET connection_name          = #{object.name,jdbcType=VARCHAR},
            parent_id                = #{object.parentIdentifier,jdbcType=INTEGER},
            protocol                 = #{object.protocol,jdbcType=VARCHAR},
            max_connections          = #{object.maxConnections,jdbcType=INTEGER},
            max_connections_per_user = #{object.maxConnectionsPerUser,jdbcType=INTEGER},
            proxy_hostname           = #{object.proxyHostname,jdbcType=VARCHAR},
            proxy_port               = #{object.proxyPort,jdbcType=INTEGER},
            proxy_encryption_method  = #{object.proxyEncryptionMethod,jdbcType=VARCHAR},
            connection_weight        = #{object.connectionWeight,jdbcType=INTEGER},
            failover_only            = #{object.failoverOnly,jdbcType=INTEGER}
        WHERE connection_id = #{object.objectID,jdbcType=INTEGER}
    </update>

    <!-- Delete attributes associated with connection -->
    <delete id="deleteAttributes">
        DELETE FROM [guacamole_connection_attribute]
        WHERE connection_id = #{object.objectID,jdbcType=INTEGER}
    </delete>

    <!-- Insert attributes for connection -->
    <insert id="insertAttributes" parameterType="org.apache.guacamole.auth.jdbc.base.ArbitraryAttributeModel">
        INSERT INTO [guacamole_connection_attribute] (
            connection_id,
            attribute_name,
            attribute_value
        )
        VALUES
            <foreach collection="object.arbitraryAttributes" item="attribute" separator=",">
                (#{object.objectID,jdbcType=INTEGER},
                 #{attribute.name,jdbcType=VARCHAR},
                 #{attribute.value,jdbcType=VARCHAR})
            </foreach>
    </insert>

</mapper>
