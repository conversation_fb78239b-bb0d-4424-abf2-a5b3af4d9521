<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.usergroup.UserGroupMemberUserMapper" >

    <!-- Select the username of all member users -->
    <select id="selectChildIdentifiers" resultType="string">
        SELECT name
        FROM [guacamole_user_group_member]
        JOIN [guacamole_entity] ON [guacamole_entity].entity_id = [guacamole_user_group_member].member_entity_id
        WHERE
            [guacamole_user_group_member].user_group_id = #{parent.objectID,jdbcType=INTEGER}
            AND [guacamole_entity].type = 'USER'
    </select>

    <!-- Select the usernames of all readable member users -->
    <select id="selectReadableChildIdentifiers" resultType="string">
        SELECT [guacamole_entity].name
        FROM [guacamole_user_group_member]
        JOIN [guacamole_entity] ON [guacamole_entity].entity_id = [guacamole_user_group_member].member_entity_id
        JOIN [guacamole_user] ON [guacamole_user].entity_id = [guacamole_entity].entity_id
        JOIN [guacamole_user_permission] ON affected_user_id = [guacamole_user].user_id
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="[guacamole_user_permission].entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND [guacamole_user_group_member].user_group_id = #{parent.objectID,jdbcType=INTEGER}
            AND [guacamole_entity].type = 'USER'
            AND permission = 'READ'
    </select>

    <!-- Delete member users by name -->
    <delete id="delete">
        DELETE [guacamole_user_group_member]
        FROM [guacamole_user_group_member]
        JOIN [guacamole_entity] ON [guacamole_entity].entity_id = member_entity_id
        WHERE
            user_group_id = #{parent.objectID,jdbcType=INTEGER}
            AND [guacamole_entity].type = 'USER'
            AND [guacamole_entity].name IN
                <foreach collection="children" item="identifier"
                         open="(" separator="," close=")">
                    #{identifier,jdbcType=VARCHAR}
                </foreach>
    </delete>

    <!-- Insert member users by name -->
    <insert id="insert">
        INSERT INTO [guacamole_user_group_member] (
            user_group_id,
            member_entity_id
        )
        SELECT DISTINCT
            #{parent.objectID,jdbcType=INTEGER},
            [guacamole_entity].entity_id
        FROM [guacamole_entity]
        WHERE
            [guacamole_entity].name IN
                <foreach collection="children" item="identifier"
                         open="(" separator="," close=")">
                    #{identifier}
                </foreach>
            AND [guacamole_entity].type = 'USER'
            AND [guacamole_entity].entity_id NOT IN (
                SELECT [guacamole_user_group_member].member_entity_id
                FROM [guacamole_user_group_member]
                WHERE [guacamole_user_group_member].user_group_id = #{parent.objectID,jdbcType=INTEGER}
            )
    </insert>

</mapper>
