/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.auth.jdbc.sharing.connection;

import com.google.inject.Inject;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.sharing.connectiongroup.SharedRootConnectionGroup;
import org.apache.guacamole.auth.jdbc.tunnel.GuacamoleTunnelService;
import org.apache.guacamole.auth.jdbc.user.RemoteAuthenticatedUser;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.auth.Connection;
import org.apache.guacamole.protocol.GuacamoleClientInformation;
import org.apache.guacamole.protocol.GuacamoleConfiguration;

/**
 * A Connection which joins an active connection, limited by restrictions
 * defined by a sharing profile.
 */
public class SharedConnection implements Connection {

    /**
     * The name of the attribute which contains the username of the user that
     * shared this connection.
     */
    public static final String CONNECTION_OWNER = "jdbc-shared-by";

    /**
     * The name of the attributes for watermark
     */
    public static final String WATERMARK_TYPE_PARM             = "watermark-type";
    public static final String WATERMARK_TEXT_PARM             = "watermark-text";
    public static final String WATERMARK_SIZE_PARM             = "watermark-size";
    public static final String WATERMARK_COLOR_PARM            = "watermark-color";
    public static final String WATERMARK_LAYOUT_PARM           = "watermark-layout";
    public static final String WATERMARK_SEMI_TRANSPARENT_PARM = "watermark-semi-transparent";
    public static final String WATERMARK_PICTURE_PARM          = "watermark-picture";
    public static final String WATERMARK_SCALE_PARM            = "watermark-scale";
    public static final String WATERMARK_WASHOUT_PARM          = "watermark-washout";

    /**
     * URL parameters for a user connection info.
     */
    public static final String ID_PARM                         = "id";
    public static final String CLOUD_USERNAME_PARM             = "cloud_username";
    public static final String USERNAME_PARM                   = "username";

    /**
     * The name of the attributes related to the h264
     */
    public static final String H264_LICENCE_PARAM              = "enable-h264";
    public static final String H264_SUPPORTED_PARAM            = "h264-supported";

    /**
     * The name of the attributes related to the multi-monitor
     */
    public static final String MMONITOR_LICENCE_PARM              = "enable-multimonitor";

    /**
     * URL parameters for the support links.
     */
    public static final String SUPPORT_MENU_NAME_PARM          = "support-menu-name";
    public static final String SUPPORT_LINK_PARM               = "support-link";
    public static final String SUPPORT_EMAIL_PARM              = "support-email";

    /**
     * Service for establishing tunnels to Guacamole connections.
     */
    @Inject
    private GuacamoleTunnelService tunnelService;

    /**
     * The user that successfully authenticated to obtain access to this
     * SharedConnection.
     */
    private RemoteAuthenticatedUser user;

    /**
     * The SharedConnectionDefinition dictating the connection being shared and
     * any associated restrictions.
     */
    private SharedConnectionDefinition definition;

    /**
     * Creates a new SharedConnection which can be used to join the connection
     * described by the given SharedConnectionDefinition.
     *
     * @param user
     *     The user that successfully authenticated to obtain access to this
     *     SharedConnection.
     *
     * @param definition
     *     The SharedConnectionDefinition dictating the connection being shared
     *     and any associated restrictions.
     */
    public void init(RemoteAuthenticatedUser user, SharedConnectionDefinition definition) {
        this.user = user;
        this.definition = definition;
    }

    @Override
    public String getIdentifier() {
        return definition.getShareKey();
    }

    @Override
    public void setIdentifier(String identifier) {
        throw new UnsupportedOperationException("Shared connections are immutable.");
    }

    @Override
    public String getName() {
        return definition.getActiveConnection().getConnection().getName();
    }

    @Override
    public void setName(String name) {
        throw new UnsupportedOperationException("Shared connections are immutable.");
    }

    @Override
    public String getParentIdentifier() {
        return SharedRootConnectionGroup.IDENTIFIER;
    }

    @Override
    public void setParentIdentifier(String parentIdentifier) {
        throw new UnsupportedOperationException("Shared connections are immutable.");
    }

    @Override
    public GuacamoleConfiguration getConfiguration() {

        // Pull the connection being shared
        Connection primaryConnection = definition.getActiveConnection().getConnection();
        Map<String, String> parameters = primaryConnection.getConfiguration().getParameters();

        // Construct a skeletal configuration that exposes only the protocol in use
        GuacamoleConfiguration config = new GuacamoleConfiguration();
        config.setProtocol(primaryConnection.getConfiguration().getProtocol());

        // Add the parameters for the watermark
        config.setParameter(WATERMARK_TYPE_PARM, parameters.get(WATERMARK_TYPE_PARM));
        config.setParameter(WATERMARK_TEXT_PARM, parameters.get(WATERMARK_TEXT_PARM));
        config.setParameter(WATERMARK_SIZE_PARM, parameters.get(WATERMARK_SIZE_PARM));
        config.setParameter(WATERMARK_COLOR_PARM, parameters.get(WATERMARK_COLOR_PARM));
        config.setParameter(WATERMARK_LAYOUT_PARM, parameters.get(WATERMARK_LAYOUT_PARM));
        config.setParameter(WATERMARK_SEMI_TRANSPARENT_PARM, parameters.get(WATERMARK_SEMI_TRANSPARENT_PARM));
        config.setParameter(WATERMARK_PICTURE_PARM, parameters.get(WATERMARK_PICTURE_PARM));
        config.setParameter(WATERMARK_SCALE_PARM, parameters.get(WATERMARK_SCALE_PARM));
        config.setParameter(WATERMARK_WASHOUT_PARM, parameters.get(WATERMARK_WASHOUT_PARM));

        // Add the parameters for the user connection info
        config.setParameter(ID_PARM, parameters.get(ID_PARM));
        config.setParameter(USERNAME_PARM, parameters.get(USERNAME_PARM));
        config.setParameter(CLOUD_USERNAME_PARM, parameters.get(CLOUD_USERNAME_PARM));

        // Add a parameter for the h264
        config.setParameter(H264_LICENCE_PARAM, parameters.get(H264_LICENCE_PARAM));
        config.setParameter(H264_SUPPORTED_PARAM, parameters.get(H264_SUPPORTED_PARAM));

        // Add a parameter for the multi-monitor
        config.setParameter(MMONITOR_LICENCE_PARM, parameters.get(MMONITOR_LICENCE_PARM));

        // Add a parameters for the support links
        config.setParameter(SUPPORT_MENU_NAME_PARM, parameters.get(SUPPORT_MENU_NAME_PARM));
        config.setParameter(SUPPORT_LINK_PARM, parameters.get(SUPPORT_LINK_PARM));
        config.setParameter(SUPPORT_EMAIL_PARM, parameters.get(SUPPORT_EMAIL_PARM));

        return config;

    }

    @Override
    public void setConfiguration(GuacamoleConfiguration config) {
        throw new UnsupportedOperationException("Shared connections are immutable.");
    }

    @Override
    public GuacamoleTunnel connect(GuacamoleClientInformation info,
            Map<String, String> tokens) throws GuacamoleException {
        return tunnelService.getGuacamoleTunnel(user, definition, info, tokens);
    }

    @Override
    public Map<String, String> getAttributes() {
        String sharedBy = definition.getActiveConnection().getUser().getIdentifier();
        return Collections.<String, String>singletonMap(CONNECTION_OWNER, sharedBy);
    }

    @Override
    public void setAttributes(Map<String, String> attributes) {
        // Do nothing - changing attributes not supported
    }

    @Override
    public Date getLastActive() {
        return null;
    }

    @Override
    public Set<String> getSharingProfileIdentifiers()
            throws GuacamoleException {
        return Collections.<String>emptySet();
    }

    @Override
    public int getActiveConnections() {
        return 0;
    }

}
