<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.permission.UserGroupPermissionMapper" >

    <!-- Result mapper for user group permissions -->
    <resultMap id="UserGroupPermissionResultMap" type="org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel">
        <result column="entity_id"         property="entityID"         jdbcType="INTEGER"/>
        <result column="permission"        property="type"             jdbcType="VARCHAR"
                javaType="org.apache.guacamole.net.auth.permission.ObjectPermission$Type"/>
        <result column="affected_name"     property="objectIdentifier" jdbcType="INTEGER"/>
    </resultMap>

    <!-- Select all permissions for a given entity -->
    <select id="select" resultMap="UserGroupPermissionResultMap">

        SELECT
            #{entity.entityID,jdbcType=INTEGER} AS entity_id,
            permission,
            affected_entity.name AS affected_name
        FROM guacamole_user_group_permission
        JOIN guacamole_user_group affected_group ON guacamole_user_group_permission.affected_user_group_id = affected_group.user_group_id
        JOIN guacamole_entity affected_entity ON affected_group.entity_id = affected_entity.entity_id
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="guacamole_user_group_permission.entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND affected_entity.type = 'USER_GROUP'::guacamole_entity_type

    </select>

    <!-- Select the single permission matching the given criteria -->
    <select id="selectOne" resultMap="UserGroupPermissionResultMap">

        SELECT DISTINCT
            #{entity.entityID,jdbcType=INTEGER} AS entity_id,
            permission,
            affected_entity.name AS affected_name
        FROM guacamole_user_group_permission
        JOIN guacamole_user_group affected_group ON guacamole_user_group_permission.affected_user_group_id = affected_group.user_group_id
        JOIN guacamole_entity affected_entity ON affected_group.entity_id = affected_entity.entity_id
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="guacamole_user_group_permission.entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = #{type,jdbcType=VARCHAR}::guacamole_object_permission_type
            AND affected_entity.name = #{identifier,jdbcType=VARCHAR}
            AND affected_entity.type = 'USER_GROUP'::guacamole_entity_type

    </select>

    <!-- Select identifiers accessible by the given entity for the given permissions -->
    <select id="selectAccessibleIdentifiers" resultType="string">

        SELECT DISTINCT affected_entity.name
        FROM guacamole_user_group_permission
        JOIN guacamole_user_group affected_group ON guacamole_user_group_permission.affected_user_group_id = affected_group.user_group_id
        JOIN guacamole_entity affected_entity ON affected_group.entity_id = affected_entity.entity_id
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="guacamole_user_group_permission.entity_id"/>
                <property name="entityID" value="#{entity.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND affected_entity.name IN
                <foreach collection="identifiers" item="identifier"
                         open="(" separator="," close=")">
                    #{identifier,jdbcType=VARCHAR}
                </foreach>
            AND permission IN
                <foreach collection="permissions" item="permission"
                         open="(" separator="," close=")">
                    #{permission,jdbcType=VARCHAR}::guacamole_object_permission_type
                </foreach>
            AND affected_entity.type = 'USER_GROUP'::guacamole_entity_type

    </select>

    <!-- Delete all given permissions -->
    <delete id="delete" parameterType="org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel">

        DELETE FROM guacamole_user_group_permission
        USING guacamole_user_group affected_group, guacamole_entity affected_entity
        WHERE
            guacamole_user_group_permission.affected_user_group_id = affected_group.user_group_id
            AND affected_group.entity_id = affected_entity.entity_id
            AND (guacamole_user_group_permission.entity_id, permission, affected_entity.name) IN
                <foreach collection="permissions" item="permission"
                         open="(" separator="," close=")">
                    (#{permission.entityID,jdbcType=INTEGER},
                     #{permission.type,jdbcType=VARCHAR}::guacamole_object_permission_type,
                     #{permission.objectIdentifier,jdbcType=INTEGER})
                </foreach>
            AND affected_entity.type = 'USER_GROUP'::guacamole_entity_type

    </delete>

    <!-- Insert all given permissions -->
    <insert id="insert" parameterType="org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel">

        INSERT INTO guacamole_user_group_permission (
            entity_id,
            permission,
            affected_user_group_id
        )
        SELECT DISTINCT
            permissions.entity_id,
            permissions.permission,
            affected_group.user_group_id
        FROM
            <foreach collection="permissions" item="permission"
                     open="(" separator="UNION ALL" close=")">
                SELECT #{permission.entityID,jdbcType=INTEGER}                               AS entity_id,
                       #{permission.type,jdbcType=VARCHAR}::guacamole_object_permission_type AS permission,
                       #{permission.objectIdentifier,jdbcType=VARCHAR}::text                 AS affected_name
            </foreach>
        AS permissions
        JOIN guacamole_entity affected_entity ON
                affected_entity.name = permissions.affected_name
            AND affected_entity.type = 'USER_GROUP'::guacamole_entity_type
        JOIN guacamole_user_group affected_group ON affected_group.entity_id = affected_entity.entity_id
        WHERE (permissions.entity_id, permissions.permission, affected_group.user_group_id) NOT IN (
            SELECT
                guacamole_user_group_permission.entity_id,
                guacamole_user_group_permission.permission,
                guacamole_user_group_permission.affected_user_group_id
            FROM guacamole_user_group_permission
        );

    </insert>

</mapper>