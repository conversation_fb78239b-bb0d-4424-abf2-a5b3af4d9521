--
-- Licensed to the Apache Software Foundation (ASF) under one
-- or more contributor license agreements.  See the NOTICE file
-- distributed with this work for additional information
-- regarding copyright ownership.  The ASF licenses this file
-- to you under the Apache License, Version 2.0 (the
-- "License"); you may not use this file except in compliance
-- with the License.  You may obtain a copy of the License at
--
--   http://www.apache.org/licenses/LICENSE-2.0
--
-- Unless required by applicable law or agreed to in writing,
-- software distributed under the License is distributed on an
-- "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
-- KIND, either express or implied.  See the License for the
-- specific language governing permissions and limitations
-- under the License.
--

--
-- Add new guacd encryption method type
--

CREATE TYPE guacamole_proxy_encryption_method AS ENUM(
    'NONE',
    'SSL'
);

--
-- Add guacd per-connection override columns
--

ALTER TABLE guacamole_connection ADD COLUMN proxy_port integer;
ALTER TABLE guacamole_connection ADD COLUMN proxy_hostname varchar(512);
ALTER TABLE guacamole_connection ADD COLUMN proxy_encryption_method guacamole_proxy_encryption_method;

--
-- Add new user profile columns
--

ALTER TABLE guacamole_user ADD COLUMN full_name           VARCHAR(256);
ALTER TABLE guacamole_user ADD COLUMN email_address       VARCHAR(256);
ALTER TABLE guacamole_user ADD COLUMN organization        VARCHAR(256);
ALTER TABLE guacamole_user ADD COLUMN organizational_role VARCHAR(256);

