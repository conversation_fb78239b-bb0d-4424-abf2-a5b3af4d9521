jose4j
Copyright 2012-2015 <PERSON>ShaAlgorithm contains code for converting the concatenated
R & S values of the signature to and from DER, which was originally
derived from the Apache Santuario XML Security library's SignatureECDSA
implementation. http://santuario.apache.org/

The Base64 implementation in this software was derived from the
Apache Commons Codec project. http://commons.apache.org/proper/commons-codec/

JSON processing in this software was derived from the JSON.simple toolkit.
https://code.google.com/p/json-simple/

