/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

var Guacamole = Guacamole || {};

/**
 * Abstract camera recorder which streams arbitrary camera data to an underlying
 * Guacamole.OutputStream. It is up to implementations of this class to provide
 * some means of handling this Guacamole.OutputStream. Data produced by the
 * recorder is to be sent along the provided stream immediately.
 *
 * @constructor
 */
Guacamole.CameraRecorder = function CameraRecorder() {

    /**
     * Callback which is invoked when the camera recording process has stopped
     * and the underlying Guacamole stream has been closed normally. Camera will
     * only resume recording if a new Guacamole.CameraRecorder is started. This
     * Guacamole.CameraRecorder instance MAY NOT be reused.
     *
     * @event
     */
    this.onclose = null;

    /**
     * Callback which is invoked when the camera recording process cannot
     * continue due to an error, if it has started at all. The underlying
     * Guacamole stream is automatically closed. Future attempts to record
     * camera should not be made, and this Guacamole.CameraRecorder instance
     * MAY NOT be reused.
     *
     * @event
     */
    this.onerror = null;

};

/**
 * Determines whether the given mimetype is supported by any built-in
 * implementation of Guacamole.CameraRecorder, and thus will be properly handled
 * by Guacamole.CameraRecorder.getInstance().
 *
 * @param {String} mimetype
 *     The mimetype to check.
 *
 * @returns {boolean}
 *     true if the given mimetype is supported by any built-in
 *     Guacamole.CameraRecorder, false otherwise.
 */
Guacamole.CameraRecorder.isSupportedType = function isSupportedType(mimetype) {

    return Guacamole.RawCameraRecorder.isSupportedType(mimetype);

};

/**
 * Returns an instance of Guacamole.CameraRecorder providing support for the
 * given camera video format. If support for the given camera video format is
 * not available, null is returned.
 *
 * @param {Guacamole.OutputStream} stream
 *     The Guacamole.OutputStream to send camera data through.
 *
 * @param {String} mimetype
 *     The mimetype of the camera data to be sent along the provided stream.
 *
 * @return {Guacamole.CameraRecorder}
 *     A Guacamole.CameraRecorder instance supporting the given mimetype and
 *     writing to the given stream, or null if support for the given mimetype
 *     is absent.
 */
Guacamole.CameraRecorder.getInstance = function getInstance(stream, mimetype) {

    // Use raw camera recorder if possible
    if (Guacamole.RawCameraRecorder.isSupportedType(mimetype)) {
        return new Guacamole.RawCameraRecorder(stream, mimetype);
    }

    // No support for given mimetype
    return null;

};

/**
 * Implementation of Guacamole.CameraRecorder providing support for raw PCM
 * format camera. This recorder relies only on the Web Audio API and does not
 * require any browser-level support for its camera formats.
 *
 * @constructor
 * @augments Guacamole.CameraRecorder
 * @param {Guacamole.OutputStream} stream
 *     The Guacamole.OutputStream to write camera data to.
 *
 * @param {String} mimetype
 *     The mimetype of the camera data to send along the provided stream, which
 *     must be a "video/webm;codecs=h264" mimetype with necessary parameters,
 *     such as: "video/webm;codecs=h264,width=640,height=480".
 */
Guacamole.RawCameraRecorder = function RawCameraRecorder(stream, mimetype) {

    /**
     * Reference to this RawCameraRecorder.
     *
     * @private
     * @type {Guacamole.RawCameraRecorder}
     */
    var recorder = this;

    /**
     * Mimetype of MediaRecorder.
     *
     * @private
     * @type {String}
     */
    this.mimetype = mimetype;

    /**
     * The specific mark of camera stream. ("APPORTO")
     *
     * @private
     * @type {Uint8Array}
     */
    this.STREAM_MARK = new Uint8Array([0x41, 0x50, 0x50, 0x4F, 0x52, 0x54, 0x4F]);

    /**
     * The width of camera video.
     *
     * @private
     * @type {int}
     */
    this.WIDTH = 640; //352;

    /**
     * The height of camera video.
     *
     * @private
     * @type {int}
     */
    this.HEIGHT = 480; //288;

    /**
     * The interval of capturing the camera video.
     *
     * @private
     * @type {int}
     */
    this.INTERVAL = 10;

    /**
     * The bitrate of sending the captured video.
     *
     * @private
     * @type {int}
     */
    this.VIDEO_BPS = 1200000; // 1.2Mbps

    // Some browsers do not implement navigator.mediaDevices - this
    // shims in this functionality to ensure code compatibility.
    if (!navigator.mediaDevices)
        navigator.mediaDevices = {};

    // Browsers that either do not implement navigator.mediaDevices
    // at all or do not implement it completely need the getUserMedia
    // method defined.  This shims in this function by detecting
    // one of the supported legacy methods.
    if (!navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia = (navigator.getUserMedia
                || navigator.webkitGetUserMedia
                || navigator.mozGetUserMedia
                || navigator.msGetUserMedia).bind(navigator);
    }

    /**
     * Guacamole.ArrayBufferWriter wrapped around the camera output stream
     * provided when this Guacamole.RawCameraRecorder was created.
     *
     * @private
     * @type {Guacamole.ArrayBufferWriter}
     */
    var writer = new Guacamole.ArrayBufferWriter(stream);

    /**
     * The MediaRecorder object to be used for recording.
     *
     * @type MediaRecorder
     */
    var mediaRecorder = null;

    /**
     * The queue managing the events (start/stop) of device.
     *
     * @type Queue
     */
     var eventQueue = new Queue();

    /**
     * getUserMedia() callback which handles successful retrieval of an
     * camera stream (successful start of recording).
     *
     * @private
     * @param {MediaStream} stream
     *     A MediaStream which provides access to camera data read from the
     *     user's local camera input device.
     */
    var streamReceived = function streamReceived(stream) {

        console.log('getUserMedia() got camera media stream:', stream);

        // save stream for later cleanup
        window.cameraStream = stream;

        // create the MediaRecorder object
        var options = {
            mimeType: recorder.mimetype,
            videoBitsPerSecond: recorder.VIDEO_BPS
        };

        try {
            mediaRecorder = new MediaRecorder(window.cameraStream, options);
        }
        catch (e) {
            console.error('Exception while creating MediaRecorder:', e);
            writer.sendEnd();
            return;
        }

        // start recording
        mediaRecorder.ondataavailable = handleDataAvailable;
        mediaRecorder.start(recorder.INTERVAL); // collect the specified interval of data
        console.log('MediaRecorder started', mediaRecorder);

    };

    var handleDataAvailable = async function handleDataAvailable(event) {

        if (event.data && event.data.size > 0) {
            // console.log("stream length=" + event.data.size);

            var buffer = await event.data.arrayBuffer();
            var data = new Uint8Array(buffer);

            if (buffer.byteLength > 1) {
                sendCameraStream(data);
            }
        }
    };

    /**
     * getUserMedia() callback which handles camera recording denial. The
     * underlying Guacamole output stream is closed, and the failure to
     * record is noted using onerror.
     *
     * @private
     */
    var streamDenied = function streamDenied() {

        // Simply end stream if camera access is not allowed
        writer.sendEnd();

        // Notify of closure
        if (recorder.onerror) {
            recorder.onerror();
        }

    };

    /**
     * Requests access to the user's microphone and begins capturing camera. All
     * received camera data is resampled as necessary and forwarded to the
     * Guacamole stream underlying this Guacamole.RawCameraRecorder. This
     * function must be invoked ONLY ONCE per instance of
     * Guacamole.RawCameraRecorder.
     *
     * @private
     */
    var beginCameraCapture = async function beginCameraCapture() {

        // Check mediaRecorder
        if (mediaRecorder != null) {
            return;
        }

        // Create constraints
        var constraints = {
            audio: false,
            video: {
                width: recorder.WIDTH,
                height: recorder.HEIGHT,
                frameRate: { ideal: 25, max: 30 }
            }
        };

        // Attempt to retrieve an camera input stream from the browser
        let stream = null;
        try {
            stream = await navigator.mediaDevices.getUserMedia(constraints);
            streamReceived(stream);
        } catch(err) {
            streamDenied();
        }

        console.log("beginCameraCapture");

    };

    /**
     * Stops capturing camera, if the capture has started, freeing all associated
     * resources. If the capture has not started, this function simply ends the
     * underlying Guacamole stream.
     *
     * @private
     */
    var stopCameraCapture = async function stopCameraCapture() {

        // Check mediaRecorder
        if (mediaRecorder === null) {
            return;
        }

        // Stop capture
        mediaRecorder.stop();
        if (window.cameraStream) {
            window.cameraStream.getAudioTracks().forEach(function(track) {
                track.stop();
            });

            window.cameraStream.getVideoTracks().forEach(function(track) {
                track.stop();
            });
        }

        // Remove references to now-unneeded components
        window.cameraStream = null;
        mediaRecorder = null;

        // End stream
        // writer.sendEnd();

        console.log("stopCameraCapture");

    };

    // Once camera stream is successfully open, request and begin reading camera
    writer.onack = function cameraStreamAcknowledged(status) {

        // Start capture if successful response and not yet started
        if (status.code === Guacamole.Status.Code.SUCCESS) {

            // Start capturing device
            eventQueue.enqueue("begin");

        }
        // Otherwise stop capture and cease handling any further acks
        else {

            // Stop capturing device
            eventQueue.enqueue("stop");

        }

    };

    /**
     * Append the specific id and the length of stream to the camera stream.
     * And send this data to the guacamole server.
     *
     * @private
     */
    var sendCameraStream = function sendCameraStream(data) {

        if (data == null || data.length == 0) {
            console.log("The camera stream to be sent is null or empty.");
            return;
        }

        var lengthArray = new Uint8Array(Int32toBytes(data.length));
        var total = new Int8Array(recorder.STREAM_MARK.length + lengthArray.length + data.length);

        total.set(recorder.STREAM_MARK);
        total.set(lengthArray, recorder.STREAM_MARK.length);
        total.set(data, recorder.STREAM_MARK.length + lengthArray.length);

        writer.sendData(total);
    };

    /**
     * Convert Int32 number to byte array.
     *
     * @private
     */
    function Int32toBytes (num) {

        arr = new ArrayBuffer(4); // an Int32 takes 4 bytes
        view = new DataView(arr);
        view.setUint32(0, num, false); // byteOffset = 0; litteEndian = false
        return arr;

    }

    /**
     * The flag indicating that the device is in working.
     *
     * @type Boolean
     */
    var working = false;

    /**
     * Handles the events periodically.
     *
     * @private
     */
    window.setInterval(async function() {

        if (!eventQueue.isEmpty()) {

            if (working === true) {
                return;
            }

            working = true;

            var event = eventQueue.dequeue();
            if (event === "begin") {
                await beginCameraCapture();
            }
            else if (event === "stop") {
                stopCameraCapture();
            }
            else {
                // empty proess
            }

            working = false;
        }
    }, 500);

};

Guacamole.RawCameraRecorder.prototype = new Guacamole.CameraRecorder();

/**
 * Determines whether the given mimetype is supported by
 * Guacamole.RawCameraRecorder.
 *
 * @param {String} mimetype
 *     The mimetype to check.
 *
 * @returns {Boolean}
 *     true if the given mimetype is supported by Guacamole.RawCameraRecorder,
 *     false otherwise.
 */
Guacamole.RawCameraRecorder.isSupportedType = function isSupportedType(mimetype) {

    try {
        // Check if MediaRecorder available.
        if (!window.MediaRecorder) {
            console.error("This browser doesn't support MediaRecorder");
            return false;
        }

        if (!MediaRecorder.isTypeSupported(mimetype)) {
            console.error(`${mimetype} is not Supported`);
            return false;
        }
    }
    catch(e) {
        console.error("This browser doesn't support MediaRecorder");
        return false;
    }

    return true;

};

/**
 * Abstract Class that manages the device events with queue.
 * Whenever receiving the device events, it doesn't immediately execute the
 * relevant hanlder. Because the synchronization of start/stope events isn't fit
 * in case that the intervals of device events is very short.
 * So, when receiving the device events, it puts them into the event queue and
 * it executes sequentially the relelvant handlers.
 *
 * @constructor
 */
Queue = function Queue() {

    this.elements = [];

}

/**
 * Put an element into queue.
 *
 * @private
 */
Queue.prototype.enqueue = function (e) {

    this.elements.push(e);

};

/**
 * Remove an element from the front of the queue
 *
 * @private
 */
Queue.prototype.dequeue = function () {

    return this.elements.shift();

};

/**
 * Check if the queue is empty
 *
 * @private
 */
Queue.prototype.isEmpty = function () {

    return this.elements.length == 0;

};

/**
 * Get the element at the front of the queue
 *
 * @private
 */
Queue.prototype.peek = function () {

    return !this.isEmpty() ? this.elements[0] : undefined;

};

/**
 * Return the count of elements in queue.
 *
 * @private
 */
Queue.prototype.length = function() {

    return this.elements.length;

}
