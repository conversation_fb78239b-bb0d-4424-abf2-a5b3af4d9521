/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define([], factory);
    } else if (typeof exports === 'object') {
        // Node. Does not work with strict CommonJS, but
        // only CommonJS-like environments that support module.exports,
        // like Node.
        module.exports = factory();
    } else {
        // Browser globals (root is window)
        root.WebCodecWorker = factory();
    }
}(this, function () {
    "use strict";

    importScripts("../components/WebGLRenderer.js")

    // Rendering. Drawing is limited to once per animation frame.
    let renderer = null;
    let pendingFrame = null;
    let frameCount = 0;
    let arrChroma = {};
    let isInitialized = false;
    let decoder = null;
    let waitingForKeyframe = false;
    let reloadTimeoutId = -1;
    let decodingStartTime = 0;
    let decodingTime = 0;
    let frameSize = 0;
    let isSafari = false;

    // If bitmap generation is requested, create bitmap after calling the draw method.
    let generateBitmap = false;


    const LIMIT_WAITING_KEYFRAME = 1000; // ms unit

    function renderFrame(frame) {
        if (!pendingFrame) {
            // Schedule rendering in the next animation frame.
            requestAnimationFrame(renderAnimationFrame);
        } else {
            // Close the current pending frame before replacing it.
            pendingFrame.close();
        }
        // Set or replace the pending frame.
        pendingFrame = frame;
    }

    function renderAnimationFrame() {
        // Render when the frame is not a chroma
        if(arrChroma[pendingFrame.timestamp] != true) {
            let startTime = performance.now();
            if (isSafari) {
                self.postMessage({pendingFrame}, [pendingFrame]);
            }
            else {
                renderer.draw(pendingFrame);
                if (generateBitmap) {
                    generateBitmap = false;
                    sendDisplayBitmap();
                }
            }
            self.postMessage({decodingTime, renderTime: performance.now() - startTime, frameSize});

            if (generateBitmap) {
                generateBitmap = false;
                sendDisplayBitmap();
            }
        }
        delete arrChroma[pendingFrame.timestamp];
        pendingFrame.close();
        pendingFrame = null;
    }

    function sendDisplayBitmap() {
        renderer.canvas.convertToBlob({ type:"image/webp" }).then(function(blob) {
            blob.arrayBuffer().then(function(buffer) {
                self.postMessage({ imageBitmap: buffer }, [buffer]);
            });
        });
    }

    // Listen for the start request.
    self.addEventListener("message", // Startup.
    function handleMessageFromMainThread(e) {
        // Pick a renderer to use.
        if (e.data.canvas != null) {
            switch (e.data.rendererName) {
                case "webgl":
                renderer = new WebGLRenderer(e.data.rendererName, e.data.canvas);
                break;
            }
        }

        if (e.data.isSafari != null) {
            isSafari = e.data.isSafari;
            setUpVideoDecoder();

            isInitialized = true;
            return;
        }

        if (!isInitialized) {
            return;
        }

        /**
         * This message should be received only from non-Safari browsers.
         */
        if ('getImageData' in e.data) {
            if (!isSafari) {
                generateBitmap = true;
            } else {
                console.error("getImageData message should be received only from non-Safari browsers.");
            }

            return;
        }

        function setUpVideoDecoder() {
            pendingFrame = null;
            // Set up a VideoDecoer.
            decoder = new VideoDecoder({
                output(frame) {
                    decodingTime = performance.now() - decodingStartTime;
                    renderFrame(frame);
                },
                error(e) {
                    console.log("decoder error occured.", decoder, decoder.state);

                    // Within a specified time, if keyframe is not detected, reconnect the session.
                    if (reloadTimeoutId < 0) {
                        reloadTimeoutId = setTimeout(function() {
                            waitingForKeyframe = false;
                            self.postMessage({reload: true});
                        }, LIMIT_WAITING_KEYFRAME);
                    }
                }
            });

            const config = {
                codec: "avc1.64001f",
                codedWidth: 640,
                codedHeight: 480,
                colorSpace: {
                    format: "nv12",
                    fullRange: true,
                    primaries: "bt709",
                    transfer: "bt709",
                    matrix: "bt709"
                }
            };

            decoder.configure(config);
        }

        function isKeyFrame(data) {
            let bytesToCheck = Math.min(data.length, 100);
            let i = 0;
            while (i < bytesToCheck) {
                // Look for the start code
                if (data[i++] === 0x00 && data[i++] === 0x00) {
                    // The start code can be 0x000001 or 0x00000001
                    if (data[i] === 0x01) {
                        i++;
                    }
                    else if (data[i++] === 0x00 && data[i++] === 0x01) {
                        // It's the longer start code
                    }
                    else {
                        // It's not a start code, continue searching
                        continue;
                    }
                
                    // The next byte is the NALU header
                    let nalUnitType = data[i] & 0x1F;
                
                    if (nalUnitType === 5) {
                        // It's a keyframe
                        console.log("keyframe is detected at = ", i);
                        return true;
                    }
                }
            }
            
            // No keyframe found
            return false;
        }

        function decodeVideo(buffer){
            const tmp = new Uint8Array(buffer);
            frameSize = buffer.length;

            // The VideoDecoder was forced closure. It means that an error occurred when decoding the frame.
            // This error occurs when resizing.
            // In that case, reinitialize the VideoDecoder with the next keyframe.
            if (decoder.state == "closed") {
                waitingForKeyframe = true;
            }

            if (waitingForKeyframe) {
                if (isKeyFrame(tmp.subarray(1))) {
                    if (reloadTimeoutId > 0) {
                        clearTimeout(reloadTimeoutId);
                        reloadTimeoutId = -1;
                    }
                    setUpVideoDecoder();
                    waitingForKeyframe = false;
                }
                else {
                    return;
                }
            }

            var timestamp = 30 * frameCount;
            var isChroma = false;

            if (tmp[0] == 2)
                isChroma = true;
            arrChroma[timestamp] = isChroma;

            const init = {
                type: "key",
                data: tmp.subarray(1),
                timestamp: timestamp,
                duration: 30,
            };
            frameCount++;

            var chunk = new EncodedVideoChunk(init);
            decodingStartTime = performance.now()
            decoder.decode(chunk);
        };

        decodeVideo(e.data.buffer);

    }, {once: false});

}));
