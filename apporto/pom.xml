<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                        http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>rs.jsw.guacamole</groupId>
    <artifactId>guacamole-apporto-extensions</artifactId>
    <packaging>pom</packaging>
    <version>1.6.2</version>
    <name>guacamole-apporto-extensions</name>
    <url>https://bitbucket.org/apporto/guacamole-client-apporto/</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <!-- Apporto extensions -->
        <module>apporto-auth-mysql</module>
        <module>apporto-stats-collect</module>
        <module>apporto-ribbon</module>
        <module>apporto-usage-timer</module>
        <module>apporto-highlight</module>
        <module>apporto-h264</module>
        <module>apporto-core</module>
        <module>apporto-tunnel</module>
        <module>apporto-classroom</module>
        <module>apporto-presenter</module>
        <module>encryptedurl</module>
    </modules>

    <build>
        <plugins>

            <!-- Verify format using Apache RAT -->
            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <version>0.12</version>

                <configuration>
                    <excludes>
                        <exclude>.dockerignore</exclude>
                        <exclude>CONTRIBUTING</exclude>
                        <exclude>bitbucket-pipelines.yml</exclude>
                        <exclude>**/README.md</exclude>
                        <exclude>**/target/**</exclude>
                        <exclude>**/scripts/**</exclude>
                        <exclude>**/home/<USER>/exclude>
                        <exclude>**/bin/**</exclude>
                        <exclude>**/etc/**</exclude>
                        <exclude>**/tools/**</exclude>
                        <exclude>**/root/**</exclude>
                        <exclude>apporto-onexit/**</exclude>
                        <exclude>.env*</exclude>
                        <exclude>*.log</exclude>
                        <exclude>tomcat-policies/**</exclude>
                        <exclude>entrypoint/**</exclude>
                        <exclude>helm/**</exclude>
                    </excludes>
                </configuration>

                <!-- Bind RAT to validate phase -->
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>

        </plugins>
    </build>

</project>
