#!/bin/bash

for i in /home/<USER>
	if [[ ${i} != "/home/<USER>" ]]; then
		session_user=$(echo ${i} | cut -d/ -f3)
		read -a USER_VNC_SERVERS < <(ps aux | grep -v grep | grep $i | grep Xvnc | cut -d: -f4 | cut -d' ' -f1) -d ' '
		IFS=$'\n' USER_VNC_SERVERS=($(sort -n <<<"${USER_VNC_SERVERS[*]}"))
		unset IFS
		for SRV in ${USER_VNC_SERVERS[@]}; do
			login_time=$(ps aux | grep -Ev 'grep|awk' | grep "^${session_user:0:7}.*Xvnc4 :${SRV}" | awk '{print $9}')
			current_timestamp=$(date --utc +%s)
			login_timestamp=$(date --utc --date ${login_time} +%s)
			if [[ $((${current_timestamp}-${login_timestamp})) -gt 240 ]]; then
				vnc_desktop=$(ps aux | grep -Ev 'grep|awk' | grep "^${session_user:0:7}.*Xvnc4 :${SRV}" | awk '{print $14}')
				vnc_desktop=${vnc_desktop#*:}
				XAUTHORITY=${i}/.Xauthority DISPLAY=:${vnc_desktop} xmessage "Less than 1 minute left until automatic logout." &
			fi
			if [[ $((${current_timestamp}-${login_timestamp})) -gt 300 ]]; then
				sudo su ${session_user} -c "vncserver -kill :${SRV}"
			fi
		done
		read -a USER_VNC_SERVERS < <(ps aux | grep -v grep | grep ${i} | grep Xvnc | cut -d: -f4 | cut -d' ' -f1) -d ' '
		IFS=$'\n' USER_VNC_SERVERS=($(sort -n <<<"${USER_VNC_SERVERS[*]}"))
		unset IFS
		echo ${USER_VNC_SERVERS[@]}
		if [[ -z ${USER_VNC_SERVERS[@]} ]]; then
			pkill -u ${session_user}
			sleep 10
			pkill -9 -u ${session_user}
			umount ${i}
			/usr/sbin/userdel -rf ${session_user}
		fi
	fi
done
