1.14.0
- Added kurento messenger
- Added classroom interface
- Added support for MacOS and Linux restart
- Minor UI changes
- Moved part of the code to core (Notifications)

1.13.0
- Added classroom view.
- Minor UI changes.
- Updated to Guacamole 1.1

1.12.0
- Bugfix release (transparent canvas, etc...)

1.11.0
- Click on welcome canvas provided to the windows; Restored old appearance for poor network, ef
- When user clicks x the session should be killed even if user hasn't clicked in ribbon: added canvas over whole screen
- Removed button for expired timeout dialog
- Added button for session dialog

1.10.0
- Added classroom view.
- Added multimonitor support.
- Added reset button.

1.9.0
- Added messenger
- Restyled UI
- Upgraded to Guacamole 1.0

1.8.1 - Bugfix release
- More resilient share linkg generation, fixed to work with LB
- Fixed handling of messages when enumerating snapshots

1.8.0 - Winter 2018 Release
- Active/Active Guacamole configuration behind load balancer
- Ability to enable/disable features in the ribbon
- Minor visual changes in icons
- Upgrade to Guacamole 0.9.14

1.7.1 - Chrome audio fix
- Added icon for enabling/disablig audio in Chrome https://developers.google.com/web/updates/2017/09/autoplay-policy-changes

1.7.0 - Fall 2018 Release
- Changed visual appearance of ribbon
- File manipulation dialogs updated
- Added file details in dialogs
- Removed unused features in sharing mode
- Added server side notifications
- Added checking network quality

1.6.2
- Added blanking of screen while snapshot operations (save/restore) are running.

1.6.1 - Summer 2018 Release
- Minor text and visual changes.
- Added expired session screen, removed login windows on session expiration.
- Changed messages in snapshots dialog.

1.6 - Summer 2018 Release
- Removed obsolete file upload/download code (SessionUpload/SessionDownload).
- Added later button to intro.
- Added licensing for snapshots feature.
- Added snapshots management feature.

1.5 - Spring 2018 Release
- Added file manager.
- Added full screen mode.
- Minor fixes to intro.
- Minor visual improvement.
- Reduced code size, improved loading.
- Made error messages more user friendly.

1.4.1 - Winter 2018 Release
- Added intro when the application if first time used.
- Fixed share links not visible bugs.
- Minor visual changes in all applications.
- Fixed timezone calculation for charts.
- Optimized sharing calls.
- Added translation string.

1.4.0
- Added chart with user and group activity and usage duration.
- Visual changes to the ribbon and sharing menu
- Added support for information box
- Bugfixes

1.3.0
- Upgrate to Guacamole 0.9.13

