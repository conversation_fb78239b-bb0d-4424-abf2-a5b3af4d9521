/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays portia chat bot
 */
angular.module('ribbon').directive('portiaChatbot', [function portiaChatbot() {

    return {
        restrict: 'E',
        scope: {
            url: '='
        },

        templateUrl: 'app/ext/ribbon/templates/portia-chatbot.html',
        controller: ['$scope', '$injector', '$rootScope',
            function portiaChatbotController($scope, $injector, $rootScope) {

            var $routeParams          = $injector.get('$routeParams');
            var $window               = $injector.get('$window');
            $scope.ribbonService      = $injector.get('ribbonService');
            $scope.infoService        = $injector.get('infoService');
            var sce                   = $injector.get('$sce');

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

            const messageHandlers = {
                ready_chatbot: (event) => {
                    const userId = $scope.ribbonService.licenses.userId;
                    const username = $scope.ribbonService.licenses.cloudUsername;

                    
                    const userInfoObject = {
                        userId: userId,
                        username: username
                    };
                    event.source.postMessage({ 
                        type: 'USER_INFO', 
                        userInfo: userInfoObject 
                    }, event.origin);
                },

                reduce_resolution: () => {
                    try {
                        lowResolution();
                    } catch (error) {
                        console.error("Failed to reduce the resolution:", error);
                    }
                },

                improved_resolution: () => {
                    try {
                        enhanceResolution();
                    } catch (error) {
                        console.error("Failed to improve the resolution:", error);
                    }
                },

                reconnect_hs_operation: () => {
                    try {
                        $rootScope.$applyAsync(() => {
                            $rootScope.$broadcast('reconnect_hs_action');
                        });
                    } catch (error) {
                        console.error("Failed to reconnect:", error);
                    }
                },

                portia_clicked: () => {
                    try {
                        // Google Tag Manager
                        dataLayer.push({
                            event: 'Submit questions to Portia',
                            button_name: 'btn_ribbon_submit_questions_to_portia',
                            sub_domain: $scope.ribbonService.licenses.subdomain
                        });
                    } catch (error) {
                        console.error("Failed to catch portia click event:", error);
                    }
                }
            };

            function handleMessage(event) {

                try {
                    const parsedUrl = new URL($scope.ribbonService.chatbotUrl);
                    if (parsedUrl.origin !== event.origin)
                        return;
                } catch (e) {
                    console.error("Invalid URL:", e.message);
                    return;
                }

                const handler = messageHandlers[event.data.type];
                if (handler) {
                    handler(event);
                } else {
                    console.warn("No handler defined for message type:", event.data.type);
                }
            }

            $window.addEventListener('message', handleMessage);

            $scope.$on('$destroy', function() {
                $window.removeEventListener('message', handleMessage);
            });

            function lowResolution() {
                $scope.ribbonService.streamingMode="Faster streaming";
                $scope.$apply(function() {
                    $rootScope.$broadcast('StreamingMode');
                });
            }

            function enhanceResolution() {
                $scope.ribbonService.streamingMode="Better quality";
                $scope.$apply(function() {
                    $rootScope.$broadcast('StreamingMode');
                });
            }

            $scope.formatChatbotUrl = function(fbUrl) {
                let currentProtocol = $window.location.protocol;
                const hasProtocol = /^https?:\/\//i.test(fbUrl);
                const isLocalhost = /localhost|127\.0\.0\.1/.test($window.location.hostname);
        
                if (!hasProtocol) {
                    if (isLocalhost) {
                        fbUrl = 'https://' + fbUrl;
                    } else {
                        fbUrl = currentProtocol + '//' + fbUrl;
                    }
                }
        
                return fbUrl;
            };

            $scope.getChatbotUrl = function() {
                var formattedFbUrl = $scope.formatChatbotUrl($scope.url);
                return sce.trustAsResourceUrl(formattedFbUrl);
            }

            $scope.closeDialog = function closeDialog() {
                $scope.ribbonService.chatbotVisible = false;
            };
        }]
    };
}]);
