/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays HelpCenter dialog
 */
angular.module('ribbon').directive('helpCenterPopup', [function helpCenterPopup() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/help-center-popup.html',
        controller: ['$scope', '$injector', '$timeout', '$rootScope', function helpCenterPopupController($scope, $injector, $timeout, $rootScope) {

            // Required services
            $scope.ribbonService     = $injector.get('ribbonService');
            var authenticationService = $injector.get('authenticationService');
            var ClientIdentifier     = $injector.get('ClientIdentifier');
            var $window              = $injector.get('$window');
            var $routeParams         = $injector.get('$routeParams');
            var $http                = $injector.get('$http');

            $scope.hyperstreamVersion = "";
            $rootScope.helpName = "";
            $rootScope.helpURL = "";

            // Default support request link
            var DEFAULT_SUPPORT_REQUEST_LINK = "https://www.apporto.com/support";
            var SHORT_DEFAULT_SUPPORT_REQUEST_LINK = "apporto.com/support";

            // Default value of support information
            const DEFAULT_SUPPORT_INFO_VALUE = "unknown";

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

            // Open the User Guide
            $scope.openUserGuide = async function openUserGuide() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Info -> Help Center',
                    button_name: 'btn_ribbon_info_help_center',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                if ($rootScope.isKioskMode) {
                    $rootScope.helpName = $scope.ribbonService.licenses.userGuideMenuName;
                    $rootScope.helpURL = $scope.ribbonService.licenses.userGuideMenuLink;
                    $rootScope.inPageDialog();
                }
                else {
                   $window.open($scope.ribbonService.licenses.userGuideMenuLink, "_blank");
                }
            }

            // Request the Support
            $scope.openSupport = function openSupport() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Info -> Support request',
                    button_name: 'btn_ribbon_info_support_request',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $http.get('app/build_info/client_version.json').then(function(response) {
                    var data = response.data;
                    var clientData = data['client'][0];
                    $scope.hyperstreamVersion = clientData['version'] +
                        (clientData['build-number'] != null ? ("-" + clientData['build-number']) : "");
                })
                .catch(function(response) {
                    $scope.hyperstreamVersion = DEFAULT_SUPPORT_INFO_VALUE;
                    console.warn(response.message);
                })
                .finally(function() {
                    // Call this function after updating supportLink
                    $timeout(function () {
                        gotoSupportLink();
                    }, 200);

                });

                var gotoSupportLink = function gotoSupportLink() {

                    // Check whether support link is default link
                    if ($scope.ribbonService.licenses.supportLink == DEFAULT_SUPPORT_REQUEST_LINK ||
                        $scope.ribbonService.licenses.supportLink == SHORT_DEFAULT_SUPPORT_REQUEST_LINK) {

                        // If the link is the default value, automatically populate Support Request form with session data
                        var connectionId = DEFAULT_SUPPORT_INFO_VALUE;
                        var userName = DEFAULT_SUPPORT_INFO_VALUE;
                        var osVersion = DEFAULT_SUPPORT_INFO_VALUE;;
                        var browserType = DEFAULT_SUPPORT_INFO_VALUE;
                        var browserVersion = DEFAULT_SUPPORT_INFO_VALUE;
                        var cpu = DEFAULT_SUPPORT_INFO_VALUE;
                        var ram = DEFAULT_SUPPORT_INFO_VALUE;
                        var gpu = DEFAULT_SUPPORT_INFO_VALUE;
                        var displayResolution = DEFAULT_SUPPORT_INFO_VALUE;
                        var osScale = DEFAULT_SUPPORT_INFO_VALUE;
                        var hyperstreamScale = DEFAULT_SUPPORT_INFO_VALUE;
                        var browserLanguage = DEFAULT_SUPPORT_INFO_VALUE;
                        var charset = DEFAULT_SUPPORT_INFO_VALUE;

                        // Connection id, user name, os version
                        connectionId = ClientIdentifier.fromString($routeParams.id).id;
                        userName = authenticationService.getCurrentUsername();
                        if (window.navigator.userAgent.indexOf("Windows NT 10.0")!= -1) osVersion="Windows 10";
                        if (window.navigator.userAgent.indexOf("Windows NT 6.3") != -1) osVersion="Windows 8.1";
                        if (window.navigator.userAgent.indexOf("Windows NT 6.2") != -1) osVersion="Windows 8";
                        if (window.navigator.userAgent.indexOf("Windows NT 6.1") != -1) osVersion="Windows 7";
                        if (window.navigator.userAgent.indexOf("Windows NT 6.0") != -1) osVersion="Windows Vista";
                        if (window.navigator.userAgent.indexOf("Windows NT 5.1") != -1) osVersion="Windows XP";
                        if (window.navigator.userAgent.indexOf("Windows NT 5.0") != -1) osVersion="Windows 2000";
                        if (window.navigator.userAgent.indexOf("Mac")            != -1) osVersion="Mac/iOS";
                        if (window.navigator.userAgent.indexOf("X11")            != -1) osVersion="UNIX";
                        if (window.navigator.userAgent.indexOf("Linux")          != -1) osVersion="Linux";

                        // Browser type, version
                        if ($scope.ribbonService.browser.isChrome) {
                            browserType = "Chrome";
                        }
                        else if ($scope.ribbonService.browser.isOpera) {
                            browserType = "Opera";
                        }
                        else if ($scope.ribbonService.browser.isFirefox) {
                            browserType = "Firefox";
                        }
                        else if ($scope.ribbonService.browser.isSafari) {
                            browserType = "Safari";
                        }
                        else if ($scope.ribbonService.browser.isIE) {
                            browserType = "IE";
                        }
                        else if ($scope.ribbonService.browser.isEdge) {
                            browserType = "Edge";
                        }
                        else if ($scope.ribbonService.browser.isBlink) {
                            browserType = "Blink";
                        }
                        browserVersion = $scope.ribbonService.browser.detailVersion;

                        // Ram, Hyperstream and OS scale, Hyperstream(Guac client) version, display resolution
                        ram = navigator.deviceMemory + "GB"; // Not accurate
                        if (ram == "8GB") {
                            ram = "8GB or more";
                        }
                        osScale = $scope.ribbonService.scale;
                        hyperstreamScale = window.devicePixelRatio * 100;

                        displayResolution = $rootScope.width + "x" + $rootScope.height;

                        // Browser language and charset
                        browserLanguage = navigator.language;
                        charset = document.characterSet;

                        // Core count of CPU and the hardware acceleration status
                        cpu = navigator.hardwareConcurrency + " cores";
                        gpu = $rootScope.bHardwareAccelerated ? "yes" : "no";

                        // User Information
                        firstName = $scope.ribbonService.userinfo.name.split(' ')[0];
                        lastName = (typeof $scope.ribbonService.userinfo.name.split(' ')[1] != 'undefined') ?
                                    $scope.ribbonService.userinfo.name.split(' ')[1] : "";
                        company = $scope.ribbonService.licenses.subdomain.split('.')[0];
                        email = $scope.ribbonService.userinfo.email;

                        // Latency
                        var sum = 0; latencyMin = 0; latencyMax = 0;
                        if ($scope.ribbonService.arrayLatency.length <= 30) {
                            latencyMax = $scope.ribbonService.arrayLatency[0];
                            latencyMin = $scope.ribbonService.arrayLatency[0];

                            // Calculate sum of the latest time(up to 1 minute) latency values
                            for (i = 0; i < $scope.ribbonService.arrayLatency.length; i ++) {
                                sum += $scope.ribbonService.arrayLatency[i];
                                if($scope.ribbonService.arrayLatency[i] > latencyMax)
                                    latencyMax = $scope.ribbonService.arrayLatency[i];
                                if($scope.ribbonService.arrayLatency[i] < latencyMin)
                                    latencyMin = $scope.ribbonService.arrayLatency[i];
                            }

                            latencyMedian = Math.round(sum / $scope.ribbonService.arrayLatency.length);
                        }
                        else {
                            latencyMedian = 0;
                        }

                        var httpParameters = {
                            wpf3762_14: connectionId,
                            wpf3762_15: $scope.ribbonService.licenses.cloudUsername,
                            wpf3762_16: osVersion,
                            wpf3762_17: browserType,
                            wpf3762_18: browserVersion,
                            wpf3762_19: cpu,
                            wpf3762_20: ram,
                            wpf3762_21: gpu,
                            wpf3762_23: displayResolution,
                            wpf3762_24: osScale,
                            wpf3762_25: hyperstreamScale,
                            wpf3762_26: $scope.hyperstreamVersion,
                            wpf3762_27: browserLanguage,
                            wpf3762_28: charset,
                            wpf3762_29: latencyMin,
                            wpf3762_30: latencyMax,
                            wpf3762_31: latencyMedian,
                            wpf3762_3: firstName,
                            wpf3762_4: lastName,
                            wpf3762_5: company,
                            wpf3762_1: email,
                        };

                        var parameters = "";
                        for (var key in httpParameters) {
                            parameters += key + "=" + httpParameters[key] + "&";
                        }

                        // Remove spaces, plus and last "&" character
                        parameters = parameters.replace(/\s/g, '%20');
                        parameters = parameters.replace(/\+/g, '%2B');
                        parameters = parameters.slice(0, -1);

                        var support_url = DEFAULT_SUPPORT_REQUEST_LINK + "?" + parameters
                        if ($rootScope.isKioskMode) {
                            $rootScope.helpName = $scope.ribbonService.licenses.supportMenuName;
                            $rootScope.helpURL = support_url;
                            $rootScope.inPageDialog();
                        }
                        else {
                            $window.open(support_url, "_blank");
                        }
                    }
                    else {
                        // If the link is not default value, go to custom link
                        if ($rootScope.isKioskMode) {
                            $rootScope.helpName = $scope.ribbonService.licenses.supportMenuName;
                            $rootScope.helpURL = $scope.ribbonService.licenses.supportLink;
                            $rootScope.inPageDialog();
                        }
                        else {
                            $window.open($scope.ribbonService.licenses.supportLink, "_blank");
                        }
                    }
                }

            }

            // Open Enable Logging dialog
            $scope.openEnableLoggingDialog = function openEnableLoggingDialog() {
                $scope.ribbonService.enableLoggingDialogVisible = true;
            }

            // Open About Apporto dialog
            $scope.openAboutApportoDialog = function openAboutApportoDialog() {
                $scope.ribbonService.aboutApportoDialogVisible = true;
            }

        }] // end HelpCenter dialog controller

    };
}]);
