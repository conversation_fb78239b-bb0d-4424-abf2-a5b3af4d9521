/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays chatting
 */

angular.module('ribbon').provider('adrConfig', function adrConfigProvider() {
    //defaults
    var defaultConfig = {
        iconPosition: [3, 3],
        mode: 'all',
        modes: ['all', 'horizontal', 'vertical']
    };
    var config = angular.extend({}, defaultConfig);
    this.$get = [function () {
        return {
            iconPosition: config.iconPosition,
            mode: config.mode,
            modes: config.modes
        };
    }];
}).directive('ngResize', ['adrConfig', '$document', function resize(adrConfig, $document) {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            var dimension = {};
            var iconPosition = adrConfig.iconPosition;
            var mode = attr.resize && adrConfig.modes.indexOf(attr.resize) > -1 ? attr.resize : adrConfig.mode;
            var position = {};
            //create button for resizing
            var btn = document.createElement("span");
            btn.style.width = '15px';
            btn.style.height = '15px';
            btn.innerHTML = "<svg>\
                <circle cx='12.5' cy='2.5' r='2' fill='#777777'></circle>\
                <circle cx='7.5' cy='7.5' r='2' fill='#777777'></circle>\
                <circle cx='12.5' cy='7.5' r='2' fill='#424242'></circle>\
                <circle cx='2.5' cy='12.5' r='2' fill='#777777'></circle>\
                <circle cx='7.5' cy='12.5' r='2' fill='#424242'></circle>\
                <circle cx='12.5' cy='12.5' r='2' fill='#212121'></circle></svg>";
            btn.style.bottom = iconPosition[0] + 'px';
            btn.style.right = iconPosition[1] + 'px';
            btn.style.position = 'absolute';
            btn.style.visibility = 'hidden';
            if (mode == 'horizontal') {
                btn.style.cursor = 'ew-resize';
            }
            else if (mode == 'vertical') {
                btn.style.cursor = 'ns-resize';
            }
            else {
                btn.style.cursor = 'nwse-resize';
            }
            //bind resize function to button;
            btn.onmousedown = function ($event) {
                $event.stopImmediatePropagation();
                position.x = $event.clientX;
                position.y = $event.clientY;
                dimension.width = element.prop('offsetWidth');
                dimension.height = element.prop('offsetHeight');
                $document.bind('mousemove', mousemove);
                $document.bind('mouseup', mouseup);
                return false;
            };
            function mousemove($event) {
                var deltaWidth = dimension.width - (position.x - $event.clientX);
                var deltaHeight = dimension.height - (position.y - $event.clientY);
                var newDimensions = {};
                if (mode == 'horizontal') {
                    newDimensions = {
                        width: deltaWidth + 'px'
                    };
                }
                else if (mode == 'vertical') {
                    newDimensions = {
                        height: deltaHeight + 'px'
                    };
                }
                else {
                    newDimensions = {
                        width: deltaWidth + 'px',
                        height: deltaHeight + 'px'
                    };
                }
                element.css(newDimensions);
                return false;
            }
            function mouseup() {
                $document.unbind('mousemove', mousemove);
                $document.unbind('mouseup', mouseup);
            }
            element.append(btn);
            //show button on hover
            element.bind('mouseover', function () {
                btn.style.visibility = 'visible';
            });
            element.bind('mouseout', function () {
                btn.style.visibility = 'hidden';
            });
        }
    };
}]).directive('ngDraggable', [function draggable() {
    return {
        restrict: 'A',
        scope: {
            dragOptions: '=ngDraggable'
        },
        link: function (scope, element, attrs) {
            if (scope.dragOptions.container && document.getElementsByClassName(scope.dragOptions.container)) {
                var container = document.getElementsByClassName(scope.dragOptions.container);
            }
            element.draggable({
                containment: container,
                handle: scope.dragOptions.handle
            });

            $(window).resize(function () {
                if (container && container.length > 0) {
                    var width = element[0].offsetWidth,
                        height = element[0].offsetHeight,
                        left = element[0].offsetLeft,
                        top = element[0].offsetTop;
                    if (!!container[0]) {
                        if (top + height > container[0].offsetTop + container[0].offsetHeight) {
                            element.css({
                                top: (container[0].offsetTop + container[0].offsetHeight - height) + 'px',
                            })
                        }
                        if (left + width > container[0].offsetLeft + container[0].offsetWidth) {
                            element.css({
                                left: (container[0].offsetLeft + container[0].offsetWidth - width) + 'px',
                            })
                        }
                    }
                }
            });
        }
    }
}]);
