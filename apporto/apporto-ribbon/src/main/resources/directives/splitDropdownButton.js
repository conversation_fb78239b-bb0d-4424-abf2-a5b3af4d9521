/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays split dropdown button.
 * Enables specifing 
 */
angular.module('ribbon').directive('splitDropdownButton', [function splitDropdownButton() {

    return {
        restrict: 'E',
        scope: {
            menuItems: '=',
            buttonClick: '&',
            menuClick: '&'
        },

        templateUrl: 'app/ext/ribbon/templates/split-dropdown-button.html',
        controller: ['$scope', '$injector', function splitDropdownButtonController($scope, $injector) {
            $scope.ribbonService  = $injector.get('ribbonService');
            $scope.ribbonService.showDropDown = false;

            $scope.toggleDropdown = function toggleDropdown() {
                $scope.ribbonService.showDropDown = !$scope.ribbonService.showDropDown
                $scope.ribbonService.settingPopupVisible = false;
                $scope.ribbonService.morePopupVisible = false;
                $scope.ribbonService.networkQualityDialogVisible = false;
                $scope.ribbonService.helpCenterPopupVisible = false;
            }

            $scope.mainButtonClick = function() {
                $scope.ribbonService.showDropDown = false;
                $scope.buttonClick(); 
            }

            $scope.menuItemClick = function (index) {
                $scope.ribbonService.showDropDown = false;
                $scope.menuClick({index: index});
            }
        }]

    };
}]);
