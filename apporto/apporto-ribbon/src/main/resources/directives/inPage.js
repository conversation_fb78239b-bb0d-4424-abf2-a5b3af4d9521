/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays HelpCenter dialog
 */
angular.module('ribbon').directive('inPage', [function inPage() {

    return {
        templateUrl: 'app/ext/ribbon/templates/in-page.html',
        controller: ['$scope', '$injector', '$rootScope', function inPageController($scope, $injector, $rootScope) {

            // Required services
            $scope.ribbonService = $injector.get('ribbonService');

            $scope.ribbonService.inPageMinimized = false;

            // Open the Help Menu URL in page
            $rootScope.inPageDialog = function inPageDialog() {
                $scope.ribbonService.inPageVisible = true;
                $scope.ribbonService.isInPage = true;

                $('.inPage-left-username').text($rootScope.helpName);

                setTimeout(function () {
                    $('#view_el').attr('src', $rootScope.helpURL);
                }, 0);
            }

            $scope.closeInPageDialog = function closeInPageDialog() {
                $scope.ribbonService.inPageVisible = false;
                $scope.ribbonService.inPageMinimized = false;
            }

            $scope.minInPageDialog = function minInPageDialog() {
                $scope.ribbonService.inPageVisible = false;
                $scope.ribbonService.inPageMinimized = true;
            }

            $rootScope.expandInPage = function expandInPage() {
                $scope.ribbonService.inPageVisible = true;
                $scope.ribbonService.inPageMinimized = false;

                setTimeout(function () {
                    $('#view_el').attr('src', $rootScope.helpURL);
                }, 0);
            }
        }]
    };
}]);
