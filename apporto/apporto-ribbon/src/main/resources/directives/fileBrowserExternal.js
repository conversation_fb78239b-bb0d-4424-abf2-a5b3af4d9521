/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays new, external, file browser
 */
angular.module('ribbon').directive('fileBrowserExternal', [function fileBrowserExternal() {

    return {
        restrict: 'E',
        scope: {
            url: '='
        },

        templateUrl: 'app/ext/ribbon/templates/file-browser-external.html',
        controller: ['$scope', '$injector', '$window', function fileBrowserExternal($scope, $injector, $window) {

            var ribbonService      = $injector.get('ribbonService');
            var $timeout           = $injector.get('$timeout');
            var sce                = $injector.get('$sce');
            var fileBrowserService = $injector.get('fileBrowserService');

            var isMinimized = false;

            const MIN_MAX = ["–", "□"];
            $scope.minMaxButton = MIN_MAX[0];
            $scope.minMaxText = "Minimize";

            $scope.formatFbUrl = function(fbUrl) {
                let currentProtocol = $window.location.protocol;
                
                const hasProtocol = /^https?:\/\//i.test(fbUrl);
                
                const isLocalhost = /localhost|127\.0\.0\.1/.test($window.location.hostname);
        
                if (!hasProtocol) {
                    if (isLocalhost) {
                        fbUrl = 'https://' + fbUrl;
                    } else {
                        fbUrl = currentProtocol + '//' + fbUrl;
                    }
                }
        
                return fbUrl;
            };

            $scope.getTrustedUrl = function() {
                var formattedFbUrl = $scope.formatFbUrl($scope.url);
                
                return sce.trustAsResourceUrl(formattedFbUrl);
            }

            $scope.closeDialog = function closeDialog() {
                ribbonService.innerFileBrowserVisible = false;
                fileBrowserService.logoutFB($scope.url);

                $scope.url = 'about:blank';
            }

            $scope.toggleDialog = function toggleDialog() {
                if (isMinimized) {
                    maximizeDialog();
                    $scope.minMaxButton = MIN_MAX[0];   // Minimize button
                    $scope.minMaxText = "Minimize";
                } 
                else {
                    minimizeDialog();
                    $scope.minMaxButton = MIN_MAX[1];   // Maximize button
                    $scope.minMaxText = "Maximize";
                }

                isMinimized = !isMinimized;
            }

            function maximizeDialog() {
                $scope.container.style.left = "10vw";
                $scope.container.style.top = "50px";
                $scope.container.style.opacity = "unset";
                $scope.fbElement.style.width = "80vw";
                $scope.fbElement.style.height = "80vh";
            }

            function minimizeDialog() {
                $scope.fbElement.style.width = "15vw";
                $scope.fbElement.style.height = "15vh";

                // Calculate postion of minimized file browser left of the messenger button
                const scrollHeight = $scope.fbElement.scrollHeight;
                $scope.container.style.left = "calc(85vw - 120px)";
                $scope.container.style.top = `calc(100vh - ${scrollHeight}px - 40px)`;

                $scope.container.style.opacity = "0.66";
            }
        }],
        link: function(scope, element) {
            scope.container = element[0].parentElement;
            scope.fbElement = element[0].getElementsByClassName("file-browser-external")[0];
        }
    };
}]);
