/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays about-apporto-dialog
 */
 angular.module('ribbon').directive('enableLoggingDialog', [function enableLoggingDialog() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/enable-logging-dialog.html',
        controller: ['$scope', '$rootScope', '$routeParams', '$injector', '$timeout', function enableLoggingDialogController($scope, $rootScope, $routeParams, $injector, $timeout) {

            // This service is required by directive used in ribbon template html
            $scope.ribbonService      = $injector.get('ribbonService');
            var guacClientManager     = $injector.get('guacClientManager');
            var infoService           = $injector.get('infoService');
            var $translate            = $injector.get('$translate');

            /**
             * The current Guacamole client instance.
             *
             * @type Guacamole.Client
             */
            var client = null;

            // Default logging duration (minutes)
            DEFAULT_LOGGING_DURATION = 5;

            // Start logging
            $scope.startLogging = function startLogging() {

                $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                // Get client
                client = $scope.client.client;

                // Sends "metrics" instruction with "1" to guacd
                client.sendStartMeasure();

                var key = "ENABLE_LOGGING.LOGGING_START_MESSAGE";

                // Show logging start notification
                $translate(key).then(function showMessage(msg) {
                    infoService.infoText = msg;
                    infoService.infoDialogVisible = true;
                    infoService.top = true;
                });

                // To set the Enable Logging button disable while logging
                $scope.ribbonService.loggingEnabled = true;
                window.loggingEnabled = true;

                // Initialize variable for the JavaScript logging file
                window.measurePerformance = "";

                // User name and connection_id
                window.measurePerformance += new Date().toISOString() + " : " + "Username, connection_id => [" +
                                            $scope.ribbonService.licenses.default_username + "]:[" + $scope.ribbonService.connectionId + "]" + `\n`;
                // Instance
                window.measurePerformance += new Date().toISOString() + " : " + "Instance => " + $scope.ribbonService.licenses.subdomain + `\n`;

                // App Name
                window.measurePerformance += new Date().toISOString() + " : " + "App Name => " + $scope.ribbonService.licenses.appname + `\n`;

                // Customer's OS
                window.measurePerformance += new Date().toISOString() + " : " + "OS => " + $scope.ribbonService.platform.osName + " " +
                                            $scope.ribbonService.platform.osVersion + `\n`;
                // Customer's Browser
                window.measurePerformance += new Date().toISOString() + " : " + "Browser => " + $scope.ribbonService.customerBrowser + " " +
                                            $scope.ribbonService.browser.detailVersion + `\n`
                // GPU
                window.measurePerformance += new Date().toISOString() + " : " + "GPU => " + $scope.ribbonService.licenses.hardWareAccelerated + `\n`;

                // Display Scale
                window.measurePerformance += new Date().toISOString() + " : " + "Display Scale => " +
                                            ($scope.ribbonService.scale == 0 ? 100 : $scope.ribbonService.scale) + `\n`;
                // Display Resolution
                window.measurePerformance += new Date().toISOString() + " : " + "Display Resolution => " + $rootScope.width + " x " +
                                            $rootScope.height + `\n`;

                // Stream mode was switched from H264 to non-H264 by RDS
                if (client.getStreamModeSwitched)
                    window.measurePerformance += new Date().toISOString() + " : " + "The RDS is sending the non-H264 stream in the H264 mode." + `\n`;

                $timeout(function () {
                    const blob = new Blob([window.measurePerformance], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    const now = new Date();
                    link.href = url;
                    link.download = 'Hyperstream-log-' + now.toISOString() + '.txt';
                    link.click();

                    // To set the Enable Logging Button active  
                    $scope.ribbonService.loggingEnabled = false;
                    window.loggingEnabled = false;

                    // Sents "metrics" instruction with "0" to guacd
                    client.sendStopMeasure();

                    var key = "ENABLE_LOGGING.LOGGING_STOP_MESSAGE";
                    // Show logging start message
                    $translate(key).then(function showMessage(msg) {
                        infoService.infoText = msg;
                        infoService.infoDialogVisible = true;
                        infoService.top = true;
                    });

                }, DEFAULT_LOGGING_DURATION * 60 * 1000);

                $scope.ribbonService.enableLoggingDialogVisible = false;

            }

            // Close enable-logging-dialog
            $scope.closeDialog = function closeDialog() {
                $scope.ribbonService.loggingEnabled = false;
                $scope.ribbonService.enableLoggingDialogVisible = false;
            }
        }] // end enable-logging-dialog controller

    };
}]);
