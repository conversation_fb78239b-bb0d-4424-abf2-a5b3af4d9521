/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays loading circle
 */
angular.module('ribbon').directive('mmonitor', [function circleLoader() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/mmonitor.html',
        controller: ['$scope', '$injector', '$timeout', function circleLoaderController($scope, $injector, $timeout) {

            $scope.ribbonService = $injector.get('ribbonService');
            
            // Create new sharing links after stop sharing session in classroom for professor 
            $scope.$on('$loadMultiMonitor', function(){
            	
            	$scope.ribbonService.mmLoad = true;
                $timeout(function(){ $scope.ribbonService.mmLoad = false;}, 10000);
            });

            $scope.$watch('ribbonService.mmLoad', function(newValue) {
                if (newValue === true) {
                    $timeout(function() {
                        $scope.ribbonService.mmLoad = false;
                    }, 30000);
                }
            });
        }] // end circle loader controller   
     };
}]);

