/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays ribbon share dialog
 */
angular.module('ribbon').directive('ribbonShareDialog', [function ribbonShareDialog() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/ribbon-share-dialog.html',
        controller: ['$scope', '$injector', '$rootScope', '$location',
            function ribbonShareDialogController($scope, $injector, $rootScope, $location) {

            var ManagedClient         = $injector.get('ManagedClient');
            var guacClientManager     = $injector.get('guacClientManager');
            var tunnelService         = $injector.get('tunnelService');
            var $translate            = $injector.get('$translate');
            var infoService           = $injector.get('infoService');
            var $routeParams          = $injector.get('$routeParams');
            var $timeout              = $injector.get('$timeout');
            var $interval             = $injector.get('$interval');
            var ClientIdentifier      = $injector.get('ClientIdentifier');
            var $window               = $injector.get('$window');
            var $rootScope            = $injector.get('$rootScope');
            var $http                 = $injector.get('$http');
            var authenticationService = $injector.get('authenticationService');
            var ManagedShareLink      = $injector.get('ManagedShareLink');

            // When started, periodically query client until it is possible to retrieve sharing links.
            var queryClient;

            if($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                return;
            }

            $scope.ribbonService      = $injector.get('ribbonService');

            // This service is required by directive used in ribbon template html
            $scope.infoService        = $injector.get('infoService');

            $scope.client = null;

            // Collaborate type
            $scope.collaborateType = {
                NOW: "now",
                OFFLINE: "offline"
            };

            // Expiration link type for the collaborate offline
            $scope.expirationType = {
                ONE_DAY: "one day",
                ONE_WEEK: "one week"
            };

            $scope.collaborateOneDayLink = null;
            $scope.collaborateOneWeekLink = null;
            // To check the collaborate offline link is created or not
            $scope.collaborateLinkCreated = false;

            $scope.model = {
                url: null,
                selectedCollaborate: $scope.collaborateType.NOW, // initial type "now"
                selectedExpirationType: $scope.expirationType.ONE_DAY // initial type "one day"
            };
            $scope.shareKeys = [];

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

            /**
             * Watch the multi-monitors
             */
            $scope.ribbonService.isOpenSecondMonitor = false;
            $scope.ribbonService.isOpenThirdMonitor = false;
            $scope.ribbonService.isLinkCopied = false;
            $scope.ribbonService.isSharingStart = false;
            $rootScope.mmonitor2Win = [];
            $rootScope.mmonitor3Win = [];
            var idleWatchMMonitor2 = null;
            var idleWatchMMonitor3 = null;
            var SECOND_UNIT = 1 * 1000; // 1 s

            $scope.closeDialog = function closeDialog() {
                // Close ribbon share dialog
                $scope.ribbonService.ribbonShareDialogVisible = false;
                // To show the dialog for selecting the collaborate now or offline
                $scope.ribbonService.nextButtonClicked = false;
                // To make the collaborate now option always checked when first loading
                $scope.model.selectedCollaborate = $scope.collaborateType.NOW;

                $scope.ribbonService.isLinkCopied = false;
                // To make the close dialog button disable while creating sharing profile
                if (!$scope.ribbonService.shareLinkCopied) {
                    $scope.model.url = null;
                }
            }

            $scope.onCloseWindow = function() {
                if($scope.shareLinkVO !== DEFAULT_VO_LINK) {
                    $scope.ribbonService.shareLinkCopied = $scope.shareLinkVO;
                }

                if($scope.shareLinkFC !== DEFAULT_FC_LINK) {
                    $scope.ribbonService.shareLinkCopied = $scope.shareLinkFC;
                }

                angular.forEach($scope.shareKeys, function (profile) {
                    $rootScope.stopSharing(profile);
                });
                $scope.shareKeys = [];
            }

            // Get client reference when route changes
            $scope.$on('$routeChangeSuccess', getClient);

            // Create new sharing links after stop sharing session in classroom for professor
            $rootScope.$on('$fillClassroom', function() {
                getClient();
            });

            // Reset the desktop size
            $rootScope.resetDesktop = function resetDesktop() {
                if ($scope.client == null) {
                    console.error("Can't get the client.");
                    return;
                }

                var client = $scope.client.client;
                client.sendDisplayUpdate();
            }

            // Watch the muli-monitor
            function watchMultiMonitor2() {
                var i = 0;
                while ( i < $rootScope.mmonitor2Win.length) {
                    if ($rootScope.mmonitor2Win[i] != null && $rootScope.mmonitor2Win[i].closed) {
                        $rootScope.mmonitor2Win.splice(i, 1);
                        continue;
                    }
                    i++;
                }

                if ($rootScope.mmonitor2Win.length == 0) {
                    // When closing the 2nd monitor, if the 3rd monitor is opened, close it as well
                    if ($rootScope.mmonitor3Win && $scope.ribbonService.isOpenThirdMonitor) {
                        for (var i = 0; i < $rootScope.mmonitor3Win.length; i++) {
                            if ($rootScope.mmonitor3Win[i] != null)
                                $rootScope.mmonitor3Win[i].close();
                            $rootScope.mmonitor3Win.splice(i, 1);
                        }
                        $scope.ribbonService.isOpenThirdMonitor = false;
                    }

                    // Open 2nd monitor if the streaming mode is "Better quality"
                    if ($scope.ribbonService.streamingMode == $scope.ribbonService.streamingModeOptions[2]) {
                        $scope.ribbonService.isOpenSecondMonitor = false;
                    }
                    $interval.cancel(idleWatchMMonitor2);
                }

                if ($rootScope.mmonitor2Win.length == 0 && $rootScope.mmonitor3Win.length == 0) {
                    $rootScope.resetDesktop();
                    $scope.$emit('$closedMultiMonitor');
                }
            }

            function watchMultiMonitor3() {
                var i = 0;
                while ( i < $rootScope.mmonitor3Win.length) {
                    if ($rootScope.mmonitor3Win[i] != null && $rootScope.mmonitor3Win[i].closed) {
                        $rootScope.mmonitor3Win.splice(i, 1);
                        continue;
                    }
                    i++;
                }

                if ($rootScope.mmonitor3Win.length == 0) {
                    // Open 3rd monitor if the streaming mode is "Better quality"
                    if ($scope.ribbonService.streamingMode == $scope.ribbonService.streamingModeOptions[2]) {
                        $scope.ribbonService.isOpenThirdMonitor = false;
                    }
                    $interval.cancel(idleWatchMMonitor3);
                }

                if ($rootScope.mmonitor2Win.length == 0 && $rootScope.mmonitor3Win.length == 0) {
                    $rootScope.resetDesktop();
                }
            }

            $scope.$watch('client', function (client) {
                if (client != null && $scope.client.shareLinks && $scope.ribbonService.serverId != null) {
                    $scope.ribbonService.mMonitorEnabled = true;
                }
            });

            $scope.$watch('client.shareLinks', function (shareLinks) {
                if ($scope.client != null && shareLinks && $scope.ribbonService.serverId != null) {
                    $scope.ribbonService.mMonitorEnabled = true;
                }
            });

            $scope.$watch('ribbonService.serverId', function (serverId) {
                if ($scope.client != null && $scope.client.shareLinks && serverId != null) {
                    $scope.ribbonService.mMonitorEnabled = true;
                }
            });

            // Open the multi-monitor
            $scope.$on('$openedMultiMonitor', function() {
                getClient();
                openMultiMonitor();
            });

            // Open the Multi Monitor
            function openMultiMonitor() {
                createShareLinksForMMonitor();

                // If current app is Linux app and resize-method is `display-update`,
                //   set a local storage value and reconnect with `reconnect` resize-method
                // For more info, please check AP-8278
                // resize-method = display-update works for H264 Linux RDS. For more info, please check AP-9470, AP-10177
                if ($scope.ribbonService.licenses.hasLinuxLicence && !$scope.ribbonService.licenses.hasH264Licence &&
                    $scope.ribbonService.resizeMethod == $scope.ribbonService.RESIZE_METHOD.DISPLAY_UPDATE) {
                    $window.localStorage.setItem($rootScope.updatedResizeMethod, "true");
                    $window.location.href = $window.location.href + '&resize-method=reconnect';
                    if (idleWatchMMonitor2 != null) $interval.cancel(idleWatchMMonitor2);
                    return;
                }

                // Watermark
                var param = '';
                if ($scope.ribbonService.licenses.hasWatermarkLicence) {
                    param = '&wm=true';
                }

                param += '&resizeMethod=' + $scope.ribbonService.resizeMethod;

                if ($rootScope.mmonitor2Win.length == 0) {
                    // Google Tag Manager
                    dataLayer.push({
                        event: '2nd monitor',
                        button_name: 'btn_ribbon_2nd_monitor',
                        sub_domain: $scope.ribbonService.licenses.subdomain
                    });

                    $scope.ribbonService.isOpenSecondMonitor = true;
                    idleWatchMMonitor2 = $interval(watchMultiMonitor2, SECOND_UNIT);
                    $rootScope.mmonitor2Win.push(
                        $window.open($scope.ribbonService.shareLinkMM + param, "_blank", "status=false,width=1024,height=768")
                    );
                }
                else if ($rootScope.mmonitor3Win.length == 0) {

                    // Google Tag Manager
                    dataLayer.push({
                        event: '3rd monitor',
                        button_name: 'btn_ribbon_3rd_monitor',
                        sub_domain: $scope.ribbonService.licenses.subdomain
                    });

                    $scope.ribbonService.isOpenThirdMonitor = true;
                    idleWatchMMonitor3 = $interval(watchMultiMonitor3, SECOND_UNIT);
                    $rootScope.mmonitor3Win.push(
                        $window.open($scope.ribbonService.shareLinkMM3 + param, "_blank", "status=false,width=1024,height=768")
                    );
                }
            }

            // When dialog is presented, get client reference (maybe it is not needed, TBC)
            $scope.$watch('ribbonService.nextButtonClicked', function(clicked) {
                if (clicked) {
                    // Collaborate now
                    if ($scope.model.selectedCollaborate == $scope.collaborateType.NOW) {
                        $rootScope.setFocusableElements(".ribbon-share-dialog", "now");

                        $('input[type=radio]').attr('disabled',true);
                        $('.viewOnly.share').attr('disabled',true);
                        $scope.ribbonService.shareableStarted = false;
                        getClient();

                        if( !$scope.ribbonService.shareLinkCopied) {
                            retrieveSharingProfiles($scope.client.tunnel.uuid);
                        }
                        else {
                            if($('.viewOnly.share').is(":disabled")) {
                                $('.viewOnly.share').attr('disabled',false);
                                $scope.ribbonService.shareableStarted = true;
                            }
                            if($('input[name="collaborateType"]').is(":disabled")) {
                                $('input[name="collaborateType"]').attr('disabled',false);
                            }
                            if($('input[name="collaborateLink"]').is(":disabled")) {
                                $('input[name="collaborateLink"]').attr('disabled',false);
                            }
                        }
                    }
                    // Collaborate offline
                    else if ($scope.model.selectedCollaborate == $scope.collaborateType.OFFLINE) {
                        $rootScope.setFocusableElements(".ribbon-share-dialog", "offline");

                        // Base collaborate offline URL
                        var baseCollaborateURL = "https://" + $scope.ribbonService.licenses.subdomain + "/collaboration/";

                        // To make the one day link option always checked when collaboration link dialog loading first
                        $scope.model.selectedExpirationType = $scope.expirationType.ONE_DAY;

                        // While creating collaborate links
                        $scope.collaborateOneDayLink = "--- Generating collaborate link...---";

                        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                        var httpParameters = {
                            token: authenticationService.getCurrentToken(),
                            id: clientIdentifier.id
                        };
                        var req = {
                            method: 'POST',
                            url: "api/session/ext/" + authenticationService.getDataSource() + "/collaborate-offline-link",
                            params: httpParameters
                        };

                        $http(req)
                        .then(function (response) {
                            var data = response.data;

                            // Set collaborate links
                            $scope.collaborateOneDayLink = baseCollaborateURL + data.keyOneDay;
                            $scope.collaborateOneWeekLink = baseCollaborateURL + data.keyOneWeek;
                            // The collaborate offline link created successfully
                            $scope.collaborateLinkCreated = true;

                            $rootScope.setFocusableElements(".ribbon-share-dialog", "offline");
                        })
                        .catch(function (response) {
                            // The collaborate offline link was not created
                            $scope.collaborateLinkCreated = false;
                            // Show error message in the top info notification
                            $translate('SHARE_SUBMENU.SHARE_COLLABORATION_OFFLINE_ERR').then(function (text) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = text;
                                $scope.infoService.infoDialogVisible = true;
                            });
                            console.error("Error in creating collaboration link: ", response.data.message);
                        })
                        .finally(function () { })
                    } else {
                        $rootScope.firstFocusableElement = null;
                        $rootScope.lastFocusableElement = null;

                        setTimeout(function () {
                            if (document.querySelector('.btn-share')) {
                                document.querySelector('.btn-share').focus();
                            }
                        }, 100);
                    }
                }
            });

            // When presenter mode, disable the button
            $scope.$watch('ribbonService.isPresenterEnabled', function(visible) {
                $('input[type=radio]').attr('disabled', visible);
                $('.viewOnly.share').attr('disabled', visible);
            });

            // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
            // try to get client after 10 seconds.
            queryClient = $interval(function() {
                if ($scope.client == null) {
                    getClient();
                }
                else {
                    $interval.cancel(queryClient);
                    queryClient = null;
                }
            }, 5 * 1000);

            function getClient() {
                if ($routeParams.id) {
                    var cl = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                    if (cl == null || cl.tunnel.uuid == null) {
                        console.debug("Sharing: Client or tunnel not yet available.");
                        return;
                    }

                    $scope.client = cl;
                    console.debug("Sharing: current uuid: " + $scope.client.tunnel.uuid);

                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    var datasource = clientIdentifier.dataSource;
                    if (datasource === 'encryptedurl-jdbc') {
                        $scope.ribbonService.sharingEnabled = true;
                    }
                }
            };

            var ERROR_SHARE_LINK = '--- Sharing is not possible at the moment, please reload the page ---';

            /**
             * The view only share link and name of the profile.
             *
             * @type String
             */
            var DEFAULT_VO_LINK = '--- Generating share link... ---';
            var PROFILE_VO = "share-VO";
            $scope.shareLinkVO = DEFAULT_VO_LINK;
            $scope.shortShareLinkVO = DEFAULT_VO_LINK;

            /**
             * The full control share link.
             *
             * @type String
             */
            var DEFAULT_FC_LINK = '--- Generating share link... ---';
            var PROFILE_FC = "share-FC";
            $scope.shareLinkFC = DEFAULT_FC_LINK;
            $scope.shortShareLinkFC = DEFAULT_FC_LINK;

            /**
             * The multimonitor share link.
             *
             * @type String
             */
            var PROFILE_MM = "share-MM";
            var PROFILE_MM_3 = "share-MM-3";

            function retrieveSharingProfiles(uuid) {
                // Only pull sharing profiles if tunnel UUID is actually available
                if (!uuid ||  $routeParams.hasOwnProperty("key")){
                    console.debug("Sharing: Cannot create profiles - uuid is undefined or shared session");
                    return;
                }

                $scope.shareLinkVO = DEFAULT_VO_LINK;
                $scope.shortShareLinkVO = DEFAULT_VO_LINK;
                $scope.shareLinkFC = DEFAULT_FC_LINK;
                $scope.shortShareLinkFC = DEFAULT_FC_LINK;

                // Pull sharing profiles for the current connection
                // Make a delay, to allow tunnel to form completely.
                let profileCreator =
                $interval(function() {
                    var sharingProfile = tunnelService.getSharingProfiles(uuid);
                    if(sharingProfile === undefined) {
                        console.debug("Sharing: Cannot create profiles - tunnel is not yet availabale. Will retry in 2s.");
                        return;
                    }

                    sharingProfile.then(async function sharingProfilesRetrieved(sharingProfiles) {
                        for (var str in sharingProfiles) {
                            if (sharingProfiles[str].name == PROFILE_FC || sharingProfiles[str].name == PROFILE_VO) {
                                var sharingCredentials = await ManagedClient.createShareLink($scope.client, sharingProfiles[str]);

                                $scope.client.shareLinks[sharingProfiles[str].identifier] =
                                    ManagedShareLink.getInstance(sharingProfiles[str], sharingCredentials);
                                console.debug("Sharing: Created link: " + sharingProfiles[str].name);
                            }
                        }

                        angular.forEach($scope.client.shareLinks, function (profile) {
                            if (profile.name == PROFILE_FC || profile.name == PROFILE_VO) {
                                $scope.shareKeys.push(profile.href);
                            }
                        });

                        createShareLinks();
                    })
                    .catch(function(err) {
                        console.error("Sharing: " + err);
                        $scope.shareLinkVO = ERROR_SHARE_LINK;
                        $scope.shareLinkFC = ERROR_SHARE_LINK;
                    });

                    $interval.cancel(profileCreator);
                }, 2 * 1000);
            };

            function createShareLinks() {
                // Only process share links if they are available (skip setting initial value to null/nothing etc....)
                if (!$scope.client || !$scope.client.shareLinks || $scope.ribbonService.serverId == null) {
                    return;
                }

                // Watermark
                var wm = '';
                if ($scope.ribbonService.licenses.hasWatermarkLicence) {
                    wm = '&wm=true';
                }

                var rep = "%20";
                for (var id in $scope.client.shareLinks) {
                    var str = $scope.client.shareLinks[id].href.replace(
                                     /hyperstream\/#/,
                                     ($scope.ribbonService.serverId != "" && $scope.ribbonService.serverGroupCount > 1 ? $scope.ribbonService.serverId+"/" : "") +
                                     "hyperstream/#");
                    if ($scope.client.shareLinks[id].name === PROFILE_VO) {
                        str += "&name=" + $scope.ribbonService.userinfo.name + "&connection_type=shared" + wm;
                        // replace white space with "%20"
                        str =  str.replace(/\s/g, rep);
                        $scope.shareLinkVO = str;
                        $scope.shortShareLinkVO = str.substr(0, 70) + "...";
                        $scope.ribbonService.shareLinkVO = str;
                    }

                    if ($scope.client.shareLinks[id].name === PROFILE_FC) {
                        str += "&name=" + $scope.ribbonService.userinfo.name + "&connection_type=shared" + wm;
                        // replace white space with "%20"
                        str =  str.replace(/\s/g, rep);
                        $scope.shareLinkFC = str;
                        $scope.shortShareLinkFC = str.substr(0, 70) + "...";
                        $scope.ribbonService.shareLinkFC = str;
                    }
                }

                $scope.model.url = $scope.shareLinkVO;
            };

            /**
             * Create temporary <input> element and use it for coping link. Link to be copied
             * must be inserted into an input box.
             */
            $scope.btnCopyLink = function btnCopyLink() {
                $scope.ribbonService.shareLinkCopied = !$scope.ribbonService.shareLinkCopied;

                // copy link
                if($scope.ribbonService.shareLinkCopied) {

                    // Google Tag Manager
                    dataLayer.push({
                        event: 'Share Screen -> Collaborate online',
                        button_name: 'btn_ribbon_collaborate_online',
                        sub_domain: $scope.ribbonService.licenses.subdomain
                    });

                    var el=document.createElement("input");
                    document.body.appendChild(el);

                    el.value = $scope.model.url;

                    $scope.ribbonService.shareKey = $scope.model.url;

                    el.select();
                    document.execCommand("copy");
                    document.body.removeChild(el);
                    $scope.ribbonService.isLinkCopied = true;

                    $translate('SHARE_SUBMENU.SHARE_COPIED').then(
                        function shareCopyReceived(text) {
                            infoService.infoText = text;
                            infoService.top = false;
                            infoService.infoDialogVisible = true;
                            $timeout(function hideInfo() {
                                infoService.infoDialogVisible = false;
                            }, $scope.ribbonService.thresholdInfo);
                        }
                    );
                }
                // stop sharing
                else {
                    angular.forEach($scope.shareKeys, function (profile) {
                        $rootScope.stopSharing(profile);
                    });
                    $scope.shareKeys = [];
                    $scope.model.url = null;
                    $scope.ribbonService.ribbonShareDialogVisible = !$scope.ribbonService.ribbonShareDialogVisible;
                    $scope.ribbonService.isLinkCopied = false;
                    // To show the dialog for selecting the collaborate now or offline
                    $scope.ribbonService.nextButtonClicked = false;
                    // To make the collaborate now option always checked when first loading
                    $scope.model.selectedCollaborate = $scope.collaborateType.NOW;
                }
            }

            $scope.copyLink = function copyLink() {
                $scope.ribbonService.isLinkCopied = true;
                var el=document.createElement("input");
                document.body.appendChild(el);

                el.value = $scope.model.url;

                $scope.ribbonService.shareKey = $scope.model.url;

                el.select();
                document.execCommand("copy");
                document.body.removeChild(el);
            }

            // When generated the share url, enable the button and radio
            $scope.$watch('shareLinkVO', function (share_url) {
                if (!!share_url && share_url !== DEFAULT_VO_LINK && share_url !== ERROR_SHARE_LINK) {
                    if($('input[type=radio]').is(":disabled")) {
                        $('input[type=radio]').attr('disabled',false);
                    }

                    if($('.viewOnly.share').is(":disabled")) {
                        $('.viewOnly.share').attr('disabled',false);
                        $scope.ribbonService.shareableStarted = true;
                    }
                }
            });

            function createShareLinksForMMonitor() {

                // Only process share links if they are available (skip setting initial value to null/nothing etc....)
                if (!$scope.client || !$scope.client.shareLinks || $scope.ribbonService.serverId == null)
                    return;

                for (var id in $scope.client.shareLinks) {
                    var str = $scope.client.shareLinks[id].href.replace(
                                     /hyperstream\/#/,
                                     ($scope.ribbonService.serverId != "" && $scope.ribbonService.serverGroupCount > 1 ? $scope.ribbonService.serverId+"/" : "") +
                                     "hyperstream/#");
                    str += "&connection_type=multimonitor"

                    if ($scope.client.shareLinks[id].name === PROFILE_MM) {
                        $scope.ribbonService.shareLinkMM = str + "&mm=1" + "&idleTimeout=" + $scope.ribbonService.licenses.idleTimeout +
                                                           "&h264=" + $scope.ribbonService.licenses.hasH264Licence;
                    }

                    if ($scope.client.shareLinks[id].name === PROFILE_MM_3) {
                        $scope.ribbonService.shareLinkMM3 = str + "&mm=2" + "&idleTimeout=" + $scope.ribbonService.licenses.idleTimeout +
                                                            "&h264=" + $scope.ribbonService.licenses.hasH264Licence;
                    }
                }
            };

            $rootScope.stopSharing = function stopSharing(url) {

                if (url == "undefined") {
                    return;
                }

                var start = url.indexOf("key=") + 4;
                var key = url.substr(start).split("&")[0];
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id),
                    key: key
                };
                var req = {
                    method: 'POST',
                    url: "api/session/ext/" + datasource + "/stopSharing",
                    params: httpParameters
                };

                $http(req)
                .then(function () {
                })
                .catch(function () {
                })
                .finally();

            };

            $scope.btnNext = function btnNext() {
                // Clicked next button
                $scope.ribbonService.nextButtonClicked = true;
            };

            /**
             * Create temporary <input> element and use it for coping link. Link to be copied
             * must be inserted into an input box.
             */
            $scope.copyCollaborateLink = function copyCollaborateLink() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Share Screen -> Collaborate offline',
                    button_name: 'btn_ribbon_collaborate_offline',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                // User selected One Day link option
                if ($scope.model.selectedExpirationType == $scope.expirationType.ONE_DAY) {
                    // Copy collaborate offline one day link to clipboard
                    navigator.clipboard.writeText($scope.collaborateOneDayLink);
                }
                else {
                    // Copy collaborate offline one week link to clipboard
                    navigator.clipboard.writeText($scope.collaborateOneWeekLink);
                }

                $translate('SHARE_SUBMENU.SHARE_COPIED').then(
                    function shareCopyReceived(text) {
                        infoService.infoText = text;
                        infoService.top = false;
                        infoService.infoDialogVisible = true;
                        $timeout(function hideInfo() {
                            infoService.infoDialogVisible = false;
                        }, $scope.ribbonService.thresholdInfo);
                    }
                );

            };

            /**
             * When either of enable-async-collaboration param and enable-screensharing param is true
             */
            $rootScope.$on("skipFirstDialog", function() {
                // When either of enable-async-collaboration param and enable-screensharing param is true
                // Skip dialog for selecting collaboration now and collaboration offline
                if ($scope.ribbonService.nextButtonClicked) {
                    // When enable-screensharing param only is true
                    if ($scope.ribbonService.licenses.hasSharingLicence) {
                        // Show collaboration now dialog
                        $scope.model.selectedCollaborate = $scope.collaborateType.NOW;
                    }
                    // When enable-async-collaboration param only is true
                    else {
                        // Show collaboration offline dialog
                        $scope.model.selectedCollaborate = $scope.collaborateType.OFFLINE;
                    }
                }
            });

        }] // end ribbon share dialog controller

    };
}]);
