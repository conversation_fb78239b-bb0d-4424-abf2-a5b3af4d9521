/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays snapshot manager dialog
 */
angular.module('ribbon').directive('snapshotManagerDialog', [function snapshotManagerDialog() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/snapshot-manager-dialog.html',
        controller: ['$scope', '$injector', '$location', function snapshotManagerDialogController($scope, $injector, $location) {

            var $timeout                = $injector.get('$timeout');
            var $translate              = $injector.get('$translate');
            var infoService             = $injector.get('infoService');
            var ClientIdentifier        = $injector.get('ClientIdentifier');
            var authenticationService   = $injector.get('authenticationService');
            var $routeParams            = $injector.get('$routeParams');
            var $http                   = $injector.get('$http');
            var guacNotification        = $injector.get('guacNotification');
            var $rootScope              = $injector.get('$rootScope');
            var $window                 = $injector.get('$window');

        	// Required services
			$scope.ribbonService         = $injector.get('ribbonService');
            $scope.circleLoaderService   = $injector.get('circleLoaderService');

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

            $scope.snapshots = [];
            $scope.selected = -1;
            $scope.loading = false;
            $scope.snapshotState = 'save';
            $scope.snapshotBtnState = 'continue';
            $scope.restoreDate = '';
            $scope.getCurrentDateTimeArrayVal = [];
            $scope.parsedSnapShotsArray = [];
            $scope.isSnapshotManagerPrepared = false;

            var MAX_SNAPSHOTS = 2;

            if($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                return;
            }

            /**
             * Select snapshot on mouse click.
             */
            
            $scope.selectSnapshot = function selectSnapshot($event, $index) {
               	var selected = $("input[name='snapshot']:checked").val();
                   $scope.restoreDate = selected;
               	if(selected) {
               		$scope.selected = 0;
               	}
            };
            
            
            
            /**
             * Read all snapshots when dialog becomes visible.
             */
            $scope.$watch('ribbonService.snapshotManagerVisible', function(newValue) {
                if (newValue) {
                    $scope.snapshots = [];
                    $scope.selected = -1;
                    readSnapshots();
                } else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;

                    setTimeout(function () {
                        if (document.querySelector('.btn-snapshot-manager')) {
                            document.querySelector('.btn-snapshot-manager').focus();
                        }
                    }, 100);

                    $scope.isSnapshotManagerPrepared = false;
                }
            });

            $scope.$watchGroup(['isSnapshotManagerPrepared', 'snapshotBtnState', 'snapshots.length'], function(isPrepared) {
                if (isPrepared) {
                    $rootScope.setFocusableElements("#snapshot-manager-dialog");
                }
            });




            /*********************************************************************************
             * Action for dialog buttons
             *********************************************************************************/

            /**
             * Action for calling save snapshot API
             */
            var SAVE_SNAPSHOT_ACTION = {
                name      : "CLIENT.ACTION_SAVE_SNAPSHOT",
                callback  : function saveSnapshotCallback() {
                                saveSnapshot();
                                guacNotification.showStatus(false);
                            }
            };

            /**
             * Action for calling restore snapshot API
             */
            var RESTORE_SNAPSHOT_ACTION = {
                name      : "CLIENT.ACTION_RESTORE_SNAPSHOT",
                callback  : function restoreSnapshotCallback() {
                                restoreSnapshot();
                                guacNotification.showStatus(false);
                            }
            };

            /**
             * Action for closing dialog
             */
            var CANCEL_ACTION = {
                name      : "APP.ACTION_CANCEL",
                callback  : function cancelCallback() {
                    guacNotification.showStatus(false);
                }
            };

            var actionsSave    = [ CANCEL_ACTION, SAVE_SNAPSHOT_ACTION ];
            var actionsRestore = [ CANCEL_ACTION, RESTORE_SNAPSHOT_ACTION ];




            /*********************************************************************************
             * Functions for calling snapshots API
             *********************************************************************************/

            /**
             * Read available snapshots
             */
            function readSnapshots() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                var httpParameters = {
                    token : authenticationService.getCurrentToken(),
                    id    : clientIdentifier.id
                };

                $scope.circleLoaderService.circleLoaderVisible = true;
                $scope.loading = true;
                $http({
                    method  : 'GET',
                    url     : 'api/session/ext/' + authenticationService.getDataSource() + '/snapshots/enumerate',
                    params  : httpParameters
                })
                .then(function(response) {
                    var data = response.data;

                    console.debug("Status: " + response.status);
                    console.debug(response.data);

                    // Remove trailing ";"
                    if (data.Date.slice(-1) == ";")
                        data.Date = data.Date.slice(0, data.Date.length - 1);

                    // If string is empty, empty array of snapshots
                    if (data.Date.trim() == "")
                        $scope.snapshots = [];
                    else
                        $scope.snapshots = data.Date.split(";");

                    if ($scope.snapshots.length == 0) {
                        $translate('DIALOGS.INFO_NO_SNAPSHOTS').then( showInfo );
                    } else {
                    	$('input:radio[name="snapshot"]').change( function(){
                            if ($(this).is(':checked')) {
                                 console.log($("input[name='snapshot']:checked").val());
                                 $scope.selected = $("input[name='snapshot']:checked").val();
                            }
                        });
                    }
                    $scope.isSnapshotManagerPrepared = false;
                })
                .catch(function(response) {
                    console.debug("Status: " + response.status);
                    console.debug(response.data.Message);
                    $translate('CLIENT.ERROR_SNAPSHOTS_READ').then( showInfo );
                    $scope.ribbonService.snapshotManagerVisible = false;
                })
                .finally(async function() {
                    $scope.loading = false;
                    $scope.circleLoaderService.circleLoaderVisible = false;
                    if ($scope.snapshots.length > 0) {
                        try {
                            for (let i = 0; i < $scope.snapshots.length; i++) {
                                $scope.parsedSnapShotsArray[i] = await $scope.parseSnapshot($scope.snapshots[i]);
                            }
                        } catch (error) {
                            console.error('Error parsing snapshots:', error);
                        }
                    }
                    $scope.isSnapshotManagerPrepared = true;
                });
            }

            /**
             * Callback function for save snapshot action.
             * Calls server REST API for making snapshot.
             */
            function saveSnapshot() {
                $rootScope.snapshotSaveState = true;

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                var httpParameters = {
                    token   : authenticationService.getCurrentToken(),
                    id      : clientIdentifier.id
                };

                $scope.ribbonService.hideDialogBackground = true;
                $scope.circleLoaderService.circleLoaderVisible = true;
                $http({
                    method  : 'GET',
                    url     : 'api/session/ext/' + authenticationService.getDataSource() + '/snapshots/backup',
                    params  : httpParameters
                })
                .then(function(response) {
                    console.debug("Status: " + response.status);
                    console.debug(response.data);
                    $scope.ribbonService.snapshotManagerVisible = false;
                })
                .catch(function(response) {
                    console.debug("Status: " + response.status);
                    console.debug(response.data);
                    $translate('CLIENT.ERROR_SNAPSHOTS_SAVE').then( showInfo );
                })
                .finally(function() {
                    $scope.circleLoaderService.circleLoaderVisible = false;
                    $scope.ribbonService.hideDialogBackground = false;
                });
            }

            /**
             * Callback function for restore snapshot action.
             * Calls server REST API for restoring snapshot.
             */
            function restoreSnapshot() {
                if ($scope.selected >= 0) {
                    $rootScope.snapshotRestoreState = true;
                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    var selected = $("input[name='snapshot']:checked").val();

                    var httpParameters = {
                        token      : authenticationService.getCurrentToken(),
                        id         : clientIdentifier.id,
                        snapshotId : selected
                    };

                    $scope.ribbonService.hideDialogBackground = true;
                    $scope.circleLoaderService.circleLoaderVisible = true;
                    $http({
                        method  : 'GET',
                        url     : 'api/session/ext/' + authenticationService.getDataSource() + '/snapshots/restore',
                        params  : httpParameters
                    })
                    .then(function(response) {
                        console.debug("Status: " + response.status);
                        console.debug(response.data);
                        $scope.ribbonService.snapshotManagerVisible = false;
                        $rootScope.snapshotId = selected;
                    })
                    .catch(function(response) {
                        console.debug("Status: " + response.status);
                        console.debug(response.data);
                        $translate('CLIENT.ERROR_SNAPSHOTS_RESTORE').then( showInfo );
                    })
                    .finally(function() {
                        $scope.circleLoaderService.circleLoaderVisible = false;
                        $scope.ribbonService.hideDialogBackground = false;
                    });
                }
            }



            /*********************************************************************************
             * Dialog buttons handlers
             *********************************************************************************/

            /**
             * Present a save snapshot dialog with warning about potential long running operaiont.
             * If the maximum number of snapshots is reached, warn about this as well.
             */
            $scope.saveSnapshots = function saveSnapshots() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Snapshot save',
                    button_name: 'btn_ribbon_snapshot_save',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                guacNotification.showStatus({
                    title   : "CLIENT.DIALOG_HEADER_SNAPSHOTS",
                    text    : {
                        key : $scope.snapshots.length == MAX_SNAPSHOTS ? "CLIENT.TEXT_SNAPSHOTS_SAVE_OVERWRITE" :
                                                                         "CLIENT.TEXT_SNAPSHOTS_SAVE_WARNING"
                    },
                    actions : actionsSave
                });
            }


            /**
             * Present a restore snapshot dialog with warning about potential long running operaiont.
             */
            $scope.restoreSnapshots = function restoreSnapshots() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Snapshot restore',
                    button_name: 'btn_ribbon_snapshot_restore',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                guacNotification.showStatus({
                    title   : "CLIENT.DIALOG_HEADER_SNAPSHOTS",
                    text    : {
                        key : "CLIENT.TEXT_SNAPSHOTS_RESTORE_WARNING"
                    },
                    actions : actionsRestore
                });
            }


            // For formating date
            $scope.parseSnapshot = async function(snapshot) {
                // Split the snapshot string into date and time parts
                var parts = snapshot.split(' ');
            
                // Date part
                var datePart = parts[0];
                var timePart = parts[1] + ' ' + parts[2]; // Concatenate time and AM/PM parts
            
                // Convert date part to a JavaScript Date object
                var date = new Date(datePart);
            
                // Format the date part as 'Month Day, Year'
                var formattedDate = date.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
            
                let dateArray = formattedDate.split(' ');
                let month = dateArray[0].toUpperCase();
            
                try {
                    // Resolve the translation promise with await
                    let text = await Promise.resolve($translate(`MONTHS.${month}`));
            
                    // Update the translated month in the date array
                    dateArray[0] = text;
                    formattedDate = dateArray.join(" ");
            
                    // Return an array containing the formatted date and time parts
                    return [formattedDate, timePart];
                } catch (error) {
                    // Handle errors if translation fails
                    console.error('Translation failed:', error);
                    return [formattedDate, timePart]; // Return default date and time
                }
            };
            
            $scope.getCurrentDateTimeArray = function() {
                if($scope.getCurrentDateTimeArrayVal.length===0){
                    var currentDate = new Date();
                    
                    // Format the date part
                    var dateOptions = { 
                        year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                };
                var formattedDate = currentDate.toLocaleDateString('en-US', dateOptions);
            
                // Format the time part
                var timeOptions = {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: true
                };
                var formattedTime = currentDate.toLocaleTimeString('en-US', timeOptions);
            
                let dateArray = formattedDate.split(' ');
                let month = dateArray[0].toUpperCase();
                $translate(`MONTHS.${month}`).then(function (text) {
                    dateArray[0] = text;
                    formattedDate = dateArray.join(" ");
                    $scope.getCurrentDateTimeArrayVal = [formattedDate, formattedTime];
                    // Return the array
                    return [formattedDate, formattedTime];
                });
            }else{
                return $scope.getCurrentDateTimeArrayVal;
            }
            };
            
            

            /**
             * Close the dialog.
             */
            $scope.closeDialog = function closeDialog() {
            	$scope.ribbonService.snapshotManagerVisible = false;
                $scope.snapshotBtnState = 'continue';
                $scope.snapshotState = 'save';
                $scope.restoreDate = '';
                $scope.getCurrentDateTimeArrayVal = [];
                $scope.parsedSnapShotsArray = [];
            };

            /**
             * Show information window
             */
            function showInfo(text) {
                infoService.infoText = text;
                infoService.infoDialogVisible = true;
                $timeout(function hideInfo() {
                    infoService.infoDialogVisible = false;
                }, $scope.ribbonService.thresholdInfo);
            }

        }] // end snapshot manager controller
      
    };
}])