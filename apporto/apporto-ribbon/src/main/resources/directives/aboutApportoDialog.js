/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays about-apporto-dialog
 */
angular.module('ribbon').directive('aboutApportoDialog', [function aboutApportoDialog() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/about-apporto-dialog.html',
        controller: ['$scope', '$rootScope', '$injector', function aboutApportoDialogController($scope, $rootScope, $injector) {

            // This service is required by directive used in ribbon template html
            $scope.ribbonService      = $injector.get('ribbonService');
            var $http                 = $injector.get('$http');

            $scope.$watch('ribbonService.aboutApportoDialogVisible', function(newValue) {
                if (newValue) {
                    $rootScope.setFocusableElements("#dlg-about-apporto");

                    $http.get('app/build_info/client_version.json').then(function(response) {
                        var data = response.data;
                        var clientData = data['client'][0];

                        $scope.guacClientGitHash     = clientData['githash'];
                        $scope.guacClientVersion     = clientData['version'];
                        $scope.guacClientBuildNumber = clientData['build-number'];
                    })
                    .catch(function(response) {
                        console.warn("cannot get client version info: error=", response.message);
                    });

                    if ($rootScope.serverVersion != null) {
                        var serverVersion = $rootScope.serverVersion['server'][0];

                        $scope.guacServerGitHash     = serverVersion['githash'];
                        $scope.guacServerVersion     = serverVersion['version'];
                        $scope.guacServerBuildNumber = serverVersion['build-number'];
                    }
                    else {
                        $http.get('app/build_info/server_version.json').then(function(response) {
                            var data = response.data;
                            var serverData = data['server'][0];
    
                            $scope.guacServerGitHash     = serverData['githash'];
                            $scope.guacServerVersion     = serverData['version'];
                            $scope.guacServerBuildNumber = serverData['build-number'];
                        })
                        .catch(function(response) {
                            console.warn("cannot get server version info: error=", response.message);
                        });
                    }

                }
                else {
                    $rootScope.firstFocusableElement = null;
                    $rootScope.lastFocusableElement = null;
                }
            });

            // Close about-apporto-dialog
            $scope.closeAboutApportoDialog = function closeAboutApportoDialog() {
                $scope.ribbonService.aboutApportoDialogVisible = false;
            }
        }] // end about-apporto-dialog controller

    };
}]);
