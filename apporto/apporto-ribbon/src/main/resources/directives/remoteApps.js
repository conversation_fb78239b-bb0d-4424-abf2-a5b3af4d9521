/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays share dialog
 */
angular.module('ribbon', ['ngCookies']).directive('remoteApps', [function remoteApps() {

    // Generic sort function
    function sortByProperty(array, property) {
        return array.sort((a, b) => {
            if (a[property].toLowerCase() < b[property].toLowerCase()) {
                return -1;
            }
            if (a[property].toLowerCase() > b[property].toLowerCase()) {
                return 1;
            }
            return 0;
        });
    }

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/remote-apps.html',
        controller: ['$scope', '$routeParams', '$injector', '$rootScope', function remoteAppsPopupController($scope, $routeParams, $injector, $rootScope) {

            // Required services
            var $location             = $injector.get('$location');
            var guacClientManager     = $injector.get('guacClientManager');

            $scope.ribbonService = $injector.get('ribbonService');

            $scope.remoteApps = $scope.ribbonService.licenses.remoteApps;

            // Available app list
            $scope.availableApps = [];
            // Already opened app list
            $scope.openApps = [];
            // Already pined app list
            $scope.pinApps = [];
            // Search result app list
            $scope.searchApps = [];
            // Search app name
            $scope.model = {
                searchInputText: ''
            };

            $scope.availableApps = [];

            if ($scope.ribbonService.licenses.hasRemoteApps) {
                $scope.availableApps = $scope.remoteApps.apps.map((app) => {
                    return {
                        name: app.program,
                        icon: $scope.remoteApps.iconStorage + app.program + ".png",
                    };
                });
            }

            $scope.openApps = [];

            $scope.pinApps = [];

            $scope.availableApps = sortByProperty($scope.availableApps, 'name');

            $scope.pinApps = sortByProperty($scope.pinApps, 'name');

            $scope.openApps = sortByProperty($scope.openApps, 'name');

            $scope.hideSearchBox = true;

            const VIEW_METHOD = {
                LIST: 'list',
                GRID: 'grid'
            }
            $scope.viewMethod = VIEW_METHOD.LIST;

            // Hide or unhide search box
            $scope.clickSearchBtn = function clickSearchBtn() {
                $scope.hideSearchBox = !$scope.hideSearchBox;
                $scope.model.searchInputText = "";
            }

            // View as list format
            $scope.clickListViewBtn = function clickListViewBtn() {
                $scope.viewMethod = VIEW_METHOD.LIST;
            }

            // View as grid format
            $scope.clickGridViewBtn = function clickGridViewBtn() {
                $scope.viewMethod = VIEW_METHOD.GRID;
            }

            // Handle the process to click one remote app button
            $scope.clickRemoteAppBtn = function clickRemoteAppBtn(app) {
                // console.log("***** clickRemoteAppBtn: " + app.name);

                const isOpened = $scope.openApps.some(item => item.name === app.name);

                if (isOpened) {
                } else {
                    const clonedApp = {
                        name: app.name,
                        icon: app.icon
                    };
                    $scope.openApps.push(clonedApp);
                    $scope.openApps = sortByProperty($scope.openApps, 'name');
                }

                // var client = guacClientManager.getManagedClient($routeParams.id);
                // client.client.disconnect();
                // var params = angular.copy($routeParams.params) || {};
                // params['remote-apps'] = name;
                // $scope.client = guacClientManager.replaceManagedClient($routeParams.id, params);

                $location.search('remote-apps', app.name);
            }

            $scope.$watch('model.searchInputText', function (newValue, oldValue) {
                if (newValue) {
                    // Filter the availableApps array based on search input
                    $scope.searchApps = $scope.availableApps.filter(function (app) {
                        return app.name.toLowerCase().includes(newValue.toLowerCase());
                    });
                } else {
                    // If search input is empty, clear the search results
                    $scope.searchApps = [];
                }
            });

            $scope.alreadyPin = function alreadyPin(nameToCheck) {
                const isIncluded = $scope.pinApps.some(item => item.name === nameToCheck);
                if (isIncluded) {
                    return true;
                }
                else {
                    return false;
                }
            }

            // Add a new app to pin apps group
            $scope.clickPinBtn = function (app) {
                const newPinApp = {
                    name: app.name,
                    icon: app.icon
                }
                $scope.pinApps.push(newPinApp);

                $scope.pinApps = sortByProperty($scope.pinApps, 'name');
            }

            // Remove an app from pin apps group
            $scope.clickUnPinBtn = function (name) {
                const updatedItems = $scope.pinApps.filter(item => item.name !== name);
                $scope.pinApps = [];
                $scope.pinApps = updatedItems;

                $scope.pinApps = sortByProperty($scope.pinApps, 'name');
            }
        }]

    };
}]);
