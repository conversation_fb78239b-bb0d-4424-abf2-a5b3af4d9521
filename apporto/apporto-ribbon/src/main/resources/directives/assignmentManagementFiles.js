/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/** 
 * Directive which displays file browser
 */
angular.module('ribbon', ['client']).directive('assignmentManagementFiles', [function assignmentManagementFiles() {

    return {
        restrict: 'E',
        scope: {
            /**
             * Enables multiple file selection 
             *
             * @type Boolean
             */
            multiSelect: '=?'
        },

        templateUrl: 'app/ext/ribbon/templates/assignment-management-files.html',
        controller: ['$scope', '$injector', '$location', '$rootScope',
            function assignmentManagementFilesController($scope, $injector, $location, $rootScope) {

                // Required services
                var ManagedClient = $injector.get('ManagedClient');
                var ManagedFilesystem = $injector.get('ManagedFilesystem');
                var guacClientManager = $injector.get('guacClientManager');
                var $translate = $injector.get('$translate');
                var $routeParams = $injector.get('$routeParams');
                var $timeout = $injector.get('$timeout');
                var $interval = $injector.get('$interval');
                var $http = $injector.get('$http');
                var ClientIdentifier = $injector.get('ClientIdentifier');
                var guacNotification = $injector.get('guacNotification');
                var authenticationService = $injector.get('authenticationService');

                $scope.ribbonService = $injector.get('ribbonService');

                // Variables related to check if the filesystem is available and if it contains
                // a mandatory directory (Desktop)
                var CHECK_FS_LIMIT_NUMBER = 60; // 300 seconds (5 mins)
                var checkFSCount = 0;

                if ($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                    return;
                }

                var fileDownloadDisabled = true;
                var assignmentPublishDisabled = true;

                $scope.circleLoaderService = $injector.get('circleLoaderService');
                $scope.loading = false;

                // This service is required by directive used in ribbon template html
                $scope.infoService = $injector.get('infoService');

                var fileNameListForUploadCompare = [];
                var matching = []; 
                var matchingCount = 0;

                $scope.sharingProfiles = {};
                $scope.client = null;
                $scope.filesystem = null;
                
                //This stores the ids of files that are to be submitted for a single submission
                $rootScope.assignmentUploadFiles = null;

                $rootScope.assignmentSubmitLoader = false;

                //Diasble the resubmit button for assigment upload
                $rootScope.disableResubmit = function disableResubmit() {
                    $rootScope.clearFileSelection()
                    $rootScope.resubmitEnabled = false;
                }

                var KEEP_BOTH = {
                    name: "DIALOGS.KEEP_BOTH",
                    // Handle action
                    callback: function acknowledgeCallback() {
                        $scope.keepBoth();
                    },
                    className: 'button'
                };

                var REPLACE = {
                    name: "DIALOGS.REPLACE",
                    // Handle action
                    callback: function acknowledgeCallback() {
                        $scope.replace();
                    },
                    className: 'button'
                };

                var CANCEL_FILE = {
                    name: "DIALOGS.BUTTON_CANCEL",
                    // Handle action
                    callback: function acknowledgeCallback() {
                        $scope.cancelFile();
                    },
                    className: 'cancelBtnClass'
                };

                // Constants
                var RDP_FS_NAME = "Shared Drive"; // Default name given by guacd to RDP FS
                var INITIAL_DIRECTORIES = ["Desktop", "Bureau"];

                // API template to upload file into LMS (canvas, d2l, blackboard)
                var uploadFileApi = "api/lti/uploadAssignmentFile";

                $scope.$on('$routeChangeSuccess', function (event, current, previous) {
                    if ($routeParams.id) {
                        $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                        // Check the file system only if the upload or download feature is enabled
                        if ($scope.ribbonService.isSftpAvailable) {
                            checkFilesystemReady();
                        }
                    }
                });

                var checkFS = null;

                $scope.$watch('ribbonService.isSftpAvailable', function () {
                    if ($scope.ribbonService.isSftpAvailable && checkFS == null) {
                        checkFS = $interval(function () {
                            if ($routeParams.id) {
                                if ($scope.client == null)
                                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                                checkFilesystemReady();
                            }
                        }, 5 * 1000);
                    }
                });

                /**
                 * Check if the filesystem is available and if it contains a mandatory directory (Desktop)
                 */
                function checkFilesystemReady() {
                    if ($scope.client != null) {

                        // Check if the filesystem is available
                        var fs = getFilesystem();
                        if (fs != null) {

                            // Check if the filesystem contains mandatory directory (Desktop)
                            $scope.ribbonService.fileExplorerReady = INITIAL_DIRECTORIES.some(dir => fs.root.files.hasOwnProperty(dir));
                            if ($scope.ribbonService.fileExplorerReady) {
                                if (checkFS != null) $interval.cancel(checkFS);
                            }
                        }

                        // Increase the count
                        checkFSCount++;

                        // After checkFS() is called within the given count, if the filesystem isn't available or
                        // it doesn't a contain mandatory directory (Desktop), we have to cancel the interval handler
                        // and show the message to users.
                        if (($scope.ribbonService.licenses.hasUploadLicence || $scope.ribbonService.licenses.hasDownloadLicence) &&
                            !$scope.ribbonService.fileExplorerReady && checkFSCount > CHECK_FS_LIMIT_NUMBER) {

                            // Cancel the interval handler
                            console.warn("checkFS abnormally cancelled.");
                            if (checkFS != null) $interval.cancel(checkFS);

                            // Show error message
                            $translate("CLIENT.CHECK_FILESYSTEM_FAILED").then(function showMessage(msg) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = msg;
                                $scope.infoService.infoDialogVisible = true;
                                $timeout(function () {
                                    $scope.infoService.infoDialogVisible = false;
                                }, $scope.ribbonService.thresholdInfo);
                            });

                            // Reset the count
                            checkFSCount = 0;
                        }
                    }
                }

                /**
                 * Returns first filesystem object that is not RDP file system. It is assumed this
                 * is SFTP file system object.
                 */
                function getFilesystem() {
                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                    for (var fs in $scope.client.filesystems) {
                        // Assume that first filesystem that is not having default RDP_FS_NAME is SFTP filesystem
                        if ($scope.client.filesystems[fs].name !== RDP_FS_NAME) {
                            return new ManagedFilesystem($scope.client.filesystems[fs]);
                        }
                    }

                    return null;
                };

                /**
                 * Change the filesystem to the initial directory.
                 */
                function changeInitialDirectory(fs) {
                    const existingDir = INITIAL_DIRECTORIES.find(dir => fs.root.files.hasOwnProperty(dir));

                    if (existingDir) {
                        $scope.ribbonService.currFileExist = true;
                        ManagedFilesystem.changeDirectory(fs, fs.root.files[existingDir]);
                    }
                    else {
                        console.error("Directory change failed, opening dialog in root directory.");
                    }
                    $scope.filesystem = fs;
                }

                /**
                 * Get filesystem when dialog is visible.
                 */
                $scope.$watch('ribbonService.assignmentManagmentFiles', function (newValue) {
                    
                    if (newValue) {
                        var fs = getFilesystem();
                        if (fs != null) {
                            // Check if filesystem contains mandatory directory (Desktop)
                            if (INITIAL_DIRECTORIES.some(dir => fs.root.files.hasOwnProperty(dir))) {
                                changeInitialDirectory(fs);
                            }
                            else {
                                ManagedFilesystem.changeDirectory(fs, fs.root);

                                // Wait for root directory to be read
                                $timeout(function () {
                                    changeInitialDirectory(fs);
                                }, 3000);
                            }
                        }
                    }
                    else {
                        $scope.filesystem = null;
                    }
                });

                $scope.$on('guacUploadComplete', function uploadComplete(event, filename) {
                    const uploadMessage = $rootScope.ribbonService.assignmentManagementDialogVisible ? "CLIENT.FILE_DOWNLOAD_SUCCESS" : "CLIENT.FILE_UPLOAD_SUCCESS"
                    $translate(uploadMessage).then(function showMessage(msg) {
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = msg;
                        $scope.infoService.infoDialogVisible = true;
                        $timeout(function () {
                            $scope.infoService.infoDialogVisible = false;
                        }, $scope.ribbonService.thresholdInfo);
                    });
                });

                /**
                 * Returns the full path to the given file as an ordered array of parent
                 * directories.
                 *
                 * @param {ManagedFilesystem.File} file
                 *     The file whose full path should be retrieved.
                 *
                 * @returns {ManagedFilesystem.File[]}
                 *     An array of directories which make up the hierarchy containing the
                 *     given file, in order of increasing depth.
                 */
                $scope.getPath = function getPath(file) {

                    var path = [];

                    // Add all files to path in ascending order of depth
                    while (file && file.parent) {
                        path.unshift(file);
                        file = file.parent;
                    }

                    return path;
                };

                /**
                 * Download file selected in a directory listing
                 */
                $scope.downloadFile = function downloadFile() {

                    // Directory listing element
                    var directoryList = angular.element(document.getElementById('directory-list'));
                    var listing = directoryList.children(0).children();

                    angular.forEach(listing, function (item) {
                        var jqItem = angular.element(item);
                        if (jqItem.hasClass('normal-file') && jqItem.hasClass('focused')) {
                            fileDownloadDisabled = false;
                            var fileName = jqItem.eq(0).eq(0).text().trim().split("\n")[0].trim();
                            ManagedFilesystem.downloadFile($scope.client, $scope.filesystem, $scope.filesystem.currentDirectory.streamName +
                                "/" + fileName);
                        }
                    })

                    if (fileDownloadDisabled) {
                        alert("𝗦𝗲𝗹𝗲𝗰𝘁 𝗮 𝗳𝗶𝗹𝗲 𝗳𝗼𝗿 𝗱𝗼𝘄𝗻𝗹𝗼𝗮𝗱!");
                    }

                    fileDownloadDisabled = true;
                }


                /**
                 * Shows the dialog for keeping or replacing or cancelling
                 *
                 */
                 var handleKeepOrReplace = function handleKeepOrReplace() {

                    if (matchingCount > 0) {
                        guacNotification.showStatus({
                            text: {
                                key: 'DIALOGS.KEEP_OR_REPLACE',
                                variables: {
                                    FILE_NAME: replaceName()
                                }
                            },
                            actions: [KEEP_BOTH, REPLACE, CANCEL_FILE]
                        });
                    }
                    else {
                        guacNotification.showStatus(false);
                        matching = [];
                        matchingCount = 0;
                    }

                }

                /**
                 * File for upload already exists.
                 * Keeps both files, the new one with an added version in the name.
                 * */
                $scope.keepBoth = function keepBoth() {
                    var newName = createNewName();
                    if (newName !== undefined) {
                        Object.defineProperty(matching[0], 'name', {
                            writable: true,
                            value: newName
                        });
                        ManagedClient.uploadFile($scope.client, matching[0], $scope.filesystem);
                        fileNameListForUploadCompare.push(matching[0].name);
                        guacNotification.showStatus(false);

                        // delete the handled file object
                        matchingCount--;
                        matching.splice(0, 1);

                        // handle the remaining matched files
                        handleKeepOrReplace();
                    }
                }

                /**
                 * File for upload already exists.
                 * Replace the old file with the new one.
                 * */
                $scope.replace = function replace() {
                    ManagedClient.uploadFile($scope.client, matching[0], $scope.filesystem);
                    fileNameListForUploadCompare.push(matching[0].name);
                    guacNotification.showStatus(false);

                    // delete the handled file object
                    matchingCount--;
                    matching.splice(0, 1);

                    // handle the remaining matched files
                    handleKeepOrReplace();
                }

                /**
                 * File for upload already exists.
                 * Cancel the new file to upload when cancel button is clicked.
                 * */
                $scope.cancelFile = function cancelFile() {
                    // delete the handled file object
                    matchingCount--;
                    matching.splice(0, 1);

                    // handle the remaining matched files
                    guacNotification.showStatus(false);
                    handleKeepOrReplace();
                }

                var createNewName = function () {

                    if (matching == undefined || matching.length == 0)
                        return;

                    // parse to get the name
                    var wholeName = matching[0].name;
                    var nameParts = wholeName.split(".");
                    var nam = nameParts[0];

                    var fileVersions = [];
                    // check if there are more versions of the file to generate a number for the new file
                    if (fileNameListForUploadCompare !== undefined)
                        for (var j = 0; j < fileNameListForUploadCompare.length; j++) {
                            if (fileNameListForUploadCompare[j].startsWith(nam))
                                fileVersions.push(fileNameListForUploadCompare[j]);
                        }

                    // create version for the new file
                    var fileVersionNumber = "";
                    if (fileVersions !== undefined) {
                        for (var j = 0; j < fileVersions.length; j++) {
                            var fileName = fileVersions[j].split(".");

                            // if the name of the current file and the name of the new file are the same
                            // no need to check fileVersion, it's 1
                            if (!angular.equals(fileName[0], nam)) {
                                fileName[0] = fileName[0].replace(nam, "");
                                fileName[0] = fileName[0].replace("(", "");
                                fileName[0] = fileName[0].replace(")", "");
                                fileName[0] = fileName[0].trim();
                                if (isNaN(fileName[0])) {
                                    continue;
                                }

                                // if file version number should have more than one number
                                var i = fileName[0].length - 1;

                                // if it's number, for the cases where there is already brackets with number in the name of different file
                                if (fileName[0] !== "") {
                                    while (i >= 0 && !isNaN(fileName[0].charAt(i))) {
                                        i--;
                                    }

                                    if (fileVersionNumber == "") {
                                        if (i >= 0 && !isNaN(fileName[0].charAt(i))) {
                                            fileVersionNumber = "";
                                        }
                                        else {
                                            fileVersionNumber = parseInt(fileName[0]);
                                        }
                                    }
                                    else {
                                        if (i < 0) {
                                            fileName[0] = parseInt(fileName[0]);
                                            fileVersionNumber = parseInt(fileVersionNumber);
                                            if (fileName[0] > fileVersionNumber) {
                                                fileVersionNumber = fileName[0];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    var i = nam.length - 1;
                    var newFileName = "";

                    newFileName = nam.substring(0, i + 1);
                    if (fileVersionNumber != undefined) {
                        fileVersionNumber++;
                        newFileName += "(" + fileVersionNumber + ")";
                    }
                    else {
                        newFileName += "(" + 1 + ")";
                    }

                    // at the end if extension of file were in file name, then add it in the new file name too
                    if (nameParts.length > 1 && nameParts[1] != "") {
                        newFileName += "." + nameParts[1];
                    }

                    return newFileName;
                }

                $scope.getAllFiles = function getAllFiles() {
                    var fileName = "";
                    fileNameListForUploadCompare = [];

                    // Directory listing element
                    var directoryList = angular.element(document.getElementById('directory-list'));
                    var listing = directoryList.children(0).children();

                    angular.forEach(listing, function (item) {
                        var jqItem = angular.element(item);
                        if (jqItem.hasClass('normal-file')) {
                            fileName = jqItem.eq(0).eq(0).text().trim().split("\n")[0].trim();
                            fileNameListForUploadCompare.push(fileName);
                        }
                    });
                }

                $scope.$watch('ribbonService.fileListReady', function (newValue) {
                    if (newValue) {
                        if ($scope.filesystem) {
                            if (!$scope.ribbonService.skeletonLoad.assignmentManagementFile) {
                                $scope.getAllFiles();
                                $scope.ribbonService.fileListReady = false;
                            }
                        }
                        else {
                            $scope.ribbonService.fileListReady = false;
                        }
                    }
                });

                var replaceName = function () {
                    if (matching !== undefined && matching.length != 0 && matching[0] != undefined) {
                        return matching[0].name;
                    }
                    else {
                        return "";
                    }
                }

                $scope.changeDirectory = function changeDirectory(file) {
                    if (!file) {
                        return;
                    }
                    // document.getElementById('download_file_btn').classList.add('disable_download_btn');
                    file.files = {};
                    ManagedFilesystem.changeDirectory($scope.filesystem, file);
                }

                /**
                 * Get the upload url to upload the assingment file
                 *
                 * @param {String} fileName
                 *     The name of the assignment to upload.
                 * @param {Number} fileSize
                 *     The size of the assignment to upload.
                 * @param {Number} assignmentId
                 *     The id of the assignment to upload.
                 */
                $scope.uploadAssignmentDetails = function uploadAssignmentDetails(fileName, fileSize, assignmentId) {
                    return new Promise(function(resolve, reject) {
                        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                
                        var httpParameters = {
                            token: encodeURIComponent(authenticationService.getCurrentToken()),
                            id: encodeURIComponent(clientIdentifier.id),
                            datasource: encodeURIComponent(clientIdentifier.dataSource),
                            fileName: fileName,
                            fileSize: fileSize,
                            assn_id: assignmentId
                        };
                
                        var req = {
                            method: 'GET',
                            url: "api/lti/uploadAssignmentDetails",
                            params: httpParameters
                        };
                
                        $scope.status = "";
                
                        $http(req)
                        .then(function (response) {
                            $scope.status = response.data.status;
                            
                            // Check status here instead of in finally
                            if ($scope.status == "200") {
                                // Call publishFile and explicitly handle its completion
                                ManagedFilesystem.publishFile($scope.client, $scope.filesystem,
                                    $scope.filesystem.currentDirectory.streamName + "/" + fileName)
                                    .then(function() {
                                        // Resolve the main promise after publishFile completes
                                        resolve(); 
                                    })
                                    .catch(function(error) {
                                        // If publishFile fails, reject the main promise
                                        reject(error);
                                    });
                            } else {
                                // If status is not 200, resolve without publishing
                                resolve();
                            }
                        }) 
                        .catch(function (response) {
                            $translate('ASSIGNMENT.ERROR_UPLOAD_ASSIGNMENT_DETAILS').then(function setError(text) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = text;
                                $scope.infoService.infoDialogVisible = true;
                            });
                            console.error("Error occured while getting the upload url: ", response.data.Message);
                            reject(response)
                        });
                    })
                }

                /**
                 * Publish file selected in a directory listing
                 */
                $scope.publishFile = function publishFile() {

                    // Directory listing element
                    var directoryList = angular.element(document.getElementById('directory-list'));
                    var listing = directoryList.children(0).children();

                    // Create array of files to upload
                    var filesToProcess = [];

                    angular.forEach(listing, function (item) { 
                        var jqItem = angular.element(item);
                        if (jqItem.hasClass('normal-file') && jqItem.hasClass('focused')) {
                            var fileDetails = jqItem.eq(0).eq(0).text().trim().replace(/\s\s+/g, '\n');
                            var fileName = fileDetails.split("\n")[0];
                            var fileSize = fileDetails.split("\n")[1];
                            if (fileSize && fileSize != undefined && fileSize != null) {
                                var fileSizeToBytes = fileSize.split(" ")[0] * 1024;
                                assignmentPublishDisabled = false;

                                // canvas
                                if ($scope.ribbonService.licenses.ltiIssuer == $scope.ribbonService.LTI_ISSUER.CANVAS) {
                                    filesToProcess.push({
                                        fileName: fileName,
                                        fileSize: parseInt(fileSizeToBytes.toFixed()),
                                        assignmentId: $rootScope.selectedAssignment.id
                                    });
                                   // $scope.uploadAssignmentDetails(fileName, parseInt(fileSizeToBytes.toFixed()), $rootScope.selectedAssignment.id);
                                }
                                // d2l, blackboard
                                else {
                                    $rootScope.assignmentSubmitLoader = true
                                    ManagedFilesystem.publishFile($scope.client, $scope.filesystem,
                                                                  $scope.filesystem.currentDirectory.streamName + "/" + fileName);
                                }
                            }
                        }
                    });

                    // For Canvas, process files sequentially
                    if ($scope.ribbonService.licenses.ltiIssuer == $scope.ribbonService.LTI_ISSUER.CANVAS && filesToProcess.length > 0) {
                        $rootScope.assignmentSubmitLoader = true
                        $scope.processFilesSequentially(filesToProcess, 0);
                    }

                    if (assignmentPublishDisabled) {
                        alert("Select an assignment file for publishing!");
                    }

                    assignmentPublishDisabled = true;
                }

                /**
                 * Sanitize a filename, replacing all URL path seperators with safe
                 * characters.
                 *
                 * @param {String} filename
                 *     An unsanitized filename that may need cleanup.
                 *
                 * @returns {String}
                 *     The sanitized filename.
                 */
                var sanitizeFilename = function sanitizeFilename(filename) {
                    return filename.replace(/[\\\/]+/g, '_');
                };

                $scope.processFilesSequentially = function(files, index) {
                    if (index >= files.length) {
                        if($rootScope.assignmentUploadFiles.length > 0) {
                            $scope.submitCanvasAssignment();
                        }
                        $rootScope.assignmentSubmitLoader = false;
                        return;
                    }
                    
                    var currentFile = files[index];
                    $scope.uploadAssignmentDetails(
                        currentFile.fileName, 
                        currentFile.fileSize, 
                        currentFile.assignmentId
                    ).then(function() {
                        // Process next file
                        $scope.processFilesSequentially(files, index + 1);
                    }).catch(function(error) {
                        console.error("Error uploading file:", error);
                        // Continue with next file despite error
                        $rootScope.assignmentSubmitLoader = false;
                        return;
                    });
                }


                $scope.submitCanvasAssignment = function() {
                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                    var httpParameters = {
                        token: encodeURIComponent(authenticationService.getCurrentToken()),
                        id: encodeURIComponent(clientIdentifier.id),
                        datasource: encodeURIComponent(clientIdentifier.dataSource),
                        assn_id: $rootScope.selectedAssignment.id
                    };
                    
                    // Add file_id parameters for each file ID
                    $rootScope.assignmentUploadFiles.forEach(function(fileId) {
                        // Using array notation for parameters with multiple values
                        if (!httpParameters.file_id) {
                            httpParameters.file_id = [];
                        }
                        httpParameters.file_id.push(fileId);
                    });
                
                    var req = {
                        method: 'GET',
                        url: "api/lti/submitCanvasAssignment",
                        params: httpParameters
                    };

                    $http(req)
                    .then(function(response) {
                        $translate('ASSIGNMENT.SUCCESS_PUBLISH_ASSIGNMENT_FILE', {ASSIGN_NAME: $rootScope.selectedAssignment.name})
                        .then(function setSuccess(text) {
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = text;
                            $scope.infoService.infoDialogVisible = true;
                            $timeout(function() {
                                $scope.infoService.infoDialogVisible = false;
                            }, $scope.ribbonService.thresholdInfo);
                        });
                        
                        $rootScope.getAssignmentDetails($rootScope.selectedAssignment.id);
                    })
                    .catch(function(response) {
                        $translate('ASSIGNMENT.ERROR_UPLOAD_ASSIGNMENT_FILE').then(function setError(text) {
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = text;
                            $scope.infoService.infoDialogVisible = true;
                        });
                        console.error("Error submitting Canvas assignment: ", response.data ? response.data.Message : response);
                    })
                    .finally(function () {
                        $rootScope.assignmentUploadFiles = null
                        $rootScope.disableResubmit();
                        $rootScope.assignmentSubmitLoader = false;
                    });
                }

                /**
                 * Makes a request to the REST API to publish the contents of a stream
                 * which has been created within the active Guacamole connection associated
                 * with the given tunnel. The contents of the stream will be uploaded to the
                 * Canvas by the relevant java service.
                 *
                 * @param {String} tunnel
                 *     The UUID of the tunnel associated with the Guacamole connection
                 *     whose stream should be downloaded as a file.
                 *
                 * @param {Guacamole.InputStream} stream
                 *     The stream whose contents should be downloaded.
                 *
                 * @param {String} mimetype
                 *     The mimetype of the stream being downloaded. This is currently
                 *     ignored, with the download forced by using
                 *     "application/octet-stream".
                 *
                 * @param {String} filename
                 *     The filename that should be given to the downloaded file.
                 */
                $rootScope.publishStream = function publishStream(tunnel, stream, filename) {
                    return new Promise(function(resolve, reject) {
                        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                
                        // Create the http parameters
                        var httpParameters = {
                            token: encodeURIComponent(authenticationService.getCurrentToken()),
                            id: encodeURIComponent(clientIdentifier.id),
                            uuid: encodeURIComponent(tunnel),
                            fileName: sanitizeFilename(filename),
                            streamIndex: encodeURIComponent(stream.index)
                        };
                
                        // Create the http request
                        var req = {
                            method: 'GET',
                            url: "api/lti/publishStream",
                            params: httpParameters
                        };
                        $translate('ASSIGNMENT.WAIT_UPLOAD_ASSIGNMENT_FILE').then(function setError(text) {
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = text;
                            $scope.infoService.infoDialogVisible = true;
                        });
                
                        // Process the http request
                        $http(req)
                        .then(function (response) {
                            $scope.uploadAssignmentFile(sanitizeFilename(filename))
                            .then(function() {
                                resolve();
                            })
                            .catch(function(error) {
                                reject(error);
                            });
                        })
                        .catch(function (response) {
                            $translate('ASSIGNMENT.ERROR_PUBLISH_ASSIGNMENT_STREAM').then(function setError(text) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = text;
                                $scope.infoService.infoDialogVisible = true;
                            });
                            console.error("Error publish stream: ", response.data.Message);
                            reject(response);
                        });
                
                        // Acknowledge (and ignore) any received blobs
                        stream.onblob = function acknowledgeData() {
                            stream.sendAck('OK', Guacamole.Status.Code.SUCCESS);
                        };
                    });
                }

                /**
                 * Upload assignment file to canvas
                 *
                 * @param {String} filename
                 *    The filename that should be given to the downloaded file.
                 */
                $scope.uploadAssignmentFile = function uploadAssignmentFile(fileName) {
                    return new Promise(function(resolve, reject) {
                        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                
                        var httpParameters = {
                            token: encodeURIComponent(authenticationService.getCurrentToken()),
                            id: encodeURIComponent(clientIdentifier.id),
                            datasource: encodeURIComponent(clientIdentifier.dataSource),
                            fileName: fileName,
                            assn_id: $rootScope.selectedAssignment.id
                        };
                
                        var url = "";
                        switch ($scope.ribbonService.licenses.ltiIssuer) {
                            case $scope.ribbonService.LTI_ISSUER.CANVAS:
                                url = uploadFileApi + "ToCanvas";
                                break;
                            case $scope.ribbonService.LTI_ISSUER.D2L:
                                url = uploadFileApi + "ToD2L";
                                break;
                            case $scope.ribbonService.LTI_ISSUER.BLACKBOARD:
                                url = uploadFileApi + "ToBlackboard";
                                httpParameters['grade_column_id'] = $rootScope.selectedAssignment.grade_column_id;
                                break;
                        }
                
                        var req = {
                            method: 'GET',
                            url: url,
                            params: httpParameters
                        };
                
                        $http(req)
                        .then(function (response) {
                            if($scope.ribbonService.licenses.ltiIssuer === $scope.ribbonService.LTI_ISSUER.CANVAS){
                                const uploadId = response.data.upload_id;
                    
                                if(!$rootScope.assignmentUploadFiles) {
                                    $rootScope.assignmentUploadFiles = [];
                                }
                    
                                $rootScope.assignmentUploadFiles.push(uploadId);
                    
                                resolve();
                            }
                            else{
                                $translate('ASSIGNMENT.SUCCESS_PUBLISH_ASSIGNMENT_FILE', {ASSIGN_NAME: $rootScope.selectedAssignment.name}).then(function setSuccess(text) {
                                    $scope.infoService.top = true;
                                    $scope.infoService.infoText = text;
                                    $scope.infoService.infoDialogVisible = true;
                                    $timeout(function () {
                                        $scope.infoService.infoDialogVisible = false;
                                    }, $scope.ribbonService.thresholdInfo);
                                });
                                $rootScope.getAssignmentDetails($rootScope.selectedAssignment.id);
                                resolve();
                            }
                        })
                        .catch(function (response) {
                            $translate('ASSIGNMENT.ERROR_UPLOAD_ASSIGNMENT_FILE').then(function setError(text) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = text;
                                $scope.infoService.infoDialogVisible = true;
                            });
                            console.error("Error occured while uploading assignmenet file: ", response.data.Message);
                            reject(response);
                        })
                        .finally(function () {
                            if ($scope.ribbonService.licenses.ltiIssuer != $scope.ribbonService.LTI_ISSUER.CANVAS) {
                                $rootScope.assignmentSubmitLoader = false;
                            }
                        });
                    });
                }

            }
        ] // end file browser controller
    };
}]);
