/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A directive which allows multiple files to be downloaded. Clicking on the
 * associated element will result in calling the provided callback function.
 */
angular.module('element').directive('guacDownload', ['$document', function guacDownload($document) {

    return {
        restrict: 'A',

        link: function linkGuacDownload($scope, $element, $attrs) {

            /**
             * The function to call whenever files are chosen. The callback is
             * provided no parameters.
             *
             * @type Function 
             */
            var guacDownload = $scope.$eval($attrs.guacDownload);

            /**
             * The element which will handle button click.
             *
             * @type Element
             */
            var element = $element[0];

            // Open file chooser when element is clicked
            element.addEventListener('click', function elementClicked() {
                // Only set chosen files selection is not canceled
                if (guacDownload)
                    guacDownload();
            });

        } // end guacDownload link function

    };

}]);
