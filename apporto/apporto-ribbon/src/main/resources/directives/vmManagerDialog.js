/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays vm backup/restore manager dialog
 */
angular.module('ribbon').directive('vmManagerDialog', [function vmManagerDialog() {

    return {
        restrict: 'E',
        scope: {
        },

        templateUrl: 'app/ext/ribbon/templates/vm-manager-dialog.html',
        controller: ['$scope', '$injector', '$location', function vmManagerDialogController($scope, $injector, $location) {

            // Required types
            var ManagedClientState      = $injector.get('ManagedClientState');

            var $timeout                = $injector.get('$timeout');
            var $translate              = $injector.get('$translate');
            var infoService             = $injector.get('infoService');
            var ClientIdentifier        = $injector.get('ClientIdentifier');
            var authenticationService   = $injector.get('authenticationService');
            var $routeParams            = $injector.get('$routeParams');
            var $http                   = $injector.get('$http');
            var guacNotification        = $injector.get('guacNotification');
            var $window                 = $injector.get('$window');
            var $rootScope              = $injector.get('$rootScope');

        	// Required services
			$scope.ribbonService         = $injector.get('ribbonService');
            $scope.circleLoaderService   = $injector.get('circleLoaderService');

            $scope.circleLoaderService.circleLoaderVisible = false;

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

			$scope.backups = [];
            $scope.message = "";
			$scope.selected = -1;
            $scope.loading = false;

            if($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                return;
            }

            /**
             * Read all backups when dialog becomes visible.
             */
            $scope.$watch('ribbonService.vmManagerVisible', function(newValue) {
                if (newValue) {
                    $scope.backups = [];
                    readBackups();
                }
            });

            /*********************************************************************************
             * Action for dialog buttons
             *********************************************************************************/

            /**
             * Action for calling vm rstore API
             */
            var RESTORE_VM_ACTION = {
                name      : "CLIENT.ACTION_RESTORE_VM",
                callback  : function restoreVmCallback() {
                                restoreVm();
                                guacNotification.showStatus(false);
                            }
            };

            /**
             * Action for calling vm backup API
             */
            var BACKUP_VM_ACTION = {
                name      : "CLIENT.ACTION_BACKUP_VM",
                callback  : function backupVmCallback() {
                                backupVm();
                                guacNotification.showStatus(false);
                            }
            };

            /**
             * Action for closing dialog
             */
            var CANCEL_ACTION = {
                name      : "APP.ACTION_CANCEL",
                callback  : function cancelCallback() {
                    guacNotification.showStatus(false);
                }
            };

            var actionsRestore    = [ CANCEL_ACTION, RESTORE_VM_ACTION ];
            var actionsBackup     = [ CANCEL_ACTION, BACKUP_VM_ACTION ];

            /*********************************************************************************
             * Dialog buttons handlers
             *********************************************************************************/

            /**
             * Read available backups
             */
            function readBackups() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                var httpParameters = {
                    token   : authenticationService.getCurrentToken(),
                    id      : clientIdentifier.id
                };

                $scope.loading = true;
                $scope.circleLoaderService.circleLoaderVisible = true;
                $http({
                	method: 'GET',
                    url: 'api/session/ext/' + authenticationService.getDataSource() + '/vm/datetime',
                    params: httpParameters
                })
                .then(function(response) {
                    var data = response.data;
                    console.debug(response.data);

                    if (data.Success == 0) {
                    	$scope.backups = data.Message;
                    }
                    else if (data.Success == 1) { 
                    	$scope.backups = [];
                        $scope.message = data.Message;
                    }
                })
                .catch(function(response) {
                    console.debug(response.data);
                    $translate('CLIENT.ERROR_BACKUP_READ').then( showInfo );
                    $scope.ribbonService.vmManagerVisible = false;
                })
                .finally(function() {
                    $scope.loading = false;
                    $scope.circleLoaderService.circleLoaderVisible = false;
                });
            }

            /**
             * Present a save snapshot dialog with warning about potential long running operaiont.
             * If the maximum number of snapshots is reached, warn about this as well.
             */
            $scope.restoreVm = function restoreVm() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'VM restore',
                    button_name: 'btn_ribbon_vm_restore',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                guacNotification.showStatus({
                    title   : "CLIENT.ACTION_RESTORE_VM",
                    text    : {
                        key : "CLIENT.TEXT_VM_RESTORE_WARNING"
                    },
                    actions : actionsRestore
                });
            }

            /**
             * Present a restore snapshot dialog with warning about potential long running operaiont.
             */
            $scope.backupVm = function backupVm() {

                // Google Tag Manager
                dataLayer.push({
                    event: 'VM backup',
                    button_name: 'btn_ribbon_vm_backup',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                guacNotification.showStatus({
                    title   : "CLIENT.ACTION_BACKUP_VM",
                    text    : {
                        key : "CLIENT.TEXT_VM_BACKUP_WARNING"
                    },
                    actions : actionsBackup
                });
            }

            /**
             * Close the dialog.
             */
            $scope.closeDialog = function closeDialog() {
            	$scope.ribbonService.vmManagerVisible = false;
            };

            /**
             * Show information window
             */
            function showInfo(text) {
                infoService.infoText = text;
                infoService.infoDialogVisible = true;
                $timeout(function hideInfo() {
                    infoService.infoDialogVisible = false;
                }, $scope.ribbonService.thresholdInfo);
            }

            /*********************************************************************************
             * Functions for calling backup/restore APIs
             *********************************************************************************/

            /**
             * Callback function for backing up the VMs.
             * Calls server REST API for backing up the VMs.
             */
            function backupVm() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                var httpParameters = {
                    token   : authenticationService.getCurrentToken(),
                    id      : clientIdentifier.id
                };

                $scope.ribbonService.hideDialogBackground = true;
                $scope.circleLoaderService.circleLoaderVisible = true;
                $http({
                	method: 'GET',
                    url: 'api/session/ext/' + authenticationService.getDataSource() + '/vm/backup',
                    params: httpParameters
                })
                .then(function(response) {
                    console.debug("Status: " + response.status);
                    console.debug(response.data);
                    $scope.ribbonService.vmManagerVisible = false;
                })
                .catch(function(response) {
                    if ($rootScope.connectionState == ManagedClientState.ConnectionState.CONNECTED) {
                        console.debug("Status: " + response.status);
                        console.debug(response.data);
                        $translate('CLIENT.ERROR_VM_BACKUP').then( showInfo );
                    }
                })
                .finally(function() {
                   $scope.circleLoaderService.circleLoaderVisible = false;
                    $scope.ribbonService.hideDialogBackground = false;
                    $scope.ribbonService.vmManagerVisible = false;
                });
            }

            /**
             * Callback function for restoring the VMs.
             * Calls server REST API for restoring the VMs.
             */
            function restoreVm() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var selected = $("input[name='backup']:checked").val();

                var httpParameters = {
                    token      : authenticationService.getCurrentToken(),
                    id         : clientIdentifier.id,
                    snapshotId : selected
                };

                $scope.ribbonService.hideDialogBackground = true;
                $scope.circleLoaderService.circleLoaderVisible = true;
                $http({
                    method  : 'GET',
                    url     : 'api/session/ext/' + authenticationService.getDataSource() + '/vm/restore',
                    params  : httpParameters
                })
                .then(function(response) {
                    console.debug("Status: " + response.status);
                    console.debug(response.data);
                    $scope.ribbonService.vmManagerVisible = false;
                })
                .catch(function(response) {
                    console.debug("Status: " + response.status);
                    console.debug(response.data);
                    $translate('CLIENT.ERROR_VM_RESTORE').then( showInfo );
                })
                .finally(function() {
                    $scope.circleLoaderService.circleLoaderVisible = false;
                    $scope.ribbonService.hideDialogBackground = false;
                    $scope.ribbonService.vmManagerVisible = false;
                });
            }

        }] // end vm manager controller

    };
}]);
