/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays network quality dialog
 */
angular.module('ribbon').directive('networkQualityDialog', [function networkQualityDialog() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/network-quality-dialog.html',
        controller: ['$scope', '$injector', function networkQualityDialogController($scope, $injector) {

            // This service is required by directive used in ribbon template html
            $scope.ribbonService      = $injector.get('ribbonService');
            
        }] // end share dialog controller

    };
}]);
