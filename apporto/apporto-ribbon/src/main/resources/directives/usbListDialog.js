/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays usb list
 */
 angular.module('ribbon').directive('usbListDialog', ['$window', function usbListDialog($window) {

    return {
        restrict: 'E',
        scope: {},

        templateUrl: 'app/ext/ribbon/templates/usb-list-dialog.html',
        controller: ['$scope', '$injector', '$rootScope', function usbListDialogController($scope, $injector, $rootScope) {

            // Required services
            $scope.ribbonService      = $injector.get('ribbonService');
            var guacClientManager     = $injector.get('guacClientManager');
            var $routeParams          = $injector.get('$routeParams');
            var $interval             = $injector.get('$interval');
            var $timeout              = $injector.get('$timeout');
            var $translate            = $injector.get('$translate');
            var ManagedClientState    = $injector.get('ManagedClientState');
            $scope.infoService        = $injector.get('infoService');

            /**
             * Valid USB connection state strings.
             */
            $scope.ConnectionState = {

                /**
                 * The USB connection has not yet been attempted.
                 * 
                 * @type String
                 */
                IDLE : "IDLE",

                /**
                 * The USB connection is being established.
                 * 
                 * @type String
                 */
                CONNECTING : "CONNECTING",

                /**
                 * The USB connection has been successfully established, and the
                 * client is now waiting for receipt of initial message data.
                 * 
                 * @type String
                 */
                WAITING : "WAITING",

                /**
                 * The USB connection has been successfully established, and
                 * initial message data has been received.
                 *
                 * @type String
                 */
                CONNECTED : "CONNECTED",

                /**
                 * The USB device has been successfully mounted.
                 *
                 * @type String
                 */
                MOUNTED : "MOUNTED"
            }

            // Indicate which status a USB feature is in
            $scope.connectionState = $scope.ConnectionState.IDLE;

            // Show the status message in dialog
            $scope.usbStatus = "";

            // Indicates the guacamole client
            $scope.client = null;

            // Connection ID of the guacd
            $scope.connection_id = "";

            // The interval timer to send the user info via websocket
            var intervalSendUserInfo = null;

            // The timer to retrieve the parameters for getting available usb devices
            var retrieveParam = null;

            // The count of retry to send the user info
            var retryCountUserInfo = 0;

            // The count of max retry
            var MAX_RETRY_COUNT = 3;

            // Set when the error is occurred while receiving the error status from USB agent.
            $scope.usbErrorState = false;

            // The list of all available usb devices
            $scope.usbList = [];

            // The selected USB device
            $scope.selectedUSB = null;

            // Time for process delay.
            var PROCESS_DELAY = 1 * 1000; // 1 second

            // Time to retrieve the parameter for getting available usb devices
            var RETRIEVE_PARAM_FREQUENCY = 5 * 1000; // 5 seconds

            // Time for response delay to receive the data from usb agent.
            var RESPONSE_DELAY = 30 * 1000; // 30 seconds

            // Timeout waiting till the usb device is mounted/unmounted.
            var mountTimeout = null;

            // Get client reference when route changes
            $scope.$on('$routeChangeSuccess', getClient);

            var queryClient = $interval(function() {
                if ($scope.client == null)
                    getClient();
                else {
                    $interval.cancel(queryClient);
                    queryClient = null;
                }
            }, 5 * 1000);

            function getClient() {
                if ($routeParams.id) {
                    var cl = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);
                    if (cl == null || cl.tunnel.uuid == null) {
                        return;
                    }
                    $scope.client = cl;
                }
            };

            $scope.$watch('client.tunnel.connection_id', function(connection_id) {
                if (connection_id == null || connection_id.length == 0 || $routeParams.hasOwnProperty("key"))
                    return;

                $scope.connection_id = connection_id;
            });

            function connect() {
                // Check if all the info is prepared and if not, try again after 3 seconds
                if (!($rootScope.session_id && $scope.username && $scope.connection_id
                    && $rootScope.servername && $rootScope.usb_server_port)) {
                    setTimeout(function() {
                        connect();
                    }, 3 * 1000);
                    return;
                }

                var SocketWrapper = function SocketWrapper(init) {
                    this.socket = new SimpleWebsocket(init);
                    this.messageHandlers = {};

                    var that = this;
                    this.socket.on('data', function (data) {
                        //Extract the message type
                        var messageData = JSON.parse(data);
                        var messageType = messageData['__MESSAGE__'];
                        delete messageData['__MESSAGE__'];

                        //If any handlers have been registered for the message type, invoke them
                        if (that.messageHandlers[messageType] !== undefined) {
                            for (index in that.messageHandlers[messageType]) {
                                that.messageHandlers[messageType][index](messageData);
                            }
                        }
                    });
                }

                SocketWrapper.prototype.on = function (event, handler) {
                    //If a standard event was specified, forward the registration to the socket's event emitter
                    if (['connect', 'close', 'data', 'error'].indexOf(event) != -1) {
                        this.socket.on(event, handler);
                    }
                    else {
                        //The event names a message type
                        if (this.messageHandlers[event] === undefined) {
                            this.messageHandlers[event] = [];
                        }
                        this.messageHandlers[event].push(handler);
                    }
                }

                SocketWrapper.prototype.send = function (message, payload) {
                    //Copy the values from the payload object, if one was supplied
                    var payloadCopy = {};
                    if (payload !== undefined && payload !== null) {
                        var keys = Object.keys(payload);
                        for (index in keys) {
                            var key = keys[index];
                            payloadCopy[key] = payload[key];
                        }
                    }

                    payloadCopy['__MESSAGE__'] = message;
                    this.socket.send(JSON.stringify(payloadCopy));
                }

                SocketWrapper.prototype.destroy = function() {
                    //Destroy the socket
                    this.socket.destroy();
                }

                var socket = new SocketWrapper("ws://127.0.0.1:" + $scope.ribbonService.usb_agent_port);

                // Generic events
                socket.on('connect', function() {
                    console.log("USB agent socket is connected!");
                    $scope.connectionState = $scope.ConnectionState.CONNECTING;
                    $scope.usbErrorState = false;
                    setStatusInfo('USB.STATUS_AGENT_CONNECT');
                });

                socket.on('data', function(data) {
                    console.log('got message: ' + data);
                    var response = JSON.parse(data);
                    switch (response['__MESSAGE__']) {
                        case "list": 
                            // All prerequisites are satisfied (AP-9298)
                            $scope.usbErrorState = false;
                            console.log("usbdevices: ", $scope.ribbonService.licenses.usbDevices);
                            $scope.usbList = response['data'];
                            $scope.selectedUSB = $scope.usbList[0];

                            var mountedDeviceIndex = $scope.usbList.findIndex(function (usb) {
                                return usb.attached == 1;
                            });

                            if (mountedDeviceIndex > -1) {
                                $scope.selectedUSB = $scope.usbList[mountedDeviceIndex];
                                setStatusInfo('USB.STATUS_DEVICE_MOUNT');
                            }
                            else {
                                $translate('USB.STATUS_DEVICE_READY').then(function setStatusMessage (text) {
                                    $scope.usbStatus = text;
                                });
                            }

                            break;
                        case "status":
                            $scope.commandProcess(response);
                    }
                });

                socket.on('close', function() {
                    console.log("USB agent socket is disconnected!");

                    setStatusInfo('USB.STATUS_AGENT_DISCONNECT');
                    initUSB();

                    $scope.usbErrorState = true;

                    setTimeout(function() {
                        connect();
                    }, 3 * 1000);
                });

                socket.on('error', function(err) {
                    socket.destroy();
                    console.log(err);
                });

                //Specific message type handlers

                socket.on('userInput', function(args) {
                    console.log("Received user input: \"" + args['input'] + "\"");
                });

                socket.on('message', function(args) {
                    console.log("Received user message: \"" + JSON.stringify(args) + "\"");
                });

                $scope.commandProcess = function commandProcess(data) {
                    switch (data['command']) {
                        case "init": 
                            if (data['status'] == "ok") {
                                $scope.connectionState = $scope.ConnectionState.WAITING;
                                sendUserInfo();

                                if ($scope.ribbonService.usbListDlgVisible) {
                                    $scope.ribbonService.mmLoad = true;
                                }
                            }
                            else if (data['status'] == "error") {
                                $scope.usbErrorState = true;
                                if (data['message']) {
                                    setStatusInfo(data['message'], "default");
                                }
                            }
                            break;
                        case "userinfo": 
                            if (data['status'] == "ok") {
                                $scope.connectionState = $scope.ConnectionState.CONNECTED;
                                retryCountUserInfo = 0;
                                if (intervalSendUserInfo) {
                                    $interval.cancel(intervalSendUserInfo);
                                    intervalSendUserInfo = null;
                                }
                                socket.send("list", {});
                            }
                            else if (data['status'] == "error") {
                                $scope.usbErrorState = true;
                                if (data['message']) {
                                    console.log("USB error: ", data['message']);
                                    setStatusInfo(data['message'], "default");
                                }
                            }
                            else if (data['status'] == "info") {
                                var statusInfo = data['message'];
                                console.log("USB info: ", statusInfo);
                                // The following status info are failed info
                                if (statusInfo.indexOf("grpcServer:no") == -1 &&
                                    statusInfo.indexOf("grpcServer:fail") == -1 &&
                                    statusInfo.indexOf("usbServer:no") == -1 &&
                                    statusInfo.indexOf("usbServerDriver:fail") == -1 &&
                                    statusInfo.indexOf("usbServer:fail") == -1 &&
                                    statusInfo.indexOf("usbDevices:no") == -1) {
                                    $translate('USB.STATUS_DEVICE_READY_INFO').then(function setStatusMessage (text) {
                                        $scope.infoService.top = true;
                                        $scope.infoService.infoText = text;
                                        $scope.infoService.infoDialogVisible = true;
                                    });

                                    // Get usb agent and server versions: usbAgentVer:x,usbServerVer:x
                                    var version = extractVersions(statusInfo);
                                    console.log("USB version info: ", version);
                                    if (version.agentVersion != version.serverVersion) {
                                        $scope.usbErrorState = true;
                                        $translate('USB.STATUS_VERSION', {VERSION: version.serverVersion}).then(function setStatusMessage (text) {
                                            $scope.usbStatus = text;
                                        });
                                    }
                                    else {
                                        $scope.connectionState = $scope.ConnectionState.CONNECTED;
                                        retryCountUserInfo = 0;
                                        if (intervalSendUserInfo) {
                                            $interval.cancel(intervalSendUserInfo);
                                            intervalSendUserInfo = null;
                                        }
                                        socket.send("list", {});
                                    }
                                }
                            }
                            $scope.ribbonService.mmLoad = false;
                            break;
                        case "mount": 
                            clearMountTimeout();

                            setTimeout(function() {
                                $scope.ribbonService.mmLoad = false;
                                if (data['status'] == "ok") {
                                    $scope.usbErrorState = false;
                                    setStatusInfo('USB.STATUS_DEVICE_MOUNT');
                                    $scope.connectionState = $scope.ConnectionState.MOUNTED;

                                    var mountedDeviceIndex = $scope.usbList.findIndex(function (usb) {
                                        return usb.busid == data['busid'];
                                    });

                                    if (mountedDeviceIndex > -1) {
                                        $scope.selectedUSB = $scope.usbList[mountedDeviceIndex];
                                    }
                                }
                                else if (data['status'] == "error") {
                                    $scope.usbErrorState = true;
                                    if (data['message']) {
                                        if (data['message'] == "grpcServer:device") {
                                            setStatusInfo('USB.STATUS_DEVICE_NOT_ALLOWED');
                                        }
                                        else {
                                            setStatusInfo(data['message'], "default");
                                        }
                                    }
                                }
                            }, PROCESS_DELAY);
                            break;
                        case "unmount": 
                            clearMountTimeout();

                            setTimeout(function() {
                                $scope.ribbonService.mmLoad = false;
                                if (data['status'] == "ok") {
                                    $scope.usbErrorState = false;
                                    setStatusInfo('USB.STATUS_DEVICE_UNMOUNT');
                                    $scope.connectionState = $scope.ConnectionState.CONNECTED;
                                }
                                else if (data['status'] == "error") {
                                    $scope.usbErrorState = true;
                                    if (data['message']) {
                                        setStatusInfo(data['message'], "default");
                                    }
                                }
                            }, PROCESS_DELAY);
                            break;
                        case "connect": 
                            $scope.ribbonService.mmLoad = false;
                            if (data['status'] == "error") {
                                clearMountTimeout();
                                $scope.usbErrorState = true;
                                $scope.usbStatus = data['message'];
                            }
                            break;
                        default: 
                            $scope.ribbonService.mmLoad = false;
                            break;
                    }
                }

                function extractVersions(inputString) {
                    const serverRegex = /usbServerVer:(\d+\.\d+)/;
                    const agentRegex = /usbAgentVer:(\d+\.\d+)/;
                    
                    const serverMatch = inputString.match(serverRegex);
                    const agentMatch = inputString.match(agentRegex);
                    
                    const result = {};
                    
                    if (serverMatch) {
                      result.serverVersion = serverMatch[1];
                    }
                    
                    if (agentMatch) {
                      result.agentVersion = agentMatch[1];
                    }
                    
                    return Object.keys(result).length > 0 ? result : null;
                }

                /**
                 * Initialize flags
                 */
                function initUSB() {
                    $scope.connectionState = $scope.ConnectionState.IDLE;
                    $scope.usbList = [];
                    $scope.selectedUSB = null;
                    $scope.usbErrorState = false;

                    if (intervalSendUserInfo) {
                        $interval.cancel(intervalSendUserInfo);
                        intervalSendUserInfo = null;
                    }

                }

                /**
                 * Clear the mountTimeout variable
                 */
                var clearMountTimeout = function clearMountTimeout() {
                    if (mountTimeout != null) {
                        $timeout.cancel(mountTimeout);
                        mountTimeout = null;
                    }
                }

                /**
                 * Show the status text
                 */
                var setStatusInfo = function setStatusInfo(statusText, messageType) {

                    // Update the status text of the USB dialog
                    if (messageType == "default") {
                        $scope.usbStatus = statusText;
                    }
                    else {
                        $translate(statusText).then(function setStatusMessage (text) {
                            $scope.usbStatus = text;
                        })
                        .catch(function(error) {
                            console.error("Translation Error:", error);
                        });
                    }

                    // Check some conditions
                    if ($scope.ribbonService.usbListDlgVisible
                        || $scope.connectionState === $scope.ConnectionState.IDLE 
                        || $scope.connectionState === $scope.ConnectionState.CONNECTING 
                        || $scope.connectionState === $scope.ConnectionState.WAITING)
                        return;

                    // Update the status text of the info dialog
                    if (messageType == "default") {
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = statusText;
                        $scope.infoService.infoDialogVisible = true;
                    }
                    else {
                        $translate(statusText).then(function setStatusMessage (text) {
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = text;
                            $scope.infoService.infoDialogVisible = true;
                        })
                        .catch(function(error) {
                            console.error("Translation Error:", error);
                        });
                    }

                }

                /**
                 * Mount/unmount usb
                 */
                $scope.mountUSB = function mountUSB() {
                    $scope.ribbonService.mmLoad = true;
                    socket.send($scope.connectionState === $scope.ConnectionState.CONNECTED? "mount" : "unmount", {
                        "busid": $scope.selectedUSB.busid,
                        "class_code": $scope.selectedUSB.class_code,
                        "devices": $scope.ribbonService.licenses.usbDevices
                    });

                    $scope.usbErrorState = false;
                    if ($scope.connectionState === $scope.ConnectionState.MOUNTED) {
                        setStatusInfo('USB.STATUS_DEVICE_UNMOUNTING');
                    }
                    else {
                        setStatusInfo('USB.STATUS_DEVICE_MOUNTING');
                    }

                    // Stop the mount/unmount action if the usb device isn't mounted/unmounted within RESPONSE_DELAY timeout.
                    mountTimeout = $timeout(function() {
                        $scope.usbErrorState = true;
                        $scope.ribbonService.mmLoad = false;

                        if ($scope.connectionState === $scope.ConnectionState.MOUNTED) {
                            setStatusInfo('USB.STATUS_DEVICE_UNMOUNT_ERROR');
                        }
                        else {
                            setStatusInfo('USB.STATUS_DEVICE_MOUNT_ERROR');
                        }
                    }, RESPONSE_DELAY);
                }

                /**
                 * Refresh the device list
                 */
                $scope.refreshDeviceList = function refreshDeviceList() {
                    socket.send("list", {});
                }

                $scope.$watch('client.clientState.connectionState', function clientStateChanged(connectionState) {
                    if (connectionState === ManagedClientState.ConnectionState.DISCONNECTED ||
                        connectionState === ManagedClientState.ConnectionState.CLIENT_ERROR ||
                        connectionState === ManagedClientState.ConnectionState.TUNNEL_ERROR) {
                        if ($scope.connectionState === $scope.ConnectionState.MOUNTED && $scope.selectedUSB != null) {
                            socket.send("unmount", {
                                "busid": $scope.selectedUSB.busid
                            });
                        }

                        $scope.ribbonService.usbListDlgVisible = false;
                        initUSB();

                    }
                });

                $scope.$watch('ribbonService.usbListDlgVisible', function (value) {

                    if (!value)
                        return;

                    if ($scope.selectedUSB) {
                        if ($scope.connectionState === $scope.ConnectionState.CONNECTED) {
                            $scope.usbErrorState = false;
                            setStatusInfo('USB.STATUS_DEVICE_READY');
                        }
                    }
                    else {
                        if ($scope.connectionState === $scope.ConnectionState.IDLE) {
                            $scope.usbErrorState = true;
                            $translate('USB.STATUS_AGENT_DISCONNECT').then(function setStatusMessage (text) {
                                $scope.usbStatus = text;
                            })
                            .catch(function(error) {
                                console.error("Translation Error:", error);
                            });
                        }
                        else if ($scope.connectionState === $scope.ConnectionState.WAITING) {
                            $scope.ribbonService.mmLoad = true;
                            sendUserInfo();
                        }
                    }

                    $scope.infoService.infoDialogVisible = false;

                });

                $rootScope.$watch('selectedUSB', function() {

                    if ($scope.selectedUSB) {
                        $scope.usbErrorState = false;
                        setStatusInfo('USB.STATUS_DEVICE_READY');
                    }

                });

                function doSendUserInfo() {

                    var gateway_port = $window.location.port || ($window.location.protocol === 'https:' ? '443' : '80');

                    if ($rootScope.session_id && $scope.username && $scope.connection_id
                        && $rootScope.servername && $rootScope.usb_server_port) {

                        $scope.usbErrorState = false;
                        setStatusInfo('USB.STATUS_DEVICE_RETRIEVING');
                        
                        socket.send("userinfo", {
                            "session_id"      : $rootScope.session_id,
                            "user_name"       : $scope.username,
                            "connection_id"   : $scope.connection_id,
                            "hostname"        : $rootScope.servername,
                            "port"            : $rootScope.usb_server_port,
                            "gateway_host"    : $window.location.hostname,
                            "gateway_port"    : gateway_port,
                            "websocket_token" : $scope.ribbonService.serverId,
                            "http_protocol"   : $window.location.protocol,
                            "grpc_server"     : $rootScope.grpc_server,
                        });

                        if (retrieveParam != null) {
                            $scope.ribbonService.mmLoad = false;
                            $interval.cancel(retrieveParam);
                            retrieveParam = null;
                        }

                        return true;
                    }
                    else {
                        return false;
                    }

                }

                function sendUserInfo() {

                    $scope.username = $scope.ribbonService.userinfo.name ? $scope.ribbonService.userinfo.name 
                                                                         : $scope.ribbonService.licenses.default_username;
                    var ret = doSendUserInfo();
                    if (ret == false && retrieveParam == null) {
                        $scope.usbErrorState = false;
                        setStatusInfo('USB.STATUS_AGENT_INITIALIZING');

                        retrieveParam = $interval(function() {
                            if ($scope.connectionState === $scope.ConnectionState.WAITING
                                && retryCountUserInfo < MAX_RETRY_COUNT) {
                                retryCountUserInfo++;
                                doSendUserInfo();
                            }
                            else {
                                $scope.ribbonService.mmLoad = false;
                                $interval.cancel(retrieveParam);
                                retrieveParam = null;
                                retryCountUserInfo = 0;
                                $scope.usbErrorState = true;
                                setStatusInfo('USB.STATUS_DEVICE_INFO_FAIL');
                            }
                        }, RETRIEVE_PARAM_FREQUENCY);
                    }
                }
            }

            $scope.$watch('ribbonService.licenses.hasUSBLicence', function (value) {
                if (value) {
                    // Create the socket between guacamole client and usb agent service
                    if ($scope.connectionState === $scope.ConnectionState.IDLE) {
                        connect();
                    }

                    // Get the user name
                    $scope.username = $scope.ribbonService.userinfo.name ? $scope.ribbonService.userinfo.name 
                                                                         : $scope.ribbonService.licenses.default_username;
                }
            });

            /**
             * Close the dialog
             */
            $scope.closeUSBListDialog = function closeUSBListDialog() {
                $scope.ribbonService.usbListDlgVisible = false;
            }

        }] // end usb list dialog controller
    };
}]);