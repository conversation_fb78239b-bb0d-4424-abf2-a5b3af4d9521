/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A directive which displays the contents of a filesystem received through the
 * Guacamole client.
 */
angular.module('client').directive('fileBrowser', [function fileBrowser() {

    return {
        restrict: 'E',
        replace: true,
        scope: {

            /**
             * The client whose file transfers should be managed by this
             * directive.
             *
             * @type ManagedClient
             */
            client: '=',

            /**
             * @type ManagedFilesystem
             */
            filesystem: '=',

            /**
             * Enables multiple file selection 
             *
             * @type Boolean
             */
            multiSelect: '=?'

        },

        templateUrl: 'app/client/templates/guacFileBrowser.html',
        controller: ['$scope', '$element', '$injector', '$rootScope', function fileBrowserController($scope, $element, $injector) {

            // Required types
            var ManagedFilesystem = $injector.get('ManagedFilesystem');

            // Required services
            var $interpolate = $injector.get('$interpolate');
            var $templateRequest = $injector.get('$templateRequest');
            var ClientIdentifier = $injector.get('ClientIdentifier');
            var guacNotification = $injector.get('guacNotification');
            var $rootScope = $injector.get('$rootScope');

            $scope.ribbonService = $injector.get('ribbonService');
            $scope.circleLoaderService = $injector.get('circleLoaderService');
            $scope.loading = false;
            

            $rootScope.clearFileSelection = function clearFileSelection() {
                if ($scope.multiSelect) {
                    var checkboxes = $element.find('.file-checkbox');
                    checkboxes.prop('checked', false);
                    checkboxes.closest('tr').removeClass('focused');
                }
            }

            /**
             * The jQuery-wrapped element representing the contents of the
             * current directory within the file browser.
             *
             * @type Element[]
             */
            var currentDirectoryContents = $element.find('.current-directory-contents');

            /**
             * Statically-cached template HTML used to render each file within
             * a directory. Once available, this will be used through
             * createFileElement() to generate the DOM elements which make up
             * a directory listing.
             *
             * @type String
             */
            var fileTemplate = null;

            /**
             * Returns whether the given file is a normal file.
             *
             * @param {ManagedFilesystem.File} file
             *     The file to test.
             *
             * @returns {Boolean}
             *     true if the given file is a normal file, false otherwise.
             */
            $scope.isNormalFile = function isNormalFile(file) {
                return file.type === ManagedFilesystem.File.Type.NORMAL;
            };

            /**
             * Returns whether the given file is a directory.
             *
             * @param {ManagedFilesystem.File} file
             *     The file to test.
             *
             * @returns {Boolean}
             *     true if the given file is a directory, false otherwise.
             */
            $scope.isDirectory = function isDirectory(file) {
                return file.type === ManagedFilesystem.File.Type.DIRECTORY;
            };

            /**
             * Changes the currently-displayed directory to the given
             * directory.
             *
             * @param {ManagedFilesystem.File} file
             *     The directory to change to.
             */
            $scope.changeDirectory = function changeDirectory(file) {
                ManagedFilesystem.changeDirectory($scope.filesystem, file);
            };

            /**
             * Initiates a download of the given file. The progress of the
             * download can be observed through guacFileTransferManager.
             *
             * @param {ManagedFilesystem.File} file
             *     The file to download.
             */
            $scope.downloadFile = function downloadFile(file) {
                ManagedFilesystem.downloadFile($scope.client, $scope.filesystem, file.streamName);
            };

            /**
             * Recursively interpolates all text nodes within the DOM tree of
             * the given element. All other node types, attributes, etc. will
             * be left uninterpolated.
             *
             * @param {Element} element
             *     The element at the root of the DOM tree to be interpolated.
             *
             * @param {Object} context
             *     The evaluation context to use when evaluating expressions
             *     embedded in text nodes within the provided element.
             */
            var interpolateElement = function interpolateElement(element, context) {

                // Interpolate the contents of text nodes directly
                if (element.nodeType === Node.TEXT_NODE)
                    element.nodeValue = $interpolate(element.nodeValue)(context);

                // Recursively interpolate the contents of all descendant text
                // nodes
                if (element.hasChildNodes()) {
                    var children = element.childNodes;
                    for (var i = 0; i < children.length; i++)
                        interpolateElement(children[i], context);
                }

            };

            /**
             * Creates a new element representing the given file and properly
             * handling user events, bypassing the overhead incurred through
             * use of ngRepeat and related techniques.
             *
             * Note that this function depends on the availability of the
             * statically-cached fileTemplate.
             *
             * @param {ManagedFilesystem.File} file
             *     The file to generate an element for.
             *
             * @returns {Element[]}
             *     A jQuery-wrapped array containing a single DOM element
             *     representing the given file.
             */
            var createFileElement = function createFileElement(file) {

                // Create from internal template
                var element = angular.element(fileTemplate);
                interpolateElement(element[0], file);

                // Double-clicking on unknown file types will do nothing

                // Add checkbox wrapped in td if in multi-select mode
                if ($scope.multiSelect) {
                    var checkboxTd = angular.element('<td class="checkbox-column"></td>');
                    var checkbox = angular.element('<input type="checkbox" class="file-checkbox">');

                    if ($scope.isDirectory(file)) {
                        checkbox.attr('disabled', true);
                    }

                    checkboxTd.append(checkbox);
                    element.prepend(checkboxTd);

                    // Update focused class based on checkbox
                    checkbox.on('change', function(e) {
                        e.stopPropagation();
                        if (this.checked) {
                            element.addClass('focused');
                        } else {
                            element.removeClass('focused');
                        }
                    });
                }

                var fileAction = function doNothing() {};

                // Change current directory when directories are clicked
                if ($scope.isDirectory(file)) {
                    element.addClass('directory');
                    fileAction = function changeDirectory() {
                        $scope.changeDirectory(file);
                    };
                }

                // Initiate downloads when normal files are clicked
                else if ($scope.isNormalFile(file)) {
                    element.addClass('normal-file');
                }

                element.attr('aria-label', file.name);

                element.attr('role', 'button');

                var name = file.streamName;
                if ($scope.isDirectory(file) && !name.includes("Desktop") &&
                    !name.includes("Documents") && !name.includes("Downloads")) {
                    
                    if ($scope.multiSelect) {
                        element.children().eq(1).attr("style", "color: #A9A9A9;");
                        element.children().eq(3).attr("style", "color: #A9A9A9;");
                        element.find('.file-checkbox').attr('disabled', true);
                    } else {
                        element.children().eq(0).attr("style", "color: #A9A9A9;");
                        element.children().eq(2).attr("style", "color: #A9A9A9;");
                    }

                    element.on('click', function handleFileClick(e) {
                        e.preventDefault();
                        e.stopPropagation();
                    });
                }
                else {
                    // Mark file as focused upon click
                    element.on('click', function handleFileClick(e) {
                        // Ignore clicks on checkbox in multi-select mode
                        if ($scope.multiSelect && (e.target.type === 'checkbox' || e.target.className === 'checkbox-column')) {
                            return;
                        }

                        if ($scope.multiSelect) {
                            // Toggle checkbox when clicking the row
                            if ($scope.isNormalFile(file)) {
                                var checkbox = element.find('.file-checkbox');
                                checkbox.prop('checked', !checkbox.prop('checked'));
                                checkbox.trigger('change');
                            } else if ($scope.isDirectory(file)) {
                                fileAction();
                            }
                        } else {
                            // Original single-select behavior
                            // Fire file-specific action if already focused
                            var download_file_btn = document.getElementById("download_file_btn");
                            if (element.hasClass('focused')) {
                                fileAction();
                                element.removeClass('focused');
                                download_file_btn.classList.add("disable_download_btn");
                                download_file_btn.setAttribute("aria-disabled", "true");
                            } else {
                                element.parent().children().removeClass('focused');
                                element.addClass('focused');
                                if(element.hasClass('normal-file')){
                                    download_file_btn.classList.remove("disable_download_btn");
                                    download_file_btn.setAttribute("aria-disabled", "false");
                                }
                            }
                        }
                    });
                }

                element.on('selectstart', function avoidSelect(e) {
                    e.preventDefault();
                    e.stopPropagation();
                });

                return element;
            };

            /**
             * Sorts the given map of files, returning an array of those files
             * grouped by file type (directories first, followed by non-
             * directories) and sorted lexicographically.
             *
             * @param {Object.<String, ManagedFilesystem.File>} files
             *     The map of files to sort.
             *
             * @returns {ManagedFilesystem.File[]}
             *     An array of all files in the given map, sorted
             *     lexicographically with directories first, followed by non-
             *     directories.
             */
            var sortFiles = function sortFiles(files) {

                // Get all given files as an array
                var unsortedFiles = [];
                for (var name in files)
                    unsortedFiles.push(files[name]);

                // Sort files - directories first, followed by all other files
                // sorted by name
                return unsortedFiles.sort(function fileComparator(a, b) {

                    // Directories come before non-directories
                    if ($scope.isDirectory(a) && !$scope.isDirectory(b))
                        return -1;

                    // Non-directories come after directories
                    if (!$scope.isDirectory(a) && $scope.isDirectory(b))
                        return 1;

                    // All other combinations are sorted by name
                    return a.name.localeCompare(b.name);

                });

            };

            function formatDateWithIntl(dateStr) {
                try {
                    const currentDate = new Date();
                    const currentYear = currentDate.getFullYear();
                    const currentMonth = currentDate.getMonth() + 1; // Months are zero-based
                    const currentDay = currentDate.getDate();
                    
                    const dateParts = dateStr.split('-');
            
                    const monthNames = {
                        "January": 1, "Jan": 1,
                        "February": 2, "Feb": 2,
                        "March": 3, "Mar": 3,
                        "April": 4, "Apr": 4,
                        "May": 5,
                        "June": 6, "Jun": 6,
                        "July": 7, "Jul": 7,
                        "August": 8, "Aug": 8,
                        "September": 9, "Sep": 9,
                        "October": 10, "Oct": 10,
                        "November": 11, "Nov": 11,
                        "December": 12, "Dec": 12
                    };
            
                    const monthNamesReverse = {
                        1: "January",
                        2: "February",
                        3: "March",
                        4: "April",
                        5: "May",
                        6: "June",
                        7: "July",
                        8: "August",
                        9: "September",
                        10: "October",
                        11: "November",
                        12: "December"
                    };
            
                    let month, day, year;
            
                    if (dateParts.length === 2) {
                        // Case where year is not provided
                        [month, day] = dateParts;
                        day = parseInt(day, 10);
            
                        if (isNaN(day) || !monthNames[month]) {
                            throw new Error('Invalid day or month name');
                        }
            
                        month = monthNames[month];
            
                        if (month < currentMonth || (month === currentMonth && day <= currentDay)) {
                            year = currentYear;
                        } else {
                            year = currentYear - 1;
                        }
                    } else if (dateParts.length === 3) {
                        if (isNaN(dateParts[0])) {
                            // Format: Month-Day-Year
                            [month, day, year] = dateParts;
                            day = parseInt(day, 10);
                            year = parseInt(year, 10);
            
                            if (isNaN(day) || isNaN(year) || !monthNames[month]) {
                                throw new Error('Invalid day, year, or month name');
                            }
            
                            month = monthNames[month];
                        } else {
                            // Format: Year-Month-Day
                            [year, month, day] = dateParts;
                            month = monthNames[month];
                            day = parseInt(day, 10);
                            year = parseInt(year, 10);
            
                            if (isNaN(month) || isNaN(day) || isNaN(year)) {
                                throw new Error('Invalid month, day, or year');
                            }
                        }
                    } else {
                        // Invalid date format
                        return dateStr;
                    }
            
                    const dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                    const date = new Date(dateString);
            
                    if (isNaN(date.getTime())) {
                        throw new Error('Invalid date');
                    }
            
                    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            
                    if (isSafari) {
                        return `${monthNamesReverse[month]} ${parseInt(day, 10)}, ${year}`;
                    } else {
                        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric', timeZone: 'UTC' });
                    }
                } catch (error) {
                    console.error('Error formatting date:', error); // Log error for debugging
                    return dateStr;
                }
            }

            // Watch directory contents once file template is available
            $templateRequest('app/client/templates/file.html').then(function fileTemplateRetrieved(html) {

                // Store file template statically
                fileTemplate = html;

                // Update the contents of the file browser whenever the current directory (or its contents) changes
                $scope.$watch('filesystem.currentDirectory.files', function currentDirectoryChanged(files) {
                    $scope.ribbonService.currFileExist = true;
                    if($scope.ribbonService.assignmentManagementDialogVisible)
                        $scope.ribbonService.skeletonLoad.assignmentManagementFile = true;
                    else
                        $scope.circleLoaderService.circleLoaderVisible = true;
                    $scope.loading = true;

                    angular.forEach(document.getElementsByClassName("upload button"), function (btn) {
                        btn.setAttribute("disabled", "disabled");
                        btn.setAttribute('data-tooltip', "Navigate to Desktop, Documents or Downloads to upload a file");
                    });

                    // Clear current content
                    currentDirectoryContents.html('');

                    // Check if the file system is null
                    if ($scope.filesystem == null) {
                        if($scope.ribbonService.assignmentManagementDialogVisible)
                            $scope.ribbonService.skeletonLoad.assignmentManagementFile = false;
                        else
                            $scope.circleLoaderService.circleLoaderVisible = false;
                        $scope.loading = false;
                        return;
                    }

                    // Check if the files of the current directory aren't all read
                    if (!$scope.filesystem.currentDirectory.ready) {
                        return;
                    }

                    var destination = $scope.filesystem.currentDirectory.streamName;
                    if (destination != "/") {
                        if (destination.includes("Documents") || destination.includes("Desktop") || destination.includes("Downloads")) {
                            angular.forEach(document.getElementsByClassName("upload button"), function (btn) {
                                btn.removeAttribute("disabled");
                                setTimeout(function () {
                                    btn.removeAttribute("title");
                                }, 1000);
                            });
                        }
                    }

                    angular.forEach(sortFiles(files), function displayFile(file) {
                        currentDirectoryContents.append(createFileElement(file));
                        if (file.streamName.includes("Documents") || file.streamName.includes("Desktop") ||
                            file.streamName.includes("Downloads") || $scope.ribbonService.fileDownloadVisible) {
                            angular.forEach(document.getElementsByClassName("caption"), function (item) {
                                if (item.innerText.trim() == "Documents" || item.innerText.trim() == "Desktop" || item.innerText.trim() == "Downloads") {
                                    var icon = item.getElementsByClassName("icon disabled");
                                    angular.forEach(icon, function (currentIcon) {
                                        currentIcon.classList.remove("disabled");
                                    })
                                }
                            });
                        }
                    });

                    var OK_FILE = {
                        name: "DIALOGS.BUTTON_OK",
                        // Handle action
                        callback: function acknowledgeCallback() {
                            guacNotification.showStatus(false);
                        },
                        className: 'cancelBtnClass'
                    };

                    var fileDetails = [];
                    var $routeParams = $injector.get('$routeParams');
                    var authenticationService = $injector.get('authenticationService');
                    var $http = $injector.get('$http');

                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    var datasource = encodeURIComponent(clientIdentifier.dataSource);

                    var httpParameters = {
                        token: encodeURIComponent(authenticationService.getCurrentToken()),
                        id: encodeURIComponent(clientIdentifier.id),
                        fileName: $scope.filesystem.currentDirectory.streamName,
                    };

                    var req = {
                        method: 'GET',
                        url: "api/session/ext/" + datasource + "/details",
                        params: httpParameters
                    };

                    let status = 0;

                    $http(req)
                    .then(function (response) {

                        console.log("Achieving details about files. \n Response: ", response);

                        var data = response.data;
                        if (!data) {
                            return;
                        }

                        // get the status code
                        status = response.status;

                        // element of array is row with three data information for one file, one element -> one file
                        var filesData = data.split("#");

                        if (filesData[0] == $scope.filesystem.currentDirectory.streamName) {
                            // Clear current content
                            currentDirectoryContents.html('');
                            for (i = 1; i < filesData.length; i++) {
                                filesData[i] = filesData[i].trim();
                                var details = filesData[i].split("\t");
                                detail = {
                                    size: details[1],
                                    modified: details[2]
                                }
                                fileDetails[details[0].trim()] = detail;
                            }

                            // Get the size of files
                            let files_size = Object.keys(files).length;

                            // Check if any file to be downloaded exists
                            if (files_size == 0) {
                                $scope.ribbonService.currFileExist = false;
                                if(!$scope.ribbonService.assignmentManagementDialogVisible) {
                                    if (document.getElementsByClassName("download button ng-binding ng-hide").length == 0) {
                                        guacNotification.showStatus({
                                            text: {
                                                key: 'DIALOGS.FILE_DOWNLOAD_EMPTY',
                                                variables: {
                                                    FILE_NAME: 'jela'
                                                }
                                            },
                                            actions: [OK_FILE]
                                        });
                                    }
                                }
                            }

                            // Display all files within current directory, sorted
                            angular.forEach(sortFiles(files), function displayFile(file) {
                                if (fileDetails[file.name] !== undefined) {
                                    if (fileDetails[file.name].size === "512" && $scope.isDirectory(file)) {
                                        file.size = "";
                                    }
                                    else {
                                        file.size = formatBytes(fileDetails[file.name].size);
                                    }

                                    file.modified = formatDateWithIntl(fileDetails[file.name].modified);
                                }
                                else {
                                    file.size, file.modified = "";
                                }

                                currentDirectoryContents.append(createFileElement(file));

                                if (file.streamName.includes("Documents") || file.streamName.includes("Desktop") ||
                                    file.streamName.includes("Downloads") || $scope.ribbonService.fileDownloadVisible) {
                                    angular.forEach(document.getElementsByClassName("caption"), function (item) {
                                        if (item.innerText.trim() == "Documents" || item.innerText.trim() == "Desktop" || item.innerText.trim() == "Downloads") {
                                            var icon = item.getElementsByClassName("icon disabled");
                                            angular.forEach(icon, function (currentIcon) {
                                                currentIcon.classList.remove("disabled");
                                            })
                                        }
                                    });
                                }
                            });
                        }
                    })
                    .catch(function () {
                        // show the error message
                        $translate('DIALOGS.FILE_ERROR_ACHIEVING_DETAILS').then(function (message) {
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = message;
                            $scope.infoService.infoDialogVisible = true;
                        });
                        console.log("Achieving details about files is FAILED.");
                    })
                    .finally(function () {
                        // only if achieving details about files successed, we can prepare to upload or download
                        if (status == 200) {
                            $scope.circleLoaderService.circleLoaderVisible = false;
                            $scope.ribbonService.skeletonLoad.assignmentManagementFile = false;
                            $scope.loading = false;
                            // if a user is tryping to upload any file
                            if (document.getElementsByClassName("download button ng-binding ng-hide").length > 0) {
                                $scope.ribbonService.fileListReady = true;
                            }
                        }
                    });

                });

            }, angular.noop); // end retrieve file template

            var formatBytes = function formatBytes(bytes, decimals) {
                if (bytes == 0) return '0 Bytes';
                var k = 1024,
                    dm = decimals || 2,
                    sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
                    i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
            }

        }]

    };
}]);
