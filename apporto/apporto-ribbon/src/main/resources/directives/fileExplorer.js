/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/** 
 * Directive which displays file browser
 */
angular.module('ribbon', ['client']).directive('fileExplorer', [function fileExplorer() {

    return {
        restrict: 'E',
        scope: {
            /**
             * Enables multiple file selection 
             *
             * @type Boolean
             */
            multiSelect: '=?'
        },

        templateUrl: 'app/ext/ribbon/templates/file-explorer.html',
        controller: ['$scope', '$injector', '$location', '$rootScope',
            function fileExplorerController($scope, $injector, $location, $rootScope) {

                // Required services
                var ManagedClient = $injector.get('ManagedClient');
                var ManagedFilesystem = $injector.get('ManagedFilesystem');
                var guacClientManager = $injector.get('guacClientManager');
                var $translate = $injector.get('$translate');
                var $routeParams = $injector.get('$routeParams');
                var $timeout = $injector.get('$timeout');
                var $interval = $injector.get('$interval');
                var $http = $injector.get('$http');
                var ClientIdentifier = $injector.get('ClientIdentifier');
                var guacNotification = $injector.get('guacNotification');
                var authenticationService = $injector.get('authenticationService');
                var $rootScope = $injector.get('$rootScope');

                $scope.ribbonService = $injector.get('ribbonService');

                // Variables related to check if the filesystem is available and if it contains
                // a mandatory directory (Desktop)
                var CHECK_FS_LIMIT_NUMBER = 60; // 300 seconds (5 mins)
                var checkFSCount = 0;

                if ($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                    return;
                }

                var fileDownloadDisabled = true;

                $scope.circleLoaderService = $injector.get('circleLoaderService');
                $scope.loading = false;

                // This service is required by directive used in ribbon template html
                $scope.infoService = $injector.get('infoService');

                var fileNameListForUploadCompare = [];
                var matching = []; 
                var matchingCount = 0;

                $scope.sharingProfiles = {};
                $scope.client = null;
                $scope.filesystem = null;
                
                //This stores the ids of files that are to be submitted for a single submission
                $rootScope.assignmentUploadFiles = null;

                $rootScope.assignmentSubmitLoader = false;

                //Diasble the resubmit button for assigment upload
                $rootScope.disableResubmit = function disableResubmit() {
                    $rootScope.clearFileSelection()
                    $rootScope.resubmitEnabled = false;
                }

                var KEEP_BOTH = {
                    name: "DIALOGS.KEEP_BOTH",
                    // Handle action
                    callback: function acknowledgeCallback() {
                        $scope.keepBoth();
                    },
                    className: 'button'
                };

                var REPLACE = {
                    name: "DIALOGS.REPLACE",
                    // Handle action
                    callback: function acknowledgeCallback() {
                        $scope.replace();
                    },
                    className: 'button'
                };

                var CANCEL_FILE = {
                    name: "DIALOGS.BUTTON_CANCEL",
                    // Handle action
                    callback: function acknowledgeCallback() {
                        $scope.cancelFile();
                    },
                    className: 'cancelBtnClass'
                };

                // Constants
                var RDP_FS_NAME = "Shared Drive"; // Default name given by guacd to RDP FS
                var INITIAL_DIRECTORIES = ["Desktop", "Bureau"];

                $scope.$on('$routeChangeSuccess', function (event, current, previous) {
                    if ($routeParams.id) {
                        $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                        // Check the file system only if the upload or download feature is enabled
                        if ($scope.ribbonService.isSftpAvailable && ($scope.ribbonService.licenses.hasUploadLicence || $scope.ribbonService.licenses.hasDownloadLicence)) {
                            checkFilesystemReady();
                        }
                    }
                });

                // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
                // try to get client each 5 seconds.
                var checkFS = null;

                // Check the file system only if the upload or download feature is enabled
                $scope.$watch('ribbonService.licenses.hasUploadLicence', function () {
                    if ($scope.ribbonService.isSftpAvailable && $scope.ribbonService.licenses.hasUploadLicence && checkFS == null) {
                        checkFS = $interval(function () {
                            if ($routeParams.id) {
                                if ($scope.client == null)
                                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                                checkFilesystemReady();
                            }
                        }, 5 * 1000);
                    }
                });

                $scope.$watch('ribbonService.licenses.hasDownloadLicence', function () {
                    if ($scope.ribbonService.isSftpAvailable && $scope.ribbonService.licenses.hasDownloadLicence && checkFS == null) {
                        checkFS = $interval(function () {
                            if ($routeParams.id) {
                                if ($scope.client == null)
                                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                                checkFilesystemReady();
                            }
                        }, 5 * 1000);
                    }
                });

                $scope.$watch('ribbonService.isSftpAvailable', function () {
                    if ($scope.ribbonService.isSftpAvailable && ($scope.ribbonService.licenses.hasUploadLicence || $scope.ribbonService.licenses.hasDownloadLicence) && checkFS == null) {
                        checkFS = $interval(function () {
                            if ($routeParams.id) {
                                if ($scope.client == null)
                                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                                checkFilesystemReady();
                            }
                        }, 5 * 1000);
                    }
                });

                /**
                 * Check if the filesystem is available and if it contains a mandatory directory (Desktop)
                 */
                function checkFilesystemReady() {
                    if ($scope.client != null) {

                        // Check if the filesystem is available
                        var fs = getFilesystem();
                        if (fs != null) {

                            // Check if the filesystem contains mandatory directory (Desktop)
                            $scope.ribbonService.fileExplorerReady = INITIAL_DIRECTORIES.some(dir => fs.root.files.hasOwnProperty(dir));
                            if ($scope.ribbonService.fileExplorerReady) {
                                console.log("checkFS normally cancelled.")
                                if (checkFS != null) $interval.cancel(checkFS);
                            }
                        }

                        // Increase the count
                        checkFSCount++;

                        // After checkFS() is called within the given count, if the filesystem isn't available or
                        // it doesn't a contain mandatory directory (Desktop), we have to cancel the interval handler
                        // and show the message to users.
                        if (($scope.ribbonService.licenses.hasUploadLicence || $scope.ribbonService.licenses.hasDownloadLicence) &&
                            !$scope.ribbonService.fileExplorerReady && checkFSCount > CHECK_FS_LIMIT_NUMBER) {

                            // Cancel the interval handler
                            console.warn("checkFS abnormally cancelled.");
                            if (checkFS != null) $interval.cancel(checkFS);

                            // Show error message
                            $translate("CLIENT.CHECK_FILESYSTEM_FAILED").then(function showMessage(msg) {
                                $scope.infoService.top = true;
                                $scope.infoService.infoText = msg;
                                $scope.infoService.infoDialogVisible = true;
                                $timeout(function () {
                                    $scope.infoService.infoDialogVisible = false;
                                }, $scope.ribbonService.thresholdInfo);
                            });

                            // Reset the count
                            checkFSCount = 0;
                        }
                    }
                }

                /**
                 * Returns first filesystem object that is not RDP file system. It is assumed this
                 * is SFTP file system object.
                 */
                function getFilesystem() {
                    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                    for (var fs in $scope.client.filesystems) {
                        // Assume that first filesystem that is not having default RDP_FS_NAME is SFTP filesystem
                        if ($scope.client.filesystems[fs].name !== RDP_FS_NAME) {
                            return new ManagedFilesystem($scope.client.filesystems[fs]);
                        }
                    }

                    return null;
                };

                /**
                 * Change the filesystem to the initial directory.
                 */
                function changeInitialDirectory(fs) {
                    const existingDir = INITIAL_DIRECTORIES.find(dir => fs.root.files.hasOwnProperty(dir));

                    if (existingDir) {
                        ManagedFilesystem.changeDirectory(fs, fs.root.files[existingDir]);
                    }
                    else {
                        console.log("Directory change failed, opening dialog in root directory.");
                    }
                    $scope.filesystem = fs;
                }

                /**
                 * Get filesystem when dialog is visible.
                 */
                $scope.$watch('ribbonService.fileExplorerVisible', function (newValue) {
                    if (newValue) {
                        var fs = getFilesystem();
                        if (fs != null) {
                            // Check if filesystem contains mandatory directory (Desktop)
                            if (INITIAL_DIRECTORIES.some(dir => fs.root.files.hasOwnProperty(dir))) {
                                changeInitialDirectory(fs);
                            }
                            else {
                                ManagedFilesystem.changeDirectory(fs, fs.root);

                                // Wait for root directory to be read
                                $timeout(function () {
                                    changeInitialDirectory(fs);
                                }, 3000);
                            }
                        }
                    }
                    else {
                        $scope.filesystem = null;
                    }
                });

                /**
                 * Close the dialog.
                 */
                $scope.closeDialog = function closeDialog() {
                    $scope.ribbonService.fileExplorerVisible = false;
                    $scope.ribbonService.fileExistsDialog = false;
                    document.getElementById("download_file_btn").classList.add("disable_download_btn");
                    fileNameListForUploadCompare = [];
                    matching = [];
                    if ($scope.ribbonService.fileUploadVisible || $scope.ribbonService.fileDownloadVisible || $scope.ribbonService.assignmentPublishVisible) {
                        $scope.ribbonService.fileUploadVisible = false;
                        $scope.ribbonService.fileDownloadVisible = false;
                        $scope.ribbonService.assignmentPublishVisible = false;
                        guacNotification.showStatus(false);
                    }
                };

                var resetForUpload = function () {
                    $scope.ribbonService.filesToUpload = [];
                }

                $scope.deleteFile = function deleteFile() {
                    // Directory listing element
                    var directoryList = angular.element(document.getElementById('directory-list'));
                    var listing = directoryList.children(0).children();

                    angular.forEach(listing, function (item) {
                        var jqItem = angular.element(item);
                        if (jqItem.hasClass('normal-file') && jqItem.hasClass('focused')) {
                            fileDownloadDisabled = !fileDownloadDisabled;

                            var fileName = jqItem.eq(0).eq(0).text().trim().split("\n")[0].trim();
                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            var datasource = encodeURIComponent(clientIdentifier.dataSource);

                            var httpParameters = {
                                token: encodeURIComponent(authenticationService.getCurrentToken()),
                                id: encodeURIComponent(clientIdentifier.id),
                                fileName: $scope.filesystem.currentDirectory.streamName + "/" + fileName,
                            };

                            var req = {
                                method: 'DELETE',
                                url: "api/session/ext/" + datasource + "/details",
                                params: httpParameters
                            };

                            $('html,body,button').css('cursor', 'wait');

                            document.getElementsByClassName("cancelBtnClass")[0].setAttribute("disabled", "disabled");
                            document.getElementsByClassName("deleteBtnClass")[0].setAttribute("disabled", "disabled");

                            $http(req).finally(function () {

                                $('html,body,button').css('cursor', 'default');
                                document.getElementsByClassName("cancelBtnClass")[0].removeAttribute("disabled");
                                document.getElementsByClassName("deleteBtnClass")[0].removeAttribute("disabled");

                                ManagedFilesystem.refresh($scope.filesystem, $scope.filesystem.currentDirectory);
                                guacNotification.showStatus(false);
                            });
                        }
                    });
                }

                $scope.cancelDelete = function cancelDelete() {
                    $scope.ribbonService.fileExistsDialog = false;
                    fileNameListForUploadCompare = [];
                    matching = [];

                    // remove dalog for file delete
                    guacNotification.showStatus(false);
                }

                $scope.$on('guacUploadComplete', function uploadComplete(event, filename) {
                    const uploadMessage = $rootScope.ribbonService.assignmentManagementDialogVisible ? "CLIENT.FILE_DOWNLOAD_SUCCESS" : "CLIENT.FILE_UPLOAD_SUCCESS"
                    $translate(uploadMessage).then(function showMessage(msg) {
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = msg;
                        $scope.infoService.infoDialogVisible = true;
                        $timeout(function () {
                            $scope.infoService.infoDialogVisible = false;
                        }, $scope.ribbonService.thresholdInfo);
                    });
                });

                /**
                 * Returns the full path to the given file as an ordered array of parent
                 * directories.
                 *
                 * @param {ManagedFilesystem.File} file
                 *     The file whose full path should be retrieved.
                 *
                 * @returns {ManagedFilesystem.File[]}
                 *     An array of directories which make up the hierarchy containing the
                 *     given file, in order of increasing depth.
                 */
                $scope.getPath = function getPath(file) {

                    var path = [];

                    // Add all files to path in ascending order of depth
                    while (file && file.parent) {
                        path.unshift(file);
                        file = file.parent;
                    }

                    return path;
                };

                /**
                 * Download file selected in a directory listing
                 */
                $scope.downloadFile = function downloadFile() {

                    // Directory listing element
                    var directoryList = angular.element(document.getElementById('directory-list'));
                    var listing = directoryList.children(0).children();

                    angular.forEach(listing, function (item) {
                        var jqItem = angular.element(item);
                        if (jqItem.hasClass('normal-file') && jqItem.hasClass('focused')) {
                            fileDownloadDisabled = false;
                            var fileName = jqItem.eq(0).eq(0).text().trim().split("\n")[0].trim();
                            ManagedFilesystem.downloadFile($scope.client, $scope.filesystem, $scope.filesystem.currentDirectory.streamName +
                                "/" + fileName);
                        }
                    })

                    if (fileDownloadDisabled) {
                        alert("𝗦𝗲𝗹𝗲𝗰𝘁 𝗮 𝗳𝗶𝗹𝗲 𝗳𝗼𝗿 𝗱𝗼𝘄𝗻𝗹𝗼𝗮𝗱!");
                    }

                    fileDownloadDisabled = true;
                }

                /**
                 * Begins a file upload through the attached Guacamole client for
                 * each file in the given FileList. Check for duplicated files.
                 *
                 * @param {FileList} files
                 *     The files to upload.
                 */
                $scope.uploadFiles = function uploadFiles(files) {

                    // Ignore file uploads if no attached client
                    if (!$scope.client)
                        return;

                    // Upload each file
                    for (var i = 0; i < files.length; i++) {

                        // if matching file isnt'finded yet check if current matches
                        if (fileNameListForUploadCompare !== undefined && fileNameListForUploadCompare.includes(files[i].name)) {
                            matching[matchingCount] = files[i];
                            matchingCount++;
                        }
                        // if the current file doesn't matching with some old just uploadFile
                        else {
                            ManagedClient.uploadFile($scope.client, files[i], $scope.filesystem);
                            fileNameListForUploadCompare.push(files[i].name);
                        }
                    }

                    // Check if the matching files exist
                    if (matchingCount > 0) {
                        $timeout(function () {
                            handleKeepOrReplace();
                        }, 3000);
                    }

                }

                /**
                 * Shows the dialog for keeping or replacing or cancelling
                 *
                 */
                 var handleKeepOrReplace = function handleKeepOrReplace() {

                    if (matchingCount > 0) {
                        guacNotification.showStatus({
                            text: {
                                key: 'DIALOGS.KEEP_OR_REPLACE',
                                variables: {
                                    FILE_NAME: replaceName()
                                }
                            },
                            actions: [KEEP_BOTH, REPLACE, CANCEL_FILE]
                        });
                    }
                    else {
                        guacNotification.showStatus(false);
                        matching = [];
                        matchingCount = 0;
                    }

                }

                /**
                 * File for upload already exists.
                 * Keeps both files, the new one with an added version in the name.
                 * */
                $scope.keepBoth = function keepBoth() {
                    var newName = createNewName();
                    if (newName !== undefined) {
                        Object.defineProperty(matching[0], 'name', {
                            writable: true,
                            value: newName
                        });
                        ManagedClient.uploadFile($scope.client, matching[0], $scope.filesystem);
                        fileNameListForUploadCompare.push(matching[0].name);
                        guacNotification.showStatus(false);

                        // delete the handled file object
                        matchingCount--;
                        matching.splice(0, 1);

                        // handle the remaining matched files
                        handleKeepOrReplace();
                    }
                }

                /**
                 * File for upload already exists.
                 * Replace the old file with the new one.
                 * */
                $scope.replace = function replace() {
                    ManagedClient.uploadFile($scope.client, matching[0], $scope.filesystem);
                    fileNameListForUploadCompare.push(matching[0].name);
                    guacNotification.showStatus(false);

                    // delete the handled file object
                    matchingCount--;
                    matching.splice(0, 1);

                    // handle the remaining matched files
                    handleKeepOrReplace();
                }

                /**
                 * File for upload already exists.
                 * Cancel the new file to upload when cancel button is clicked.
                 * */
                $scope.cancelFile = function cancelFile() {
                    // delete the handled file object
                    matchingCount--;
                    matching.splice(0, 1);

                    // handle the remaining matched files
                    guacNotification.showStatus(false);
                    handleKeepOrReplace();
                }

                var createNewName = function () {

                    if (matching == undefined || matching.length == 0)
                        return;

                    // parse to get the name
                    var wholeName = matching[0].name;
                    var nameParts = wholeName.split(".");
                    var nam = nameParts[0];

                    var fileVersions = [];
                    // check if there are more versions of the file to generate a number for the new file
                    if (fileNameListForUploadCompare !== undefined)
                        for (var j = 0; j < fileNameListForUploadCompare.length; j++) {
                            if (fileNameListForUploadCompare[j].startsWith(nam))
                                fileVersions.push(fileNameListForUploadCompare[j]);
                        }

                    // create version for the new file
                    var fileVersionNumber = "";
                    if (fileVersions !== undefined) {
                        for (var j = 0; j < fileVersions.length; j++) {
                            var fileName = fileVersions[j].split(".");

                            // if the name of the current file and the name of the new file are the same
                            // no need to check fileVersion, it's 1
                            if (!angular.equals(fileName[0], nam)) {
                                fileName[0] = fileName[0].replace(nam, "");
                                fileName[0] = fileName[0].replace("(", "");
                                fileName[0] = fileName[0].replace(")", "");
                                fileName[0] = fileName[0].trim();
                                if (isNaN(fileName[0])) {
                                    continue;
                                }

                                // if file version number should have more than one number
                                var i = fileName[0].length - 1;

                                // if it's number, for the cases where there is already brackets with number in the name of different file
                                if (fileName[0] !== "") {
                                    while (i >= 0 && !isNaN(fileName[0].charAt(i))) {
                                        i--;
                                    }

                                    if (fileVersionNumber == "") {
                                        if (i >= 0 && !isNaN(fileName[0].charAt(i))) {
                                            fileVersionNumber = "";
                                        }
                                        else {
                                            fileVersionNumber = parseInt(fileName[0]);
                                        }
                                    }
                                    else {
                                        if (i < 0) {
                                            fileName[0] = parseInt(fileName[0]);
                                            fileVersionNumber = parseInt(fileVersionNumber);
                                            if (fileName[0] > fileVersionNumber) {
                                                fileVersionNumber = fileName[0];
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    var i = nam.length - 1;
                    var newFileName = "";

                    newFileName = nam.substring(0, i + 1);
                    if (fileVersionNumber != undefined) {
                        fileVersionNumber++;
                        newFileName += "(" + fileVersionNumber + ")";
                    }
                    else {
                        newFileName += "(" + 1 + ")";
                    }

                    // at the end if extension of file were in file name, then add it in the new file name too
                    if (nameParts.length > 1 && nameParts[1] != "") {
                        newFileName += "." + nameParts[1];
                    }

                    return newFileName;
                }

                $scope.getAllFiles = function getAllFiles() {
                    var fileName = "";
                    fileNameListForUploadCompare = [];

                    // Directory listing element
                    var directoryList = angular.element(document.getElementById('directory-list'));
                    var listing = directoryList.children(0).children();

                    angular.forEach(listing, function (item) {
                        var jqItem = angular.element(item);
                        if (jqItem.hasClass('normal-file')) {
                            fileName = jqItem.eq(0).eq(0).text().trim().split("\n")[0].trim();
                            fileNameListForUploadCompare.push(fileName);
                        }
                    });

                    // Upload the selected files
                    $scope.uploadFiles($scope.ribbonService.filesToUpload);

                    // Reset the list of the selected files
                    resetForUpload();
                }

                $scope.$watch('ribbonService.fileUploadVisible', function (newValue) {
                    if (newValue) {
                        var fs = getFilesystem();
                        if (fs != null) {
                            ManagedFilesystem.changeDirectory(fs, fs.root);
                            // Wait for root directory to be read
                            $timeout(function () {
                                const existingDir = INITIAL_DIRECTORIES.find(dir => fs.root.files.hasOwnProperty(dir));
                                
                                if (existingDir) {
                                    ManagedFilesystem.changeDirectory(fs, fs.root.files[existingDir]);
                                }
                                else {
                                    console.log("Directory change failed, opening dialog in root directory.");
                                }

                                $scope.filesystem = fs;
                            }, 3000);
                        }
                    }
                    else {
                        $scope.filesystem = null;
                    }
                });

                $scope.$watch('ribbonService.fileListReady', function (newValue) {
                    if (newValue) {
                        if ($scope.filesystem) {
                            if (!$scope.circleLoaderService.circleLoaderVisible) {
                                $scope.getAllFiles();
                                $scope.ribbonService.fileListReady = false;
                            }
                        }
                        else {
                            $scope.ribbonService.fileListReady = false;
                            console.log("Can't access file system.");
                        }
                    }
                });

                var replaceName = function () {
                    if (matching !== undefined && matching.length != 0 && matching[0] != undefined) {
                        return matching[0].name;
                    }
                    else {
                        return "";
                    }
                }

                $scope.changeDirectory = function changeDirectory(file) {
                    if (!file) {
                        console.log("Can't change the directory.");
                        return;
                    }
                    document.getElementById('download_file_btn').classList.add('disable_download_btn');
                    file.files = {};
                    ManagedFilesystem.changeDirectory($scope.filesystem, file);
                }
            }
        ] // end file browser controller
    };
}]);
