/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays share dialog
 */
angular.module('ribbon', ['ngCookies']).directive('settingPopup', [function settingPopup() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/ribbon/templates/setting-popup.html',
        controller: ['$scope', '$routeParams', '$injector', '$rootScope', function settingPopupController($scope, $routeParams, $injector, $rootScope) {

            // Required services
            var guacNotification    = $injector.get('guacNotification');
            var $cookieStore        = $injector.get('$cookieStore');
            var $window             = $injector.get('$window');
            var ClientIdentifier    = $injector.get('ClientIdentifier');
            $scope.ribbonService    = $injector.get('ribbonService');

            $scope.isModeH264 = $scope.ribbonService.licenses.hasH264Licence;

            // Google Tag Manager
            var dataLayer = $window.dataLayer = $window.dataLayer || [];

            // Set the text indicating the constant string.
            var settings_text = 'browser settings';

            // Prepare to show the notification
            var OK_BTN = {
                name        : "DIALOGS.BUTTON_OK",
                callback    : function acknowledgeCallback() { // Handle action
                    guacNotification.showStatus(false);
                },
                className	: 'cancelBtnClass'
            };
            var actions = [ OK_BTN ]; // Build array of available actions

            $scope.showResolutionPopup = function showResolutionPopup() {
                $scope.streamingModeOptionsVisible = false;
                $scope.lockModeOptionsVisible = false;
                $scope.resolutionOptionsVisible = !$scope.resolutionOptionsVisible;
            }

            $scope.showStreamingModePopup = function showStreamingModePopup() {
                $scope.resolutionOptionsVisible = false;
                $scope.lockModeOptionsVisible = false;
                $scope.streamingModeOptionsVisible = !$scope.streamingModeOptionsVisible;
            }

            $scope.showLockModePopup = function showLockModePopup() {
                $scope.resolutionOptionsVisible = false;
                $scope.streamingModeOptionsVisible = false;
                $scope.lockModeOptionsVisible = !$scope.lockModeOptionsVisible;
            }

            // When clicking the clipboard icon only if the clipboard is disabled or asked in the browser,
            // enable clipboard or ask a user to check the browser settings.
            $scope.checkClipboardEnabled = function checkClipboardEnabled() {

                // Query the clipboard permission of browser
                navigator.permissions.query(
                    { name: 'clipboard-read' }
                ).then(function(permissionStatus){

                    // If the state of this permission is "denied", shows the relevant message.
                    if (permissionStatus.state == "denied") {

                        // Show the confirmation dialog
                        guacNotification.showStatus({
                            title   : "CLIENT.SETTINGS_CLIPBOARD_NOT_ENABLED_TITLE",
                            text    : {
                                key : "CLIENT.SETTINGS_CLIPBOARD_NOT_ENABLED_TEXT",
                                variables: {BROWSER_SETTINGS: settings_text}
                            },
                            actions : actions
                        });

                        return;
                    }

                    // If the state of this permission is "prompt", triggers the clipboard
                    // permission popup of browser.
                    if (permissionStatus.state == "prompt") {
                        if (navigator.clipboard && navigator.clipboard.write) {
                            navigator.clipboard.writeText("")
                            .then(function() {
                                // Change the status of clipboard.
                                $scope.ribbonService.clipboardEnabled = true;

                            }, function() {
                                // Show the confirmation dialog
                                guacNotification.showStatus({
                                    title   : "CLIENT.SETTINGS_CLIPBOARD_NOT_ENABLED_TITLE",
                                    text    : {
                                        key : "CLIENT.SETTINGS_CLIPBOARD_NOT_ENABLED_TEXT",
                                        variables: {BROWSER_SETTINGS: settings_text}
                                    },
                                    actions : actions
                                });
                            });
                        }

                        return;
                    }

                    if (permissionStatus.state == "granted") {
                        guacNotification.showStatus({
                            title   : "CLIENT.CLIPBOARD_ACCESS",
                            text    : {
                                key : "CLIENT.ALREADY_HAS_CLIPBOARD_ACCESS",
                            },
                            actions : actions
                        });
                        return;
                    }

                    // Hide the settings popup menu
                    $scope.ribbonService.settingPopupVisible = false;

                })

            }

            // Show/Hide the frame statistics
            $scope.toggleFrameStatistics = function toggleFrameStatistics() {
                $scope.ribbonService.frameStatistics = !$scope.ribbonService.frameStatistics
            }

            // Update the resolution to MatchLocalDisplay
            $scope.setMatchLocalDisplay = function setMatchLocalDisplay() {
                $scope.ribbonService.resolution = {
                    width: 0,
                    height: 0
                }

                $scope.ribbonService.scale = 0;

                $cookieStore.put("scale", $scope.ribbonService.scale);
                $scope.ribbonService.isAutoResize = true;
                $rootScope.$broadcast('Resolution.MatchLocalDisplay');
            }

            // Put the scale value to the cookie store
            $scope.setScale = function setScale(scale) {
                $scope.ribbonService.scale = scale;
                $cookieStore.put("scale", $scope.ribbonService.scale);
            }

            // Update the resolution to fixed one
            $scope.setFixedResolution = function setFixedResolution(width, height) {
                $scope.ribbonService.resolution = {
                    width: width,
                    height: height
                }

                $scope.ribbonService.isAutoResize = false;
            }

            // Get the current resolution mode
            $scope.getCurrentResolutionMode = function getCurrentResolutionMode(width, height) {
                return $scope.ribbonService.resolution.width === width && $scope.ribbonService.resolution.height === height ? "" : "place-holder-icon"
            }

            // Update the scale to fixed one
            $scope.setFixedScale = function setFixedScale(scale) {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Screen Scaling',
                    button_name: 'btn_ribbon_screen_scaling',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.ribbonService.scale = scale;

                $cookieStore.put("scale", $scope.ribbonService.scale);
                $scope.ribbonService.isAutoResize = true;
                $rootScope.$broadcast('Resolution.FixedScale');
            }

            // Update the scale to fixed one
            $scope.setStreamingMode = function setStreamingMode(mode) {

                // Google Tag Manager
                dataLayer.push({
                    event: 'Screen Resolution',
                    button_name: 'btn_ribbon_screen_resolution',
                    sub_domain: $scope.ribbonService.licenses.subdomain
                });

                $scope.ribbonService.streamingMode = mode;

                $rootScope.$broadcast('StreamingMode');
            }

            // Get the current scale mode
            $scope.getCurrentScaleMode = function getCurrentScaleMode(scale) {
                return $scope.ribbonService.scale === scale ? "" : "place-holder-icon"
            }

            $scope.isScaleChecked = function isScaleChecked(scale) {
                return $scope.ribbonService.scale === scale ? "true" : "false";
            }

            // Get the current streaming mode
            $scope.getCurrentStreamingMode = function getCurrentStreamingMode(mode) {
                return $scope.ribbonService.streamingMode === mode ? "" : "place-holder-icon"
            }

            // Hide the settings menu
            $scope.hideSettings = function hideSettings() {
                $scope.resolutionOptionsVisible = false;
                $scope.streamingModeOptionsVisible = false;
                $scope.ribbonService.settingPopupVisible = false;
            }

            // Prompt the microphone permission
            function promptEnableMic() {

                if (!navigator.mediaDevices.getUserMedia)
                    navigator.mediaDevices.getUserMedia = (navigator.getUserMedia
                            || navigator.webkitGetUserMedia
                            || navigator.mozGetUserMedia
                            || navigator.msGetUserMedia).bind(navigator);

                navigator.mediaDevices.getUserMedia({ audio: true })
                .then(function(stream) {
                    // Change the on/off status of microphone.
                    $scope.ribbonService.micEnabled = true;
                })
                .catch(function(err) {
                    // Change the on/off status of microphone.
                    $scope.ribbonService.micEnabled = false;

                    // Show the confirmation dialog
                    guacNotification.showStatus({
                        title   : "CLIENT.SETTINGS_MIC_NOT_ENABLED_TITLE",
                        text    : {
                            key : "CLIENT.SETTINGS_MIC_NOT_ENABLED_TEXT",
                            variables: {BROWSER_SETTINGS: settings_text}
                        },
                        actions : actions
                    });
                });

            }

            // Prompt the camera permission
            function promptEnableCamera() {

                if (!navigator.mediaDevices.getUserMedia)
                    navigator.mediaDevices.getUserMedia = (navigator.getUserMedia
                            || navigator.webkitGetUserMedia
                            || navigator.mozGetUserMedia
                            || navigator.msGetUserMedia).bind(navigator);

                navigator.mediaDevices.getUserMedia({ video: true })
                .then(function(stream) {
                    // Change the on/off status of camera.
                    $scope.ribbonService.cameraEnabled = true;
                })
                .catch(function(err) {
                    // Change the on/off status of camera.
                    $scope.ribbonService.cameraEnabled = false;

                    // Show the confirmation dialog
                    guacNotification.showStatus({
                        title   : "CLIENT.SETTINGS_CAMERA_NOT_ENABLED_TITLE",
                        text    : {
                            key : "CLIENT.SETTINGS_CAMERA_NOT_ENABLED_TEXT",
                            variables: {BROWSER_SETTINGS: settings_text}
                        },
                        actions : actions
                    });
                });

            }

            // Show the USB device list
            $scope.showUSBListDialog = function showUSBListDialog() {
                // Show the usb list dialog
                $scope.ribbonService.usbListDlgVisible = true;

                // Hide the settings popup menu
                $scope.ribbonService.settingPopupVisible = false;
            }

            // Switch between H264 and non H264
            $scope.setModeH264 = function setModeH264() {
                // "OK" and "Cancel" buttons for confirmation dialog
                var CANCEL_BTN = {
                    name        : "DIALOGS.BUTTON_CANCEL",
                    // Handle action
                    callback    : function acknowledgeCallback() {
                        guacNotification.showStatus(false);
                    },
                    className   : 'cancelBtnClass'
                };

                var OK_BTN = {
                    name      : "APP.ACTION_CONTINUE",
                    callback  : function cancelCallback() {
                        $rootScope.$broadcast('reconnect:enable-h264', !$scope.isModeH264);
                        guacNotification.showStatus(false);
                    },
                    className : 'cancelBtnClass'
                };

                // Build array of available actions
                var actions = [CANCEL_BTN, OK_BTN];

                // Show the confirmation dialog
                if ($scope.isModeH264) {
                    guacNotification.showStatus({
                        title   : "CLIENT.SETTINGS_DISABLE_H264_TITLE",
                        text    : {
                            key : "CLIENT.SETTINGS_DISABLE_H264_TEXT"
                        },
                        actions : actions
                    });
                }
                else {
                    guacNotification.showStatus({
                        title   : "CLIENT.SETTINGS_ENABLE_H264_TITLE",
                        text    : {
                            key : "CLIENT.SETTINGS_ENABLE_H264_TEXT"
                        },
                        actions : actions
                    });
                }
            }

            $scope.$watch('ribbonService.licenses.hasH264Licence', function() {
                $scope.isModeH264 = $scope.ribbonService.licenses.hasH264Licence;
            });

            // Check whether this browser is Firefox.
            if ($scope.ribbonService.browser.isFirefox || $scope.ribbonService.browser.isSafari) {

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                datasource = clientIdentifier.dataSource;
                
                // Check if Microphone is enabled in the browser
                if(datasource === 'encryptedurl-jdbc') {
                    promptEnableMic();
                }

                $scope.checkMicrophoneEnabled = function checkMicrophoneEnabled() {

                    // Prompt the microphone permission
                    promptEnableMic();

                    // Hide the settings popup menu
                    $scope.ribbonService.settingPopupVisible = false;

                }

                $scope.checkCameraEnabled = function checkCameraEnabled() {

                    // Prompt the camera permission
                    promptEnableCamera();

                    // Hide the settings popup menu
                    $scope.ribbonService.settingPopupVisible = false;

                }

                // Check if Clipboard is enabled in the browser
                if (navigator.clipboard && navigator.clipboard.write) {
                    $scope.ribbonService.clipboardEnabled = true;
                }
                else {
                    $scope.ribbonService.clipboardEnabled = false;
                }

                $scope.checkClipboardEnabled = function checkClipboardEnabled() {

                    // Hide the settings popup menu
                    $scope.ribbonService.settingPopupVisible = false;

                }

                return;
            }

            /**
             * Check if Microphone is enabled in the browser
             */
            // Get the current state of the microphone permission.
            navigator.permissions.query(
                { name: 'microphone' }
            ).then(function(permissionStatus) {

                // Check the current state.
                if (permissionStatus.state == "granted") {
                    $scope.ribbonService.micEnabled = true;
                }
                else {
                    $scope.ribbonService.micEnabled = false;
                }

                // Add the handler to detect the change of the state.
                permissionStatus.onchange = function(){
                    if (permissionStatus.state == "granted") {
                        $scope.ribbonService.micEnabled = true;
                    }
                    else {
                        $scope.ribbonService.micEnabled = false;
                    }
                }

            });

            /**
             * Check if Camera is enabled in the browser
             */
            // Get the current state of the Camera permission.
            navigator.permissions.query(
                { name: 'camera' }
            ).then(function(permissionStatus) {

                // Check the current state.
                if (permissionStatus.state == "granted") {
                    $scope.ribbonService.cameraEnabled = true;
                }
                else {
                    $scope.ribbonService.cameraEnabled = false;
                }

                // Add the handler to detect the change of the state.
                permissionStatus.onchange = function(){
                    if (permissionStatus.state == "granted") {
                        $scope.ribbonService.cameraEnabled = true;
                    }
                    else {
                        $scope.ribbonService.cameraEnabled = false;
                    }
                }

            });

            // When clicking the mic icon only if the mic is disabled or asked in the browser,
            // enable microphone or ask a user to check the browser settings.
            $scope.checkMicrophoneEnabled = function checkMicrophoneEnabled() {

                // Query the microphone permission of browser
                navigator.permissions.query(
                    { name: 'microphone' }
                ).then(function(permissionStatus){

                    // If the state of this permission is "denied", shows the relevant message.
                    if (permissionStatus.state == "denied") {

                        // Show the confirmation dialog
                        guacNotification.showStatus({
                            title   : "CLIENT.SETTINGS_MIC_NOT_ENABLED_TITLE",
                            text    : {
                                key : "CLIENT.SETTINGS_MIC_NOT_ENABLED_TEXT",
                                variables: {BROWSER_SETTINGS: settings_text}
                            },
                            actions : actions
                        });

                        return;
                    }

                    // If the state of this permission is "prompt", triggers the microphone
                    // permission popup of browser.
                    if (permissionStatus.state == "prompt") {
                        promptEnableMic(actions);
                        return;
                    }

                    // If the state of this permission is "granted", shows the relevant message.
                    if (permissionStatus.state == "granted") {
                        guacNotification.showStatus({
                            title   : "CLIENT.MICROPHONE_ACSESS",
                            text    : {
                                key : "CLIENT.ALREADY_HAS_MIC_ACCESS",
                            },
                            actions : actions
                        });
                        return;
                    }

                    // Hide the settings popup menu
                    $scope.ribbonService.settingPopupVisible = false;

                });

            }

            // When clicking the camera icon only if the camera is disabled or asked in the browser,
            // enable camera or ask a user to check the browser settings.
            $scope.checkCameraEnabled = function checkCameraEnabled() {
                // Query the camera permission of browser
                navigator.permissions.query(
                    { name: 'camera' }
                ).then(function(permissionStatus){

                    // If the state of this permission is "denied", shows the relevant message.
                    if (permissionStatus.state == "denied") {

                        // Show the confirmation dialog
                        guacNotification.showStatus({
                            title   : "CLIENT.SETTINGS_CAMERA_NOT_ENABLED_TITLE",
                            text    : {
                                key : "CLIENT.SETTINGS_CAMERA_NOT_ENABLED_TEXT",
                                variables: {BROWSER_SETTINGS: settings_text}
                            },
                            actions : actions
                        });

                        return;
                    }

                    // If the state of this permission is "prompt", triggers the camera
                    // permission popup of browser.
                    if (permissionStatus.state == "prompt") {
                        promptEnableCamera(actions);
                        return;
                    }

                    // If the state of this permission is "granted", shows the relevant message.
                    if (permissionStatus.state == "granted") {
                        guacNotification.showStatus({
                            title   : "CLIENT.CAMERA_ACSESS",
                            text    : {
                                key : "CLIENT.ALREADY_HAS_CAMERA_ACCESS",
                            },
                            actions : actions
                        });
                        return;
                    }

                    // Hide the settings popup menu
                    $scope.ribbonService.settingPopupVisible = false;

                });
            }

            /**
             * Check if Clipboard is enabled in the browser
             */
            // Get the current state of the Clipboard permission.
            navigator.permissions.query(
                { name: 'clipboard-read' }
            ).then(function(permissionStatus) {

                // Check the current state.
                if (permissionStatus.state == "granted") {
                    $scope.ribbonService.clipboardEnabled = true;
                }
                else {
                    $scope.ribbonService.clipboardEnabled = false;
                }

                // Add the handler to detect the change of the state.
                permissionStatus.onchange = function(){
                    // console.log("Clipboard Permission changed to " + this.state);

                    if (permissionStatus.state == "granted") {
                        $scope.ribbonService.clipboardEnabled = true;
                    }
                    else {
                        $scope.ribbonService.clipboardEnabled = false;
                    }
                }

            });

        }] // end setting dialog controller

    };
}]);
