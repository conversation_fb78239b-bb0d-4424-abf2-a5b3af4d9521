<guac-viewport>

  <!-- Client view -->
  <div class="client-view">
    <div class="client-view-content">

      <!-- Central portion of view -->
      <div class="client-body" guac-touch-drag="clientDrag" guac-touch-pinch="clientPinch">

        <!-- Client -->
        <guac-client client="client"></guac-client>

      </div>

      <!-- Bottom portion of view -->
      <div class="client-bottom">

        <!-- Text input -->
        <div class="text-input-container" ng-if="showTextInput">
          <guac-text-input needs-focus="showTextInput"></guac-text-input>
        </div>

        <!-- On-screen keyboard -->
        <div class="keyboard-container" ng-if="showOSK">
          <guac-osk layout="'CLIENT.URL_OSK_LAYOUT' | translate"></guac-osk>
        </div>

        <!-- Guacamole Client Statistics -->
        <guac-client-statistics client="client" ng-if="ribbonService.frameStatistics && ribbonService.licenses.hasStatisticsLicence"></guac-client-statistics>

      </div>

    </div>
  </div>

  <!-- File transfers -->
  <div id="file-transfer-dialog" ng-show="hasTransfers()">
    <file-transfer-manager client="client"></file-transfer-manager>
  </div>

  <!-- Connection stability warning -->
  <div id="connection-warning" ng-show="isConnectionUnstable()">
    {{'CLIENT.TEXT_CLIENT_STATUS_UNSTABLE' | translate}}
  </div>

  <!-- Menu -->
  <div class="menu" ng-class="{open: menu.shown}" id="guac-menu">
    <div class="menu-content" ng-if="menu.shown">

      <!-- Stationary header -->
      <div class="header">
        <h2>{{username}}</h2>
      </div>

      <!-- Scrollable body -->
      <div class="menu-body" guac-touch-drag="menuDrag" guac-scroll="menu.scrollState">

        <!-- Clipboard -->
        <div class="menu-section" id="clipboard-settings">
          <h3>{{'CLIENT.SECTION_HEADER_CLIPBOARD' | translate}}</h3>
          <div class="content">
            <p class="description">{{'CLIENT.HELP_CLIPBOARD' | translate}}</p>
            <guac-clipboard data="client.clipboardData"></guac-clipboard>
          </div>
        </div>

        <!-- Input method -->
        <div class="menu-section" id="keyboard-settings">
          <h3>{{'CLIENT.SECTION_HEADER_INPUT_METHOD' | translate}}</h3>
          <div class="content">
            <fieldset>
              <legend>Choose a {{'CLIENT.SECTION_HEADER_INPUT_METHOD' | translate}}:</legend>

              <!-- No IME -->
              <div class="choice">
                <label><input id="ime-none" name="input-method" ng-change="closeMenu()" ng-model="menu.inputMethod"
                    type="radio" value="none" /> {{'CLIENT.NAME_INPUT_METHOD_NONE' | translate}}</label>
                <p class="caption"><label for="ime-none">{{'CLIENT.HELP_INPUT_METHOD_NONE' | translate}}</label></p>
              </div>

              <!-- Text input -->
              <div class="choice">
                <div class="figure"><label for="ime-text"><img src="images/settings/tablet-keys.png" alt="" /></label>
                </div>
                <label>
                  <input id="ime-text" name="input-method" ng-change="closeMenu()" ng-model="menu.inputMethod"
                    type="radio" value="text" /> {{'CLIENT.NAME_INPUT_METHOD_TEXT' | translate}}
                </label>
                <p class="caption"><label for="ime-text">{{'CLIENT.HELP_INPUT_METHOD_TEXT' | translate}} </label></p>
              </div>

              <!-- Guac OSK -->
              <div class="choice">
                <label><input id="ime-osk" name="input-method" ng-change="closeMenu()" ng-model="menu.inputMethod"
                    type="radio" value="osk" /> {{'CLIENT.NAME_INPUT_METHOD_OSK' | translate}}</label>
                <p class="caption"><label for="ime-osk">{{'CLIENT.HELP_INPUT_METHOD_OSK' | translate}}</label></p>
              </div>

            </fieldset>
          </div>
        </div>

        <!-- Mouse mode -->
        <div class="menu-section" id="mouse-settings" ng-hide="client.multiTouchSupport">
          <h3>{{'CLIENT.SECTION_HEADER_MOUSE_MODE' | translate}}</h3>
          <div class="content">
            <p class="description">{{'CLIENT.HELP_MOUSE_MODE' | translate}}</p>

            <fieldset>
              <legend>Choose a {{'CLIENT.SECTION_HEADER_MOUSE_MODE' | translate}}:</legend>
              <!-- Touchscreen -->
              <div class="choice">
                <input name="mouse-mode" ng-change="closeMenu()" ng-model="client.clientProperties.emulateAbsoluteMouse"
                  type="radio" ng-value="true" checked="checked" id="absolute" />
                <div class="figure">
                  <label for="absolute"><img src="images/settings/touchscreen.png"
                      alt="{{'CLIENT.NAME_MOUSE_MODE_ABSOLUTE' | translate}}" /></label>
                  <p class="caption"><label for="absolute">{{'CLIENT.HELP_MOUSE_MODE_ABSOLUTE' | translate}}</label></p>
                </div>
              </div>

              <!-- Touchpad -->
              <div class="choice">
                <input name="mouse-mode" ng-change="closeMenu()" ng-model="client.clientProperties.emulateAbsoluteMouse"
                  type="radio" ng-value="false" id="relative" />
                <div class="figure">
                  <label for="relative"><img src="images/settings/touchpad.png"
                      alt="{{'CLIENT.NAME_MOUSE_MODE_RELATIVE' | translate}}" /></label>
                  <p class="caption"><label for="relative">{{'CLIENT.HELP_MOUSE_MODE_RELATIVE' | translate}}</label></p>
                </div>
              </div>
            </fieldset>

          </div>
        </div>

      </div>

    </div>
  </div>

  <!-- Filesystem menu -->
  <div id="filesystem-menu" class="menu" ng-class="{open: isFilesystemMenuShown()}">
    <div class="menu-content">

      <!-- Stationary header -->
      <div class="header" aria-hidden="true">
        <h2>{{filesystemMenuContents.name}}</h2>
        <button class="upload button" guac-upload="uploadFiles">{{'CLIENT.ACTION_UPLOAD_FILES' | translate}}</button>
        <button class="back" ng-click="hideFilesystemMenu()" tabindex="-1">{{'CLIENT.ACTION_NAVIGATE_BACK' |
          translate}}</button>
      </div>

      <!-- Breadcrumbs -->
      <div class="header breadcrumbs" aria-hidden="true">
        <div class="breadcrumb root" ng-click="changeDirectory(filesystemMenuContents, filesystemMenuContents.root)">
        </div>
        <div class="breadcrumb" ng-repeat="file in getPath(filesystemMenuContents.currentDirectory)"
          ng-click="changeDirectory(filesystemMenuContents, file)">{{file.name}}</div>
      </div>

      <!-- Scrollable body -->
      <div class="menu-body" aria-hidden="true">
        <file-browser client="client" filesystem="filesystemMenuContents"></file-browser>
      </div>

    </div>
  </div>
  <div ng-if="ribbonService.isSharingScreenWith" id="sharing_screen_text">You are sharing your screen with {{ribbonService.isSharingScreenWith}}</div>
</guac-viewport>