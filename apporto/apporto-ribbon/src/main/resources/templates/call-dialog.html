<div ng-if="ServiceCall.callConfirmDialogVisible" id="call-confirm-dialog">
  <div ng-if="ServiceCall.callState == 1 || ServiceCall.callState == 5">
    <span class="username">Speaking with {{ServiceCall.getFrom()}}</span>
  </div>
  <div ng-if="ServiceCall.callState == 2">
    <span class="username">{{ServiceCall.getFrom()}}</span>
    <span class="info">is calling you</span>
  </div>
  <div ng-if="ServiceCall.callState == 3">
    <span class="username">Calling {{ServiceCall.getUsername()}}</span>
  </div>
  <iframe ng-src="https://olafwempe.com/mp3/silence/silence.mp3" type="audio/mp3" allow="autoplay" id="audio"
    style="display:none"></iframe>
  <audio ng-src="{{ServiceCall.ring}}" autoplay loop
    ng-if="ServiceCall.callState == 2 || ServiceCall.callState == 3"></audio>
  <div class="call-btn-group" ng-if="ServiceCall.callState == 1 || ServiceCall.callState == 5">
    <button class="call" ng-click="terminate()"
      ng-style="ServiceCall.callState == 5 && {'pointer-events': 'none', 'background': '#C0C0C0'}" tabindex="0">
      Hang up
    </button>
  </div>
  <div class="call-btn-group" ng-if="ServiceCall.callState == 2">
    <button class="terminate" ng-click="decline()" tabindex="0">
      Decline
    </button>
    <button class="call" ng-click="accept()" tabindex="0">
      Accept
    </button>
  </div>
  <div class="call-btn-group" ng-if="ServiceCall.callState == 3">
    <button class="terminate" ng-click="reject()" style="background:#C0C0C0;" tabindex="0">
      Cancel
    </button>
  </div>
</div>