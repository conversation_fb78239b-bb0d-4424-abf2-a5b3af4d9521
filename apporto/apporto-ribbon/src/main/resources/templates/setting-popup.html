<div id="ribbonSettingsPopup" class="dialog-outer shown setting-popup">
    <div class="setting-popup-content shown">
        <!-- Content -->
        <div>
            <ul>
                <li class="setting-menu"
                    ng-show="!ribbonService.licenses.hasMacOSLicence && !ribbonService.licenses.hasLinuxLicence && ribbonService.resizeMethod === ribbonService.RESIZE_METHOD.DISPLAY_UPDATE">
                    <span class="with-submenu hoverable resolutionOptions" ng-class="{'on': resolutionOptionsVisible}" ng-mouseenter="showResolutionPopup()"
                        ng-mouseleave="resolutionOptionsVisible=false" ng-click="showResolutionPopup()" tabindex="0">
                        <a class="button-settings-resolution btn-with-label no-click" title="{{'DIALOGS.SETTINGS_CHANGE_SCREEN_SCALING' | translate}}" role="button"
                            ng-keypress="mouseoverOnEnter(event, '.button-settings-resolution')">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"  style="margin-right:4px;">
                                <g id="screenshot_monitor">
                                <mask id="mask0_2_753" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                <rect id="Bounding box" width="24" height="24" fill="#D9D9D9"/>
                                </mask>
                                <g mask="url(#mask0_2_753)">
                                <path id="screenshot_monitor_2" d="M15.25 15.8461H18.8461V12.25H17.6538V14.6538H15.25V15.8461ZM5.15383 9.74995H6.34613V7.34613H8.74995V6.15383H5.15383V9.74995ZM8.5 20.5V18.5H4.3077C3.80257 18.5 3.375 18.325 3.025 17.975C2.675 17.625 2.5 17.1974 2.5 16.6923V5.3077C2.5 4.80257 2.675 4.375 3.025 4.025C3.375 3.675 3.80257 3.5 4.3077 3.5H19.6923C20.1974 3.5 20.625 3.675 20.975 4.025C21.325 4.375 21.5 4.80257 21.5 5.3077V16.6923C21.5 17.1974 21.325 17.625 20.975 17.975C20.625 18.325 20.1974 18.5 19.6923 18.5H15.5V20.5H8.5ZM4.3077 17H19.6923C19.7692 17 19.8397 16.9679 19.9038 16.9038C19.9679 16.8397 20 16.7692 20 16.6923V5.3077C20 5.23077 19.9679 5.16024 19.9038 5.09613C19.8397 5.03203 19.7692 4.99998 19.6923 4.99998H4.3077C4.23077 4.99998 4.16024 5.03203 4.09613 5.09613C4.03202 5.16024 3.99998 5.23077 3.99998 5.3077V16.6923C3.99998 16.7692 4.03202 16.8397 4.09613 16.9038C4.16024 16.9679 4.23077 17 4.3077 17Z" fill="#1A1A1A"/>
                                </g>
                                </g>
                                </svg>
                                
                            <p>{{'DIALOGS.SETTINGS_SCREEN_SCALING' | translate}}</p>
                        </a>
                        <ul class="submenu" aria-label="Screen Scaling" aria-live="assertive">
                            <div class="gap_line"></div>
                            <li role="checkbox" ng-attr-aria-checked="{{isScaleChecked(0)}}">
                                <a ng-click="setMatchLocalDisplay(); hideSettings();"
                                    class="button-settings-resolution-auto btn-with-label" tabindex="0" role="button"
                                    ng-keypress="clickOnEnter($event, '.button-settings-resolution-auto')">
                                    <svg
                                        ng-class="getCurrentScaleMode(0)"
                                        class="photon-icon photon-icon-checked" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 16 16">
                                        <polygon fill-rule="evenodd"
                                            points="3.309 8.168 4.016 7.461 6.137 9.582 11.087 4.632 11.794 5.34 6.137 10.996"></polygon>
                                    </svg> {{'DIALOGS.SETTINGS_MATCH_LOCAL_DISPLAY' | translate}}
                                </a>
                            </li>
                            <li ng-repeat="scale in ribbonService.scaleOptions" role="checkbox" ng-attr-aria-checked="{{isScaleChecked(scale)}}">
                                <a ng-click="setFixedScale(scale); hideSettings();"
                                    class="button-settings-resolution-fixed-2560 button-settings-resolution-fixed btn-with-label"
                                    tabindex="0" role="button" ng-keypress="clickOnEnter($event, '.button-settings-resolution-fixed')">
                                    <svg
                                        ng-class="getCurrentScaleMode(scale)"
                                        class="photon-icon photon-icon-checked" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 16 16">
                                        <polygon fill-rule="evenodd"
                                            points="3.309 8.168 4.016 7.461 6.137 9.582 11.087 4.632 11.794 5.34 6.137 10.996"></polygon>
                                    </svg> 
                                    {{scale}}%
                                </a>
                            </li>
                        </ul>
                    </span>
                </li>
                <li class="setting-menu" ng-show="ribbonService.isPrimaryScreen">
                    <span class="with-submenu hoverable streamingModeOptions" ng-class="{'on': streamingModeOptionsVisible}" ng-click="showStreamingModePopup()"
                        ng-mouseenter="showStreamingModePopup()" ng-mouseleave="streamingModeOptionsVisible=false" tabindex="0">
                        <a class="button-settings-resolution btn-with-label no-click"
                            title="{{'DIALOGS.SETTINGS_CHANGE_SCREEN_RESOLUTION' | translate}}" tabindex="0"
                            role="button" ng-keypress="mouseoverOnEnter(event, '.button-settings-resolution')">
                            <div class="streaming-mode-icon"></div>
                            <p>{{'DIALOGS.SETTINGS_SCREEN_RESOLUTION' | translate}}</p>
                        </a>
                        <ul class="submenu submenu2">
                            <div class="gap_line"></div>
                            <li ng-repeat="mode in ribbonService.streamingModeOptions">
                                <a ng-click="setStreamingMode(mode); hideSettings();"
                                    class="button-settings-resolution-fixed-2560 button-settings-resolution-fixed btn-with-label"
                                    tabindex="0" role="button" ng-keypress="clickOnEnter($event, '.button-settings-resolution-fixed')">
                                    <svg
                                        ng-class="getCurrentStreamingMode(mode)"
                                        class="photon-icon photon-icon-checked" xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 16 16">
                                        <polygon fill-rule="evenodd"
                                            points="3.309 8.168 4.016 7.461 6.137 9.582 11.087 4.632 11.794 5.34 6.137 10.996"></polygon>
                                    </svg>{{ 'DIALOGS.SETTINGS_STREAMING_' + mode.split(' ')[0].toUpperCase() | translate}}
                                </a>
                            </li>
                        </ul>
                    </span>
                </li>
                <li class="setting-menu" ng-show="ribbonService.isPrimaryScreen">
                    <span class="with-submenu hoverable lockModeOptions" ng-class="{'on': lockModeOptionsVisible}" ng-click="showLockModePopup()"
                        ng-mouseenter="showLockModePopup()" ng-mouseleave="lockModeOptionsVisible=false" tabindex="0">
                        <a class="button-settings-resolution btn-with-label no-click"
                            title="{{'DIALOGS.SETTINGS_POINTER_LOCK_KEYBOARD' | translate}}" tabindex="0"
                            role="button" ng-keypress="mouseoverOnEnter(event, '.button-settings-resolution')">
                            <div class="lock-mode-icon"></div>
                            <p>{{'DIALOGS.SETTINGS_POINTER_LOCK' | translate}}</p>
                        </a>
                        <ul class="submenu submenu3">
                            <p>{{'DIALOGS.SETTINGS_POINTER_LOCK_DESCRIPTION' | translate}}</p>
                        </ul>
                    </span>
                </li>
                <div class="setting-seperator" ng-if="ribbonService.isPrimaryScreen"></div>
                <li class="setting-menu" ng-if="ribbonService.isPrimaryScreen && 
                                                !ribbonService.licenses.hasMacOSLicence && 
                                                !ribbonService.licenses.hasLinuxLicence">
                    <a ng-click="setModeH264()" tabindex="0" role="button">
                        <div class="btn1-container">
                            <div class="btn-switch" ng-class="{'btn-switch--on': isModeH264 }">
                                <div class="btn-switch-circle" ng-class="{'btn-switch-circle--on': isModeH264 }"></div>
                            </div>
                        </div>
                        <div>
                            <!-- Video Compression -->
                            {{'DIALOGS.SETTINGS_COMPRESSION' | translate}}
                        </div>
                    </a>
                </li>
                <li class="setting-menu" ng-if="ribbonService.isPrimaryScreen">
                    <a ng-click="checkMicrophoneEnabled()" tabindex="0" role="button">
                        <div class="btn1-container">
                            <div class="btn-switch" ng-class="{'btn-switch--on': ribbonService.micEnabled}">
                                <div class="btn-switch-circle" ng-class="{'btn-switch-circle--on': ribbonService.micEnabled}"></div>
                            </div>
                        </div>
                        <div>
                            {{'PRESENTER.GROUPCALL_TITLE' | translate}}
                        </div>
                    </a>
                </li>
                <li class="setting-menu" ng-if="ribbonService.isPrimaryScreen && ribbonService.licenses.hasCameraLicence">
                    <a ng-click="checkCameraEnabled()" tabindex="0" role="button">
                        <div class="btn1-container">
                            <div class="btn-switch" ng-class="{'btn-switch--on': ribbonService.cameraEnabled }">
                                <div class="btn-switch-circle" ng-class="{'btn-switch-circle--on': ribbonService.cameraEnabled }"></div>
                            </div>
                        </div>
                        <div>
                            {{'DIALOGS.SETTINGS_POPUP_ENABLE_CAMERA' | translate}}
                        </div>
                    </a>
                </li>
                <li class="setting-menu" ng-if="ribbonService.isPrimaryScreen && ribbonService.licenses.hasClipboard">
                    <a ng-click="checkClipboardEnabled()" tabindex="0" role="button">
                        <div class="btn1-container">
                            <div class="btn-switch" ng-class="{'btn-switch--on': ribbonService.clipboardEnabled }">
                                <div class="btn-switch-circle" ng-class="{'btn-switch-circle--on': ribbonService.clipboardEnabled }"></div>
                            </div>
                        </div>
                        <div>
                            {{'DIALOGS.SETTINGS_CLIPBOARD' | translate}}
                        </div>
                    </a>
                </li>
                <li class="setting-menu" ng-if="ribbonService.isPrimaryScreen && ribbonService.licenses.hasStatisticsLicence" tabindex="0">
                    <a ng-click="toggleFrameStatistics()" tabindex="0" role="button">
                        <!-- <div class="statistics-icon"></div> -->
                        <div class="btn1-container">
                            <div class="btn-switch" ng-class="{'btn-switch--on': ribbonService.frameStatistics }">
                                <div class="btn-switch-circle" ng-class="{'btn-switch-circle--on': ribbonService.frameStatistics }"></div>
                            </div>
                        </div>
                        <div>
                            {{'DIALOGS.SETTINGS_FRAME_STATISTICS' | translate}}
                        </div>
                    </a>
                </li>
                <div class="setting-seperator" ng-if="(ribbonService.isPrimaryScreen && ribbonService.licenses.hasUSBLicence) || ribbonService.licenses.hasRebootLicence || $root.isKioskMode"></div>
                <li id="menu-usb" class="setting-menu" ng-class="{'disabled': !$root.hasServerName}" ng-if="ribbonService.isPrimaryScreen && ribbonService.licenses.hasUSBLicence">
                    <a class="button-settings-usb" ng-click="showUSBListDialog()" href="#" role="button">
                        <div class="usb-icon"></div>
                        <p>{{'DIALOGS.SETTINGS_POPUP_USB_DEVICES' | translate}}</p>
                    </a>
                </li>
                <li class="more-li"  ng-if="ribbonService.licenses.hasRebootLicence" ng-click="ribbonService.queryRebootVMInSetting(true)">
                    <button class="btn-vm ribbon-button morePopupBtn rebootVm"
                        title="{{ribbonService.licenses.hasRebootLicence ? 'RIBBON.REBOOT' : 'RIBBON.REBOOT_NOT_LICENSED' | translate}}"
                        tabindex="0" >{{'RIBBON.REBOOT' | translate}}</button>
                </li>
                <li class="more-li" ng-if="$root.isKioskMode" ng-click="$root.onCloseWindow()">
                    <button class="btn-exit ribbon-button morePopupBtn signout"
                        title="{{'RIBBON.SIGN_OUT' | translate}}"
                        tabindex="0" >{{'RIBBON.SIGN_OUT' | translate}}</button>
                </li>
            </ul>
        </div>
    </div>
</div>