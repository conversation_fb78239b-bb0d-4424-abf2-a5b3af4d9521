<div class="dialog-middle">
  <div class="vm-manager dialog">
    <div>
      <button class="close" ng-click="closeDialog()" tabindex="0" aria-label="Close">&times;</button>
      <div class="menu-content">
        <!-- Stationary header -->
        <div class="vm-header">
          <h3 class="vm-manager title">{{'DIALOGS.VM_MANAGER_TITLE' | translate}}</h3>
          <h4 class="vm-manager subtitle">{{'DIALOGS.VM_MANAGER_SUBTITLE' | translate}}</h4>
        </div>

        <!-- Scrollable body -->
        <div class="menu-body">

          <!-- Backup list -->
          <div class="list-item" ng-if="backups.length != 0">
          <!-- Backup name -->
            <label class="caption" tabindex="0">
              <input type="radio" class="radio" name="backup" value="{{backups}}">
              <span>{{backups}}</span>
            </label>
          </div>

          <!-- No backups available message -->
          <div class="vm-message-container" ng-if="backups.length == 0 && !loading">
              <p class="vm-message-text">{{message}}</p>
          </div>

          <!-- Loading VMs message -->
          <div class="vm-message-container">
            <p class="vm-message-text">{{'DIALOGS.VM_MSG' | translate}}</p>
          </div>
        </div>

        <div class="footer">
          <button class="restore button" ng-click="restoreVm()" ng-disabled="backups.length == 0 && !loading"
            tabindex="0">{{'DIALOGS.BUTTON_VM_RESTORE' | translate}}</button>
          <button class="backup button" ng-click="backupVm()" tabindex="0">{{'DIALOGS.BUTTON_VM_BACKUP' |
            translate}}</button>
        </div>
      </div>
      <mmonitor ng-show="circleLoaderService.circleLoaderVisible" class="colorLoader"></mmonitor>
    </div>
  </div>
</div>