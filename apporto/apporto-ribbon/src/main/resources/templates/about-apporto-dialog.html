<div class="dialog-middle">
    <div class="about-apporto-dialog dialog" id="dlg-about-apporto">
        <button class="enable-close" tabindex="0" aria-labelledby="closeTooltip" style="position: absolute;"
            ng-click="closeAboutApportoDialog()">
                <div id="closeTooltip" class="dialog-tooltip close-tooltip" role="tooltip">Close</div>                
            </button>
        <div style="height: 100%; padding: 32px;">
            <h1 class="title-about-apporto">{{'ABOUT_APPORTO.TITLE_APPORTO' | translate}}</h1>
            <div class="block-info">
                <h2 class="block-title">{{'ABOUT_APPORTO.TITLE_CLIENT' | translate}}</h2>
                <div class="item-info">
                    <span tabindex="0">{{'ABOUT_APPORTO.GIT_HASH' | translate}}</span>
                    <span>{{guacClientGitHash}}</span>
                </div>
                <div class="item-info">
                    <span tabindex="0">{{'ABOUT_APPORTO.VERSION' | translate}}</span>
                    <span>{{guacClientVersion}}</span>
                </div>
                <div class="item-info" ng-if="guacClientBuildNumber ? true : false">
                    <span>{{'ABOUT_APPORTO.BUILD_NUMBER' | translate}}</span>
                    <span>{{guacClientBuildNumber}}</span>
                </div>
            </div>
            <div class="block-info">
                <h2 class="block-title">{{'ABOUT_APPORTO.TITLE_SERVER' | translate}}</h2>
                <div class="item-info">
                    <span tabindex="0">{{'ABOUT_APPORTO.GIT_HASH' | translate}}</span>
                    <span>{{guacServerGitHash}}</span>
                </div>
                <div class="item-info">
                    <span tabindex="0">{{'ABOUT_APPORTO.VERSION' | translate}}</span>
                    <span>{{guacServerVersion}}</span>
                </div>
                <div class="item-info" ng-if="guacServerBuildNumber ? true : false">
                    <span>{{'ABOUT_APPORTO.BUILD_NUMBER' | translate}}</span>
                    <span>{{guacServerBuildNumber}}</span>
                </div>
            </div>
        </div>
    </div>
</div>