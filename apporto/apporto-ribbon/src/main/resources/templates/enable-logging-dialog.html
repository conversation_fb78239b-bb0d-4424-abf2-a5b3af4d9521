<div class="dialog-middle">
	<div class="logging-dialog dialog">
		<!-- Dialog title -->
		<h2 class="title-logging">{{'ENABLE_LOGGING.TITLE' | translate}}</h2>

    	<div class="body">
      		<!-- Dialog text -->
			<div class="text">
				<p>{{'ENABLE_LOGGING.INFO_TEXT' | translate}} 
                    <span ng-if="!ribbonService.licenses.supportEmail">
                        {{'LINKS.HELP_CENTER_SUPPORT_EMAIL' | translate}}
                    </span>
                    <span ng-if="!!ribbonService.licenses.supportEmail">
                        {{ribbonService.licenses.supportEmail}}
                    </span>
                </p>
			</div>
		</div>

    	<!-- Buttons -->
		<div class="footer">
			<button ng-click="closeDialog()"
				class="tClose">{{'DIALOGS.BUTTON_CANCEL' | translate}}</button>
			<button ng-click="startLogging()"
				class="tClose" ng-disabled="ribbonService.loggingEnabled">{{'ENABLE_LOGGING.BUTTON_ENABLE_LOGGING' | translate}}</button>
		</div>
  	</div>
</div>
