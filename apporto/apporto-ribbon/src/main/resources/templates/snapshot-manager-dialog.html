<div class="dialog-middle">
  <div class="snapshot-manager dialog" id="snapshot-manager-dialog" style="padding: 24px; position: relative;" aria-modal="true" role="dialog">
    <div>
      <button class="close" style="position: absolute; top: 16px; right: 16px;" ng-click="closeDialog()" tabindex="0" aria-label="Close">&times;</button>
      <div class="menu-content">
        <!-- Stationary header -->
        <div class="snapshot-header">
          <h1 class="snapshot-manager snapshot-title">
            {{ (snapshotBtnState === 'continue') ? 'DIALOGS.SNAPSHOT_MAIN_TITLE' : (snapshotBtnState === 'save') ? 'CLIENT.ACTION_SAVE_SNAPSHOT' : 'CLIENT.ACTION_RESTORE_SNAPSHOT' | translate }}
        </h1>
        
        </div>

      <div id="select_snapshpshot_state"  ng-show="snapshotBtnState === 'continue' " >
        <div id="snapshot_options">
          <div ng-click="snapshotState='save'" class="snapshot_option" ng-class="{'selected_share_url': (snapshotState === 'save')}" tabindex="0">
            <div id="save_snapshot_bg"></div>
            <p>{{'DIALOGS.SNAPSHOT_TEXT' | translate}}</p>
          </div>
          <div ng-click="snapshots.length>0 ?(snapshotState='restore') : angular.noop();" class="snapshot_option" ng-class="{'selected_share_url': (snapshotState == 'restore'),'disable_snappshot_option': (snapshots.length === 0)}" tabindex="0">
            <div id="restore_snapshot_bg"></div>
            <p>{{'DIALOGS.RESTORE_TEXT' | translate}}</p>
          </div>
        </div>
        <div id="snapshot_info">{{'DIALOGS.SNAPSHOT_SAVE_INFO' | translate}}</div>
        <button class="snapshot_buttons" ng-click="snapshotState==='save' ? (snapshotBtnState='save') : (snapshotBtnState='restore');" >Continue</button>
      </div>
        <!-- Scrollable body -->
        <div  ng-if="snapshotBtnState==='restore' " class="menu-body">
          <div class="txt_alg_lt" ng-if="snapshots.length>0">{{'DIALOGS.RESTORE_INFO' | translate}}</div>
          <!-- Snapshots list -->
          <div style="display: flex; flex-direction: column; gap: 8px;">
            <div style="height: max-content;" class="list-item" ng-repeat="snapshot in snapshots">
              <!-- Snapshot name -->
  
              <label class="caption res_snap" tabindex="0" ng-class="{'selected_share_url': (restoreDate === snapshot)}">
                <input style="display: none;" type="radio" class="radio" name="snapshot" value="{{snapshot}}"
                  ng-click="selectSnapshot($event, $index)">
                  <span style="width: 50%;">{{parsedSnapShotsArray[$index][0]}}</span> <!-- Date part -->
                  <span style="width: 50%; text-align: right;">{{parsedSnapShotsArray[$index][1]}}</span> <!-- Time part -->
              </label>
  
            </div>
          </div>
          <div class="txt_alg_lt">{{'DIALOGS.SNAPSHOT_INFO_TEXT' | translate}}</div>
          <!-- No snapshots available message -->
          <div class="snapshots-message-container" ng-if="snapshots.length==0 && !loading">
            <p class="snapshots-message-text">{{'DIALOGS.SNAPSHOTS_NOT_AVAILABLE' | translate}}</p>
          </div>
          <!-- Loading snapshots message -->
          <div class="snapshots-message-container" ng-if="loading">
            <p class="snapshots-message-text">{{'DIALOGS.SNAPSHOTS_LOADING' | translate}}</p>
          </div>

          <button class="restore button snapshot_buttons" style="margin: 4pxs;" ng-click="restoreSnapshots()"
          ng-disabled="selected < 0" tabindex="0">
          <span id="restore_snap_button"></span>
          <span>{{'DIALOGS.BUTTON_SNAPSHOT_RESTORE' |
            translate}} & {{'DIALOGS.SNAPSHOT_BUTTON_RESTART' |
            translate}} </span>
          </button>
        </div>

        <div id="save_snapshot_cont" ng-if="snapshotBtnState==='save' ">
          <div class="txt_alg_lt">{{'DIALOGS.SNAPSHOT_MAX_SAVE_INFO' | translate}}</div>
          <div id="current_time_block">
            <!-- Display current date -->
            <span>{{getCurrentDateTimeArray()[0]}}</span>
            <!-- Display current time -->
            <span style="color: #22538F;">{{getCurrentDateTimeArray()[1]}}</span>
        </div>
        <div class="txt_alg_lt">{{'DIALOGS.SNAPSHOT_INFO_TEXT' | translate}}</div>
          <button class="save button snapshot_buttons" ng-click="saveSnapshots()" tabindex="0">
            <span id="save_snap_button"></span>
            <span>{{'DIALOGS.BUTTON_SNAPSHOT_SAVE'
              | translate}} & {{'DIALOGS.SNAPSHOT_BUTTON_RESTART'
              | translate}} </span>
            </button>
        </div>

      </div>
      <mmonitor ng-show="circleLoaderService.circleLoaderVisible" class="colorLoader" aria-live="polite"></mmonitor>
    </div>
  </div>
</div>