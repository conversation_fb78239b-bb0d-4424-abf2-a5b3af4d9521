<div
  id="compressed_ribbon_bar_block"
  tabindex="0"
  ng-class="{compressed_ribbon_bar : (hideRibbonBar && isSmallRibbonBarVisible)}"
  ng-mouseout="hideSmallRibbonBar()"
  ng-mouseenter="ribbonService.ribbonActive=true"
  ng-mouseleave="ribbonService.ribbonActive=false"
  ng-keydown="[13, 32].includes($event.keyCode) && showRibbonBar($event)"
  ng-click="showRibbonBar($event)"
  aria-labelledby="showRibbonTooltip"
  role="button">
  <div id="showRibbonTooltip" class="ribbon-tooltip" role="tooltip">
    {{'RIBBON.HIDE_SMALL_RIBBON_TEXT' | translate}}
  </div>
</div>
<div style="transition: all 0.5s;" class="ribbon-bar {{ribbonService.browser.isSafari ? ' ribbon-bar-safari' : ''}}" ng-mouseenter="ribbonService.ribbonActive=true;" ng-mouseleave="ribbonService.ribbonActive=false"
    ng-init="ribbonService.ribbonActive=false" ng-class="{'ribbon_bar_shown' : !hideRibbonBar , 'ribbon_bar_hidden' : hideRibbonBar}"    role="toolbar"
    aria-label="Desktop features">
    <header>
        <nav>
            <ul class="ribbon-bar {{ribbonService.browser.isSafari ? ' ribbon-bar-safari' : ''}}" id="ribbon-bar">
                <li>
                    <button class="ribbon-button compressed_ribbon_bar_btn" id="compressed_ribbon_bar_btn"
                        ng-click="hideRibbonMenu()" ng-keydown="[13, 32].includes($event.keyCode) && hideRibbonMenu()" title="{{'RIBBON.HIDE_RIBBON_BAR' | translate}}" aria-expanded="true"></button>
                </li>
                <li ng-if="!$root.isKioskMode" class="add_line"></li>
                <li>
                    <button ng-class="{'btn-full-screen': !isFullScreen(), 'btn-minimize-screen': isFullScreen()}" class="ribbon-button"
                        ng-if="!$root.isKioskMode && ribbonService.licenses.hasFullscreenLicence"
                        ng-click="toggleFullScreen()" title="{{'RIBBON.FULL_SCREEN' | translate}}"  />
                </li>
                <li ng-if="ribbonService.licenses.hasUploadLicence || ribbonService.licenses.hasFileBrowserLicence" class="add_line"></li>
                <li>
                    <button class="btn-file-browser split-button" ng-disabled="!ribbonService.fileBrowserReady" ng-if="ribbonService.licenses.hasFileBrowserLicence" ng-click="fileBrowser(0)">{{'RIBBON.FILE_BROWSER' | translate}}</button>
                </li>
                <li class="add_line"></li>
                <li>
                    <button class="btn-chatbot split-button" ng-click="openChatbot()">Portia</button>
                </li>
                <li ng-if="false">
                    <split-dropdown-button menu-items="['Open inline', 'Open in a new tab']" button-click="fileBrowserDefault()" menu-click="fileBrowser(index)"
                        ng-if="ribbonService.licenses.hasFileBrowserLicence"
                        title="{{ribbonService.licenses.hasFileBrowserLicence ? 'RIBBON.FILE_BROWSER' : 'RIBBON.FILE_BROWSER_NOT_LICENSED' | translate}}"
                        />
                </li>
                <li>
                    <button class="btn-file-upload btn-name btn-gap ribbon-button ribbon-btn-common-class" guac-upload-attribute ng-click="toggleFileUpload()"
                        ng-if="ribbonService.licenses.hasSftpLicence && ribbonService.licenses.hasUploadLicence" ng-disabled="!ribbonService.fileExplorerReady"
                        title="{{ribbonService.licenses.hasUploadLicence ? (ribbonService.fileExplorerReady ? 'RIBBON.FILE_UPLOAD' : 'RIBBON.CONNECTING') : 'RIBBON.UPLOAD_NOT_LICENSED' | translate}}"
                         >
                        <div id="fileUploadIcon"></div>
                        <p>
                            {{'DIALOGS.BUTTON_UPLOAD' | translate}}
                        </p>
                    </button>
                </li>
                <li>
                    <button class="btn-file-download btn-name btn-gap ribbon-button ribbon-btn-common-class" ng-click="toggleFileDownload()"
                        ng-keydown="[13, 32].includes($event.keyCode) && toggleFileDownload()"
                        ng-if="ribbonService.licenses.hasSftpLicence && ribbonService.licenses.hasDownloadLicence" ng-disabled="!ribbonService.fileExplorerReady"
                        title="{{ribbonService.licenses.hasDownloadLicence ? (ribbonService.fileExplorerReady ? 'RIBBON.FILE_DOWNLOAD' : 'RIBBON.CONNECTING') : 'RIBBON.DOWNLOAD_NOT_LICENSED' | translate}}"
                         >
                        <div class="ribbon-bar-btn" id="downloadIcon"></div>
                        <p>
                            {{'DIALOGS.BUTTON_DOWNLOAD' | translate}}
                        </p>
                    </button>
                    <file-explorer ng-if="!ribbonService.assignmentManagementDialogVisible" ng-class="{shown: ribbonService.fileExplorerVisible}" class="dialog-outer"></file-explorer>
                </li>
                <li ng-if="ribbonService.licenses.hasSharingLicence || ribbonService.licenses.hasAsyncCollaborationLicence" class="add_line"></li>
                <li>
                    <button class="btn-osk ribbon-button" ng-if="tablet" ng-click="toggleOSK()"
                        title="{{'RIBBON.ON_SCREEN_KEYBOARD' | translate}}"  />
                </li>
                <li>
                    <button class="btn-share btn-name btn-gap ribbon-button ribbon-btn-common-class" ng-click="showHideDialog()" ng-if="ribbonService.licenses.hasSharingLicence || ribbonService.licenses.hasAsyncCollaborationLicence"
                        ng-keydown="[13, 32].includes($event.keyCode) && showHideDialog()"
                        ng-disabled="!ribbonService.sharingEnabled"
                        title="{{'CLIENT.ACTION_SHARE' | translate}}"
                         >
                        <div class="ribbon-bar-btn"  id="shareScreenIcon"></div>
                        <p>
                            {{'PRESENTER.SHARE_SCREEN' | translate}}
                        </p>
                    </button>
                    <ribbon-share-dialog ng-class="{shown: ribbonService.ribbonShareDialogVisible}" class="dialog-outer">
                    </ribbon-share-dialog>
                </li>
                <li ng-if="ribbonService.licenses.hasMessengerLicence && !$root.isKioskMode" class="add_line"></li>
                <li>
                    <button class="btn-chat ribbon-button btn-name btn-gap ribbon-btn-common-class" 
                        style="position: relative;"
                        ng-show="ribbonService.licenses.hasMessengerLicence && !$root.isKioskMode"
                        ng-disabled="!ribbonService.licenses.hasChattingLicence"
                        ng-click="toggleChattingDialog()"
                        ng-keydown="[13, 32].includes($event.keyCode) && toggleChattingDialog()"
                        title="{{ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence ? 'RIBBON.CHAT' : 'RIBBON.MESSENGER_NOT_LICENSED' | translate}}">
                        <div class="ribbon-bar-btn"  id="chatIcon"></div>
                        <div ng-if="getUnreadCount() != 0" id="blue_dot"></div>
                        <p>
                            {{'RIBBON.CHAT' | translate}}
                        </p>
                        <span class="btn__badge" ng-if="getUnreadCount() != 0">({{getUnreadCount()}})</span>
                    </button>

                    <div id="kurento"
                        ng-if="ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence && !$root.isKioskMode"
                        ng-class="{shown: ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence}">
                        <div class="kurento-chatboxes" ng-class="{'hidden': !ribbonService.chattingMinimized}">
                            <button ng-click="expandChatting()" id="toggle-controlbox" class="toggle-controlbox" >
                                <span class="toggle-feedback">{{'RIBBON.MESSENGER' | translate}}</span>
                            </button>
                        </div>
                        <div class="dialog-outer chatting-dialog" ng-class="{'hidden': ribbonService.chattingMinimized}"
                            ng-draggable='dragOptions'>
                            <chatting-dialog>
                            </chatting-dialog>
                        </div>
                        <div class="dialog-outer chatting-room"
                            ng-repeat="member in ribbonService.activeMembers track by member.windows_name" ng-style="roomStyle($index)"
                            ng-draggable='dragOptions'>
                            <chatting-room roominfo="member" id="chatting-room-{{member.uid}}"></chatting-room>
                        </div>
                    </div>
                    <messenger-share-dialog
                        ng-if="ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence"
                        ng-class="{shown: ribbonService.messengerShareDialogVisible}" class="dialog-outer"></messenger-share-dialog>
                    <div class="dialog-outer call-dialog" ng-if="ServiceCall.callDialogVisible || ServiceCall.callConfirmDialogVisible"
                        ng-class="{'shown': ServiceCall.callDialogVisible || ServiceCall.callConfirmDialogVisible}"
                        ng-draggable='dragOptionsCall'>
                        <call-dialog></call-dialog>
                    </div>
                </li>
                <li>
                    <button class="btn-highlight ribbon-button" ng-if="false && ribbonService.highlightAvailable" 
                        ng-if="ribbonService.licenses.hasHighlightLicence" ng-click="btnHighlight()" ng-controller="highlightController"
                        title="{{ribbonService.licenses.hasHighlightLicence ? 'RIBBON.HIGHLIGHT' : RIBBON.HIGHLIGHT_NOT_LICENSED | translate}}" />
                </li>
                <li>
                    <button class="btn-reset-non-window-session ribbon-button" ng-click="queryRebootVM(false)"
                        ng-if="!ribbonService.licenses.hasRebootLicence && ribbonService.licenses.hasMacOSLicence"
                        title="{{ribbonService.licenses.hasMacOSLicence ? 'RIBBON.RESET_MACOS' : 'RIBBON.RESET_SESSION_NOT_LICENSED' | translate}}"
                         />
                </li>
                <li>
                    <button class="btn-reset-non-window-session ribbon-button" ng-click="queryRebootVM(false)"
                        ng-if="!ribbonService.licenses.hasRebootLicence && ribbonService.licenses.hasLinuxLicence"
                        title="{{ribbonService.licenses.hasLinuxLicence ? 'RIBBON.RESET_LINUX' : 'RIBBON.RESET_SESSION_NOT_LICENSED' | translate}}"
                         />
                </li>
                <li>

                    <classroom-dialog ng-class="{shown: ribbonService.classroomDialogVisible}"
                        ng-disable="!ribbonService.visibleClassroomView" class="dialog-outer"></classroom-dialog>
                </li>
                <li>
                    <button class="btn-hand-off raiseHand ribbon-button" ng-if="$root.visibleClassroomView" ng-click="ribbonService.handRaise()"
                        title="{{'RIBBON.RAISE_HAND' | translate}}"  >
                        {{'RIBBON.RAISE_HAND' | translate}}
                    </button>
                </li>
                <li>
                    <button class="btn-hand-on raiseHand ribbon-button" ng-if="$root.handRaised" ng-click="ribbonService.removeHand()"
                        title="{{'RIBBON.LOWER_HAND' | translate}}"  >
                        {{'RIBBON.LOWER_HAND' | translate}}
                    </button>
                </li>
                <li>
                    <presenter-request-dialog ng-class="{shown: ribbonService.presenterRequestDialogVisible}" class="dialog-outer">
                    </presenter-request-dialog>
                    <presenter-dialog ng-class="{shown: ribbonService.presenterDialogVisible}" class="dialog-outer"></presenter-dialog>
                    <div id="presenter">
                        <div class="dialog-outer"
                            ng-class="{shown: ribbonService.presenterThumbnailVisible && !ribbonService.presenterMinimized, 'presenter-thumbnail': !ribbonService.presenterThumbnailVisible}"
                            style="width: 480px; height: 360px; left: calc(50vw - 444px); top: calc(50vh - 284px); z-index: 1;"
                            ng-draggable='dragOptionsPresenter'>
                            <presenter-thumbnail></presenter-thumbnail>
                        </div>
                        <div class="presenter-boxes" ng-if="ribbonService.isPresenter" ng-style="presenterStyle()"
                            ng-class="{'hidden': !ribbonService.presenterMinimized}">
                            <a ng-click="expandThumbnail()" class="toggle-controlbox">
                                <span>{{'RIBBON.PRESENTER' | translate}}</span>
                            </a>
                        </div>
                    </div>
                </li>
                <li>
                  <div>
                    <div id="stopSharingModal" ng-if="!ribbonService.presenterDialogVisible && ribbonService.isPresenterEnabled" >
                        <div id="co_present"></div>
                        <div>{{'PRESENTER.PRESENTER_SUBTITLE_SHARED' | translate}} {{ribbonService.selectOptionValue}}</div>
                        <button id="stopSharingBtn" ng-click="ribbonService.stopSharing()">Stop sharing</button>
                    </div>
                  </div>
                </li>
                <li>
                    <button class="btn-assignment-manage btn-gap ribbon-button ribbon-btn-common-class" ng-click="toggleAssignmentManage()" ng-keydown="$event.keyCode === 32 ? toggleAssignmentManage() : $event.keyCode === 13 ? $event.preventDefault() : null"
                        ng-if="ribbonService.licenses.hasLTILicence && (ribbonService.userinfo.roles == 'authenticated user' || ribbonService.userinfo.roles == 'user' || ribbonService.userinfo.roles == null)"
                        ng-disabled="!ribbonService.fileExplorerReady"
                        title={{assignmentTitle}}
                        tabindex="0">
                        <div class="ribbon-bar-btn" id="assignmentIcon"></div>
                        <p>{{ 'RIBBON.MANAGE_ASSIGNMENT' | translate}}</p>
                    </button>
                    <assignment-management-dialog ng-class="{shown: ribbonService.assignmentManagementDialogVisible}" class="dialog-outer">             </assignment-management-dialog>
                </li>
                <li class="add_line" ng-if="(!tablet && ribbonService.licenses.hasMMonitorLicence) || ribbonService.licenses.hasSnapshotsLicence || (!tablet && ribbonService.licenses.hasActivityTrackLicence) || (desktop && ribbonService.licenses.hasClassroomLicence  && ribbonService.classroomGroups != null && ribbonService.classroomGroups.length > 1) || ((desktop && ribbonService.licenses.hasPresenterLicence  && ribbonService.presenterVisible && !(ribbonService.presenterGroups != null && ribbonService.presenterGroups.length === 0)) || ribbonService.isPresenter)"></li>
                <li>
                    <button class="btn-more ribbon-button" id="ribbonMore" ng-click="toggleMorePopup()"
                        title="{{'RIBBON.MORE_TEXT' | translate}}"
                        ng-keydown="[13, 32].includes($event.keyCode) && toggleMorePopup()"
                         aria-expanded="{{ribbonService.morePopupVisible}}"
                        ng-class="{'highlightButton': ribbonService.morePopupVisible}"
                        ng-if="(!tablet && ribbonService.licenses.hasMMonitorLicence) || ribbonService.licenses.hasSnapshotsLicence || (!tablet && ribbonService.licenses.hasActivityTrackLicence) || (desktop && ribbonService.licenses.hasClassroomLicence  && ribbonService.classroomGroups != null && ribbonService.classroomGroups.length > 1) || ((desktop && ribbonService.licenses.hasPresenterLicence  && ribbonService.presenterVisible && !(ribbonService.presenterGroups != null && ribbonService.presenterGroups.length === 0)) || ribbonService.isPresenter)"
                        >
                        <p>
                            {{'RIBBON.MORE_TEXT' | translate}}
                        </p>
                        <div id="moreIcon"></div>
                    </button>
                        <div id="ribbonMorePopup" ng-show="ribbonService.morePopupVisible"
                        ng-mouseleave="ribbonService.morePopupVisible=false" class="dialog-outer shown setting-popup">
                            <div class="setting-popup-content shown">
                              <div>
                                <ul>
                                    <li class="more-li" ng-if="!$root.isKioskMode && desktop && ribbonService.licenses.hasClassroomLicence  && ribbonService.classroomGroups != null && ribbonService.classroomGroups.length > 1">
                                        <button class="btn-classroom-mandatory morePopupBtn ribbon-button"
                                        ng-disabled="!(ribbonService.classroomReady)" ng-click="ribbonService.toggleClassroom()"
                                        title="{{ribbonService.licenses.hasClassroomLicence ? 'RIBBON.CLASSROOM' : 'RIBBON.CLASSROOM_NOT_LICENSED' | translate}}"
                                        >Virtual classroom</button>
                                    </li>
                                    <li class="more-li" ng-if="!$root.isKioskMode && desktop && $root.selectedGroup && ribbonService.licenses.hasClassroomLicence && ribbonService.classroomGroups != null && ribbonService.classroomGroups.length <= 1">
                                        <button class="btn-classroom-mandatory morePopupBtn ribbon-button" ng-disabled="$root.displayClassroomView"
                                        ng-click="$root.openClassroom()"
                                        title="{{ribbonService.licenses.hasClassroomLicence ? 'RIBBON.CLASSROOM' : 'RIBBON.CLASSROOM_NOT_LICENSED' | translate}}"
                                         >Virtual classroom</button>
                                    </li>
                                    <li class="more-li"   ng-if="!$root.isKioskMode && ((desktop && ribbonService.licenses.hasPresenterLicence  && ribbonService.presenterVisible && (ribbonService.presenterGroups != null)) || ribbonService.isPresenter)">
                                        <button class="btn-presenter morePopupBtn ribbon-button"
                                        ng-click="ribbonService.togglePresenter()" title="{{'RIBBON.PRESENTER_MODE' | translate}}" >{{(ribbonService.userinfo.roles=="Faculty Admin")?'Presenter mode':'View Presenter Screen'}}</button>
                                    </li>
                                    <li class="more-li" ng-if="!$root.isKioskMode && !tablet && ribbonService.licenses.hasMMonitorLicence">
                                        <button class="btn-mmonitor morePopupBtn ribbon-button" ng-click="openMultiMonitor()" 
                                            ng-disabled="!ribbonService.mMonitorEnabled ||
                                                         (ribbonService.isOpenSecondMonitor && ribbonService.isOpenThirdMonitor)"
                                            title="{{'RIBBON.MULTIMONITOR' | translate}}" >{{ (ribbonService.isOpenSecondMonitor) ? 'RIBBON.THIRD_MULTIMONITOR' : 'RIBBON.SECOND_MULTIMONITOR'| translate }}
                                        </button>
                                    </li>
                                    <li class="more-li" ng-if="ribbonService.licenses.hasSnapshotsLicence">
                                        <button class="btn-snapshot-manager morePopupBtn ribbon-button" ng-click="toggleSnapshotManager()"
                                            ng-keydown="[13, 32].includes($event.keyCode) && toggleSnapshotManager()"
                                            title="{{ribbonService.licenses.hasSnapshotsLicence ? 'RIBBON.MANAGE_SNAPSHOTS' : RIBBON.SNAPSHOTS_NOT_LICENSED | translate}}"
                                             >{{'INTRO_TITLE.SNAPSHOT' | translate}}</button>
                                    </li>
                                    <li class="more-li"  ng-if="ribbonService.licenses.hasBackupLicence">
                                        <button class="btn-backup morePopupBtn ribbon-button" ng-click="toggleVmManager()"
                                            title="{{ribbonService.licenses.hasBackupLicence ? 'RIBBON.BACKUP' : 'RIBBON.BACKUP_NOT_LICENSED' | translate}}"
                                             >
                                            {{'RIBBON.BACKUP' | translate}}
                                        </button>
                                    </li>
                                    <li class="more-li" ng-if="!tablet && ribbonService.licenses.hasActivityTrackLicence">
                                        <button class="btn-analytics morePopupBtn ribbon-button" ng-click="displayAnalyticDashboard()" 
                                            title="{{ribbonService.licenses.hasActivityTrackLicence ? 'RIBBON.ANALITYCS' : 'RIBBON.ANALYTICS_NOT_LICENSED' | translate}}" >{{'RIBBON.ANALITYCS' | translate}}</button>
                                    </li>
                                </ul>
                              </div>
                            </div>
                        </div>
                </li>
                <li class="flex-grow">
                </li>
                <li>
                    <button id="network-indicator-btn" class="ribbon-button last-btns ribbon-network-indicator" title="{{'RIBBON.NETWORK_INDICATOR' | translate}}"
                        ng-if="ribbonService.isPrimaryScreen"
                        ng-click="toggleNetworkPopUp()"
                        ng-class="{'highlightButton': ribbonService.networkQualityDialogVisible}"
                        ng-keydown="[13, 32].includes($event.keyCode) && (ribbonService.networkQualityDialogVisible = !ribbonService.networkQualityDialogVisible)"
                        aria-expanded="false"
                        >
                        <div class="network-indicator" ng-class="{'btn-excellent' : ribbonService.networkStatus == 5, 'btn-excellent-medium' : ribbonService.networkStatus == 4,
                            'btn-fair' : ribbonService.networkStatus == 3, 'btn-fair-medium' : ribbonService.networkStatus == 2,
                            'btn-poor' : ribbonService.networkStatus == 1, 'btn-unstable' : ribbonService.networkStatus == 0}"></div>
                    </button>
                    <div id="networkPopUp" ng-class="{shown: ribbonService.networkQualityDialogVisible, 'undock-box': ribbonService.networkQualityDialogVisible, 'box-hidden': !ribbonService.networkQualityDialogVisible}"
                        style="background:unset;"
                        class="dialog-outer" 
                        >
                        <button  id="close_network_tab_button" class="close file" ng-click="toggleNetworkPopUp()"  aria-label="Close">&times;</button>
                        <network-quality-dialog></network-quality-dialog>
                    </div>
                </li>
                <li>
                    <button class="btn-sound last-btns ribbon-button" role="button" id="toggle"
                        ng-class="{'btn-sound-on': !ribbonService.soundMute, 'btn-sound-off': ribbonService.soundMute}"
                        ng-keydown="[13, 32].includes($event.keyCode) && switchSoundOnOff()"
                        ng-click="switchSoundOnOff()" ng-if="!ribbonService.isShared" title="{{'RIBBON.SPEAKER_GENERIC' | translate}}"
                        aria-pressed="{{!!ribbonService.soundMute}}"  />
                </li>
                <li>
                    <button class="btn-help last-btns ribbon-button" id="ribbonHelpCenterBtn" ng-click="toggleHelpCenterPopup()"
                        title="{{'RIBBON.HELP' | translate}}"
                        ng-keydown="[13, 32].includes($event.keyCode) && toggleHelpCenterPopup()"
                        ng-class="{'highlightButton': ribbonService.helpCenterPopupVisible}"
                        
                        aria-expanded="{{ribbonService.helpCenterPopupVisible}}"
                        aria-label="Help Menu" />
                    <help-center-popup ng-show="ribbonService.helpCenterPopupVisible"
                        ng-mouseleave="ribbonService.helpCenterPopupVisible=false"></help-center-popup>
                    <div id="inPage">
                        <div class="dialog-outer"
                            ng-class="{shown: ribbonService.inPageVisible && !ribbonService.inPageMinimized, 'in-page': !ribbonService.inPageVisible}"
                            style="left: 10vw; top: 40px; z-index: 1;">
                            <in-page></in-page>
                        </div>
                        <div class="inPage-boxes" ng-if="ribbonService.isInPage" ng-style="presenterStyle()"
                            ng-class="{'hidden': !ribbonService.inPageMinimized}">
                            <a ng-click="$root.expandInPage()" class="toggle-controlbox">
                                <span>{{$root.helpName}}</span>
                            </a>
                        </div>
                    </div>
                <li>
                    <enable-logging-dialog ng-class="{shown: ribbonService.enableLoggingDialogVisible}" class="dialog-outer">
                    </enable-logging-dialog>
                </li>
                <li>
                    <about-apporto-dialog ng-class="{shown: ribbonService.aboutApportoDialogVisible}" class="dialog-outer">
                    </about-apporto-dialog>
                </li>
                <li ng-if="ribbonService.isPrimaryScreen || (!ribbonService.isPrimaryScreen && ribbonService.resizeMethod === ribbonService.RESIZE_METHOD.DISPLAY_UPDATE)">
                    <button class="btn-gear last-btns btn-gear ribbon-button" id="ribbonSettingBtn" ng-click="toggleSettingsPopup()"
                        title="{{'RIBBON.SETTINGS_GENERIC' | translate}}"
                        ng-keydown="[13, 32].includes($event.keyCode) && toggleSettingsPopup()"
                         aria-expanded="{{ribbonService.settingPopupVisible}}"
                        ng-class="{'highlightButton': ribbonService.settingPopupVisible}"
                        aria-label="Setting Menu" />
                    <setting-popup ng-show="ribbonService.settingPopupVisible"
                        ng-mouseleave="ribbonService.settingPopupVisible=false"></setting-popup>
                </li>
                <li ng-if="(ribbonService.isPrimaryScreen || (!ribbonService.isPrimaryScreen && ribbonService.resizeMethod === ribbonService.RESIZE_METHOD.DISPLAY_UPDATE)) && ribbonService.licenses.hasRemoteApps">
                    <button class="btn-remoteapps last-btns btn-remoteapps ribbon-button" id="ribbonRemoteAppsBtn" ng-click="toggleRemoteAppsPopup()"
                        title="{{'RIBBON.REMOTE_APPS' | translate}}"
                        ng-keydown="[13, 32].includes($event.keyCode) && toggleRemoteAppsPopup()"
                        tabindex="0" aria-expanded="{{ribbonService.remoteAppsPopupVisible}}"
                        ng-class="{'highlightButton': ribbonService.remoteAppsPopupVisible}"
                        aria-label="Remote Apps Menu" />
                    <remote-apps ng-show="ribbonService.remoteAppsPopupVisible"
                        ng-mouseleave="ribbonService.remoteAppsPopupVisible=false"></remote-apps>
                </li>
            </ul>
        </nav>
    </header>
</div>
<snapshot-manager-dialog ng-class="{ 'shown': ribbonService.snapshotManagerVisible, 'hide-bg': ribbonService.hideDialogBackground, 'raise': ribbonService.hideDialogBackground }"
    class="dialog-outer snapshot-manager-outer"></snapshot-manager-dialog>

<info-message ng-class="{'info-dialog': !infoService.top, 'info-dialog-top': infoService.top}" role="alert"
    ng-show="infoService.infoDialogVisible" ></info-message>

<summary-chart-dialog ng-show="ribbonService.summaryChartVisible || ribbonService.introRunning"
    ng-class="{shown: ribbonService.summaryChartVisible}" class="dialog-outer"></summary-chart-dialog>

<vm-manager-dialog
    ng-class="{ 'shown': ribbonService.vmManagerVisible, 'hide-bg': ribbonService.hideDialogBackground, 'raise': ribbonService.hideDialogBackground }"
    class="dialog-outer vm-manager-outer"></vm-manager-dialog>

<div ng-class="{shown: ribbonService.usbListDlgVisible, 'undock-box': ribbonService.usbListDlgVisible, 'box-hidden': !ribbonService.usbListDlgVisible}"
    style="left:calc(50vw - 265px); top: calc(50vh - 135px); z-index:1; background:unset;"
    class="dialog-outer" ng-draggable="dragOptionsUsbDlg">
    <usb-list-dialog></usb-list-dialog>
</div>

    <div ng-show="ribbonService.innerFileBrowserVisible" ng-class="{shown: ribbonService.innerFileBrowserVisible, 'center-fb': ribbonService.innerFileBrowserVisible, 'box-hidden': !ribbonService.innerFileBrowserVisible}"
        style="background:unset;"
        class="dialog-outer" ng-draggable="dragOptionsFileBrowserDlg">
        <file-browser-external url="ribbonService.innerFileBrowserUrl"/>
    </div>

    <div ng-show="ribbonService.chatbotVisible"
        style="background:unset;"
        class="dialog-outer portia-chatbot" ng-class="{shown: ribbonService.chatbotVisible, 'center-fb': ribbonService.chatbotVisible}" ng-draggable="dragOptionsChatbotDlg" id="chatbotWindow">
        <portia-chatbot url="ribbonService.chatbotUrl" />
    </div>

<mmonitor ng-show="ribbonService.mmLoad" class="mm-dialog-outer"></mmonitor>

<div id="participants" class="dialog-outer">
</div>

<div id="main-video" class="dialog-outer">
</div>

<video id="videoOutput" autoplay="" width="320px" height="240px" class="dialog-outer" style="
    top: 36px;
    width: 320px;
    height: 240px;
    opacity: 0;
    visibility: hidden;
"></video>

<video id="videoInput" autoplay="" width="240px" height="180px" class="dialog-outer" style="
    top: 36px;
    left: 330px;
    width: 240px;
    height: 180px;
    opacity: 0;
    visibility: hidden;
"></video>
