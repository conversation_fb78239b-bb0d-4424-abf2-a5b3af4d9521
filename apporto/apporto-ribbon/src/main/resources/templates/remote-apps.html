<div id="ribbonRemoteAppsPopup" class="dialog-outer shown setting-popup">
    <div class="setting-popup-content shown remoteapps-popup">
        <!-- Content -->
        <div>
            <ul>
                <li class="remoteapps-li">
                    <p>OPEN APPS</p>
                </li>
                <li class="remoteapps-li">
                    <button ng-repeat="openApp in openApps"
                        class="ribbon-button remoteapps-icon"
                        style="background-image: url('{{ openApp.icon }}')"
                        ng-click="clickRemoteAppBtn(openApp)"
                        tabindex="0"
                        aria-label="{{ openApp.name }}" ></button>
                </li>
            </ul>
            <ul>
                <li class="remoteapps-li availableapps-title-li">
                    <p>AVAILABLE APPS</p>
                    <div class="pined-apps">
                        <button ng-if="viewMethod == 'grid'" class="btn-remoteapps-listview"
                            ng-click="clickListViewBtn()" aria-label="List View" >
                        </button>
                        <button ng-if="viewMethod == 'list'" class="btn-remoteapps-gridview"
                            ng-click="clickGridViewBtn()" aria-label="Grid View" >
                        </button>
                        <button class="btn-remoteapps-search"
                            ng-click="clickSearchBtn()" aria-label="Search Remote Apps" >
                        </button>
                    </div>
                </li>

                <!-- search box -->
                <li ng-if="!hideSearchBox" class="search-box-li">
                    <div class="remoteapps-search">
                        <img src="app/ext/ribbon/images/remote_apps_search.svg">
                        <input type="text" 
                            class="search-value" 
                            placeholder="Search" 
                            ng-model="model.searchInputText">
                    </div>
                </li>

                <!-- pined apps -->
                <li ng-if="viewMethod == 'list' && model.searchInputText == ''"
                    class="remoteapps-li listview-li" ng-repeat="pinApp in pinApps">
                    <div class="pined-apps">
                        <button class="ribbon-button remoteapps-icon"
                            style="background-image: url('{{ pinApp.icon }}')"
                            tabindex="0"
                            ng-click="clickRemoteAppBtn(pinApp)"
                            aria-label="{{ pinApp.name }}" ></button>
                        <p>{{pinApp.name}}</p>
                    </div>
                    <button class="btn-remoteapps-pin ribbon-button" tabindex="0"
                        ng-click="clickUnPinBtn(pinApp.name)"
                        aria-label="{{ pinApp.name }}" ></button>
                </li>

                <!-- black seperator line -->
                <div ng-if="viewMethod == 'list' && model.searchInputText == ''" class="setting-seperator"></div>

                <!-- unpined apps -->
                <li ng-if="viewMethod == 'list' && model.searchInputText == ''"
                    class="remoteapps-li listview-li" ng-repeat="availableApp in availableApps">
                    <div class="pined-apps">
                        <button
                            class="ribbon-button remoteapps-icon"
                            style="background-image: url('{{ availableApp.icon }}')"
                            tabindex="0"
                            aria-label="{{ availableApp.name }}"
                            ng-click="clickRemoteAppBtn(availableApp)"></button>
                        <p>{{availableApp.name}}</p>
                    </div>
                    <button ng-if="alreadyPin(availableApp.name)" class="btn-remoteapps-pin ribbon-button" tabindex="0"
                        ng-click="clickUnPinBtn(availableApp.name)" aria-label="{{ availableApp.name }}" ></button>
                    <button ng-if="!alreadyPin(availableApp.name)" class="btn-remoteapps-unpin ribbon-button" tabindex="0"
                        ng-click="clickPinBtn(availableApp)" aria-label="{{ availableApp.name }}" ></button>
                </li>

                <li ng-if="viewMethod == 'list' && model.searchInputText != ''"
                    class="remoteapps-li listview-li" ng-repeat="availableApp in searchApps">
                    <div class="pined-apps">
                        <button
                            class="ribbon-button remoteapps-icon"
                            style="background-image: url('{{ availableApp.icon }}')"
                            tabindex="0" aria-label="{{ availableApp.name }}" ></button>
                        <p>{{availableApp.name}}</p>
                    </div>
                    <button ng-if="alreadyPin(availableApp.name)" class="btn-remoteapps-pin ribbon-button" tabindex="0"
                        ng-click="clickUnPinBtn(availableApp.name)" aria-label="{{ availableApp.name }}" ></button>
                    <button ng-if="!alreadyPin(availableApp.name)" class="btn-remoteapps-unpin ribbon-button" tabindex="0"
                        ng-click="clickPinBtn(availableApp)" aria-label="{{ availableApp.name }}" ></button>
                </li>

                <li ng-if="viewMethod == 'grid' && model.searchInputText == ''" class="gridview-li">
                    <button ng-repeat="availableApp in availableApps"
                        style="background-image: url('{{ availableApp.icon }}')"
                        class="ribbon-button remoteapps-icon" tabindex="0" aria-label="{{ availableApp.name }}" ></button>
                </li>

                <li ng-if="viewMethod == 'grid' && model.searchInputText != ''" class="gridview-li">
                    <button ng-repeat="availableApp in searchApps"
                        style="background-image: url('{{ availableApp.icon }}')"
                        class="ribbon-button remoteapps-icon" tabindex="0" aria-label="{{ availableApp.name }}" ></button>
                </li>

                <li ng-if="model.searchInputText != '' && searchApps.length == 0" class="no-search-result">
                    <p> No apps match your query. <br>Please try again. </p>
                </li>
            </ul>
        </div>
    </div>
</div>