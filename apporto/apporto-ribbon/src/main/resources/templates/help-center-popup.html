<div id="ribbonHelpCenterPopup" class="dialog-outer shown help-center-popup">
    <div class="help-center-popup-content dialog-outer shown">

        <!-- Content -->
        <div>
            <ul>
                <li ng-if="!!ribbonService.licenses.userGuideMenuName && !$root.isKioskMode">
                    <a ng-click="openUserGuide()" tabindex="0" role="button">
                        {{ribbonService.licenses.userGuideMenuName}}
                    </a>
                </li>
                <li ng-if="!!ribbonService.licenses.supportMenuName && !$root.isKioskMode">
                    <a ng-click="openSupport()" tabindex="0" role="button">
                        {{ribbonService.licenses.supportMenuName}}
                    </a>
                </li>
                <li ng-if="(ribbonService.licenses.hasH264Licence && ribbonService.licenses.hardWareAccelerated) && !$root.isKioskMode">
                    <a id="btnEnableLogging" ng-click="openEnableLoggingDialog()" tabindex="0" role="button">
                        {{'DIALOGS.HELP_CENTER_POPUP_ENABLE_LOGGING' | translate}}
                    </a>
                </li>
                <li>
                    <a ng-click="openAboutApportoDialog()" tabindex="0" role="button">
                        {{'DIALOGS.HELP_CENTER_POPUP_ABOUT_APPORTO' | translate}}
                    </a>
                </li>
            </ul>
        </div>

    </div>
</div>
