<div id="chatting-room">
  <div>
    <!--head-->
    <div style="padding: 16px 16px 9px 16px;" class="chat-head controlbox-head">
      <div style="display: flex; margin-right: auto">
        <div role="none" class="avatar">
          <div role="none" class="avatar-text">
            <div class="avatar-short-text">{{roominfo.initials}}</div>
          </div>
          <span ng-if="roominfo.state==0" class="fa fa-circle" style="color: var(--chat-status-online); background-color: var(--chat-status-online);"></span>
          <span ng-if="roominfo.state==1" class="fa fa-minus-circle" style="color: var(--chat-status-busy); background-color: var(--chat-status-busy);"></span>
          <span ng-if="roominfo.state==2" class="fa fa-circle" style="color: var(--chat-status-away); background-color: var(--chat-status-away);"></span>
          <span ng-if="roominfo.state==3" class="fa fa-circle" style="color: var(--chat-status-offline); background-color: var(--chat-status-offline);"></span>
        </div>
        <div role="none" class="username">
          <p style="font-size: 16px !important; font-weight: 600 !important;" class="chatting_room_text_white_color">{{roominfo.name}}</p>
          <p ng-if="roominfo.state==0" class="chatting_room_text_white_color">{{'MESSENGER.ONLINE' | translate}}</p>
          <p ng-if="roominfo.state==1" class="chatting_room_text_white_color">{{'MESSENGER.BUSY' | translate}}</p>
          <p ng-if="roominfo.state==2" class="chatting_room_text_white_color">{{'MESSENGER.AWAY' | translate}}</p>
          <p ng-if="roominfo.state==3" class="chatting_room_text_white_color">{{'MESSENGER.OFFLINE' | translate}}</p>
        </div>
      </div>
      <button ng-if="!ribbonService.isVirtualClassroom && !ribbonService.isVirtualClassroomThumbnail && roominfo.state != 3 && (serviceShare.state == serviceShare.NOT_SHARE || serviceShare.user.windows_name == roominfo.windows_name)" 
        class="context-menu icon share" ng-click=shareDialog() tabindex="0" aria-label="Share"></button>
      <button ng-if="(roominfo['id'] != 'group' || !ribbonService.callVisible) && roominfo.state != 3" 
        class="context-menu icon call" ng-class="{'disabled': callState!=0}" ng-click=call() tabindex="0" aria-label="Call"></button>
      <button ng-if="ribbonService.isVirtualClassroomThumbnail" class="context-menu icon minus" ng-click=minimizeChat()
        tabindex="0" aria-label="Minimize"></button>
      <button ng-if="!ribbonService.isVirtualClassroomThumbnail" id="chatroom-close-btn" class="context-menu icon times" ng-click=closeChat()
        tabindex="0" aria-label="Close" aria-labelledby="closeChatroomTooltip">
        <div id="closeChatroomTooltip" class="chat-tooltip close-btn-tooltip" role="tooltip">Close</div>
      </button>
    </div>

    <div style="height: 247px;overflow-y: auto;overflow-x: hidden;" class="scroll" id="room-{{roominfo.windows_name}}">
      <div class="chat">
        <div ng-repeat="message in messages" class="messages"
          ng-class="{'mine': ribbonService.userName.includes(message.user), 'yours': !ribbonService.userName.includes(message.user)}">
          <div class="message" title="{{message.time}}" ng-class="{'last': message.last}">
            {{message.type == ribbonService.CHAT ? message.message : message.callMessage}}
            <span class="message-state read" ng-if="message.state == 'read' && ribbonService.userName.includes(message.user)">
              <span data-testid="msg-check" aria-label=" Read " data-icon="msg-check"><svg viewBox="0 0 16 11"
                height="11" width="16" preserveAspectRatio="xMidYMid meet" fill="none">
                <path
                    d="M11.1549 0.652832C11.0745 0.585124 10.9729 0.55127 10.8502 0.55127C10.7021 0.55127 10.5751 0.610514 10.4693 0.729004L4.28038 8.36523L1.87461 6.09277C1.8323 6.04622 1.78151 6.01025 1.72227 5.98486C1.66303 5.95947 1.60166 5.94678 1.53819 5.94678C1.407 5.94678 1.29275 5.99544 1.19541 6.09277L0.884379 6.40381C0.79128 6.49268 0.744731 6.60482 0.744731 6.74023C0.744731 6.87565 0.79128 6.98991 0.884379 7.08301L3.88047 10.0791C4.02859 10.2145 4.19574 10.2822 4.38194 10.2822C4.48773 10.2822 4.58929 10.259 4.68663 10.2124C4.78396 10.1659 4.86436 10.1003 4.92784 10.0156L11.5738 1.59863C11.6458 1.5013 11.6817 1.40186 11.6817 1.30029C11.6817 1.14372 11.6183 1.01888 11.4913 0.925781L11.1549 0.652832Z"
                    fill="currentcolor"></path>
                </svg>
              </span>
            </span>
          </div>
        </div>
      </div>

      <!-- <div>
        <div ng-repeat="message in messages" class="contact-item">
          <!- - Message of receiver - ->
          <div class="list-item-text compact" ng-if="message.user == ribbonService.userName"
            style="margin-left: 20%;">
            <p class="message-userinfo" ng-style="{'text-align': 'right'}">
              <small>{{message.user}}, {{message.time}}</small>
            </p>
            <div class="message-content-send">
              {{message.message}}
            </div>
          </div>
          <!- - Message of sender - ->
          <div class="list-item-text compact" ng-if="message.user != ribbonService.userName"
            style="margin-right: 20%;">
            <p class="message-userinfo" ng-style="{'text-align': 'left'}">
               <small>{{message.user}}, {{message.time}}</small>
            </p>
            <div class="message-content-receive">
              {{message.message}}
            </div>
          </div>
        </div>
      </div> -->
    </div>
    <div class="btn-send-group">
      <form ng-submit="createMessage()" style="text-align: initial;">
        <input ng-model="newMessage" class="message-input"
          placeholder="Type something" maxlength="500" aria-label="Type message" />
        <button type="submit" class="btn-send btn-image" tabindex="0"  aria-labelledby="sendChatTooltip" >
          <div id="sendChatTooltip" class="chat-tooltip send-btn-tooltip" role="tooltip">Send</div>
        </button>
      </form>
    </div>
  </div>
</div>