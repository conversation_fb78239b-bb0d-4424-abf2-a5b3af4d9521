<div class="dialog-middle">
  <div class="file-explorer dialog">
    <div>
      <button class="close file" ng-click="closeDialog()" tabindex="0" aria-labelledby="closeTooltip" >
        &times;
        <div id="closeTooltip" class="dialog-tooltip close-tooltip" role="tooltip">Close</div>
      </button>
      <div class="menu-content">

        <!-- Stationary header -->
        <div>
          <div id="titleHeader" class="header" ng-show="ribbonService.fileDownloadVisible">
            <h2 id="fileDownLoadTitle">{{'DIALOGS.FILE_DOWNLOAD_TITLE' | translate}}</h2>
          </div>
        </div>

        <!-- Breadcrumbs -->
        <div class="header breadcrumbs" ng-show="ribbonService.fileDownloadVisible">
          <div id="instructionText" class="instruction" ng-show="ribbonService.fileDownloadVisible">
            {{'DIALOGS.FILE_DOWNLOAD_INSTRUCTION' |translate}}</div>
          <div id="line-gap"></div>
          <button class="download breadcrumb" ng-click="changeDirectory(filesystem.root)" tabindex="0" aria-label="Breadcrumb: {{'DIALOGS.CLOUD_DESKTOP' | translate}}"
          ng-attr-aria-current="{{ getPath(filesystem.currentDirectory).length === 0 ? 'page' : undefined }}" >
            {{'DIALOGS.CLOUD_DESKTOP' | translate}}</button>
          <button class="download breadcrumb" ng-repeat="file in getPath(filesystem.currentDirectory)" aria-label="Breadcrumb: {{file.name}}"
          ng-attr-aria-current="{{ $last ? 'page' : undefined }}" >
            <div class="download next"></div>
            <div class="download directory" ng-click="changeDirectory(file)">{{file.name}}
            </div>
          </button> 
          <div id="line-gap"></div>
        </div>

        <!-- Scrollable body -->
        <div id="menuBody" class="menu-body">
          <file-browser id="directory-list" client="client" filesystem="filesystem"></file-browser>
        </div>
        <mmonitor ng-show="circleLoaderService.circleLoaderVisible" class="colorLoader"></mmonitor>
        <!-- Bottom line -->
        <div class="footer">
          <button id="download_file_btn" class="download button disable_download_btn" ng-show="ribbonService.fileDownloadVisible" ng-click="downloadFile()"
          tabindex="0" aria-disabled="true">{{'DIALOGS.BUTTON_DOWNLOAD' | translate}}</button>
        </div>
      </div>
    </div>
  </div>
</div>