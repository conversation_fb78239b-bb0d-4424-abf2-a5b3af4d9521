<div class="dialog-middle" ng-show="ribbonService.ribbonShareDialogVisible">
    <div class="ribbon-share-dialog dialog" id="dlg-share">
        <button ng-show="!ribbonService.nextButtonClicked || (ribbonService.nextButtonClicked && model.selectedCollaborate == collaborateType.OFFLINE)"
            class="enable-close" tabindex="0" aria-labelledby="closeTooltip" style="position: absolute;"
            ng-click="closeDialog()">
                <div id="closeTooltip" class="dialog-tooltip close-tooltip" role="tooltip">Close</div>                
            </button>
        <button ng-show="ribbonService.nextButtonClicked && model.selectedCollaborate == collaborateType.NOW"
            ng-class="(model.selectedCollaborate == collaborateType.NOW && model.url == null) ? 'disable-close' : ''"
            class="enable-close" tabindex="0" aria-labelledby="closeTooltip" style="position: absolute;"
            ng-click="model.url && closeDialog()">
                <div id="closeTooltip" class="dialog-tooltip close-tooltip" role="tooltip">Close</div>                
            </button>
        <h2 class="title-share">{{'SHARE_SUBMENU.SHARE_TITLE' | translate}}</h2>
        <p class="subtitle-share">{{'SHARE_SUBMENU.SHARE_SUBTITLE' | translate}}</p>
        <div class="collaboration-select" ng-show="ribbonService.ribbonShareDialogVisible && !ribbonService.nextButtonClicked">
            <div role="radiogroup" aria-labelledby="dlg-share">

                <fieldset  style="min-inline-size: auto; border: none; padding: 0px !important; display: flex; flex-direction: column; gap: 8px; margin: 24px 0px;">
                    <label class="label-share" style="width: 100%; margin: 0 auto; display: flex; justify-content: center;" role="radio" ng-class="{'selected_share_url': (model.selectedCollaborate === collaborateType.NOW), 'disable_text': (model.selectedCollaborate === null)}" 
                        aria-checked="{{model.selectedCollaborate == collaborateType.NOW}}" tabindex="0">
                        <input style="display: none;" type="radio" name="collaborateType"
                            aria-label="{{'SHARE_SUBMENU.SHARE_COLLABORATION_NOW' | translate}}"
                            ng-value="collaborateType.NOW"
                            ng-checked="model.selectedCollaborate == collaborateType.NOW"
                            ng-model="model.selectedCollaborate">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_COLLABORATION_NOW' | translate}}</span>
                    </label>

                    <label class="label-share" style="width: 100%; margin: 0 auto; display: flex; justify-content: center;" role="radio" ng-class="{'selected_share_url': (model.selectedCollaborate == collaborateType.OFFLINE),'disable_text': (model.selectedCollaborate === null)}" 
                        aria-checked="{{model.selectedCollaborate == collaborateType.OFFLINE}}" tabindex="0">
                        <input style="display: none;" type="radio" name="collaborateType"
                            aria-label="{{'SHARE_SUBMENU.SHARE_COLLABORATION_OFFLINE' | translate}}"
                            ng-value="collaborateType.OFFLINE"
                            ng-checked="model.selectedCollaborate == collaborateType.OFFLINE"
                            ng-model="model.selectedCollaborate">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_COLLABORATION_OFFLINE' | translate}}</span>
                    </label>
                </fieldset>

            </div>
            <div class="footer" style = "padding: 0px; text-align: left;">
                <button style="margin: 0px;" class="collaboration select" ng-click="btnNext()" tabindex="0">{{'SHARE_SUBMENU.SHARE_NEXT' | translate}}</button>
            </div>
        </div>

        <div class="collaboration-now" style="min-width: 494px;"
            ng-show="ribbonService.ribbonShareDialogVisible && ribbonService.nextButtonClicked && model.selectedCollaborate == collaborateType.NOW">
            <div role="radiogroup" aria-labelledby="dlg-share">

                <fieldset style="min-inline-size: auto; border: none; padding: 0px; display: flex; flex-direction: column; gap: 8px; margin: 24px 0px;">
                    <label class="label-share" ng-class="{'selected_share_url': (shareLinkVO == model.url) , 'disable_url': ((ribbonService.highlightActivated || ribbonService.shareLinkCopied) && (shareLinkVO !== model.url)) , 'disable_text': (model.url == null)}" role="radio" aria-checked="{{shareLinkVO == model.url}}" tabindex="0">
                        <input style="display: none;" type="radio" name="shareLink" ng-value="shareLinkVO" ng-checked="shareLinkVO == model.url"
                            ng-model="model.url"
                            ng-disabled="ribbonService.highlightActivated || ribbonService.shareLinkCopied"
                            aria-label="{{'SHARE_SUBMENU.SHARE_VIEW_ONLY' | translate}}">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_VIEW_ONLY' | translate}}</span>
                        <span class="disabled">{{'SHARE_SUBMENU.SHARE_VIEW_ONLY_DESCRIPTION' | translate}}</span>
                    </label>

                    <label class="label-share" ng-class="{'selected_share_url': (shareLinkVO+'&hl=true' === model.url), 'disable_url': ((ribbonService.highlightActivated || ribbonService.shareLinkCopied) && (shareLinkVO+'&hl=true' !== model.url)), 'disable_text': (model.url == null) }"  role="radio" aria-checked="{{shareLinkVO+'&hl=true' == model.url}}" tabindex="0">
                        <input style="display: none;" type="radio" name="shareLink" ng-value="shareLinkVO+'&hl=true'" ng-model="model.url"
                            ng-disabled="ribbonService.highlightActivated || ribbonService.shareLinkCopied"
                            aria-label="{{'SHARE_SUBMENU.SHARE_VIEW_AND_ANNOTATE' | translate}}">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_VIEW_AND_ANNOTATE' | translate}}</span>
                        <span class="disabled">{{'SHARE_SUBMENU.SHARE_VIEW_AND_ANNOTATE_DESCRIPTION' | translate}}</span>
                    </label>

                    <label class="label-share" ng-class="{'selected_share_url': (shareLinkFC === model.url),'disable_url': ((ribbonService.highlightActivated || ribbonService.shareLinkCopied) && (shareLinkFC !== model.url)), 'disable_text': (model.url == null) }"  role="radio" aria-checked="{{shareLinkFC == model.url}}" tabindex="0">
                        <input style="display: none;" type="radio" name="shareLink" ng-value="shareLinkFC" ng-model="model.url"
                            ng-disabled="ribbonService.highlightActivated || ribbonService.shareLinkCopied"
                            aria-label="{{'SHARE_SUBMENU.SHARE_FULL_CONTROL' | translate}}">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_FULL_CONTROL' | translate}}</span>
                        <div style="display: flex; flex-direction: column; gap: 16px; width: 286px;">
                            <span class="disabled">{{'SHARE_SUBMENU.SHARE_FULL_CONTROL_DESCRIPTION' | translate}}</span>
                            <span class="warning_mez_text">{{'SHARE_SUBMENU.SHARE_FULL_CONTROL_WARNING' | translate}}</span>
                        </div>
                    </label>
                </fieldset>

            </div>
            <div class="footer" style = "padding: 0px; text-align: left;">
                <button class="viewOnly share image" style="margin-right: 8px;"
                    ng-click="copyLink()"
                    ng-if="ribbonService.isSharingStart"
                    ng-controller="highlightController" tabindex="0">{{((ribbonService.highlightActivated ||
                    ribbonService.shareLinkCopied) && ribbonService.isLinkCopied) ? 'SHARE_SUBMENU.SHARE_LINK_COPIED' : 'SHARE_SUBMENU.SHARE_COPY_LINK'
                    | translate}}</button>
                <button class="viewOnly share" ng-class="{'show_bg_in_btn': ribbonService.isSharingStart}"
                    ng-click="!ribbonService.highlightActivated ? btnCopyLink() : angular.noop(); (model.url.endsWith('&hl=true')) ?  btnHighlight() : angular.noop(); ribbonService.isSharingStart = !ribbonService.isSharingStart; !ribbonService.isSharingStart ? (model.url=null) : angular.noop();"
                    ng-controller="highlightController" tabindex="0"
                    ng-keydown="$event.key === 'Enter' && $event.preventDefault()">{{(ribbonService.highlightActivated ||
                        ribbonService.shareLinkCopied) ? 'SHARE_SUBMENU.SHARE_STOP_ANNOTATION' : 'SHARE_SUBMENU.SHARE_COPY_LINK'
                        | translate}}</button>
            </div>
        </div>

        <div class="collaboration-offline"
            ng-show="ribbonService.ribbonShareDialogVisible && ribbonService.nextButtonClicked && model.selectedCollaborate == collaborateType.OFFLINE">
            <p class="subtitle-collaborate-share" style="margin-bottom: 0em; text-align: left;">{{'SHARE_SUBMENU.SHARE_DESCRIBE' | translate}}</p>
            <div role="radiogroup" aria-labelledby="dlg-share">

                <fieldset style="min-inline-size: auto; display: flex; flex-direction: column; gap: 8px; border: none; padding: 0px; margin: 24px 0px;">
                    <label class="label-share" tabindex="0">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_DESKTOP_LINK' | translate}}</span>
                        <span class="disabled" style="overflow: hidden; text-overflow: ellipsis;">
                            {{model.selectedExpirationType == expirationType.ONE_DAY ? collaborateOneDayLink : collaborateOneWeekLink}}
                        </span>
                    </label>

                    <label class="label-share" style="width: 100%; justify-content: center;" role="radio" ng-class="{'selected_share_url': (model.selectedExpirationType === expirationType.ONE_DAY)}"
                        aria-checked="{{model.selectedExpirationType == expirationType.ONE_DAY}}" tabindex="0">
                        <input style="display: none;"  type="radio" name="collaborateLink" aria-label="{{'SHARE_SUBMENU.SHARE_LINK_DAY' | translate}}"
                            ng-checked="model.selectedExpirationType == expirationType.ONE_DAY"
                            ng-value="expirationType.ONE_DAY"
                            ng-model="model.selectedExpirationType">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_LINK_DAY' | translate}}</span>
                    </label>

                    <label class="label-share" style="width: 100%; justify-content: center;" role="radio" ng-class="{'selected_share_url': (model.selectedExpirationType == expirationType.ONE_WEEK)}"
                        aria-checked="{{model.selectedExpirationType == expirationType.ONE_WEEK}}" tabindex="0">
                        <input style="display: none;"  type="radio" name="collaborateLink" aria-label="{{'SHARE_SUBMENU.SHARE_LINK_WEEK' | translate}}"
                            ng-value="expirationType.ONE_WEEK"
                            ng-model="model.selectedExpirationType">
                        <span class="label-padding">{{'SHARE_SUBMENU.SHARE_LINK_WEEK' | translate}}</span>
                    </label>
                </fieldset>

            </div>
            <div class="footer" style = "padding: 0px; text-align: left;">
                <button class="collaborateOffline share"
                    ng-click="copyCollaborateLink()"
                    ng-disabled="!collaborateLinkCreated"
                    tabindex="0">{{'SHARE_SUBMENU.SHARE_COPY_LINK' | translate}}</button>
            </div>
        </div>
    </div>
</div>