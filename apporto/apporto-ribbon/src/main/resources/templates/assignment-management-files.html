<div ng-class="{'dialog-middle':!ribbonService.assignmentManagementDialogVisible}">
  <div class="assignment-management-files dialog" >
    <div ng-show="!$root.assignmentSubmitLoader">
      <div class="menu-content">

        <!-- Breadcrumbs -->
        <div class="header breadcrumbs" ng-show="ribbonService.assignmentManagementDialogVisible">
          <div class="publish breadcrumb" ng-click="changeDirectory(filesystem.root)" tabindex="0">
            {{'DIALOGS.CLOUD_DESKTOP' | translate}}</div>
          <div class="publish breadcrumb" ng-repeat="file in getPath(filesystem.currentDirectory)">
            <div class="publish next"></div>
            <div class="publish directory" ng-click="changeDirectory(file)" tabindex="0">{{file.name}}</div>
          </div>
        </div>

        <!-- Scrollable body -->
        <div id="menuBody" class="menu-body">
          <div ng-if="!ribbonService.currFileExist" class="no-file-section">
            <p>{{'DIALOGS.ADD_FILES_TO_DESKTOP' | translate}}</p>
          </div>
          <file-browser ng-show="ribbonService.currFileExist" id="directory-list" client="client" filesystem="filesystem" multi-select="multiSelect"></file-browser>
        </div>
        <!-- <mmonitor ng-show="circleLoaderService.circleLoaderVisible" class="colorLoader"></mmonitor> -->
        <!-- Bottom line -->
        <div class="footer" ng-if="ribbonService.currFileExist">
          <button class="primary-btn" ng-show="ribbonService.assignmentManagementDialogVisible" ng-click="publishFile()"
            tabindex="0">{{$root.resubmitEnabled?"Re-submit":'DIALOGS.BUTTON_PUBLISH' | translate}}</button>
          <button ng-if="$root.resubmitEnabled" class="secondary-btn" ng-click="$root.disableResubmit()" ng-show="ribbonService.assignmentManagementDialogVisible"
            tabindex="0">Cancel</button>
        </div>
      </div>
    </div>

    <div ng-if="$root.assignmentSubmitLoader" class="submit-loader-container">
      <div class="submit-loader">
          <div class="submit-progress"></div>
      </div>
    </div>
  </div>
</div>