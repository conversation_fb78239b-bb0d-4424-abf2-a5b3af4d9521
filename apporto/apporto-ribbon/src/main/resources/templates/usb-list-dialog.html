<div class="dialog-middle">
    <div class="usb-list-dialog dialog" id="dlg-usb-list">
        <div>
            <div id="move-usb-dlg" class="usb-title">
                <h3 class="title-usb-list text-noselect">{{'USB.TITLE_USB_DEVICES' | translate}}</h3>
                <span class="close" ng-click="closeUSBListDialog()" tabindex="0">&times;</span>
            </div>

            <div class="usb-subtitle">
                <span class="title-usb-list text-noselect">{{'USB.SUBTITLE_USB_DEVICE' | translate}}</span>
            </div>

            <div id="usb-form" style="width: 435px; margin: 20px auto 0px;">
                <select id="selected-usb" ng-model="selectedUSB" style="width: 60%;"
                    ng-options="usb.product_name for usb in usbList"
                    ng-disabled="connectionState == ConnectionState.MOUNTED" tabindex="0">
                </select>
                <div class="fetch-usb-list">
                    <button class="btn-fetch refresh" title="{{'USB.REFRESH_DEVICE_LIST' | translate}}" ng-click="refreshDeviceList()"
                        ng-disabled="connectionState != ConnectionState.CONNECTED" tabindex="0"></button>
                </div>
            </div>

            <div class="status-form">
                <span ng-class="{'usb-connect-status': !usbErrorState, 'usb-disconnect-status': usbErrorState}">{{usbStatus}}</span>
            </div>
        </div>
        <div class="usb-dlg-footer">
            <button class="tClose button" style="border-radius: 6px;  width: 6em; height: 2em;"
                ng-click="closeUSBListDialog()" tabindex="0">{{'USB.BUTTON_CLOSE' | translate}}</button>
            <button class="tClose button" style="border-radius: 6px;  width: 6em; height: 2em;"
                ng-click="mountUSB()" ng-disabled="!selectedUSB" tabindex="0">
                {{connectionState === ConnectionState.MOUNTED ? 'USB.BUTTON_UNMOUNT' : 'USB.BUTTON_MOUNT' | translate}}</button>
        </div>
    </div>
</div>
