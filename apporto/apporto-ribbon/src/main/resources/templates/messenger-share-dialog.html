<div class="status-middle">
    <div id="share_confirm_dialog" class="notification error add_padding">
        <button id="share_close_icon" ng-class="(loadingProfile) ? 'disable-close' : ''" class="enable-close" ng-click="!loadingProfile && closeDialog()" tabindex="0" aria-label="Close">&times;</button>

        <div id="share_dialog_body" class="body">
            <div class="text confirm_text" ng-if="serviceShare.state != serviceShare.SHARING">{{'SHARE_SUBMENU.SHARE_CONFIRM_SEND_MESSAGE' | translate}} 
                <div id="share_screen_username">
                    {{serviceShare.user.name}}
                </div>
                ?</div>
            <p class="text" ng-if="serviceShare.state == serviceShare.SHARING">{{'SHARE_SUBMENU.SHARE_STOP_SEND_MESSAGE'
                | translate}}</p>

            <div style="margin-top: 24px;" ng-if="serviceShare.state != serviceShare.SHARING && ribbonService.messengerShareDialogVisible">
                <label style="display: flex !important; gap: 8px; align-items: center;" class="label-share center ">
                    <input id="share_checkbox" type="checkbox" ng-model="isRemoteControl" name="isRemoteControl"
                        ng-change="toggleRemoteControl()"
                        ng-disabled="ribbonService.highlightActivated || ribbonService.shareLinkCopied || serviceShare.state != serviceShare.NOT_SHARE">
                    <span>{{'SHARE_SUBMENU.SHARE_ENABLE_REMOTE_CONTROL' | translate}}</span>
                </label>
            </div>
        </div>

        <div class="buttons share_btns">
            <button id="share_close_btn" ng-click="btnCloseWait()" class="tClose" ng-if="serviceShare.state == serviceShare.WAITING"
                tabindex="0">{{'SHARE_SUBMENU.SHARE_CANCEL' | translate}}</button>
            <button  id="share_close_btn" ng-click="!loadingProfile && btnClose()" class="fClose" ng-if="serviceShare.state != serviceShare.WAITING"
                tabindex="0">{{'SHARE_SUBMENU.SHARE_CANCEL' | translate}}</button>
            <button id="share_screen_btn" ng-click="btnShareScreen()" class="tClose" ng-if="serviceShare.state != serviceShare.WAITING"
                ng-disabled="serviceShare.state == serviceShare.WAITING" tabindex="0">{{serviceShare.state ==
                serviceShare.NOT_SHARE ? 'SHARE_SUBMENU.SHARE_MY_SCREEN' : (serviceShare.state == serviceShare.WAITING ?
                'SHARE_SUBMENU.SHARE_WAIT_ACCEPT' : 'SHARE_SUBMENU.SHARE_STOP_MY_SCREEN') | translate}}</button>
            <button id="share_screen_btn" ng-click="btnShareScreen()" class="fClose" ng-if="serviceShare.state == serviceShare.WAITING"
                ng-disabled="serviceShare.state == serviceShare.WAITING" tabindex="0">{{serviceShare.state ==
                serviceShare.NOT_SHARE ? 'SHARE_SUBMENU.SHARE_MY_SCREEN' : (serviceShare.state == serviceShare.WAITING ?
                'SHARE_SUBMENU.SHARE_WAIT_ACCEPT' : 'SHARE_SUBMENU.SHARE_STOP_MY_SCREEN') | translate}}</button>
        </div>
    </div>
</div>