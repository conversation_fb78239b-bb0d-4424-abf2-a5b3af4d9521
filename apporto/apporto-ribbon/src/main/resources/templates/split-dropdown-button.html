<div class="split-dropdown-button" >
  <button class="btn-file-browser split-button" ng-click="mainButtonClick()">File browser</button>
  <div class="dropdown-menu">
    <button class="dropdown-menu-button" ng-click="toggleDropdown()">
    </button>
    <div class="dropdown-menu-content" ng-show="ribbonService.showDropDown" ng-mouseleave="ribbonService.showDropDown=false">
      <div ng-repeat="item in menuItems" ng-click="menuItemClick($index)">{{item}}</div>
    </div>
  </div>
</div>