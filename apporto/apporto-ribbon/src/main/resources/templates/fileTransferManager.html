<div class="transfer-manager">

  <!-- File transfer manager header -->
  <div class="header">
    <h2 style="text-transform: none; color: #1A1A1A; font-size: 14px;">{{client.uploads.length}} {{ribbonService.fileTransferMessage | translate}}</h2>
    <button ng-click="clearCompletedTransfers()" class="tClose" tabindex="0">{{'CLIENT.ACTION_CLEAR_COMPLETED_TRANSFERS'
      | translate}}</button>
  </div>

  <!-- Sent/received files -->
  <div class="transfer-manager-body">
    <div class="transfers">
      <file-transfer transfer="upload" ng-repeat="upload in client.uploads">
      </file-transfer>
      <file-transfer transfer="download" ng-repeat="download in client.downloads">
      </file-transfer>
    </div>
  </div>

</div>