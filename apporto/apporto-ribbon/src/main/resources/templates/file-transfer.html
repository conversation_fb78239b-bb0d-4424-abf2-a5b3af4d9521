<div class="transfer" ng-class="{'in-progress': isInProgress(), 'savable': isSavable(), 'error': hasError()}"
  ng-click="save()" tabindex="0">

  <!-- Overall status of transfer -->
  <div class="transfer-status">
    <div>
      <button class="close-transfer" ng-click="abortTransfer(transfer.filename)" tabindex="0" aria-label="Close"
        ng-class="{'cancel-transfer' : getPercentDone() < 100, 'success-transfer' : getPercentDone() == 100}"></button>
    </div>
    <!-- Filename and progress bar -->
    <div class="filename">
      <div class="progress">
        <div ng-style="{'width': getPercentDone() + '%'}" class="bar"></div>
      </div>
      {{transfer.filename}}
    </div>

    <!-- Error text -->
    <p class="error-text">{{getErrorText() | translate}}</p>
  </div>

</div>