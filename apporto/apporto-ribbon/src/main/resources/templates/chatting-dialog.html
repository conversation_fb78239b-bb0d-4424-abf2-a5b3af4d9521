<div id="chatting-dialog">
  <!--CHAT-->
  <div id="controlbox" class="chatbox">
    <div id="effect" class="effect curve_border">
      <!--head-->
      <div class="chat-head controlbox-head">
        <div>
          <div role="none" class="avatar me_user">
            <div role="none" class="avatar-text">
              <div class="avatar-short-text">{{ribbonService.userinfo.initials}}</div>
            </div>
            <span ng-if="ribbonService.userinfo.state==0" class="fa fa-circle" 
              style="color: var(--chat-status-online); background-color: var(--chat-status-online);" 
              title="{{'MESSENGER.I_AM_ONLINE' | translate}}"></span>
            <span ng-if="ribbonService.userinfo.state==1" class="fa fa-minus-circle"
              style="color: var(--chat-status-busy); background-color: var(--chat-status-busy);" 
              title="{{'MESSENGER.I_AM_BUSY' | translate}}"></span>
            <span ng-if="ribbonService.userinfo.state==2" class="fa fa-circle" 
              style="color: var(--chat-status-away); background-color: var(--chat-status-away);" 
              title="{{'MESSENGER.I_AM_AWAY' | translate}}"></span>
            <span ng-if="ribbonService.userinfo.state==3" class="fa fa-circle"
              style="color: var(--chat-status-offline);" title="{{'MESSENGER.I_AM_OFFLINE' | translate}}"></span>
          </div>
        </div>
        <div id="show_status" class="user-state">
          <button id="status_change_btn" class="context-menu icon edit" ng-disabled="ribbonService.userinfo.state==3" 
            ng-click=openContext() ng-style="ribbonService.userinfo.state==3 && {'pointer-events': 'none'}" 
            tabindex="0" aria-label="Update your status" ng-keypress="($event.key === 'Enter') && ribbonService.userinfo.state!=3 && openContext()" aria-labelledby="changeStatusTooltip" aria-expanded="{{stateContextMenuVisible}}">
            <div id="changeStatusTooltip" class="chat-tooltip change-status-btn-tooltip" role="tooltip">Click to change Status</div>
          </button>
          <div class="dropdown-content show" id="status_drop_down_menu" ng-if="stateContextMenuVisible">
            <a ng-click="saveState('0')" tabindex="0" role="button">{{'MESSENGER.ONLINE' | translate}}</a>
            <a ng-click="saveState('1')" tabindex="0" role="button">{{'MESSENGER.DO_NOT_DISTURB' | translate}}</a>
            <a ng-click="saveState('2')" tabindex="0" role="button">{{'MESSENGER.AWAY' | translate}}</a>
          </div>
          <div id="status_drop_down" class="dropdown">
            <div role="none" class="username">
              <p id="userInfo_userName">{{ribbonService.userinfo.name}}</p>
            </div>
            <p ng-if="ribbonService.userinfo.state==0">{{'MESSENGER.ONLINE' | translate}}</p>
            <p ng-if="ribbonService.userinfo.state==1">{{'MESSENGER.BUSY' | translate}}</p>
            <p ng-if="ribbonService.userinfo.state==2">{{'MESSENGER.AWAY' | translate}}</p>
            <p ng-if="ribbonService.userinfo.state==3">{{'MESSENGER.OFFLINE' | translate}}</p>
          </div>
        </div>

        
    
        <button id="chat_minimized_btn" class="context-menu icon minus" 
        ng-click=minimizeChat() tabindex="0" aria-label="Minimize" aria-labelledby="minimizeTooltip">
          <div id="minimizeTooltip" class="chat-tooltip minimize-btn-tooltip" role="tooltip">Minimize</div>
        </button>
        <button id="chat_close_btn" ng-if="!ribbonService.isVirtualClassroom && ribbonService.licenses.hasMessengerLicence"
        class="context-menu icon times" ng-click=closeChat() tabindex="0" aria-label="Close" aria-labelledby="closeChatDialogTooltip">
          <div id="closeChatDialogTooltip" class="chat-tooltip close-chat-btn-tooltip" role="tooltip">Close Chat</div>  
        </button>
      </div>

      <div class="controlbox-panes" style="overflow-y: hidden; border-radius: 8px;">
        <div class="controlbox-pane scroll" style="max-height: 300px; min-height: 300px;">
          <div id="kurento-roster" class="controlbox-section">
            <div class="list-container roster-contacts">
              <div class="roster-group" ng-repeat="group in groups">
                <a style="display: flex; flex-direction: row-reverse; justify-content: space-between;" aria-colspan="" ng-click="groups[$index].isCollapsed = !groups[$index].isCollapsed"
                  class="list-toggle group-toggle controlbox-padded" title="Click to hide these contacts" tabindex="0" 
                  aria-expanded="{{!groups[$index].isCollapsed}}" ng-keydown="toggleChatGroup($event, $index)" role="button">
                  <span class="fa fa-angle-down"
                    ng-class="{'fa-angle-right': groups[$index].isCollapsed}">
                  </span> {{group.name}}</a>
                  <div class="line_gap_div"></div>
                <ul class="items-list roster-group-contacts"
                  ng-class="{'collapsed': groups[$index].isCollapsed, 'disabled': ribbonService.userinfo.state == 3}"
                  ng-modal="members" ng-style="ribbonService.userinfo.state == 3 && {'pointer-events': 'none'}">
                  <!-- <li ng-click="openChat('group', group)" class="list-item d-flex controlbox-padded both current-xmpp-contact">
                                        <a class="list-item-link cbox-list-item open-chat w-100 " title="Click to chat with {{group.name}}">
                                            <span class="fa fa-circle chat-status chat-status--online"
                                                title="This contact is online"></span>
                                            <span class="contact-name">{{group.name}} Group</span>
                                        </a>
                                    </li> -->
                  <li ng-repeat="member in ribbonService.groupMembers[group.name] track by $index"
                    ng-click="openChat(member, group)"
                    class="list-item d-flex controlbox-padded both current-xmpp-contact" tabindex="0" role="button">
                    <div style="display: flex;" title="Click to chat with {{member.name}}">
                      <div role="none" class="avatar other_users">
                        <div role="none" class="avatar-text" aria-hidden="true">
                          <div class="avatar-short-text">{{member.initials}}</div>
                        </div>
                        <span ng-if="member.state == '0'" class="fa fa-circle" style="color: var(--chat-status-online); background-color: var(--chat-status-online); "
                          title="{{'MESSENGER.THIS_CONTACT_IS_ONLINE' | translate}}"></span>
                        <span ng-if="member.state == '1'" class="fa fa-minus-circle"
                          style="color: var(--chat-status-busy); background-color: var(--chat-status-busy);"
                          title="{{'MESSENGER.THIS_CONTACT_IS_BUSY' | translate}}"></span>
                        <span ng-if="member.state == '2'" class="fa fa-circle" style="color: var(--chat-status-away); background-color: var(--chat-status-away);"
                          title="{{'MESSENGER.THIS_CONTACT_IS_AWAY' | translate}}"></span>
                        <span ng-if="member.state == '3'" class="fa fa-circle"
                          style="color: var(--chat-status-offline); background-color: var(--chat-status-offline);"
                          title="{{'MESSENGER.THIS_CONTACT_IS_OFFLINE' | translate}}"></span>
                      </div>
                      <div role="none" class="username">
                        <p ng-class="{'unread-msgs': member.isUnread}">{{member.name}}</p>
                      </div>
                    </div>
                    <div class="unread_mez_count_num" ng-if="member.unreadMezCount">
                     {{member.unreadMezCount}}
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>