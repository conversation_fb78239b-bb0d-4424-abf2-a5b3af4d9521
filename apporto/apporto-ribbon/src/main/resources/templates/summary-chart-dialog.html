<div class="dialog-middle">
  <div class="chart-dialog dialog">
    <button class="close sActivity" ng-click="closeDialog()" tabindex="0" aria-label="Close">&times;</button>
    <div class="beta-notice">
    </div>
    <h1 class="title-analytics">{{'CHARTS.ANALYTICS_TITLE' | translate}}</h1>
    <div id="chartCateg">
      <div style="cursor: pointer;" ng-click="toggleActivity()">Activity</div>
      <div style="cursor: pointer;" ng-click="toggleDuration()">Total time</div>
      <div class="chartUnderLine" ng-class="{onDurationCateg: chartState==='Duration'}"></div>
    </div>
    <div id="summaryChartLegend">
      <ul>
        <li ng-class="{inactive: summaryChart.data.datasets[0].hidden}" ng-click="toggleChart(0)" tabindex="0" ng-show="chartState==='Activity'">
          <canvas class="bar bar-red" width="40" height="15"></canvas>
          <span>{{'CHARTS.USER_ACTIVITY' | translate}}</span>
        </li>
        <li ng-class="{inactive: summaryChart.data.datasets[1].hidden}" ng-click="toggleChart(1)" tabindex="0" ng-show="chartState==='Duration'">
          <span class="line line-blue"></span>
          <span">{{'CHARTS.USER_DURATION' | translate}}</span>
        </li>
        <li ng-class="{inactive: summaryChart.data.datasets[2].hidden}" ng-click="toggleChart(2)" tabindex="0" ng-show="chartState==='Activity'">
          <canvas class="bar bar-blue" width="40" height="15"></canvas>
          <span">{{'CHARTS.GROUP_ACTIVITY' | translate}}</span>
        </li>
        <li ng-class="{inactive: summaryChart.data.datasets[3].hidden}" ng-click="toggleChart(3)" tabindex="0" ng-show="chartState==='Duration'">
          <span class="line line-dashed"></span>
          <span>{{'CHARTS.GROUP_DURATION' | translate}}</span>
        </li>
      </ul>
    </div>
    <div class="canvasParentElem">
      <canvas id="summaryChart"></canvas>
      <img class="canvasImg" alt="{{'CHARTS.ALT_TEXT' | translate}}">
    </div>
  </div>
</div>