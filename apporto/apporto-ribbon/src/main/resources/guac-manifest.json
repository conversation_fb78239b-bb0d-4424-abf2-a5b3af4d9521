{"guacamoleVersion": "1.0.0", "name": "Ribbon bar Guacamole extension", "namespace": "ribbon", "translations": ["translations/en.json", "translations/fr.json", "translations/de.json", "translations/es.json"], "js": ["apporto-ribbon.min.js", "components/Chart.bundle.min.js", "components/intro.min.js", "components/sha256.js", "components/adapter.js", "components/EventEmitter.js", "components/angular-intro.min.js", "components/kurento-utils.js", "components/kurento-jsonrpc.js", "components/KurentoRoom.js", "components/web-notification.js", "components/angular-web-notification.js", "components/simplewebsocket.min.js"], "css": ["apporto-ribbon.min.css"], "resources": {"templates/ribbon-bar.html": "text/html", "templates/ribbon-share-dialog.html": "text/html", "templates/messenger-share-dialog.html": "text/html", "templates/file-explorer.html": "text/html", "templates/assignment-management-files.html": "text/html", "templates/summary-chart-dialog.html": "text/html", "templates/snapshot-manager-dialog.html": "text/html", "templates/circle-loader.html": "text/html", "templates/file-details.html": "text/html", "templates/mmonitor.html": "text/html", "templates/vm-manager-dialog.html": "text/html", "templates/chatting-dialog.html": "text/html", "templates/chatting-room.html": "text/html", "templates/call-dialog.html": "text/html", "templates/network-quality-dialog.html": "text/html", "templates/upload-instruction.html": "text/html", "templates/setting-popup.html": "text/html", "templates/remote-apps.html": "text/html", "templates/file-transfer.html": "text/html", "templates/fileTransferManager.html": "text/html", "templates/client.html": "text/html", "templates/404.html": "text/html", "templates/help-center-popup.html": "text/html", "templates/assignment-management-dialog.html": "text/html", "templates/enable-logging-dialog.html": "text/html", "templates/about-apporto-dialog.html": "text/html", "templates/many-connection.html": "text/html", "templates/usb-list-dialog.html": "text/html", "templates/split-dropdown-button.html": "text/html", "templates/file-browser-external.html": "text/html", "templates/portia-chatbot.html": "text/html", "templates/in-page.html": "text/html", "images/upload.png": "image/png", "images/icons8-mute.png": "image/png", "images/arrow_down.png": "image/png", "images/network-indicator-nw.png": "image/png", "images/download.png": "image/png", "images/share.png": "image/png", "images/upload-black.png": "image/png", "images/download-black.png": "image/png", "images/share-black.png": "image/png", "images/chat-black.png": "image/png", "images/circle-arrows.png": "image/png", "images/circle-arrows-blue.png": "image/png", "images/circle-lightblue.png": "image/png", "images/file-browser.png": "image/png", "images/file-explorer.png": "image/png", "images/file-explorer-black.png": "image/png", "images/drive.png": "image/png", "images/folder.png": "image/png", "images/folder-black.png": "image/png", "images/keyboard-black.png": "image/png", "images/keyboard-black-32x32.png": "image/png", "images/apporto-logo.png": "image/png", "images/file-upload.png": "image/png", "images/file-download.png": "image/png", "images/next.png": "image/png", "images/speaker.png": "image/png", "images/mute.png": "image/png", "images/settings.png": "image/png", "images/folder-grey.png": "image/png", "images/network.png": "image/png", "images/plus_icon.png": "image/png", "images/cloudMounter.png": "image/png", "images/classroom-mandatory.png": "image/png", "images/expand.png": "image/png", "images/vm.png": "image/png", "images/share-cl.png": "image/png", "images/side-menu.png": "image/png", "images/backup_restore.png": "image/png", "images/session-on-off.png": "image/png", "images/presenter.png": "image/png", "images/microphone.png": "image/png", "images/mute-microphone.png": "image/png", "images/close-transfer.png": "image/png", "images/attendance.png": "image/png", "images/sort.png": "image/png", "images/usb.png": "image/png", "images/groups.png": "image/png", "images/ads_click.png": "image/png", "images/groups_black.png": "image/png", "images/session-expire.png": "image/png", "images/arrow_left_alt.png": "image/png", "images/arrow_right_alt.png": "image/png", "images/logo_footer.png": "image/png", "images/logout.png": "image/png", "images/server_ready.png": "image/png", "images/chats/angle-arrow.png": "image/png", "images/chats/call-hang-up.png": "image/png", "images/chats/minus.png": "image/png", "images/chats/offline.png": "image/png", "images/chats/online.png": "image/png", "images/chats/send.png": "image/png", "images/chats/call.svg": "image/svg+xml", "images/chats/present_to_all.svg": "image/svg+xml", "images/chats/arrow_drop_down.svg": "image/svg+xml", "images/chats/minimize.svg": "image/svg+xml", "images/chats/close.svg": "image/svg+xml", "images/favicons/apple-icon-114x114.png": "image/png", "images/favicons/apple-icon-120x120.png": "image/png", "images/favicons/apple-icon-144x144.png": "image/png", "images/favicons/apple-icon-152x152.png": "image/png", "images/favicons/apple-icon-180x180.png": "image/png", "images/favicons/apple-icon-57x57.png": "image/png", "images/favicons/apple-icon-60x60.png": "image/png", "images/favicons/apple-icon-72x72.png": "image/png", "images/favicons/apple-icon-76x76.png": "image/png", "images/favicons/android-icon-192x192.png": "image/png", "images/favicons/favicon-16x16.png": "image/png", "images/favicons/favicon-32x32.png": "image/png", "images/favicons/favicon-96x96.png": "image/png", "images/favicons/favicon.ico": "image/png", "images/favicons/manifest.json": "image/png", "images/favicons/ms-icon-144x144.png": "image/png", "images/classroom-optional.png": "image/png", "images/hand-off.png": "image/png", "images/hand-on.png": "image/png", "images/mic.svg": "image/svg+xml", "images/presenter_mode.svg": "image/svg+xml", "images/co_present.svg": "image/svg+xml", "images/electric.svg": "image/svg+xml", "images/clipboard.svg": "image/svg+xml", "images/camera.svg": "image/svg+xml", "images/fullscreen.svg": "image/svg+xml", "images/backup.svg": "image/svg+xml", "images/cloud_download.svg": "image/svg+xml", "images/restore_snap.svg": "image/svg+xml", "images/snapshot_save.svg": "image/svg+xml", "images/shareScreenIcon.svg": "image/svg+xml", "images/chat_bubble.svg": "image/svg+xml", "images/bar_chart_4_bars.svg": "image/svg+xml", "images/second_monitor.svg": "image/svg+xml", "images/volume_up.svg": "image/svg+xml", "images/settings.svg": "image/svg+xml", "images/remote_apps.svg": "image/svg+xml", "images/remote_apps_listview.svg": "image/svg+xml", "images/remote_apps_gridview.svg": "image/svg+xml", "images/remote_apps_search.svg": "image/svg+xml", "images/remote_apps_pin.svg": "image/svg+xml", "images/remote_apps_unpin.svg": "image/svg+xml", "images/help.svg": "image/svg+xml", "images/sd.svg": "image/svg+xml", "images/pointer-lock.svg": "image/svg+xml", "images/fullscreen_exit.svg": "image/svg+xml", "images/screenshot_monitor.svg": "image/svg+xml", "images/settings_backup_restore.svg": "image/svg+xml", "images/chats/send.svg": "image/svg+xml", "images/1share.png": "image/png", "images/presentation.png": "image/png", "images/fatal-dogs.gif": "image/gif", "images/cloud_download_icon.svg": "image/svg+xml", "images/check_circle.svg": "image/svg+xml", "images/close_icon.svg": "image/svg+xml", "images/share_close.svg": "image/svg+xml", "images/link.svg": "image/svg+xml", "images/classroom-madatory.svg": "image/svg+xml", "images/person.svg": "image/svg+xml", "images/chat.svg": "image/svg+xml", "images/assignment.svg": "image/svg+xml", "images/downloadFile.svg": "image/svg+xml", "images/layout.svg": "image/svg+xml", "images/requestControl.svg": "image/svg+xml", "images/threeDots.svg": "image/svg+xml", "images/raiseHand.svg": "image/svg+xml", "images/back_hand_white.svg": "image/svg+xml", "images/back_hand_black.svg": "image/svg+xml", "images/backup-restore.svg": "image/svg+xml", "images/assignmentLock.svg": "image/svg+xml", "images/assignmentSubmitted.svg": "image/svg+xml", "images/ai-icon.svg": "image/svg+xml", "components/simplewebsocket.min.js": "application/javascript", "styles/kurento.css": "text/css", "styles/webfonts/fa-brands-400.svg": "image/svg+xml", "styles/webfonts/fa-regular-400.svg": "image/svg+xml", "styles/webfonts/fa-solid-900.svg": "image/svg+xml", "images/save_snapshot.svg": "image/svg+xml", "images/restore_snapshot.svg": "image/svg+xml", "styles/webfonts/baumans.ttf": "font/ttf", "styles/webfonts/fa-brands-400.ttf": "font/ttf", "styles/webfonts/fa-regular-400.ttf": "font/ttf", "styles/webfonts/fa-solid-900.ttf": "font/ttf", "styles/webfonts/muli.ttf": "font/ttf", "styles/webfonts/fa-brands.eot": "application/vnd.ms-fontobject", "styles/webfonts/fa-regular-400.eot": "application/vnd.ms-fontobject", "styles/webfonts/fa-solid-900.eot": "application/vnd.ms-fontobject", "styles/webfonts/fa-brands-400.woff": "font/woff", "styles/webfonts/fa-regular-400.woff": "font/woff", "styles/webfonts/fa-solid-900.woff": "font/woff", "styles/webfonts/fa-brands-400.woff2": "font/woff2", "styles/webfonts/fa-regular-400.woff2": "font/woff2", "styles/webfonts/fa-solid-900.woff2": "font/woff2", "rings/calls_outgoing_ring.mp3": "audio/mpeg", "rings/calls_incoming_ring.mp3": "audio/mpeg", "workers/TimeTrackingWorker.js": "application/javascript"}}