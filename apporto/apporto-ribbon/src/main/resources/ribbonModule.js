/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
angular.module('ribbon', [
    'core',
    'angular-intro',
    'h264',
    'ngCookies'
]).
config(function() {

    var body_html ="<ribbon-bar ng-show=\"ribbonVisible && !fatalError\"></ribbon-bar>";
    document.body.insertAdjacentHTML('afterbegin', body_html);

    var font_load = "<link rel=\"stylesheet\" type=\"text/css\" href=\"//fonts.googleapis.com/css?family=Lato\" />";
    document.head.insertAdjacentHTML('afterbegin',font_load);

    var html_el = document.getElementsByTagName("html");
    html_el[0].setAttribute("ng-controller", "ribbonIndexController");
    html_el[0].setAttribute("role", "main");
    html_el[0].setAttribute("lang", "en");

    var body_el = document.getElementsByTagName("body");
    body_el[0].setAttribute("tabindex", "-1");

    var login_el = document.getElementsByTagName("guac-login");
    login_el[0].setAttribute("ng-show", "expectedCredentials && !loginHidden");

    var expired_session_html =
        "<div ng-show='notifyNoCredentials' class='login-ui'>" +
        "<div class='login-dialog-middle'>" +
        "<div class='logo'></div>" +
        "<div class='session-expire'>{{'CLIENT.SESSION_EXPIRED' | translate}}</div>" +
        "<div class='back-home'>{{'CLIENT.BACK_HOME' | translate}}</div>" +
        "<br><button class='back-home-btn' ng-click='goAppStore()'>" +
        "<img src='app/ext/ribbon/images/arrow_left_alt.png' alt='Back' class='btn-icon' />" +
        "{{'CLIENT.BACK_HOME_BTN' | translate}}</button>" +
        "<img src='app/ext/ribbon/images/logo_footer.png' alt='Apporto Logo' class='apporto-logo' />" +
        "</div>" +
        "</div>";
    document.body.insertAdjacentHTML('beforeend', expired_session_html);

    var fatal_page_html =
        "<div class='fatal-dogs'></div>" +
        "<p>{{'CLIENT.FATAL_SOMETHING_WENT_WRONG' | translate}} " +
        "<a href=\"mailto:{{ribbonService.licenses.supportEmail}}\">{{ribbonService.licenses.supportEmail}}</a>.</p>" +
        "<button class='back-home-btn' ng-click='goBackHome()' ng-if='$root.isKioskMode'>" +
        "<img src='app/ext/ribbon/images/arrow_left_alt.png' alt='Back' class='btn-icon' />" +
        "{{'CLIENT.BACK_HOME_BTN' | translate}}</button>" +
        "<br><div class='logo'></div>";

    var fatal_el = document.getElementsByClassName("fatal-page-error");
    fatal_el[0].innerHTML = fatal_page_html;

    //Add the empty button to load reconnect icon before the connection is lost
    var btn = document.createElement("button");
    btn.style.width = 0;
    btn.style.height = 0;
    btn.style.margin = 0;
    btn.style.padding = 0;
    btn.style.position = "absolute";
    btn.setAttribute("tabindex", "-1");
    btn.setAttribute("class", "reconnect");
    btn.setAttribute("aria-label", "Reconnect");
    document.body.appendChild(btn);

    /**
     * Prevents Firefox select all issue
     *
     * PT issues #148865191, #151329066
     */
    document.addEventListener("selectionchange", function() {
        unSelectViewPort();
    });

    document.onkeydown = function (e) {
        if (e.keyCode == 13) {
            document.activeElement.click();
        }
    }

    function unSelectViewPort() {
        // Skip IE, it does not support containsNode functions
        var IE = /*@cc_on!@*/false || !!document.documentMode;

        if (!IE) {
            var sel = window.getSelection();
            var can = document.getElementsByTagName("canvas");

            // Check if canvas is node; it may happen that it is still not in DOM (ng-if="!expectedCredentials")
            if (can.length >= 2 && can[2] != null && 'nodeType' in can[2] && sel.containsNode(can[2], true)) {
                sel.removeAllRanges();
            }
        }
    };

}).
run(['$rootScope', '$injector', function($rootScope, $injector) {

    var ClientIdentifier        = $injector.get('ClientIdentifier');
    var authenticationService   = $injector.get('authenticationService');
    var $routeParams            = $injector.get('$routeParams');
    var $timeout                = $injector.get('$timeout');
    var $http                   = $injector.get('$http');
    var ribbonService           = $injector.get('ribbonService');
    var $window                 = $injector.get('$window');
    var ua                      = navigator.userAgent;
    var serverProperties        = $injector.get('serverProperties');
    var $location               = $injector.get('$location');
    var $cookieStore            = $injector.get('$cookieStore');
    var $translate              = $injector.get('$translate');

    $translate('LINKS.HELP_CENTER_SUPPORT_EMAIL').then(function (value) {
        ribbonService.licenses.supportEmail = value;
    });

    $rootScope.$on('$routeChangeSuccess', function(event, current, previous) {

        if (current.$$route && current.$$route.bodyClassName === "classroom") {
            var body_html = angular.element(document.getElementsByTagName("ribbon-bar"));
            body_html.empty();
            body_html.remove();
            return;
        }

        if (!current.$$route || current.$$route.bodyClassName === "other") {
            $rootScope.ribbonVisible = false;
            ribbonService.ribbonActive = false;
            return;
        }

        if ($routeParams.hasOwnProperty("mm")) {
            ribbonService.isPrimaryScreen = false;
            ribbonService.licenses.hasClipboard = true;
            document.getElementsByClassName('btn-gear')[0].style.marginLeft = 'auto';
            return;
        }

        if ($routeParams.hasOwnProperty("key")) {
            var body_html = angular.element(document.getElementsByTagName("ribbon-bar"));
            body_html.empty();
            body_html.remove();
            ribbonService.ribbonActive = false;
            ribbonService.licenses.hasClipboard = true;
            return;
        }

        if ($routeParams.id && authenticationService.getDataSource()) {
            isKioskMode();
            setEnableOpusParam();
            getLicence();
            getServerCapacityThreshold();
            getRouterServerThreshold();
            // minimize();
        }

        serverProperties.getProperty("prevent-caps-lock")
        .then(function(property_value) {
            ribbonService.preventCapsLock = (/true/i).test(property_value);
        })
        .catch(function(reason) {
            console.warn("Property prevent-caps-lock will be requested once again from server in 10s");
        });
    });

    var linkOrigin;
    if (!$window.location.origin) {
        linkOrigin = $window.location.protocol + '//' + $window.location.hostname;
        linkOrigin += ($window.location.port ? (':' + $window.location.port) : '');
    }
    else {
        linkOrigin = $window.location.origin;
    }

    // Build base link
    var link = linkOrigin + $window.location.pathname + '#/';

    // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
    // try to get client after 10 seconds.
    $timeout(function() {
        if ($location.path().indexOf("classroom") > -1) {
            return;
        }

        if ($routeParams.hasOwnProperty("key")) {
            return;
        }

        if (!ribbonService.licenses.hasSnapshotsLicence && $routeParams.id) {
            setEnableOpusParam();
            getLicence();
            getServerCapacityThreshold();
            getRouterServerThreshold();
        }

        if ($routeParams.id && ribbonService.preventCapsLock == null) {
            console.log("Trying to get server property prevent-caps-lock once again....");
            serverProperties.getProperty("prevent-caps-lock")
            .then(function(property_value) {
                ribbonService.preventCapsLock = (/true/i).test(property_value);
                console.log("Server property prevent-caps-lock retrieved.");
            })
            .catch(function() {
                console.warn("Failed to retrieve prevent-capse-lock. No more retries.");
            });
        }
    }, 10 * 1000);

    function isKioskMode() {
        $rootScope.isKioskMode = Math.abs(window.innerWidth - screen.width) < 10 && 
                                Math.abs(window.innerHeight - screen.height) < 10;
    }

    /**
     * Function that retrieves licence information from server
     */
    function getLicence() {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var httpParameters = {
            token   : authenticationService.getCurrentToken(),
            id      : clientIdentifier.id
        };

        if(!isNaN(clientIdentifier.id)){
            $http({
                method  : 'GET',
                url     : 'api/session/ext/' + authenticationService.getDataSource() + '/licences',
                params  : httpParameters
            })
            .then(function(response) {
                ribbonService.licenses.hasClassroomLicence = response.data.hasClassroom;
                ribbonService.licenses.hasPresenterLicence = response.data.hasPresenter;
                ribbonService.licenses.hasUploadLicence = response.data.hasUpload;
                ribbonService.licenses.hasDownloadLicence = response.data.hasDownload;
                ribbonService.licenses.hasSharingLicence = response.data.hasSharing;
                ribbonService.licenses.hasAsyncCollaborationLicence = response.data.hasAsyncCollaboration;
                ribbonService.licenses.hasAnalyticsLicence = response.data.hasAnalytics;
                ribbonService.licenses.hasSnapshotsLicence = response.data.hasSnapshots;
                ribbonService.licenses.hasHighlightLicence = response.data.hasHighlight;
                ribbonService.licenses.hasMounterLicence = response.data.hasMounter;
                ribbonService.licenses.hasActivityTrackLicence = response.data.hasActivityTracker;
                ribbonService.licenses.hasClipboard = response.data.hasClipboard;
                ribbonService.licenses.hasClipboardIn = response.data.hasClipboardIn;
                ribbonService.licenses.hasClipboardOut = response.data.hasClipboardOut;
                ribbonService.licenses.hasRebootLicence = response.data.hasReboot;
                ribbonService.licenses.hasMMonitorLicence = response.data.hasMMonitor;
                ribbonService.licenses.hasBackupLicence = response.data.hasBackup;
                ribbonService.licenses.hasMacOSLicence = response.data.hasMacOS;
                ribbonService.licenses.hasLinuxLicence = response.data.hasLinux;
                ribbonService.licenses.hasH264Licence = response.data.hasH264;
                ribbonService.licenses.hasFileBrowserLicence = response.data.hasFileBrowser;
                ribbonService.licenses.hardWareAccelerated = response.data.supportH264;
                ribbonService.licenses.hasLTILicence = response.data.hasLTI;
                ribbonService.licenses.hasWatermarkLicence = response.data.hasWatermark;
                ribbonService.licenses.monitorCount = response.data.monitorCount;
                ribbonService.licenses.appname = response.data.appName;
                ribbonService.licenses.subdomain = response.data.subdomain;
                ribbonService.licenses.hasCameraLicence = response.data.hasCamera;
                ribbonService.licenses.hasStatisticsLicence = response.data.hasStatistics;
                ribbonService.licenses.hasSftpLicence = response.data.hasSftp;
                ribbonService.licenses.cloudUsername = response.data.cloudUsername;
                ribbonService.licenses.userId = response.data.userId;
                ribbonService.isSftpCheckNeeded = response.data.hasSftpCheckNeeded;

                ribbonService.isSftpAvailable = ribbonService.licenses.hasSftpLicence && ribbonService.licenses.hasUploadLicence && ribbonService.licenses.hasDownloadLicence;

                if (authenticationService.getDataSource() === 'encryptedurl-jdbc-shared'){
                    ribbonService.licenses.hasStatisticsLicence = false;
                }

                ribbonService.licenses.idleTimeout = response.data.idleTimeout;
                ribbonService.licenses.h264MaxResolution = response.data.h264MaxResolution;
                ribbonService.licenses.preventCapsLock = response.data.hasPreventCapsLock;
                ribbonService.licenses.cloudProvider = response.data.cloudProvider;
                ribbonService.licenses.ltiIssuer = response.data.ltiIssuer;
                ribbonService.licenses.ltiCourseName = response.data.ltiCourseName;
                ribbonService.licenses.latencyThreshold = response.data.latencyThreshold;
                ribbonService.licenses.latencyCheckInterval = response.data.latencyCheckInterval;
                ribbonService.licenses.latencyCheckCount = response.data.latencyCheckCount;
                if (response.data.industryType != '') {
                    ribbonService.licenses.industryType = response.data.industryType;
                }

                ribbonService.licenses.supportMenuName = response.data.supportMenuName;

                if(ribbonService.licenses.hasFileBrowserLicence){
                    console.log('Checking Status - filebrowser');
                    ribbonService.checkFileBrowserStatus();
                }

                ribbonService.getChatbotUrl();

                if (!response.data.supportLink) {
                    // It should be reflected if there are some changes in custom translation file
                    $translate('LINKS.HELP_CENTER_SUPPORT_REQUEST').then(function (value) {
                        if (value !== null && value !== ribbonService.licenses.supportLink)
                            ribbonService.licenses.supportLink = value;
                    });
                } else {
                    ribbonService.licenses.supportLink = response.data.supportLink;
                }

                if (!response.data.supportEmail) {
                    // It should be reflected if there are some changes in custom translation file
                    $translate('LINKS.HELP_CENTER_SUPPORT_EMAIL').then(function (value) {
                        if (value !== null && value !== ribbonService.licenses.supportEmail)
                            ribbonService.licenses.supportEmail = value;
                            $cookieStore.put("support-email", ribbonService.licenses.supportEmail);
                    });
                }
                else {
                    ribbonService.licenses.supportEmail = response.data.supportEmail;
                    $cookieStore.put("support-email", ribbonService.licenses.supportEmail);
                }

                ribbonService.licenses.userGuideMenuName = response.data.userGuideName;
                ribbonService.licenses.userGuideMenuLink = response.data.userGuideLink;

                ribbonService.licenses.hasUSBLicence = response.data.hasUSB;
                ribbonService.licenses.default_username = response.data.username;

                ribbonService.licenses.extendedCompute = response.data.extendedCompute;
                ribbonService.licenses.remainingTime = response.data.remainingTime;
                ribbonService.licenses.hostName = response.data.hostName;
                ribbonService.licenses.rdpPort = response.data.rdpport;
                ribbonService.licenses.rdpFarmName = response.data.rdpfarmName;
                ribbonService.licenses.payloadType = response.data.payloadType;
                ribbonService.licenses.payloadVersion = response.data.payloadVersion;

                ribbonService.GLOBAL_ROOM_NAME = ribbonService.licenses.subdomain ? 'apporto/' + ribbonService.licenses.subdomain : 'apporto';

                $rootScope.usb_server_port = response.data.usbPort;

                if(/Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(ua)) {
                    ribbonService.licenses.hasMessengerLicence = false;
                }
                else {
                    ribbonService.licenses.hasMessengerLicence = response.data.hasMessenger;
                }

                ribbonService.licenses.rollbarAccessToken = response.data.rollbarAccessToken;

                ribbonService.licenses.launchFullScreen = response.data.launchFullScreen;

                ribbonService.licenses.usbDevices = response.data.usbDevices;
                ribbonService.licenses.remoteAppMode = response.data.remoteAppMode;

                ribbonService.licenses.hasRemoteApps = false;
                ribbonService.licenses.remoteApps = '';
                if (response.data.remoteApps !== '') {
                    try {
                        ribbonService.licenses.remoteApps = JSON.parse(response.data.remoteApps);
                        ribbonService.licenses.hasRemoteApps = true;
                    } catch (e) {
                        console.log("Error parsing remoteApps JSON: ", e);
                        console.log("remoteApps: ", response.data.remoteApps);
                    }
                }

                ribbonService.licenses.hasRemoteApps = false;
                ribbonService.licenses.remoteApps = '';
                if (response.data.remoteApps !== '') {
                    try {
                        ribbonService.licenses.remoteApps = JSON.parse(response.data.remoteApps);
                        ribbonService.licenses.hasRemoteApps = true;
                    } catch (e) {
                        console.log("Error parsing remoteApps JSON: ", e);
                        console.log("remoteApps: ", response.data.remoteApps);
                    }
                }

                // Configure the rollbar service with access token that get from properties file 
                Rollbar.configure({
                    accessToken: ribbonService.licenses.rollbarAccessToken
                });
                Rollbar.info("Configure successfully with access token that get from properties file.");

                $rootScope.$broadcast("setLicenses");
            })
            .catch(function(response) {
                console.log("Error getting licence info:", response.message);
            });
        }
    }

    /**
     * Get the checking server capacity thresholds
     */
    function getServerCapacityThreshold() {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        var httpParameters = {
            token   : authenticationService.getCurrentToken(),
            id      : clientIdentifier.id
        };
        var req = {
            method: 'GET',
            url: "api/session/ext/" + datasource + "/loadbalancer/capacity-threshold",
            params: httpParameters
        }
        $http(req).then(function(response) {
            ribbonService.max_hap_capacity_timeout = response.data.max_hap_capacity_timeout;
            ribbonService.max_allowed_failed_start = response.data.max_allowed_failed_start;
        })
        .catch(function(response) { });
    }

    /**
     * Get the RDP Router Serivce's thresholds
     */
    function getRouterServerThreshold() {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        var httpParameters = {
            token   : authenticationService.getCurrentToken(),
            id      : clientIdentifier.id
        };
        var req = {
            method: 'GET',
            url: "api/session/ext/" + datasource + "/rdp-router/router-threshold",
            params: httpParameters
        }
        $http(req).then(function(response) {
            ribbonService.rdp_router_api_timeout = response.data.rdp_router_api_timeout;
            ribbonService.max_router_api_attempt_count = response.data.max_router_api_attempt_count;
            ribbonService.rdp_router_keep_alive_interval = response.data.rdp_router_keep_alive_interval;
            $rootScope.$broadcast('routerSession');
        })
        .catch(function(response) { });
    }

    $window.onerror = function () {
        ribbonService.firstStartMessenger = false;
    };

    /**
     * Set the "enable-audio-input-opus" param.
     */
    function setEnableOpusParam() {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);

        var httpParameters = {
            token: encodeURIComponent(authenticationService.getCurrentToken()),
            id: encodeURIComponent(clientIdentifier.id),
            enableOpus: ribbonService.browser.isChrome
        };

        var req = {
            method: 'POST',
            url: "api/session/ext/" + datasource + "/enable-opus",
            params: httpParameters
        };

        $http(req)
        .then(function () {
            console.debug("enable-audio-input-opus : ", ribbonService.browser.isChrome);
        })
        .catch(function () {
        })
        .finally(function () {
        });
    }

}]);

// Ensure the ribbon module is loaded along with the rest of the app
angular.module('index').requires.push('ribbon');
