/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define([], factory);
    } else if (typeof exports === 'object') {
        // Node. Does not work with strict CommonJS, but
        // only CommonJS-like environments that support module.exports,
        // like Node.
        module.exports = factory();
    } else {
        // Browser globals (root is window)
        root.TimeTrackingWorker = factory();
    }
}(this, function () {
    "use strict";
    
    const OWNER_IDLE_TIME = "IDLE_TIME";
    const MINUTE_UNIT = 60 * 1000; // 1 min
    var idleTimerID = null;
    var _idleTime = 0;

    function checkIdleTime() {
        _idleTime = _idleTime + 1;
        self.postMessage({ type: OWNER_IDLE_TIME, idleTime:_idleTime });
    }

    // Listen for the start request.
    self.addEventListener("message", // Startup.
    function handleMessageFromMainThread(e) {
        switch (e.data.type) {
            // For calculating idle time
            case OWNER_IDLE_TIME:
                if (e.data.startTimer) {
                    if (idleTimerID != null) {
                        this.clearInterval(idleTimerID);
                        idleTimerID = null;
                    }
        
                    _idleTime = 0;
                    idleTimerID = this.setInterval(checkIdleTime, MINUTE_UNIT);
                    self.postMessage({ type: OWNER_IDLE_TIME, idleTimeChecker:idleTimerID });
                }

                if (e.data.cancelTimer) {
                    if (idleTimerID != null) {
                        _idleTime = 0;
                        this.clearInterval(idleTimerID);
                        idleTimerID = null;
                    }
                }
                break;
            default:
                break;
        }

    }, {once: false});

}));
