/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Decorator that alters the behaviour of ManagedClient class.
 *
 * The following functionalities are altered:
 * - optimal height includes ribbon height in calculation
 * - clipboard integration
 */
angular.module('ribbon').config(
    ['$provide', function($provide) {

    $provide.decorator('ManagedClient', ['$delegate', '$rootScope', '$injector',
    function ManagedClientDecorator($delegate, $rootScope, $injector) {

        // Required types
        var ClientProperties       = $injector.get('ClientProperties');
        var ClientIdentifier       = $injector.get('ClientIdentifier');
        var ClipboardData          = $injector.get('ClipboardData');
        var ManagedClientState     = $injector.get('ManagedClientState');
        var ManagedClientThumbnail = $injector.get('ManagedClientThumbnail');
        var ManagedDisplay         = $injector.get('ManagedDisplay');
        var ManagedFilesystem      = $injector.get('ManagedFilesystem');
        var ManagedFileUpload      = $injector.get('ManagedFileUpload');
        var ManagedShareLink       = $injector.get('ManagedShareLink');

        // Required services
        var $document              = $injector.get('$document');
        var $q                     = $injector.get('$q');
        var $rootScope             = $injector.get('$rootScope');
        var $window                = $injector.get('$window');
        var authenticationService  = $injector.get('authenticationService');
        var connectionGroupService = $injector.get('connectionGroupService');
        var connectionService      = $injector.get('connectionService');
        var requestService         = $injector.get('requestService');
        var tunnelService          = $injector.get('tunnelService');
        var guacAudio              = $injector.get('guacAudio');
        var guacHistory            = $injector.get('guacHistory');
        var guacImage              = $injector.get('guacImage');
        var guacVideo              = $injector.get('guacVideo');
        var ribbonService          = $injector.get('ribbonService');
        var $http                  = $injector.get('$http');
        var mmonitorService        = $injector.get('mmonitorService');
        var infoService            = $injector.get('infoService');
        var $routeParams           = $injector.get('$routeParams');
        var $location              = $injector.get('$location');
        var guacNotification       = $injector.get('guacNotification');
        var $translate             = $injector.get('$translate');
        var $cookieStore           = $injector.get('$cookieStore');

        $rootScope.ribbonService       = $injector.get('ribbonService');
        $rootScope.watermark           = null;

        var displayedNetworkWarningMsg = false;
        var pingTimeout                = null;

        /**
         * Function to check current latency using a ping
         * @type Function
         */
        var checkPing                  = null;

        /**
         * Length of a stack to check average latency in last 10 seconds, and the stack
         * @type Number, Array
         */
        var MAX_LATENCY_STACK_LENGTH   = 5;
        var stackLatency               = [];

        /**
         * Times (s) to send a ping in the dialog and not in the dialog
         * @type Number
         */
        var PING_SECONDS_DIALOG        = 1;
        var PING_SECONDS_NORMAL        = 2;

        /**
         * Time (ms) to send a ping
         * @type Number
         */
        var PING_FREQUENCY             = PING_SECONDS_NORMAL * 1000;

        /**
         * Latency threshold (ms)
         * @type Number
         */
        var LIMIT_THRESHOLD            = 120;

        /**
         * Flag to check the first connect
         * @type Boolean
         */
        var isFirstConnect             = false;

        /**
         * All camera error codes handled and passed off for translation. Any error
         * code not present in this list will be represented by the "DEFAULT"
         * translation.
         */
        var CAMERA_ERRORS = {
            0x0070: true,
            0x0071: true,
            0x0072: true,
            0x0073: true
        };

        /**
         * The constant values to identify the operations that shows the progress dialog.
         */
        var PROGRESS_ON_COPY  = 1;

        /**
         * The minimum amount of time to wait between updates to the client
         * thumbnail, in milliseconds.
         *
         * @type Number
         */
        var THUMBNAIL_UPDATE_FREQUENCY = 5000;

        /**
         * Object which serves as a surrogate interface, encapsulating a Guacamole
         * client while it is active, allowing it to be detached and reattached
         * from different client views.
         *
         * @constructor
         * @param {ManagedClient|Object} [template={}]
         *     The object whose properties should be copied within the new
         *     ManagedClient.
         */
        var ManagedClient = function ManagedClient(template) {

            // Use empty object by default
            template = template || {};

            /**
             * The ID of the connection associated with this client.
             *
             * @type String
             */
            this.id = template.id;

            /**
             * The actual underlying Guacamole client.
             *
             * @type Guacamole.Client
             */
            this.client = template.client;

            /**
             * The tunnel being used by the underlying Guacamole client.
             *
             * @type Guacamole.Tunnel
             */
            this.tunnel = template.tunnel;

            /**
             * The display associated with the underlying Guacamole client.
             *
             * @type ManagedDisplay
             */
            this.managedDisplay = template.managedDisplay;

            /**
             * The name returned associated with the connection or connection
             * group in use.
             *
             * @type String
             */
            this.name = template.name;

            /**
             * The title which should be displayed as the page title for this
             * client.
             *
             * @type String
             */
            this.title = template.title;

            /**
             * The most recently-generated thumbnail for this connection, as
             * stored within the local connection history. If no thumbnail is
             * stored, this will be null.
             *
             * @type ManagedClientThumbnail
             */
            this.thumbnail = template.thumbnail;

            /**
             * The current clipboard contents.
             *
             * @type ClipboardData
             */
            this.clipboardData = template.clipboardData || new ClipboardData({
                type : 'text/plain',
                data : ''
            });

            /**
             * All uploaded files. As files are uploaded, their progress can be
             * observed through the elements of this array. It is intended that
             * this array be manipulated externally as needed.
             *
             * @type ManagedFileUpload[]
             */
            this.uploads = template.uploads || [];

            /**
             * All currently-exposed filesystems. When the Guacamole server exposes
             * a filesystem object, that object will be made available as a
             * ManagedFilesystem within this array.
             *
             * @type ManagedFilesystem[]
             */
            this.filesystems = template.filesystems || [];

            /**
             * All available share links generated for the this ManagedClient via
             * ManagedClient.createShareLink(). Each resulting share link is stored
             * under the identifier of its corresponding SharingProfile.
             *
             * @type Object.<String, ManagedShareLink>
             */
            this.shareLinks = template.shareLinks || {};

            /**
             * The number of simultaneous touch contacts supported by the remote
             * desktop. Unless explicitly declared otherwise by the remote desktop
             * after connecting, this will be 0 (multi-touch unsupported).
             *
             * @type Number
             */
            this.multiTouchSupport = template.multiTouchSupport || 0;

            /**
             * The current state of the Guacamole client (idle, connecting,
             * connected, terminated with error, etc.).
             *
             * @type ManagedClientState
             */
            this.clientState = template.clientState || new ManagedClientState();

            /**
             * Properties associated with the display and behavior of the Guacamole
             * client.
             *
             * @type ClientProperties
             */
            this.clientProperties = template.clientProperties || new ClientProperties();

        };

        /**
         * The mimetype of audio data to be sent along the Guacamole connection if
         * audio input is supported.
         *
         * @constant
         * @type String
         */
        $delegate.AUDIO_INPUT_MIMETYPE = 'audio/L16;rate=44100,channels=2';

        /**
         * Returns a promise which resolves with the string of connection
         * parameters to be passed to the Guacamole client during connection. This
         * string generally contains the desired connection ID, display resolution,
         * and supported audio/video/image formats. The returned promise is
         * guaranteed to resolve successfully.
         *
         * @param {ClientIdentifier} identifier
         *     The identifier representing the connection or group to connect to.
         *
         * @param {String} [connectionParameters]
         *     Any additional HTTP parameters to pass while connecting.
         *
         * @returns {Promise.<String>}
         *     A promise which resolves with the string of connection parameters to
         *     be passed to the Guacamole client, once the string is ready.
         */
        var getConnectString = function getConnectString(identifier, connectionParameters) {

            var deferred = $q.defer();

            // Get the device pixel ratio of the real display
            var devicePixelRatio = $window.devicePixelRatio || 1;
            var pixelDensity = 1;

            // If the current VM isn't based on the Windows system, it is not needed to use this resolution
            // feature because the NuoRDS and xrdp doesn't support the display-update feature on the Mac OSX
            // and Linux systems.
            // And, we need to use inner size of window. That's because the method for calculating desktop size
            // of Linux and Mac OSX systems are different from Windows system.
            if (ribbonService.licenses.hasMacOSLicence || ribbonService.licenses.hasLinuxLicence) {
                devicePixelRatio = 1;
            }

            // If browser zoom under 100 % when first loading, it's needed to set like this
            if ($window.devicePixelRatio < 1) {
                devicePixelRatio = 1;
            }

            // Calculate optimal width/height for display
            var optimal_dpi = pixelDensity * 96;
            var innerHeight = $window.innerHeight;

            if (encodeURIComponent(identifier.dataSource) === 'encryptedurl-jdbc' && ribbonService.licenses.hasStatisticsLicence) {
                innerHeight = innerHeight - ribbonService.STATISTICS_BAR_SIZE;
            }

            var optimal_width = $window.innerWidth * devicePixelRatio;
            var optimal_height = innerHeight * devicePixelRatio;

            if (ribbonService.licenses.hasH264Licence && (optimal_width * optimal_height) > ribbonService.licenses.h264MaxResolution) {

                optimal_width = Math.sqrt(ribbonService.licenses.h264MaxResolution * optimal_width / optimal_height);
                optimal_height = ribbonService.licenses.h264MaxResolution / optimal_width;

                if (optimal_width > ribbonService.H264_MAX_WIDTH) {
                    optimal_width = ribbonService.H264_MAX_WIDTH;
                    optimal_height = ribbonService.H264_MAX_WIDTH * optimal_height / optimal_width;
                }
                else if (optimal_height > ribbonService.H264_MAX_HEIGHT) {
                    optimal_height = ribbonService.H264_MAX_HEIGHT;
                    optimal_width = ribbonService.H264_MAX_HEIGHT * optimal_width / optimal_height;
                }

            }

            if(encodeURIComponent(identifier.dataSource) === 'encryptedurl-jdbc') {
                // Build base connect string
                var connectString =
                    "token="               + encodeURIComponent(authenticationService.getCurrentToken())
                    + "&GUAC_DATA_SOURCE=" + encodeURIComponent(identifier.dataSource)
                    + "&GUAC_ID="          + encodeURIComponent(identifier.id)
                    + "&GUAC_TYPE="        + encodeURIComponent(identifier.type)
                    + "&GUAC_WIDTH="       + Math.floor(optimal_width)
                    + "&GUAC_HEIGHT="      + Math.floor(optimal_height)
                    + "&GUAC_DPI="         + Math.floor(optimal_dpi)
                    + "&GUAC_WIDTH2="      + 0
                    + "&GUAC_HEIGHT2="     + 0
                    + "&GUAC_WIDTH3="      + 0
                    + "&GUAC_HEIGHT3="     + 0
                    + (connectionParameters ? '&' + connectionParameters : '');
                mmonitorService.sendMonitorsProperties($window.innerWidth, innerHeight, devicePixelRatio, "primary");
            }
            else {
                if ($routeParams.mm == "1") {
                    var connectString =
                        "token=" + encodeURIComponent(authenticationService.getCurrentToken())
                        + "&GUAC_DATA_SOURCE=" + encodeURIComponent(identifier.dataSource)
                        + "&GUAC_ID=" + encodeURIComponent(identifier.id)
                        + "&GUAC_TYPE=" + encodeURIComponent(identifier.type)
                        + "&GUAC_WIDTH=" + 0
                        + "&GUAC_HEIGHT=" + 0
                        + "&GUAC_DPI=" + Math.floor(optimal_dpi)
                        + "&GUAC_WIDTH2=" + Math.floor(optimal_width)
                        + "&GUAC_HEIGHT2=" + Math.floor(optimal_height)
                        + "&GUAC_WIDTH3=" + 0
                        + "&GUAC_HEIGHT3=" + 0
                        + (connectionParameters ? '&' + connectionParameters : '');
                    mmonitorService.sendMonitorsProperties($window.innerWidth, innerHeight, devicePixelRatio, "secondary");
                }
                else if ($routeParams.mm == "2") {
                    var connectString =
                        "token=" + encodeURIComponent(authenticationService.getCurrentToken())
                        + "&GUAC_DATA_SOURCE=" + encodeURIComponent(identifier.dataSource)
                        + "&GUAC_ID=" + encodeURIComponent(identifier.id)
                        + "&GUAC_TYPE=" + encodeURIComponent(identifier.type)
                        + "&GUAC_WIDTH=" + 0
                        + "&GUAC_HEIGHT=" + 0
                        + "&GUAC_DPI=" + Math.floor(optimal_dpi)
                        + "&GUAC_WIDTH2=" + 0
                        + "&GUAC_HEIGHT2=" + 0
                        + "&GUAC_WIDTH3=" + Math.floor(optimal_width)
                        + "&GUAC_HEIGHT3=" + Math.floor(optimal_height)
                        + (connectionParameters ? '&' + connectionParameters : '');
                    mmonitorService.sendMonitorsProperties($window.innerWidth, innerHeight, devicePixelRatio, "tertiary");
                }
                else {
                    var connectString =
                        "token=" + encodeURIComponent(authenticationService.getCurrentToken())
                        + "&GUAC_DATA_SOURCE=" + encodeURIComponent(identifier.dataSource)
                        + "&GUAC_ID=" + encodeURIComponent(identifier.id)
                        + "&GUAC_TYPE=" + encodeURIComponent(identifier.type)
                        + "&GUAC_WIDTH=" + 0
                        + "&GUAC_HEIGHT=" + 0
                        + "&GUAC_DPI=" + Math.floor(optimal_dpi);
                        + (connectionParameters ? '&' + connectionParameters : '');
                }
            }

            // Add audio mimetypes to connect string
            guacAudio.supported.forEach(function(mimetype) {
                connectString += "&GUAC_AUDIO=" + encodeURIComponent(mimetype);
            });

            // Add video mimetypes to connect string
            guacVideo.supported.forEach(function(mimetype) {
                connectString += "&GUAC_VIDEO=" + encodeURIComponent(mimetype);
            });

            // Add image mimetypes to connect string
            guacImage.getSupportedMimetypes().then(function supportedMimetypesKnown(mimetypes) {

                // Add each image mimetype
                angular.forEach(mimetypes, function addImageMimetype(mimetype) {
                    connectString += "&GUAC_IMAGE=" + encodeURIComponent(mimetype);
                });

                // Connect string is now ready - nothing else is deferred
                deferred.resolve(connectString);

            });

            return deferred.promise;

        };

        /**
         * API for catching unstable network connection
         */
        function checkNetworkQuality(id) {
            var clientIdentifier = ClientIdentifier.fromString(id);;
            var datasource = encodeURIComponent(clientIdentifier.dataSource);

            var httpParameters = {
                token: encodeURIComponent(authenticationService.getCurrentToken()),
                id: encodeURIComponent(clientIdentifier.id)
            };

            var req = {
                method: 'POST',
                url: "api/session/ext/" + datasource + "/quality/poor",
                params: httpParameters
            };

            $http(req)
            .then(function(response) {
            })
            .catch(function(response) {
                console.error("Error network ping: ", response.message);
            })
            .finally( function() {
            });
        }

        $rootScope.$watch('ribbonService.networkQualityDialogVisible', function (newValue) {
            clearTimeout(pingTimeout);
            if (newValue) {
                PING_FREQUENCY = PING_SECONDS_DIALOG * 1000;  // If the network meter is expanded, set the frequency as 1 second.
            }
            else {
                PING_FREQUENCY = PING_SECONDS_NORMAL * 1000; // If the network meter is not expanded, set the frequency as 30 seconds.
            }

            if (isFirstConnect) {
                checkPing();
            }
        });

        $rootScope.$on('mediumResolutionWarning', function mediumResolutionWarning() {
            var OK_ACTION = {
                name      : "APP.ACTION_ACKNOWLEDGE",
                callback  : function cancelCallback() {
                    guacNotification.showStatus(false);
                    ribbonService.streamingMode = ribbonService.streamingModeOptions[2];
                    $rootScope.$broadcast('StreamingMode');
                }
            };

            // Build array of available actions
            var actions = [OK_ACTION];

            guacNotification.showStatus({
                title   : 'CLIENT.DIALOG_HEADER_RESOLUTION_WARNING',
                text    : {
                    key : 'CLIENT.TEXT_QUERY_MEDIUM_RESOLUTION'
                } ,
                actions : actions
            });
        });

        /**
         * Requests the creation of a new audio stream, recorded from the user's
         * local audio input device. If audio input is supported by the connection,
         * an audio stream will be created which will remain open until the remote
         * desktop requests that it be closed. If the audio stream is successfully
         * created but is later closed, a new audio stream will automatically be
         * established to take its place. The mimetype used for all audio streams
         * produced by this function is defined by
         * ManagedClient.AUDIO_INPUT_MIMETYPE.
         *
         * @param {Guacamole.Client} client
         *     The Guacamole.Client for which the audio stream is being requested.
         */
        var requestAudioStream = function requestAudioStream(client) {

            // Create new audio stream, associating it with an AudioRecorder
            var stream = client.createAudioStream($delegate.AUDIO_INPUT_MIMETYPE);
            var recorder = Guacamole.AudioRecorder.getInstance(stream, $delegate.AUDIO_INPUT_MIMETYPE,
                                                                ribbonService.browser.isChrome);

            // If creation of the AudioRecorder failed, simply end the stream
            if (!recorder) {
                stream.sendEnd();
            }

            // Otherwise, ensure that another audio stream is created after this
            // audio stream is closed
            // else
                // recorder.onclose = requestAudioStream.bind(this, client);

        };

        /**
         * Creates a new ManagedClient, connecting it to the specified connection
         * or group.
         *
         * @param {String} id
         *     The ID of the connection or group to connect to. This String must be
         *     a valid ClientIdentifier string, as would be generated by
         *     ClientIdentifier.toString().
         *
         * @param {String} [connectionParameters]
         *     Any additional HTTP parameters to pass while connecting.
         *
         * @param {Boolean} isEnableH264
         *     The value of enable-h264 parameter to pass while connecting.
         *
         * @param {Boolean} isEnableMultiMonitor
         *     The value of enable-multimonitor parameter to pass while connecting.
         *
         * @returns {ManagedClient}
         *     A new ManagedClient instance which is connected to the connection or
         *     connection group having the given ID.
         */
        $delegate.getInstance = function getInstance(id, connectionParameters, isEnableH264 = false,
                                                     isEnableMultiMonitor = false) {

            var tunnel;
            var mediaTunnel;
            var h264Tunnel;
            var clipboardTunnel;
            // Local storage value to indicate that the resize-method was updated (from `display-update` to `reconnect`)
            $rootScope.updatedResizeMethod  = ClientIdentifier.fromString($routeParams.id).id + '-updatedResizeMethod';

            // If WebSocket available, try to use it.
            if ($window.WebSocket) {
                tunnel = new Guacamole.ChainedTunnel(
                    new Guacamole.WebSocketTunnel('websocket-tunnel')
                );

                mediaTunnel = new Guacamole.ChainedTunnel(
                    new Guacamole.WebSocketTunnel('websocket-media-tunnel')
                );

                h264Tunnel = new Guacamole.ChainedTunnel(
                    new Guacamole.WebSocketTunnel('websocket-h264-tunnel')
                );

                clipboardTunnel = new Guacamole.ChainedTunnel(
                    new Guacamole.WebSocketTunnel('websocket-clipboard-tunnel')
                )
            }
            // If no WebSocket, then use HTTP.
            else {
                $rootScope.$broadcast('WebSocketBroken');
                return;
            }

            // Reconnect when the H264 optimizations of RDP server are enabled and Hyperstream is working on the Firefox or Safari version lower than 17.0.
            function onCABACDetected () {
                $rootScope.$broadcast("onCABACDetected");
            }

            // Get new client instance
            var client = new Guacamole.Client(tunnel, mediaTunnel, h264Tunnel, clipboardTunnel, isEnableH264,
                                              isEnableMultiMonitor, onCABACDetected);

            // Associate new managed client with new client and tunnel
            var managedClient = new ManagedClient({
                id     : id,
                client : client,
                tunnel : tunnel,
                mediaTunnel: mediaTunnel,
                clipboardTunnel: clipboardTunnel
            });

            // Fire events for tunnel errors
            tunnel.onerror = function tunnelError(status) {
                $rootScope.$apply(function handleTunnelError() {
                    ManagedClientState.setConnectionState(managedClient.clientState,
                        ManagedClientState.ConnectionState.TUNNEL_ERROR,
                        status.code);
                });
            };

            // Pull protocol-specific information from tunnel once tunnel UUID is
            // known
            tunnel.onuuid = function tunnelAssignedUUID(uuid) {
                tunnelService.getProtocol(uuid).then(function protocolRetrieved(protocol) {
                    managedClient.protocol = protocol.name;
                    managedClient.forms = protocol.connectionForms;
                }, requestService.WARN);
            };

            // Update connection state as tunnel state changes
            tunnel.onstatechange = function tunnelStateChanged(state) {
                $rootScope.$evalAsync(function updateTunnelState() {

                    switch (state) {

                        // Connection is being established
                        case Guacamole.Tunnel.State.CONNECTING:
                            ManagedClientState.setConnectionState(managedClient.clientState,
                                ManagedClientState.ConnectionState.CONNECTING);
                            break;

                        // Connection is established / no longer unstable
                        case Guacamole.Tunnel.State.OPEN:
                            ManagedClientState.setTunnelUnstable(managedClient.clientState, false);
                            break;

                        // Connection has closed
                        case Guacamole.Tunnel.State.CLOSED:
                            ManagedClientState.setConnectionState(managedClient.clientState,
                                ManagedClientState.ConnectionState.DISCONNECTED);

                            clipboardTunnel.disconnect();
                            mediaTunnel.disconnect();
                            h264Tunnel.disconnect();
                            break;

                    }

                });
            };

            // Update connection state as media tunnel state changes
            mediaTunnel.onstatechange = function tunnelStateChanged(state) {
                $rootScope.$evalAsync(function updateTunnelState() {

                    switch (state) {

                        // Connection is established / no longer unstable
                        case Guacamole.Tunnel.State.OPEN:
                            mediaTunnel.sendMessage("", tunnel.uuid);

                            // Begin streaming audio input if possible
                            requestAudioStream(client);
                            break;

                        default:
                            break;

                    }

                });
            };

            // Update connection state as clipboard tunnel state changes
            clipboardTunnel.onstatechange = function tunnelStateChanged(state) {
                $rootScope.$evalAsync(function updateTunnelState() {

                    switch (state) {

                        // Connection is established / no longer unstable
                        case Guacamole.Tunnel.State.OPEN:
                            clipboardTunnel.sendMessage("", tunnel.uuid);
                            break;

                        default:
                            break;

                    }

                });
            };

            // Update connection state as h264 tunnel state changes
            h264Tunnel.onstatechange = function tunnelStateChanged(state) {
                $rootScope.$evalAsync(function updateTunnelState() {

                    switch (state) {

                        // Connection is established / no longer unstable
                        case Guacamole.Tunnel.State.OPEN:
                            h264Tunnel.sendMessage("", tunnel.uuid);
                            break;

                        default:
                            break;

                    }

                });
            };

            // Update connection state as client state changes
            client.onstatechange = function clientStateChanged(clientState) {
                $rootScope.$evalAsync(function updateClientState() {

                    switch (clientState) {

                        // Idle
                        case 0:
                            ManagedClientState.setConnectionState(managedClient.clientState,
                                ManagedClientState.ConnectionState.IDLE);
                            break;

                        // Ignore "connecting" state
                        case 1: // Connecting
                            break;

                        // Connected + waiting
                        case 2:
                            ManagedClientState.setConnectionState(managedClient.clientState,
                                ManagedClientState.ConnectionState.WAITING);
                            break;

                        // Connected
                        case 3:
                            if (!isFirstConnect) {
                                isFirstConnect = true;
                                clearTimeout(pingTimeout);

                                // Set the letency stack length & the interval & the threshold
                                MAX_LATENCY_STACK_LENGTH = ribbonService.licenses.latencyCheckCount;
                                PING_SECONDS_NORMAL = ribbonService.licenses.latencyCheckInterval;
                                PING_FREQUENCY = PING_SECONDS_NORMAL * 1000;
                                LIMIT_THRESHOLD = ribbonService.licenses.latencyThreshold;
                                checkPing();
                            }

                            ManagedClientState.setConnectionState(managedClient.clientState,
                                ManagedClientState.ConnectionState.CONNECTED);

                            // Send any clipboard data already provided
                            if (managedClient.clipboardData) {
                                $delegate.setClipboard(managedClient, managedClient.clipboardData);
                            }

                            // Update thumbnail with initial display contents
                            $delegate.updateThumbnail(managedClient);

                            // Connect the tunnels
                            clipboardTunnel.connect(client.data)
                            mediaTunnel.connect(client.data);
                            h264Tunnel.connect(client.data);

                            break;

                            // Update history when disconnecting
                        case 4: // Disconnecting
                        case 5: // Disconnected
                            $delegate.updateThumbnail(managedClient);
                            break;
                        case 6: // Server Closed
                            ManagedClientState.setConnectionState(managedClient.clientState,
                                ManagedClientState.ConnectionState.SERVER_CLOSED);
                            break;

                    }

                });
            };

            // Update connection state as client state changes
            client.onsessionidchange = function clientSessionidchanged(session_id) {
                $rootScope.session_id = session_id;

                // If current page is a normal shared screen, it is not needed to use this resolution feature
                if ($routeParams.key && !$routeParams.mm) {
                    return;
                }

                // If the current VM isn't based on the Windows system, it is not needed to use this resolution
                // feature because the NuoRDS and xrdp doesn't support the display-update feature on the Mac OSX
                // and Linux systems.
                if (ribbonService.licenses.hasMacOSLicence || ribbonService.licenses.hasLinuxLicence) {
                    return;
                }

                setTimeout(() => {
                    var scale = $cookieStore.get("scale");

                    if (!scale) {
                        ribbonService.scale = 0;
                        ribbonService.isAutoResize = true;
                        $rootScope.$broadcast('Resolution.MatchLocalDisplay');
                    }
                    else {
                        ribbonService.scale = scale;
                        ribbonService.isAutoResize = true;
                        $rootScope.$broadcast('Resolution.FixedScale');
                    }
                }, 3000);
            };

            // Update Server version
            client.onServerVersionData = function clientServerVersionData(serverVersionData) {
                try {
                    $rootScope.serverVersion = JSON.parse(serverVersionData);
                } catch (error) {
                    $rootScope.serverVersion = null;
                }
            };

            // Disconnect and update status when the client receives an error
            client.onerror = function clientError(status) {
                $rootScope.$apply(function handleClientError() {

                    // Disconnect, if connected
                    client.disconnect();

                    // Update state
                    ManagedClientState.setConnectionState(managedClient.clientState,
                        ManagedClientState.ConnectionState.CLIENT_ERROR,
                        status.code);

                });
            };

            // Automatically update the client thumbnail
            client.onsync = function syncReceived() {

                var thumbnail = managedClient.thumbnail;
                var timestamp = new Date().getTime();

                // Update thumbnail if it doesn't exist or is old
                if (!thumbnail || timestamp - thumbnail.timestamp >= THUMBNAIL_UPDATE_FREQUENCY) {
                    $rootScope.$apply(function updateClientThumbnail() {
                        $delegate.updateThumbnail(managedClient);
                    });
                }

            };

            // Handle any received clipboard data
            client.onclipboard = function clientClipboardReceived(stream, mimetype) {

                if (!ribbonService.licenses.hasClipboardOut)
                    return;

                var reader;

                // If the received data is text, read it as a simple string
                if (/^text\//.exec(mimetype)) {

                    reader = new Guacamole.StringReader(stream);

                    // Assemble received data into a single string
                    var data = '';
                    reader.ontext = function textReceived(text) {
                        data += text;
                    };

                    // Set clipboard contents once stream is finished
                    reader.onend = function textComplete() {
                        $rootScope.$apply(function updateClipboard() {
                            managedClient.clipboardData = new ClipboardData({
                                type : mimetype,
                                data : data
                            });
                        });
                    };

                }

                // Otherwise read the clipboard data as a Blob
                else {
                    reader = new Guacamole.BlobReader(stream, mimetype);
                    reader.onend = function blobComplete() {
                        $rootScope.$apply(function updateClipboard() {
                            managedClient.clipboardData = new ClipboardData({
                                type : mimetype,
                                data : reader.getBlob()
                            });

                            // Hide progress dialog
                            infoService.infoDialogVisible = false;
                        });
                    };
                }

            };

            // Handle the flag indicating to show/hide the progress dialog when copying
            // a big image from remote to client or printing a big data.
            client.onshowprogress = function clientShowProgress(flag, kind) {
                if (flag === 1) {
                    // Get the message by the kind of the operation
                    var message_key;
                    if (kind == PROGRESS_ON_COPY) {
                        message_key = "CLIENT.TEXT_CLIENT_STATUS_COPY_TO_CLIENT";
                    }
                    else {
                        message_key = "CLIENT.TEXT_CLIENT_STATUS_DEFAULT";
                    }

                    // Show progress dialog
                    $translate(message_key).then(function showMessage(msg) {
                        infoService.infoText = msg;
                        infoService.infoDialogVisible = true;
                        infoService.top = true;
                    });
                }
                else {
                    infoService.infoDialogVisible = false;
                }

            };

            // Handle the flag indicating to show the error message while
            // using camera device
            client.oncamera = function cameraErrorReceived(errorCode) {

                // Determine translation name of error
                var errorName = (errorCode in CAMERA_ERRORS) ?
                                    errorCode.toString(16).toUpperCase() : "DEFAULT";
                var key = "CLIENT.ERROR_CAMERA_" + errorName;

                // Show error message
                $translate(key).then(function showMessage(msg) {
                    infoService.infoText = msg;
                    infoService.infoDialogVisible = true;
                    infoService.top = true;
                });

            };

            // Initialize the size of 3 monitors
            var primaryWidth    = 0;
            var primaryHeight   = 0;
            var secondaryWidth  = 0;
            var secondaryHeight = 0;
            var tertiaryWidth   = 0;
            var tertiaryHeight  = 0;

            // Initialize the size of watermark canvas
            var watermarkWidth   = 0;
            var watermarkHeight  = 0;

            // When the browser is resized, draws the black area on the shared screen
            client.onmonitorsize = function monitorSizeReceived(monitor_id, width, height) {

                if (monitor_id == 0) {
                    primaryWidth = width;
                    primaryHeight = height;
                }
                else if (monitor_id == 1) {
                    secondaryWidth = width;
                    secondaryHeight = height;
                }
                else if (monitor_id == 2) {
                    tertiaryWidth = width;
                    tertiaryHeight = height;
                }

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                datasource = clientIdentifier.dataSource;

                // Draw Watermark
                // When Primary connection
                if (datasource === 'encryptedurl-jdbc') {
                    if (monitor_id == 0 && ribbonService.licenses.hasWatermarkLicence) {
                        drawWatermark(monitor_id, clientIdentifier.id);
                    }
                }
                // When Multi monitor connection
                else if ($routeParams.hasOwnProperty("mm") && $routeParams.wm) {
                    if ((monitor_id == 1 && $routeParams.mm == 1) || (monitor_id == 2 && $routeParams.mm == 2)) {
                        connectionService.getConnection(clientIdentifier.dataSource, clientIdentifier.id)
                            .then(function connectionRetrieved(connection) {
                                drawWatermark(monitor_id, connection.name, clientIdentifier.id);
                            });
                    }
                }
                // When Shared connection
                else {
                    if ($routeParams.wm) {
                        connectionService.getConnection(clientIdentifier.dataSource, clientIdentifier.id)
                            .then(function connectionRetrieved(connection) {
                                drawWatermark(monitor_id, connection.name, clientIdentifier.id);
                            });
                    }
                }

                setTimeout(function() {

                    if (primaryHeight >= secondaryHeight && primaryHeight >= tertiaryHeight) {
                        drawBlackArea(primaryWidth, secondaryHeight, secondaryWidth, primaryHeight-secondaryHeight);
                        drawBlackArea(primaryWidth + secondaryWidth, tertiaryHeight, tertiaryWidth, primaryHeight-tertiaryHeight);
                    }
                    else if (secondaryHeight >= primaryHeight && secondaryHeight >= tertiaryHeight) {
                        drawBlackArea(0, primaryHeight, primaryWidth, secondaryHeight-primaryHeight);
                        drawBlackArea(primaryWidth + secondaryWidth, tertiaryHeight, tertiaryWidth, secondaryHeight-tertiaryHeight);
                    }
                    else if (tertiaryHeight >= primaryHeight && tertiaryHeight >= secondaryHeight) {
                        drawBlackArea(0, primaryHeight, primaryWidth, tertiaryHeight-primaryHeight);
                        drawBlackArea(primaryWidth, secondaryHeight, secondaryWidth, tertiaryHeight-secondaryHeight);
                    }

                }, 2000);
            };

            // Indicate that the mmonitor is triggered after reconnecting
            var mmTrigered = false;
            // Update the resize method when a "resize_method" instruction is received
            client.onresizemethod = function resizeMethodReceived(resize_method) {
                ribbonService.resizeMethod = resize_method;
                
                if (mmTrigered) return;
                // If this local storage value is set, it means that the mmonitor was triggered previously and reconnected with `reconnect` resize-method
                // So, in this reconnected session, HS must open the second monitor automatically
                if ($window.localStorage.getItem($rootScope.updatedResizeMethod) != null) {
                    $rootScope.mmonitor2Win = [];
                    $rootScope.mmonitor3Win = [];
                    $window.localStorage.removeItem($rootScope.updatedResizeMethod);
                    document.querySelector(".btn-mmonitor").click();
                }
                mmTrigered = true;
            }

            // Update level of multi-touch support when known
            client.onmultitouch = function multiTouchSupportDeclared(layer, touches) {
                managedClient.multiTouchSupport = touches;
            };

            // Update title when a "name" instruction is received
            client.onname = function clientNameReceived(name) {
                $rootScope.$apply(function updateClientTitle() {
                    managedClient.title = name;
                });
            };

            // Handle any received files
            client.onfile = function clientFileReceived(stream, mimetype, filename) {
                if (mimetype == "application/pdf") {
                    tunnelService.printStream(tunnel.uuid, stream, mimetype, filename);
                }
                else {
                    tunnelService.downloadStream(tunnel.uuid, stream, mimetype, filename);
                }
            };

            // Handle any received filesystem objects
            client.onfilesystem = function fileSystemReceived(object, name) {
                $rootScope.$apply(function exposeFilesystem() {
                    managedClient.filesystems.push(ManagedFilesystem.getInstance(object, name));
                });
            };

            // Manage the client display
            managedClient.managedDisplay = ManagedDisplay.getInstance(client.getDisplay());

            // Connect the Guacamole client
            var clientIdentifier = ClientIdentifier.fromString(id);
            getConnectString(clientIdentifier, connectionParameters)
            .then(function connectClient(connectString) {
                client.connect(connectString);

                if (ribbonService.licenses.hasMacOSLicence || ribbonService.licenses.hasLinuxLicence) {
                    devicePixelRatio = 1;
                    client.getDisplay().setRibbonScale(-1);
                }
                else {
                    client.getDisplay().setRibbonScale(ribbonService.scale / 100);
                }
            });

            $rootScope.$on("setLicenses", function() {
                if(ribbonService.licenses.appname != "") {
                    managedClient.title = ribbonService.licenses.appname ;
                }
                else if ($routeParams.hasOwnProperty("mm")) {
                    if ($routeParams.mm == 1) {
                        managedClient.title = "Second-monitor";
                    }
                    if ($routeParams.mm == 2) {
                        managedClient.title = "Third-monitor";
                    }
                }
                else if($routeParams.hasOwnProperty("key")) {
                    managedClient.title = "Shared screen";
                }
                else {
                    managedClient.title = clientIdentifier.id;
                }
                managedClient.title += " | Apporto";
            });

            // If using a connection, pull connection name
            if (clientIdentifier.type === ClientIdentifier.Types.CONNECTION) {
                if($location.path().indexOf("classroom") > -1) {
                    return;
                }

                connectionService.getConnection(clientIdentifier.dataSource, clientIdentifier.id)
                .then(function connectionRetrieved(connection) {
                    managedClient.name = managedClient.title = connection.name;
                    if(ribbonService.licenses.appname != "") {
                        managedClient.title = ribbonService.licenses.appname ;
                    }
                    else if ($routeParams.hasOwnProperty("mm")) {
                        if ($routeParams.mm == 1) {
                            managedClient.title = "Second-monitor";
                        }
                        if ($routeParams.mm == 2) {
                            managedClient.title = "Third-monitor";
                        }
                    }
                    else if($routeParams.hasOwnProperty("key")) {
                        managedClient.title = "Shared screen";
                    }
                    else {
                        managedClient.title = connection.name;
                    }

                    managedClient.title += " | Apporto";
                }, requestService.WARN);
            }

            // If using a connection group, pull connection name
            else if (clientIdentifier.type === ClientIdentifier.Types.CONNECTION_GROUP) {
                connectionGroupService.getConnectionGroup(clientIdentifier.dataSource, clientIdentifier.id)
                .then(function connectionGroupRetrieved(group) {
                    managedClient.name = managedClient.title = group.name;
                }, requestService.WARN);
            }

            /**
             * API for testing ping
             */
            checkPing = function checkPing() {

                /**
                 * OK action processor in poor internet dialog
                 */
                var OK_ACTION = {
                    name      : 'DIALOGS.BUTTON_OK',
                    callback  : function okCallback() {
                        ribbonService.poorInternet = true;

                        // If the 2nd monitor is opened, close it
                        if ($rootScope.mmonitor2Win) {
                            for (var i = 0; i < $rootScope.mmonitor2Win.length; i++) {
                                if ($rootScope.mmonitor2Win[i] != null)
                                    $rootScope.mmonitor2Win[i].close();
                                $rootScope.mmonitor2Win.splice(i, 1);
                            }
                        }

                        // If the 3rd monitor is opened, close it
                        if ($rootScope.mmonitor3Win) {
                            for (var i = 0; i < $rootScope.mmonitor3Win.length; i++) {
                                if ($rootScope.mmonitor3Win[i] != null)
                                    $rootScope.mmonitor3Win[i].close();
                                $rootScope.mmonitor3Win.splice(i, 1);
                            }
                        }

                        ribbonService.streamingMode = ribbonService.streamingModeOptions[0];
                        $rootScope.$broadcast('Resolution.BestResponsiveness');
                        guacNotification.showStatus(false);
                        ribbonService.isCheckLowLatency = true;
                    }
                };

                /**
                 * Cancel action processor in poor internet dialog
                 */
                var NO_THANKS_ACTION = {
                    name     : 'DIALOGS.NO_THANKS',
                    callback : function cancelCallback() {
                        ribbonService.streamingMode = ribbonService.streamingModeOptions[2];
                        guacNotification.showStatus(false);
                        ribbonService.isCheckLowLatency = true;
                    }
                };

                clearTimeout(pingTimeout);
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                if (datasource === 'encryptedurl-jdbc-shared')
                    return;

                var currentTime = new Date().getTime();
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id),
                    t: currentTime
                };
                var req = {
                    method: 'HEAD',
                    url: '/',
                    params: httpParameters
                };

                $http(req)
                .then(function() {})
                .catch(function() {
                    console.log("api calling is failed.");
                })
                .finally(function() {
                    var params = "?id=" + encodeURIComponent(clientIdentifier.id);
                    params = params + "&t=" + currentTime;
                    params = params + "&token=" + encodeURIComponent(authenticationService.getCurrentToken());

                    var pingUrl = $window.location.protocol + "//" + $window.location.host + "/" + params;
                    var pingTime = performance.getEntriesByName(pingUrl);

                    if (pingTime.length > 0) {
                        var difference = Math.round(pingTime[0].responseEnd - pingTime[0].requestStart);

                        // Fill a stack for latency values
                        stackLatency.push(difference);
                        if(stackLatency.length > MAX_LATENCY_STACK_LENGTH) {
                            stackLatency.shift();
                        }

                        // Set average latency
                        var max = 0, sum = 0;
                        if (stackLatency.length == MAX_LATENCY_STACK_LENGTH) {
                            max = stackLatency[0];
                            sum = 0;

                            // Calculate sum of the latest MAX_LATENCY_STACK_LENGTH latency values
                            for (i = 0; i < stackLatency.length; i ++) {
                                sum += stackLatency[i];
                                if(stackLatency[i] > max)
                                    max = stackLatency[i];
                            }
                            sum -= max; // Not calculate a peak value

                            ribbonService.avgLatency = Math.round(sum / (MAX_LATENCY_STACK_LENGTH - 1));
                        }
                        else {
                            ribbonService.avgLatency = 0;
                        }

                        // Check if the poor network and trigger by user's decision
                        if (LIMIT_THRESHOLD < ribbonService.avgLatency && !ribbonService.poorInternet &&
                            !ribbonService.isCheckLowLatency) { // Checked poor from good
                            // Confirm switch request
                            guacNotification.showStatus({
                                title   : 'CLIENT.DIALOG_HEADER_POOR_INTERNET',
                                text    : {
                                    key : 'CLIENT.TEXT_QUERY_BEST_PERFORMANCE'
                                } ,
                                actions : [ NO_THANKS_ACTION, OK_ACTION ]
                            });

                        }

                        performance.clearResourceTimings();
                        setNetworkBar(difference);
                        managedClient.client.onSendLatencyMetrics(difference);
                    }

                    pingTimeout = setTimeout(function() {
                        if (ManagedClientState.isDisconnectState() == true) {
                            return;
                        }

                        checkPing();
                    }, PING_FREQUENCY);
                });
            }

            /**
             * Draw watermark
             */
            var drawWatermark = async function drawWatermark(monitor_id, sessionId, key) {
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(sessionId),
                    key: key
                };
                var req = {
                    method: 'GET',
                    url: 'api/session/ext/' + authenticationService.getDataSource() + '/watermark',
                    params: httpParameters
                };

                if (!$rootScope.watermark) {
                    var response = await $http(req);

                    if (response.data && response.data.type) {
                        $rootScope.watermark = response.data;
                    }
                    else {
                        console.log("Watermark api calling is failed.");
                        return;
                    }
                }

                var watermark = $rootScope.watermark;
                var display = managedClient.managedDisplay.display;
                var wmCanvas;
                var width = 0;
                var height = 0;

                if (!key) {
                    width = primaryWidth;
                    height = primaryHeight;
                }
                else if ($routeParams.hasOwnProperty("mm")) {
                    switch (monitor_id) {
                        case 0:
                            width = primaryWidth;
                            height = primaryHeight;
                            break;

                        case 1:
                            width = secondaryWidth;
                            height = secondaryHeight;
                            break;

                        case 2:
                            width = tertiaryWidth;
                            height = tertiaryHeight;
                            break;
                    }
                }
                else {
                    width = primaryWidth + secondaryWidth + tertiaryWidth;
                    height = Math.max(primaryHeight, secondaryHeight, tertiaryHeight);
                }

                if (width && height && (watermarkWidth != width || watermarkHeight != height)) {
                    watermarkWidth = width;
                    watermarkHeight = height;
                    if (watermark.type == ribbonService.WATERMARKTYPE.TEXT) {
                        wmCanvas = display.getDefaultLayer().getWaterMarkCanvas();

                        wmCanvas.width = width;
                        wmCanvas.height = height;

                        var wmContext = wmCanvas.getContext('2d');
                        wmContext.clearRect(0, 0, wmCanvas.width, wmCanvas.height);

                        if (watermark.semiTranparent)
                            wmContext.globalAlpha = 0.5;
                        else
                            wmContext.globalAlpha = 0.75;

                        // setup text for filling
                        var fontsize = 72;
                        if (watermark.size != "auto") {
                            fontsize = watermark.size;
                        }
                        else {
                            wmContext.font = fontsize + "px Lato";
                            wmContext.fillStyle = watermark.color;
                            // get the metrics with font settings
                            var metrics = wmContext.measureText(watermark.text);

                            fontsize = fontsize * (width / metrics.width) / 2;
                        }

                        wmContext.font = fontsize + "px Lato";
                        wmContext.fillStyle = watermark.color;

                        // get the metrics with font settings
                        var metrics = wmContext.measureText(watermark.text);
                        var wmWidth = metrics.width;
                        // height is font size
                        var wmHeight = fontsize;

                        // change the origin coordinate to the middle of the context
                        wmContext.translate(width / 2, height / 2);
                        // rotate the context (so it's rotated around its center)
                        if (watermark.layout === ribbonService.WATERMARKLAYOUT.DIAGONAL) {
                            wmContext.rotate(-Math.atan(height / width));
                        }

                        // as the origin is now at the center, just need to center the text
                        wmContext.fillText(watermark.text, -wmWidth / 2, wmHeight / 2);
                    }
                    else if (watermark.type == ribbonService.WATERMARKTYPE.PICTURE) {
                        wmCanvas = display.getDefaultLayer().getWaterMarkCanvas();
                        wmCanvas.width = width;
                        wmCanvas.height = height;

                        var wmContext = wmCanvas.getContext('2d');
                        wmContext.clearRect(0, 0, wmCanvas.width, wmCanvas.height);

                        var image = new Image();
                        image.onload = function () {
                            wmContext.imageSmoothingEnabled = true;
                            wmContext.clearRect(0, 0, wmCanvas.width, wmCanvas.height);

                            if (watermark.washout)
                                wmContext.globalAlpha = 0.5;
                            else
                                wmContext.globalAlpha = 0.75;

                            var imageWidth = image.width;
                            var imageHeight = image.height;
                            if (watermark.scale != "auto") {
                                imageWidth = imageWidth * watermark.scale / 100;
                                imageHeight = imageHeight * watermark.scale / 100;
                            }
                            else {
                                if (imageWidth >= imageHeight) {
                                    imageWidth = Math.max(width, image.width) / 2;
                                    imageHeight = imageHeight * (imageWidth / image.width);
                                }
                                else {
                                    imageHeight = Math.max(height, image.height) / 2;
                                    imageWidth = imageWidth * (imageHeight / image.height);
                                }
                            }

                            // change the origin coordinate to the middle of the context
                            wmContext.translate(width / 2, height / 2);
                            wmContext.drawImage(image, -imageWidth / 2, -imageHeight / 2, imageWidth, imageHeight);
                        }

                        image.src = watermark.picture;
                    }

                    if ($routeParams.hasOwnProperty("mm") && wmCanvas) {
                        if ($routeParams.mm == 1) {
                            wmCanvas.style.left = primaryWidth + 'px';
                        }
                        else if ($routeParams.mm == 2) {
                            wmCanvas.style.left = (primaryWidth + secondaryWidth) + 'px';
                        }
                    }
                }
            }

            function setNetworkBar(latency) {
                //Set the latency
                ribbonService.currentLatency = latency;

                // Ping time for the logging feature
                if (ribbonService.loggingEnabled)
                    window.measurePerformance += new Date().toISOString() + " : " + "Ping Time => " + ribbonService.currentLatency + `\n`;

                //Set the bandwidth
                ribbonService.averageBandwidth = Guacamole.Tunnel.averageBandwidth;

                if (ribbonService.currentLatency > ribbonService.poorThreshold) {
                    ribbonService.networkStatus = 0;
                    managedClient.clientState.tunnelUnstable = true;
                    if ($routeParams.hasOwnProperty("key")) {
                        return;
                    }

                    if (client && !displayedNetworkWarningMsg) {
                        //If the first poor network is detected in a session, notify to Drupal.
                        checkNetworkQuality(id);

                        //Show the ribbon dialog when the first poor network is detected in a sesseion.
                        $translate('CLIENT.POOR_NETWORK_UNSTABLE_STATUS').then(function setTitle(text) {
                            infoService.top = true;
                            infoService.infoText = text;
                            infoService.infoDialogVisible = true;
                            displayedNetworkWarningMsg = true;
                        });

                        setTimeout(function() {
                            infoService.infoDialogVisible = false;
                        }, ribbonService.thresholdInfo);
                    }
                }
                else {
                    if (ribbonService.currentLatency <= ribbonService.excellentThreshold) {
                        ribbonService.networkStatus = 5;
                    }
                    else if (ribbonService.currentLatency > ribbonService.excellentThreshold && ribbonService.currentLatency <= ribbonService.excellentMediumThreshold) {
                        ribbonService.networkStatus = 4;
                    }
                    else if (ribbonService.currentLatency > ribbonService.excellentMediumThreshold && ribbonService.currentLatency <= ribbonService.fairThreshold) {
                        ribbonService.networkStatus = 3;
                    }
                    else if (ribbonService.currentLatency > ribbonService.fairThreshold && ribbonService.currentLatency <= ribbonService.fairMediumThreshold) {
                        ribbonService.networkStatus = 2;
                    }
                    else if (ribbonService.currentLatency > ribbonService.fairMediumThreshold && ribbonService.currentLatency <= ribbonService.poorThreshold) {
                        ribbonService.networkStatus = 1;
                    }
                }

                ribbonService.arrayLatency.push(ribbonService.currentLatency);
                if (ribbonService.arrayLatency.length > 30) {
                    ribbonService.arrayLatency.shift();
                }
            }

            /**
             * Draws the black area on the shared screen
             *
             * @param {Number} left
             *     The left position of the black area
             *
             * @param {Number} top
             *     The top position of the black area
             *
             * @param {Number} width
             *     The width of the black area
             *
             * @param {Number} height
             *     The height of the black area
             */
            var drawBlackArea = function drawBlackArea(left, top, width, height) {
                if (!$routeParams.hasOwnProperty("mm") && $routeParams.hasOwnProperty("key")) {
                    var display = managedClient.managedDisplay.display;
                    var default_layer = display.getDefaultLayer();
                    var canvas = default_layer.getCanvas();
                    var ctx = canvas.getContext('2d');
                    ctx.fillStyle='black';
                    ctx.fillRect(left, top, width, height);
                }
            }

            return managedClient;

        };

        /**
         * Uploads the given file to the server through the given Guacamole client.
         * The file transfer can be monitored through the corresponding entry in
         * the uploads array of the given managedClient.
         *
         * @param {ManagedClient} managedClient
         *     The ManagedClient through which the file is to be uploaded.
         *
         * @param {File} file
         *     The file to upload.
         *
         * @param {ManagedFilesystem} [filesystem]
         *     The filesystem to upload the file to, if any. If not specified, the
         *     file will be sent as a generic Guacamole file stream.
         *
         * @param {ManagedFilesystem.File} [directory=filesystem.currentDirectory]
         *     The directory within the given filesystem to upload the file to. If
         *     not specified, but a filesystem is given, the current directory of
         *     that filesystem will be used.
         */
        $delegate.uploadFile = function uploadFile(managedClient, file, filesystem, directory) {

            // Use generic Guacamole file streams by default
            var object = null;
            var streamName = null;

            // If a filesystem is given, determine the destination object and stream
            if (filesystem) {
                object = filesystem.object;
                streamName = (directory || filesystem.currentDirectory).streamName + '/' + file.name;
            }

            // Start and manage file upload
            managedClient.uploads.push(ManagedFileUpload.getInstance(managedClient, file, object, streamName));

        };

        /**
         * Sends the given clipboard data over the given Guacamole client, setting
         * the contents of the remote clipboard to the data provided.
         *
         * @param {ManagedClient} managedClient
         *     The ManagedClient over which the given clipboard data is to be sent.
         *
         * @param {ClipboardData} data
         *     The clipboard data to send.
         */
        $delegate.setClipboard = function setClipboard(managedClient, data) {

            var writer;

            // Create stream with proper mimetype
            var stream = managedClient.client.createClipboardStream(data.type);

            // Send data as a string if it is stored as a string
            if (typeof data.data === 'string') {
                writer = new Guacamole.StringWriter(stream);
                writer.sendText(data.data);
                writer.sendEnd();
            }

            // Otherwise, assume the data is a File/Blob
            else {

                // Write File/Blob asynchronously
                writer = new Guacamole.ArrayBufferWriter(stream);
                writer.oncomplete = function clipboardSent() {
                    // Hide progress dialog
                    infoService.infoDialogVisible = false;

                    // End sending data
                    writer.sendEnd();
                };

                // Show progress dialog
                $translate("CLIENT.TEXT_CLIENT_STATUS_COPY_TO_REMOTE").then(function showMessage(msg) {
                    infoService.infoText = msg;
                    infoService.infoDialogVisible = true;
                    infoService.top = true;
                });

                // Begin sending data
                data.data.arrayBuffer().then(buffer => {
                    writer.sendData(buffer);
                });

            }

        };

        /**
         * Produces a sharing link for the given ManagedClient using the given
         * sharing profile. The resulting sharing link, and any required login
         * information, can be retrieved from the <code>shareLinks</code> property
         * of the given ManagedClient once the various underlying service calls
         * succeed.
         *
         * @param {ManagedClient} client
         *     The ManagedClient which will be shared via the generated sharing
         *     link.
         *
         * @param {SharingProfile} sharingProfile
         *     The sharing profile to use to generate the sharing link.
         *
         * @returns {Promise}
         *     A Promise which is resolved once the sharing link has been
         *     successfully generated, and rejected if generating the link fails.
         */
        $delegate.createShareLink = function createShareLink(client, sharingProfile) {

            // Retrieve sharing credentials for the sake of generating a share link
            var credentialRequest = tunnelService.getSharingCredentials(
                    client.tunnel.uuid, sharingProfile.identifier);

            // Add a new share link once the credentials are ready
            return credentialRequest;
        };

        /**
         * Returns whether the given ManagedClient is being shared. A ManagedClient
         * is shared if it has any associated share links.
         *
         * @param {ManagedClient} client
         *     The ManagedClient to check.
         *
         * @returns {Boolean}
         *     true if the ManagedClient has at least one associated share link,
         *     false otherwise.
         */
        $delegate.isShared = function isShared(client) {

            // The connection is shared if at least one share link exists
            for (var dummy in client.shareLinks) {
                return true;
            }

            // No share links currently exist
            return false;

        };

        /**
         * Store the thumbnail of the given managed client within the connection
         * history under its associated ID. If the client is not connected, this
         * function has no effect.
         *
         * @param {ManagedClient} managedClient
         *     The client whose history entry should be updated.
         */
        $delegate.updateThumbnail = function updateThumbnail(managedClient) {

            var display = managedClient.client.getDisplay();

            // Update stored thumbnail of previous connection
            if (display && display.getWidth() > 0 && display.getHeight() > 0) {

                // Get screenshot
                var canvas = display.flatten();

                // Calculate scale of thumbnail (max 320x240, max zoom 100%)
                var scale = Math.min(320 / canvas.width, 240 / canvas.height, 1);

                // Create thumbnail canvas
                var thumbnail = $document[0].createElement("canvas");
                thumbnail.width  = canvas.width * scale;
                thumbnail.height = canvas.height * scale;

                // Scale screenshot to thumbnail
                var context = thumbnail.getContext("2d");
                context.drawImage(canvas,
                    0, 0, canvas.width, canvas.height,
                    0, 0, thumbnail.width, thumbnail.height
                );

                // Store updated thumbnail within client
                managedClient.thumbnail = new ManagedClientThumbnail({
                    timestamp : new Date().getTime(),
                    canvas    : thumbnail
                });

                // Update historical thumbnail
                guacHistory.updateThumbnail(managedClient.id, thumbnail.toDataURL("image/png"));

            }
        }

        return $delegate;
    }]);
}]);

