/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Decorator that alters the behaviour of guacClient directive.
 * + Changes event listener on drop for displayContainer element
 */
angular.module('ribbon').config(
    [ '$provide', function($provide) {

        $provide.decorator('guacClientDirective', ['$delegate', '$injector', '$compile',
            function guacClientDecorator($delegate, $injector, $compile) {

                $delegate[0].controller = ['$scope', '$routeParams', '$timeout', '$injector', '$element',
                    function guacClientController($scope, $routeParams, $timeout, $injector, $element) {

                        // Required types
                        var ManagedClient         = $injector.get('ManagedClient');
                        var ManagedClientState    = $injector.get('ManagedClientState');

                        // Required services
                        var $window               = $injector.get('$window');
                        var $rootScope            = $injector.get('$rootScope');
                        var guacNotification      = $injector.get('guacNotification');

                        // Ribbon service
                        var ribbonService         = $injector.get('ribbonService');
                        $scope.ribbonService      = $injector.get('ribbonService');
                        var ClientIdentifier      = $injector.get('ClientIdentifier');
                        var mmonitorService       = $injector.get('mmonitorService');
                        var authenticationService = $injector.get('authenticationService');
                        var connectionService     = $injector.get('connectionService');
                        var $q                    = $injector.get('$q');
                        var $location             = $injector.get('$location');
                        var $http                 = $injector.get('$http');

                        var $interval             = $injector.get('$interval');

                        /**
                         * Whether the local, hardware mouse cursor is in use.
                         *
                         * @type Boolean
                         */
                        var localCursor = false;

                        /**
                         * The current Guacamole client instance.
                         *
                         * @type Guacamole.Client
                         */
                        var client = null;

                        /**
                         * The display of the current Guacamole client instance.
                         *
                         * @type Guacamole.Display
                         */
                        var display = null;

                        /**
                         * The element associated with the display of the current
                         * Guacamole client instance.
                         *
                         * @type Element
                         */
                        var displayElement = null;

                        /**
                         * The element which must contain the Guacamole display element.
                         *
                         * @type Element
                         */
                        var displayContainer = $element.find('.display')[0];

                        /**
                         * The main containing element for the entire directive.
                         *
                         * @type Element
                         */
                        var main = $element[0];

                        /**
                         * Guacamole mouse event object, wrapped around the main client
                         * display.
                         *
                         * @type Guacamole.Mouse
                         */
                        var mouse = new Guacamole.Mouse(displayContainer);

                        /**
                         * Guacamole absolute mouse emulation object, wrapped around the
                         * main client display.
                         *
                         * @type Guacamole.Mouse.Touchscreen
                         */
                        var touchScreen = new Guacamole.Mouse.Touchscreen(displayContainer);

                        /**
                         * Guacamole relative mouse emulation object, wrapped around the
                         * main client display.
                         *
                         * @type Guacamole.Mouse.Touchpad
                         */
                        var touchPad = new Guacamole.Mouse.Touchpad(displayContainer);

                        /**
                         * Guacamole touch event handling object, wrapped around the main
                         * client dislay.
                         *
                         * @type Guacamole.Touch
                         */
                        var touch = new Guacamole.Touch(displayContainer);

                        /**
                         * A flag that is set when the user action (mouse event, key event) is occurred.
                         *
                         * @type Number
                         */
                        var hasAction = false;

                        /**
                         * Set true when shift key is pressed.
                         *
                         * @type Boolean
                         */
                        var isShiftKey = false;

                        /**
                         * The timeout for checking sftp
                         */
                        var sftpTimeout = null;

                        /**
                         * Frequency value to send the user action (mouse event, key event).
                         *
                         * @type Number
                         */
                        const SEND_FREQ = 3 * 60 * 1000; // 3 mins

                        /**
                         * Frequency value to check sftp available
                         *
                         * @type Number
                         */
                        const CHECKING_FREQUENCY = 30 * 1000; // 30 seconds

                        /**
                         * Map of all currently pressed keys by keysym. If a particular key is
                         * currently pressed, the value stored under that key's keysym within this
                         * map will be true. All keys not currently pressed will not have entries
                         * within this map.
                         *
                         * @type Object.<Number, Boolean>
                         */
                        var keysCurrentlyPressed = {};

                        const ALT_KEYS = {0xFFE9 : true, 0xFFEA : true,
                                          0xFFE7 : true, 0xFFE8 : true};
                        const CTRL_KEYS  = {0xFFE3 : true, 0xFFE4 : true};

                        /**
                         * Set true If the focus is in the desktop
                         *
                         * @type Boolean
                         */
                        $rootScope.isRemoteDesktop = true;

                        /**
                         * If screen is shared mode, set this flag false
                         */
                        $scope.primaryScreen = true
                        if ($routeParams.key && !$routeParams.mm) {
                            $scope.primaryScreen = false;
                        }

                        /**
                         * Updates the scale of the attached Guacamole.Client based on current window
                         * size and "auto-fit" setting.
                         */
                        var updateDisplayScale = function updateDisplayScale() {

                            if (!display || !$scope.client.clientProperties.autoFit)
                                return;

                            if (ribbonService.resolution.width === -1 && ribbonService.resolution.height === -1) {
                                return;
                            }

                            var el = main.getElementsByClassName("layer-h264")[0];
                            var primaryWidth = (!!el) ? el.children[0].offsetWidth : display.getWidth();
                            var primaryHeight = (!!el) ? el.children[0].offsetHeight : display.getHeight();

                            if ($routeParams.hasOwnProperty("mm") && $routeParams.hasOwnProperty("key")) {
                                var devicePixelRatio = $window.devicePixelRatio || 1;
                                if ($window.devicePixelRatio < 1) {
                                    devicePixelRatio = 1;
                                }
                                $scope.client.clientProperties.scale = $scope.client.clientProperties.minScale = 1 / devicePixelRatio;
                                return;
                            }

                            // If the current VM is based on the Linux system, we need to maintain the scale to 1
                            // because xrdp doesn't support the display-update feature.
                            // $window.devicePixelRatio < 1 => browser zoom under 100 %
                            if (ribbonService.licenses.hasLinuxLicence || ($window.devicePixelRatio < 1)) {
                                $scope.client.clientProperties.scale = $scope.client.clientProperties.minScale = 1;
                                return;
                            }

                            $scope.client.clientProperties.scale = $scope.client.clientProperties.minScale = 1 / ($window.devicePixelRatio || 1);

                            // If the current VM is a MacOS VM, we need to calculate the scale because the NuoRDS doesn't support
                            // the display-update feature.
                            if ($routeParams.hasOwnProperty("key") || ribbonService.licenses.hasMacOSLicence) {
                                var el = main.getElementsByClassName("layer-h264")[0];
                                var primaryWidth = (!!el) ? el.children[0].offsetWidth : display.getWidth();
                                var primaryHeight = (!!el) ? el.children[0].offsetHeight : display.getHeight();

                                if(ribbonService.licenses.hasMMonitorLicence && primaryWidth && primaryHeight) {
                                    // Calculate scale to fit screen
                                    $scope.client.clientProperties.minScale = Math.min(
                                        main.offsetWidth  / Math.max(Math.min(primaryWidth, display.getWidth()),  1),
                                        main.offsetHeight / Math.max(Math.min(primaryHeight, display.getHeight()), 1)
                                    );
                                }
                                else {
                                    // Calculate scale to fit screen
                                    $scope.client.clientProperties.minScale = Math.min(
                                        main.offsetWidth  / Math.max(display.getWidth(),  1),
                                        main.offsetHeight / Math.max(display.getHeight(), 1)
                                    );
                                }

                                // Calculate appropriate maximum zoom level
                                $scope.client.clientProperties.maxScale = Math.max($scope.client.clientProperties.minScale, 3);

                                // Clamp zoom level, maintain auto-fit
                                if (display.getScale() < $scope.client.clientProperties.minScale || $scope.client.clientProperties.autoFit) {
                                    $scope.client.clientProperties.scale = $scope.client.clientProperties.minScale;
                                }
                                else if (display.getScale() > $scope.client.clientProperties.maxScale) {
                                    $scope.client.clientProperties.scale = $scope.client.clientProperties.maxScale;
                                }
                            }

                        };

                        /**
                         * Scrolls the client view such that the mouse cursor is visible.
                         *
                         * @param {Guacamole.Mouse.State} mouseState 
                         *     The current mouse state.
                         */
                        var scrollToMouse = function scrollToMouse(mouseState) {

                            // Determine mouse position within view
                            var mouse_view_x = mouseState.x + displayContainer.offsetLeft - main.scrollLeft;
                            var mouse_view_y = mouseState.y + displayContainer.offsetTop  - main.scrollTop;

                            // Determine viewport dimensions
                            var view_width  = main.offsetWidth;
                            var view_height = main.offsetHeight;

                            // Determine scroll amounts based on mouse position relative to document
                            var scroll_amount_x;
                            if (mouse_view_x > view_width) {
                                scroll_amount_x = mouse_view_x - view_width;
                            }
                            else if (mouse_view_x < 0) {
                                scroll_amount_x = mouse_view_x;
                            }
                            else {
                                scroll_amount_x = 0;
                            }

                            var scroll_amount_y;
                            if (mouse_view_y > view_height) {
                                scroll_amount_y = mouse_view_y - view_height;
                            }
                            else if (mouse_view_y < 0) {
                                scroll_amount_y = mouse_view_y;
                            }
                            else {
                                scroll_amount_y = 0;
                            }

                            // Scroll (if necessary) to keep mouse on screen.
                            main.scrollLeft += scroll_amount_x;
                            main.scrollTop  += scroll_amount_y;

                        };

                        /**
                         * Handles a mouse event originating from the user's actual mouse.
                         * This differs from handleEmulatedMouseEvent() in that the
                         * software mouse cursor must be shown only if the user's browser
                         * does not support explicitly setting the hardware mouse cursor.
                         *
                         * @param {Guacamole.Mouse.MouseEvent} event
                         *     The mouse event to handle.
                         */
                        var handleMouseEvent = function handleMouseEvent(event) {

                            // Do not attempt to handle mouse state changes if the client
                            // or display are not yet available
                            if (!client || !display)
                                return;

                            // Send mouse state, show cursor if necessary
                            display.showCursor(!localCursor);
                            client.sendMouseState(event.state, true);

                            // Increase the count when the mouse event is occurred
                            hasAction = true;
                        };

                        /**
                         * Handles a mouse event originating from one of Guacamole's mouse
                         * emulation objects. This differs from handleMouseState() in that
                         * the software mouse cursor must always be shown (as the emulated
                         * mouse device will not have its own cursor).
                         *
                         * @param {Guacamole.Mouse.MouseEvent} event
                         *     The mouse event to handle.
                         */
                        var handleEmulatedMouseEvent = function handleEmulatedMouseEvent(event) {

                            // Do not attempt to handle mouse state changes if the client
                            // or display are not yet available
                            if (!client || !display)
                                return;

                            event.stopPropagation();
                            event.preventDefault();

                            // Ensure software cursor is shown
                            display.showCursor(true);

                            // Send mouse state, ensure cursor is visible
                            scrollToMouse(event.state);
                            client.sendMouseState(event.state, true);

                            // Increase the count when the emulated mouse event is occurred
                            hasAction = true;
                        };

                        /**
                         * Handles a touch event originating from the user's device.
                         *
                         * @param {Guacamole.Touch.Event} touchEvent
                         *     The touch event.
                         */
                        var handleTouchEvent = function handleTouchEvent(event) {

                            // Do not attempt to handle touch state changes if the client
                            // or display are not yet available
                            if (!client || !display)
                                return;

                            event.preventDefault();

                            // Send touch state, hiding local cursor
                            display.showCursor(false);
                            client.sendTouchState(event.state, true);

                            // Increase the count when the touch event is occurred
                            hasAction = true;
                        };

                        /*
                         * This function sleeps in the specified duration in milliseconds
                         */
                        function sleep(ms) {
                            return new Promise(resolve => setTimeout(resolve, ms));
                        }

                        /*
                         * Check the sftp service until the sftp service is available.
                         */
                        var checkSftp = async function checkSftp() {

                            do {

                                if (($scope.client && $scope.client.clientState &&
                                    ($scope.client.clientState.connectionState == ManagedClientState.ConnectionState.DISCONNECTED ||
                                    $scope.client.clientState.connectionState == ManagedClientState.ConnectionState.CLIENT_ERROR ||
                                    $scope.client.clientState.connectionState == ManagedClientState.ConnectionState.TUNNEL_ERROR)) ||
                                    $rootScope.ribbonService.isIdleExpired) {
                                    console.log("The sftp service check stopped because the session was terminated.");
                                    break;
                                }

                                sftpTimeout = setTimeout(function() {
                                    checkSftpAvailable();
                                }, 0);

                                await sleep(CHECKING_FREQUENCY);

                                clearTimeout(sftpTimeout);

                            } while (!ribbonService.licenses.hasSftpLicence || !ribbonService.isSftpAvailable);

                        };

                        // Check the sftp available using `check-sftp-available` api
                        var checkSftpAvailable = function checkSftpAvailable() {

                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            var httpParameters = {
                                token   : authenticationService.getCurrentToken(),
                                id      : clientIdentifier.id
                            };

                            $http({
                                method  : 'GET',
                                url     : 'api/session/ext/' + authenticationService.getDataSource() + '/check-sftp-available',
                                params  : httpParameters
                            })
                            .then(function(response) {
                                if (response.data.sftp_available) {
                                    ribbonService.licenses.hasSftpLicence = true;
                                    ribbonService.isSftpAvailable = true;
                                    console.log("sftp is available now.");
                                }
                                else {
                                    console.warn("Error checking sftp available:", response.data.message);
                                }
                            })
                            .catch(function(response) {
                                console.error("Error checking sftp available:", response);
                            });

                        }

                        // Sends the sftp-connect instruction when Sftp license is true, Sftp Available is true and connection state is CONNECTED
                        $scope.$watchGroup([
                            'ribbonService.licenses.hasSftpLicence',
                            'ribbonService.isSftpAvailable',
                            'client.clientState.connectionState'], function() {

                                if ($scope.client && $scope.client.clientState
                                    && $scope.client.clientState.connectionState == ManagedClientState.ConnectionState.CONNECTED
                                    && ribbonService.licenses.hasSftpLicence && ribbonService.isSftpAvailable) {
                                    client.sendSftpConnect();
                                }

                        });

                        // Attach any given managed client
                        $scope.$watch('client', function attachManagedClient(managedClient) {

                            // Remove any existing display
                            displayContainer.innerHTML = "";

                            // Only proceed if a client is given
                            if (!managedClient)
                                return;

                            // Get Guacamole client instance
                            client = managedClient.client;

                            // Attach possibly new display
                            display = client.getDisplay();
                            var name_layer = document.createElement("span");
                            // Watch for changes on the cursor_name property
                            $scope.$watch(function() {
                                return $rootScope.ribbonService.cursor_name;
                            }, function(newValue) {
                                // If cursor_name is an empty string, hide the name_layer element
                                if (newValue === "") {
                                    name_layer.style.display = 'none';
                                } else {
                                    // If cursor_name has a value, show the name_layer element and update the text
                                    name_layer.style.display = '';
                                    name_layer.innerText = newValue; // Update the text with the new value
                                }
                            });
                            name_layer.style.fontFamily = "Lato";
                            name_layer.style.fontSize = "14px";
                            name_layer.style.fontWeight = "500";
                            name_layer.style.lineHeight = "14.4px";
                            name_layer.style.textAlign = "left";
                            name_layer.style.background = "#FFDC00";
                            name_layer.style.padding = "4px 8px";
                            name_layer.style.borderRadius = "4px";
                            name_layer.style.position = "absolute";
                            name_layer.style.color = "#1A1A1A";
                            name_layer.style.left = "20px";
                            name_layer.style.top = "2px";
                            name_layer.style.whiteSpace = "nowrap";
                            // Do not set innerText here, as it will be managed by the $watch listener
                            $compile(name_layer)($scope);

                            var cursor_layer = display.getCursorLayer().getElement();
                            cursor_layer.append(name_layer);
                            display.scale($scope.client.clientProperties.scale);

                            // Add display element
                            displayElement = display.getElement();
                            displayContainer.appendChild(displayElement);

                            // Do nothing when the display element is clicked on
                            display.getElement().onclick = function(e) {
                                e.preventDefault();
                                return false;
                            };

                        });

                        // Update actual view scrollLeft when scroll properties change
                        $scope.$watch('client.clientProperties.scrollLeft', function scrollLeftChanged(scrollLeft) {
                            if (!$scope.client)
                                return;

                            main.scrollLeft = scrollLeft;
                            $scope.client.clientProperties.scrollLeft = main.scrollLeft;
                        });

                        // Update actual view scrollTop when scroll properties change
                        $scope.$watch('client.clientProperties.scrollTop', function scrollTopChanged(scrollTop) {
                            if (!$scope.client)
                                return;

                            main.scrollTop = scrollTop;
                            $scope.client.clientProperties.scrollTop = main.scrollTop;
                        });

                        // Update scale when display is resized
                        $scope.$watch('client.managedDisplay.size', function setDisplaySize() {
                            $scope.$evalAsync(updateDisplayScale);
                        });

                        // Keep local cursor up-to-date
                        $scope.$watch('client.managedDisplay.cursor', function setCursor(cursor) {
                            // Check the platform and browser for preventing the black box of the blank transparent cursor
                            var isWindowsChromeEdge;
                            if (ribbonService.platform.osName === "Windows" &&
                               (ribbonService.browser.isChrome || ribbonService.browser.isEdge)) {
                                isWindowsChromeEdge = true;
                            }
                            else {
                                isWindowsChromeEdge = false;
                            }

                            // Set the custom cursor
                            if (cursor) {
                                localCursor = mouse.setCursor(cursor.canvas, cursor.x, cursor.y, isWindowsChromeEdge);
                            }
                        });

                        // Update touch event handling depending on remote multi-touch
                        // support and mouse emulation mode
                        $scope.$watchGroup([
                                'client.multiTouchSupport',
                                'client.clientProperties.emulateAbsoluteMouse'
                            ], function touchBehaviorChanged(emulateAbsoluteMouse) {

                            if (!$scope.client) return;

                            // Clear existing event handling
                            touch.offEach(['touchstart', 'touchmove', 'touchend'], handleTouchEvent);
                            touchScreen.offEach(['mousedown', 'mousemove', 'mouseup'], handleEmulatedMouseEvent);
                            touchPad.offEach(['mousedown', 'mousemove', 'mouseup'], handleEmulatedMouseEvent);

                            // Directly forward local touch events
                            if ($scope.client.multiTouchSupport) {
                                touch.onEach(['touchstart', 'touchmove', 'touchend'], handleTouchEvent);
                            }
                            // Switch to touchscreen if mouse emulation is required and
                            // absolute mouse emulation is preferred
                            else if ($scope.client.clientProperties.emulateAbsoluteMouse) {
                                touchScreen.onEach(['mousedown', 'mousemove', 'mouseup'], handleEmulatedMouseEvent);
                            }
                            // Use touchpad for mouse emulation if absolute mouse emulation
                            // is not preferred
                            else {
                                touchPad.onEach(['mousedown', 'mousemove', 'mouseup'], handleEmulatedMouseEvent);
                            }
                        });

                        // Adjust scale if modified externally
                        $scope.$watch('client.clientProperties.scale', function changeScale(scale) {

                            if (!$scope.client)
                                return;

                            // Fix scale within limits
                            scale = Math.max(scale, $scope.client.clientProperties.minScale);
                            scale = Math.min(scale, $scope.client.clientProperties.maxScale);

                            // If at minimum zoom level, hide scroll bars
                            if (scale === $scope.client.clientProperties.minScale) {
                                main.style.overflow = "hidden";
                            }
                            // If not at minimum zoom level, show scroll bars
                            else {
                                main.style.overflow = "auto";
                            }

                            if (!ribbonService.isAutoResize) {
                                scale = Math.min(scale, 1);
                            }

                            mmonitorService.setDisplayScale(scale);

                            // Apply scale if client attached
                            if (display) {
                                display.scale(scale);
                            }

                            if (scale !== $scope.client.clientProperties.scale) {
                                $scope.client.clientProperties.scale = scale;
                            }

                        });

                        // If autofit is set, the scale should be set to the minimum scale, filling the screen
                        $scope.$watch('client.clientProperties.autoFit', function changeAutoFit(autoFit) {

                            if (!$scope.client)
                                return;

                            if(autoFit) {
                                $scope.client.clientProperties.scale = $scope.client.clientProperties.minScale;
                            }

                        });

                        /*
                         * If the sftp service is unavailable, start the check of the sftp service.
                         */
                        $scope.$watch('ribbonService.isSftpCheckNeeded', function changeHasSftpLicense(isSftpCheckNeeded) {

                            if (!ribbonService.licenses.hasSftpLicence && isSftpCheckNeeded) {
                                checkSftp();
                                ribbonService.isSftpCheckNeeded = false;
                            }

                        });

                        /**
                         * Calculate real H264 resolution when customer uses large monitor
                         * @return {Object}
                         */
                        var calcH264Resolution = function calcH264Resolution(browserW, browserH) {

                            var h264W = Math.sqrt(ribbonService.licenses.h264MaxResolution * browserW / browserH);
                            var h264H = ribbonService.licenses.h264MaxResolution / h264W;

                            if (h264W > ribbonService.H264_MAX_WIDTH) {
                                h264W = ribbonService.H264_MAX_WIDTH;
                                h264H = ribbonService.H264_MAX_WIDTH * browserH / browserW;
                            }
                            else if (h264H > ribbonService.H264_MAX_HEIGHT) {
                                h264H = ribbonService.H264_MAX_HEIGHT;
                                h264W = ribbonService.H264_MAX_HEIGHT * browserW / browserH;
                            }

                            var h264Resolution = {
                                width: h264W,
                                height: h264H
                            }

                            // H264 resolution
                            return h264Resolution

                        };

                        // Check if the browser is running on iPad device
                        var isIPad = (/iPad|iPhone|iPod/.test(navigator.userAgent)) || (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 2);

                        // If the element is resized, attempt to resize client
                        $scope.mainElementResized = function mainElementResized() {

                            if (display) {
                                // Initialize resolution to the non high resolution
                                display.setLargeMonitor(false);
                                display.scale($scope.client.clientProperties.scale);
                                mmonitorService.setDisplayScale($scope.client.clientProperties.scale);
                            }

                            var pixelDensity;
                            var devicePixelRatio;
                            if (ribbonService.scale == 0 || ribbonService.scale == -1) {
                                devicePixelRatio = pixelDensity = $window.devicePixelRatio || 1;
                                ribbonService.scale = 0;
                            }
                            else if (ribbonService.scale > 0) {
                                pixelDensity = ribbonService.scale / 100;
                                devicePixelRatio = $window.devicePixelRatio || 1;
                            }

                            // On iPad, the devicePixelRatio is 2, but we use fixed pixel ratio of 1
                            if (ribbonService.licenses.hasMacOSLicence || ribbonService.licenses.hasLinuxLicence || ($window.devicePixelRatio < 1) || isIPad) {
                                devicePixelRatio = pixelDensity = 1;
                                $scope.client && $scope.client.client.getDisplay().setRibbonScale(-1);
                            }
                            else {
                                $scope.client && $scope.client.client.getDisplay().setRibbonScale(ribbonService.scale / 100);
                            }

                            var width  = main.offsetWidth  * devicePixelRatio;
                            var height = main.offsetHeight * devicePixelRatio;

                            // Needs to use the $window.innerWidth when using the 2nd & 3rd monitor
                            // because the main.offsetWidth value includes the width of the prior monitors.
                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            datasource = clientIdentifier.dataSource;
                            if (datasource != 'encryptedurl-jdbc' && ($routeParams.mm == 1 || $routeParams.mm == 2)) {
                                width  = $window.innerWidth  * devicePixelRatio;
                            }

                            // To set width whenever resize window
                            $rootScope.width = width;
                            $rootScope.height = height;

                            // Display resolution for the logging feature
                            if (ribbonService.loggingEnabled)
                                window.measurePerformance += new Date().toISOString() + " : " + "Display Resolution => " +
                                                            $rootScope.width + " x " + $rootScope.height + `\n`;

                            // Set the width & height to 1024x768 when detecting the poor network speen and set the streaming mode
                            // to "Faster streaming"
                            if (ribbonService.poorInternet && ribbonService.streamingMode == ribbonService.streamingModeOptions[0]) {
                                width = ribbonService.BEST_RESPONSIVENESS_WIDTH * devicePixelRatio;
                                height = ribbonService.BEST_RESPONSIVENESS_HEIGHT * devicePixelRatio;
                                $rootScope.width = width;
                                $rootScope.height = height;

                                // Set the large monitor mode to false when the streaming mode is "Faster streaming"
                                if (display) display.setLargeMonitor(false);
                            }
                            // Set the width & height to 2560x1440 when setting the streaming mode to "Medium resolution"
                            else if (ribbonService.streamingMode == ribbonService.streamingModeOptions[1]) {
                                width = ribbonService.MEDIUM_RESOLUTION_WIDTH * devicePixelRatio;
                                height = ribbonService.MEDIUM_RESOLUTION_HEIGHT * devicePixelRatio;
                                $rootScope.width = width;
                                $rootScope.height = height;

                                // Set the large monitor mode to false when the streaming mode is "Medium resolution"
                                if (display) display.setLargeMonitor(false);
                            }
                            // When over 4k resolution
                            else if ((width * height) > ribbonService.licenses.h264MaxResolution || 
                                     width > ribbonService.H264_MAX_WIDTH || 
                                     height > ribbonService.H264_MAX_HEIGHT) {
                                // If monitor is primary screen, check the value of hasH264Licence.
                                // If monitor is 2nd or 3rd screen, check the value of h264 in URL.
                                if (ribbonService.licenses.hasH264Licence || $routeParams.h264 === "true") {
                                    var h264Resolution = calcH264Resolution(width, height);
                                    width = h264Resolution.width;
                                    height = h264Resolution.height;

                                    // Notify to the display instance that it's high resolution
                                    display.setLargeMonitor(true);
                                    if ($routeParams.mm == 1 || $routeParams.mm == 2) {
                                        display.scale($window.innerWidth / width);
                                        // Adjust the left value correctly for the 2nd and 3rd monitors
                                        mmonitorService.setDisplayScale($window.innerWidth / width);
                                    }
                                    else {
                                        display.scale(main.offsetWidth / width);
                                        // Adjust the left value correctly for the 2nd and 3rd monitors
                                        mmonitorService.setDisplayScale(main.offsetWidth / width);
                                    }

                                }
                            }
                            // When the device is iPad
                            else if (isIPad) {
                                if ($routeParams.mm == 1 || $routeParams.mm == 2) {
                                    display.scale($window.innerWidth / width);
                                    mmonitorService.setDisplayScale($window.innerWidth / width);
                                }
                                else {
                                    display.scale(main.offsetWidth / width);
                                    mmonitorService.setDisplayScale(main.offsetWidth / width);
                                }
                            }

                            // Set canvas width and height whenever resizing window
                            if (ribbonService.highlightAvailable) {
                                var hlCanvasContainer = document.getElementById("highlightCanvasContainer");
                                hlCanvasContainer.style.width  = parseInt(width / devicePixelRatio)  + "px";
                                hlCanvasContainer.style.height = parseInt(height / devicePixelRatio) + "px";
                                if (ribbonService.highlightActivated) {
                                    var hlCanvas = document.getElementById("highlightCanvas");
                                    hlCanvas.style.width  = parseInt(width / devicePixelRatio)  + "px";
                                    hlCanvas.style.height = parseInt(height / devicePixelRatio) + "px";
                                    hlCanvas.width  = parseInt(width / devicePixelRatio);
                                    hlCanvas.height = parseInt(height / devicePixelRatio);
                                }
                            }

                            // Send new display size, if changed
                            if (client && display) {

                                var optimal_dpi = pixelDensity * 96;

                                if (display.getWidth() !== width || display.getHeight() !== height) {

                                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                                    datasource = clientIdentifier.dataSource;
                                    if (ribbonService.resolution.width === 0 && ribbonService.resolution.height === 0) {
                                        if (datasource === 'encryptedurl-jdbc') {
                                            client.sendSize(width, height, 0, 0, 0, 0, optimal_dpi);
                                            mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                                   devicePixelRatio, "primary");
                                        }
                                        else {
                                            // remove width of the primary monitor (main element
                                            // is primary + secondary wide, ofsetted to the left
                                            // for the size of the primary monitor.
                                            if ($routeParams.mm == "1") {
                                                client.sendSize(0, 0, width, height, 0, 0, optimal_dpi);
                                                mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                                       devicePixelRatio, "secondary");
                                            }
                                            else if ($routeParams.mm == "2") {
                                                client.sendSize(0, 0, 0, 0, width, height, optimal_dpi);
                                                mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                                       devicePixelRatio, "tertiary");
                                            }
                                        }
                                    }
                                    else if (ribbonService.resolution.width === -1 && ribbonService.resolution.height === -1) {
                                        // empty process
                                    }
                                    else {
                                        if (datasource === 'encryptedurl-jdbc') {
                                            client.sendSize(ribbonService.resolution.width , ribbonService.resolution.height, 0, 0, 0, 0, optimal_dpi);
                                            mmonitorService.sendMonitorsProperties(ribbonService.resolution.width, ribbonService.resolution.height, pixelDensity, "primary");
                                        }
                                        else {
                                            // remove width of the primary monitor (main element
                                            // is primary + secondary wide, offsetted to the left
                                            // for the size of the primary monitor.
                                            if ($routeParams.mm == 1) {
                                                client.sendSize(0, 0, ribbonService.resolution.width * pixelDensity, ribbonService.resolution.height * pixelDensity, 0, 0, optimal_dpi);
                                                mmonitorService.sendMonitorsProperties(ribbonService.resolution.width , ribbonService.resolution.height, pixelDensity, "secondary");
                                            }
                                            else if ($routeParams.mm == 2) {
                                                client.sendSize(0, 0, 0, 0, ribbonService.resolution.width * pixelDensity, ribbonService.resolution.height * pixelDensity, optimal_dpi);
                                                mmonitorService.sendMonitorsProperties(ribbonService.resolution.width , ribbonService.resolution.height, pixelDensity, "tertiary");
                                            }
                                        }
                                    }
                                }
                            }

                            $scope.$evalAsync(updateDisplayScale);

                        };

                        // Ensure focus is regained via mousedown before forwarding event
                        mouse.on('mousedown', document.body.focus.bind(document.body));

                        // Forward all mouse events
                        mouse.onEach(['mousedown', 'mousemove', 'mouseup'], handleMouseEvent);

                        // Hide software cursor when mouse leaves display
                        mouse.on('mouseout', function() {
                            if (!display) return;
                            display.showCursor(false);
                        });

                        // Update remote clipboard if local clipboard changes
                        $scope.$on('guacClipboard', function onClipboard(event, data) {
                            if (client && !angular.equals($scope.client.clipboardData.data, data.data)) {
                                ManagedClient.setClipboard($scope.client, data);
                                $scope.client.clipboardData = data;
                            }
                        });

                        /**
                         * Convert into lowercase if the given keysym corresponds to uppercase character
                         *
                         * @param {Number} keysym
                         *     The keysym to check.
                         *
                         * @returns {Number}
                         *
                         */
                        var toLowercase = function toLowercase(keysym) {

                            // Convert upercase to lowercase
                            return (keysym >= 65 && keysym <= 90 ? keysym + 32 : keysym);

                        };

                        // Translate local keydown events to remote keydown events if keyboard is enabled
                        $scope.$on('guacKeydown', function keydownListener(event, keysym, keyboard) {
                            const KEY_B = { 66: true, 98: true };

                            if (CTRL_KEYS[keysym] || ALT_KEYS[keysym]) {
                                keysCurrentlyPressed[keysym] = true;
                            }

                            var currentKeysPressedKeys = Object.keys(keysCurrentlyPressed);

                            if (keyboard.modifiers.ctrl && KEY_B[keysym] && !$rootScope.isRemoteDesktop) {
                                // Don't send this event through to the client.
                                event.preventDefault();

                                $rootScope.isRemoteDesktop = true;
                                return;
                            }
                            else if (!_.isEmpty(_.pick(ALT_KEYS, currentKeysPressedKeys)) && !_.isEmpty(_.pick(CTRL_KEYS, currentKeysPressedKeys))) {
                                // Don't send this event through to the client.
                                event.preventDefault();

                                $rootScope.isRemoteDesktop = false;
                                $rootScope.$broadcast('guacCtrlAltKeydown');
                                return;
                            }

                            if ($rootScope.isRemoteDesktop) {
                                if (!$scope.client) return;

                                if ($scope.client.clientProperties.keyboardEnabled && !event.defaultPrevented) {

                                    // Return if the given keysym corresponds to "CapsLock" key
                                    if (keysym == 65509 && ribbonService.licenses.preventCapsLock) return;

                                    // Set true if the given keysym corresponds to "Shift" key in keydownListener
                                    if (keysym == 65505 || keysym == 65506) isShiftKey = true;

                                    if (ribbonService.licenses.preventCapsLock) {
                                        if (isShiftKey) {
                                            // Send uppercase without converting into lowercase if "prevent-caps-lock" flag is true and "Shift" key is pressed
                                            client.sendKeyEvent(1, keysym);
                                        }
                                        else {
                                            // Send only lowercase if "prevent-caps-lock" flag is true and "Shift" key is not pressed
                                            client.sendKeyEvent(1, toLowercase(keysym));
                                        }
                                    }
                                    else {
                                        client.sendKeyEvent(1, keysym);
                                    }
                                    event.preventDefault();
                                }

                                // Increase the count when the keydown event is occurred
                                hasAction = true;
                            }
                        });

                        // Translate local keyup events to remote keyup events if keyboard is enabled
                        $scope.$on('guacKeyup', function keyupListener(event, keysym, keyboard) {
                            delete keysCurrentlyPressed[keysym];

                            if (!$scope.client) return;

                            if ($scope.client.clientProperties.keyboardEnabled && !event.defaultPrevented) {

                                // Return if the given keysym corresponds to "CapsLock" key
                                if (keysym == 65509 && ribbonService.licenses.preventCapsLock) return;


                                if (ribbonService.licenses.preventCapsLock) {
                                    if (isShiftKey) {
                                        // Send uppercase without converting into lowercase if "prevent-caps-lock" flag is true and "Shift" key is pressed
                                        client.sendKeyEvent(0, keysym);
                                    }
                                    else {
                                        // Send only lowercase if "prevent-caps-lock" flag is true and "Shift" key is not pressed
                                        client.sendKeyEvent(0, toLowercase(keysym));
                                    }
                                }
                                else {
                                    client.sendKeyEvent(0, keysym);
                                }

                                // Set false if the given keysym corresponds to "Shift" key in keyupListener
                                if (keysym == 65505 || keysym == 65506) isShiftKey = false;

                                event.preventDefault();
                            }
                        });

                        // Universally handle all synthetic keydown events
                        $scope.$on('guacSyntheticKeydown', function syntheticKeydownListener(event, keysym) {
                            client.sendKeyEvent(1, keysym);

                            // Increase the count when the synthetic keydown event is occurred
                            hasAction = true;
                        });

                        // Universally handle all synthetic keyup events
                        $scope.$on('guacSyntheticKeyup', function syntheticKeyupListener(event, keysym) {
                            client.sendKeyEvent(0, keysym);
                        });

                        // Update the resolution to MatchLocalDisplay
                        $scope.$on('Resolution.MatchLocalDisplay', function setMatchLocalDisplay() {

                            // Initialize resolution to the non high resolution
                            if (display) display.setLargeMonitor(false);

                            var devicePixelRatio = $window.devicePixelRatio || 1;

                            // If browser zoom under 100 %, it's needed to set like this.
                            // On iPad, the devicePixelRatio is 2, but we use fixed pixel ratio of 1.
                            if ($window.devicePixelRatio < 1 || isIPad) {
                                devicePixelRatio = 1;
                            }

                            var width  = main.offsetWidth  * devicePixelRatio;
                            var height = main.offsetHeight * devicePixelRatio;
                            var optimal_dpi = devicePixelRatio * 96;

                            // Needs to use the $window.innerWidth when using the 2nd & 3rd monitor
                            // because the main.offsetWidth value includes the width of the prior monitors.
                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            datasource = clientIdentifier.dataSource;
                            if (datasource != 'encryptedurl-jdbc' && ($routeParams.mm == 1 || $routeParams.mm == 2)) {
                                width  = $window.innerWidth  * devicePixelRatio;
                            }

                            // Set the width & height to 1024x768 when detecting the poor network speen and set the streaming mode
                            // to "Faster streaming"
                            if (ribbonService.poorInternet && ribbonService.streamingMode == ribbonService.streamingModeOptions[0]) {
                                width = ribbonService.BEST_RESPONSIVENESS_WIDTH * devicePixelRatio;
                                height = ribbonService.BEST_RESPONSIVENESS_HEIGHT * devicePixelRatio;

                                // Set the large monitor mode to false when the streaming mode is "Faster streaming"
                                if (display) display.setLargeMonitor(false);
                            }
                            // Set the width & height to 2560x1440 when setting the streaming mode to "Medium resolution"
                            else if (ribbonService.streamingMode == ribbonService.streamingModeOptions[1]) {
                                width = ribbonService.MEDIUM_RESOLUTION_WIDTH * devicePixelRatio;
                                height = ribbonService.MEDIUM_RESOLUTION_HEIGHT * devicePixelRatio;
                                $rootScope.width = width;
                                $rootScope.height = height;

                                // Set the large monitor mode to false when the streaming mode is "Medium resolution"
                                if (display) display.setLargeMonitor(false);
                            }
                            // When over 4k resolution
                            else if ((width * height) > ribbonService.licenses.h264MaxResolution || 
                                     width > ribbonService.H264_MAX_WIDTH || 
                                     height > ribbonService.H264_MAX_HEIGHT) {
                                // If monitor is primary screen, check the value of hasH264Licence.
                                // If monitor is 2nd or 3rd screen, check the value of h264 in URL.
                                if (ribbonService.licenses.hasH264Licence || $routeParams.h264 === "true") {
                                    var h264Resolution = calcH264Resolution(width, height);
                                    width = h264Resolution.width;
                                    height = h264Resolution.height;

                                    // Notify to the display instance that it's high resolution
                                    display.setLargeMonitor(true);
                                    if ($routeParams.mm == 1 || $routeParams.mm == 2) {
                                        display.scale($window.innerWidth / width);
                                        // Adjust the left value correctly for the 2nd and 3rd monitors
                                        mmonitorService.setDisplayScale($window.innerWidth / width);
                                    }
                                    else {
                                        display.scale(main.offsetWidth / width);
                                        // Adjust the left value correctly for the 2nd and 3rd monitors
                                        mmonitorService.setDisplayScale(main.offsetWidth / width);
                                    }

                                }
                            }
                            // When the device is iPad
                            else if (isIPad) {
                                if ($routeParams.mm == 1 || $routeParams.mm == 2) {
                                    display.scale($window.innerWidth / width);
                                    mmonitorService.setDisplayScale($window.innerWidth / width);
                                }
                                else {
                                    display.scale(main.offsetWidth / width);
                                    mmonitorService.setDisplayScale(main.offsetWidth / width);
                                }
                            }

                            if (ribbonService.licenses.hasMacOSLicence || ribbonService.licenses.hasLinuxLicence || isIPad) {
                                $scope.client && $scope.client.client.getDisplay().setRibbonScale(-1);
                            }
                            else {
                                $scope.client && $scope.client.client.getDisplay().setRibbonScale(0);
                            }

                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            datasource = clientIdentifier.dataSource;

                            if (datasource === 'encryptedurl-jdbc') {
                                client.sendSize(width, height, 0, 0, 0, 0, optimal_dpi);
                                mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                       devicePixelRatio, "primary");
                            }
                            else {
                                // remove width of the primary monitor (main element
                                // is primary + secondary wide, ofsetted to the left
                                // for the size of the primary monitor.
                                if ($routeParams.mm == "1") {
                                    client.sendSize(0, 0, width, height, 0, 0, optimal_dpi);
                                    mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                           devicePixelRatio, "secondary");
                                }
                                else if ($routeParams.mm == "2") {
                                    client.sendSize(0, 0, 0, 0, width, height, optimal_dpi);
                                    mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                           devicePixelRatio, "tertiary");
                                }
                            }

                            display.setIsAutoResize(true);
                            displayElement = display.getElement();
                            displayElement.style.width = display.getWidth() + "px";
                            displayElement.style.height = display.getHeight() + "px";
                        });

                        // Update the scale to fixed one
                        $scope.$on('Resolution.FixedScale', function setFixedScale() {

                            if (display) {
                                // Initialize resolution to the non high resolution
                                display.setLargeMonitor(false);
                                display.scale($scope.client.clientProperties.scale);
                                mmonitorService.setDisplayScale($scope.client.clientProperties.scale);
                            }

                            var devicePixelRatio = $window.devicePixelRatio || 1;

                            // If browser zoom under 100 %, it's needed to set like this.
                            // On iPad, the devicePixelRatio is 2, but we use fixed pixel ratio of 1.
                            if ($window.devicePixelRatio < 1 || isIPad) {
                                devicePixelRatio = 1;
                            }

                            var pixelDensity = ribbonService.scale / 100;
                            var width  = main.offsetWidth * devicePixelRatio;
                            var height = main.offsetHeight * devicePixelRatio;
                            var optimal_dpi = pixelDensity * 96;

                            // Needs to use the $window.innerWidth when using the 2nd & 3rd monitor
                            // because the main.offsetWidth value includes the width of the prior monitors.
                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            datasource = clientIdentifier.dataSource;
                            if (datasource != 'encryptedurl-jdbc' && ($routeParams.mm == 1 || $routeParams.mm == 2)) {
                                width  = $window.innerWidth  * devicePixelRatio;
                            }

                            // Set the width & height to 1024x768 when detecting the poor network speen and set the streaming mode
                            // to "Faster streaming"
                            if (ribbonService.poorInternet && ribbonService.streamingMode == ribbonService.streamingModeOptions[0]) {
                                width = ribbonService.BEST_RESPONSIVENESS_WIDTH * devicePixelRatio;
                                height = ribbonService.BEST_RESPONSIVENESS_HEIGHT * devicePixelRatio;
                                $rootScope.width = width;
                                $rootScope.height = height;

                                // Set the large monitor mode to false when the streaming mode is "Faster streaming"
                                if (display) display.setLargeMonitor(false);
                            }
                            // Set the width & height to 2560x1440 when setting the streaming mode to "Medium resolution"
                            else if (ribbonService.streamingMode == ribbonService.streamingModeOptions[1]) {
                                width = ribbonService.MEDIUM_RESOLUTION_WIDTH * devicePixelRatio;
                                height = ribbonService.MEDIUM_RESOLUTION_HEIGHT * devicePixelRatio;
                                $rootScope.width = width;
                                $rootScope.height = height;

                                // Set the large monitor mode to false when the streaming mode is "Medium resolution"
                                if (display) display.setLargeMonitor(false);
                            }
                            // When over 4k resolution
                            else if ((width * height) > ribbonService.licenses.h264MaxResolution || 
                                     width > ribbonService.H264_MAX_WIDTH || 
                                     height > ribbonService.H264_MAX_HEIGHT) {
                                // If monitor is primary screen, check the value of hasH264Licence.
                                // If monitor is 2nd or 3rd screen, check the value of h264 in URL.
                                if (ribbonService.licenses.hasH264Licence || $routeParams.h264 === "true") {
                                    var h264Resolution = calcH264Resolution(width, height);
                                    width = h264Resolution.width;
                                    height = h264Resolution.height;
    
                                    // Notify to the display instance that it's high resolution
                                    display.setLargeMonitor(true);
                                    if ($routeParams.mm == 1 || $routeParams.mm == 2) {
                                        display.scale($window.innerWidth / width);
                                        // Adjust the left value correctly for the 2nd and 3rd monitors
                                        mmonitorService.setDisplayScale($window.innerWidth / width);
                                    }
                                    else {
                                        display.scale(main.offsetWidth / width);
                                        // Adjust the left value correctly for the 2nd and 3rd monitors
                                        mmonitorService.setDisplayScale(main.offsetWidth / width);
                                    }
                                }

                            }
                            // When the device is iPad
                            else if (isIPad) {
                                if ($routeParams.mm == 1 || $routeParams.mm == 2) {
                                    display.scale($window.innerWidth / width);
                                    mmonitorService.setDisplayScale($window.innerWidth / width);
                                }
                                else {
                                    display.scale(main.offsetWidth / width);
                                    mmonitorService.setDisplayScale(main.offsetWidth / width);
                                }
                            }

                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            datasource = clientIdentifier.dataSource;

                            if (ribbonService.licenses.hasMacOSLicence || ribbonService.licenses.hasLinuxLicence || isIPad) {
                                $scope.client && $scope.client.client.getDisplay().setRibbonScale(-1);
                            }
                            else {
                                $scope.client && $scope.client.client.getDisplay().setRibbonScale(pixelDensity);
                            }

                            if (datasource === 'encryptedurl-jdbc') {
                                client.sendSize(width, height, 0, 0, 0, 0, optimal_dpi);
                                mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                       devicePixelRatio, "primary");
                            }
                            else {
                                // remove width of the primary monitor (main element
                                // is primary + secondary wide, ofsetted to the left
                                // for the size of the primary monitor.
                                if ($routeParams.mm == "1") {
                                    client.sendSize(0, 0, width, height, 0, 0, optimal_dpi);
                                    mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                           devicePixelRatio, "secondary");
                                }
                                else if ($routeParams.mm == "2") {
                                    client.sendSize(0, 0, 0, 0, width, height, optimal_dpi);
                                    mmonitorService.sendMonitorsProperties(Math.floor(width / devicePixelRatio), Math.floor(height / devicePixelRatio),
                                                                           devicePixelRatio, "tertiary");
                                }
                            }

                            display.setIsAutoResize(true);
                            displayElement = display.getElement();
                            displayElement.style.width = display.getWidth() + "px";
                            displayElement.style.height = display.getHeight() + "px";
                        });

                        // Update the resolution based on the internet status
                        $scope.$on('Resolution.BestResponsiveness', function setBestResponsiveness() {
                            // Force to send the 'size' event that hyperstream server change the display resolution
                            // to the low resolution (1024*768)
                            $scope.mainElementResized();

                            // Need to disable the multi-monitor button on the ribbon when switching
                            // to the 'low resolution' mode
                            ribbonService.isOpenSecondMonitor = true;
                            ribbonService.isOpenThirdMonitor = true;

                            // Re-calculate the position of the remote display
                            displayContainer.style.position = "absolute";
                            displayContainer.style.left = "calc(50vw - " + ribbonService.BEST_RESPONSIVENESS_WIDTH / 2 + "px)";
                            displayContainer.style.top = "calc(50vh - " + ribbonService.BEST_RESPONSIVENESS_HEIGHT / 2 + "px)";

                            // Set the flag indicating the 'low resolution' mode
                            var display = $scope.client.client.getDisplay();
                            display.setBestResponsiveness(true);
                        });

                        // Update the resolution based on the internet status
                        $scope.$on('Resolution.MediumResolution', function setMediumResolution() {
                            // Force to send the 'size' event that hyperstream server change the display resolution
                            // to the medium resolution (2560*1440)
                            $scope.mainElementResized();

                            // Need to disable the multi-monitor button on the ribbon when switching
                            // to the 'medium resolution' mode
                            ribbonService.isOpenSecondMonitor = true;
                            ribbonService.isOpenThirdMonitor = true;

                            // Re-calculate the position of the remote display
                            displayContainer.style.position = "absolute";
                            displayContainer.style.left = "calc(50vw - " + ribbonService.MEDIUM_RESOLUTION_WIDTH / 2 + "px)";
                            displayContainer.style.top = "calc(50vh - " + ribbonService.MEDIUM_RESOLUTION_HEIGHT / 2 + "px)";

                            // Set the flag indicating the 'medium resolution' mode
                            var display = $scope.client.client.getDisplay();
                            display.setMediumResolution(true);
                        });

                        // Update the resolution when setting the `Better quality` option
                        $scope.$on('StreamingMode', function setStreamingMode() {

                            if (ribbonService.streamingMode == ribbonService.streamingModeOptions[0]) {
                                ribbonService.poorInternet = true;
                                ribbonService.isCheckLowLatency = true;

                                // If the 2nd monitor is opened, close it
                                if ($rootScope.mmonitor2Win) {
                                    for (var i = 0; i < $rootScope.mmonitor2Win.length; i++) {
                                        if ($rootScope.mmonitor2Win[i] != null)
                                            $rootScope.mmonitor2Win[i].close();
                                        $rootScope.mmonitor2Win.splice(i, 1);
                                    }
                                }

                                // If the 3rd monitor is opened, close it
                                if ($rootScope.mmonitor3Win) {
                                    for (var i = 0; i < $rootScope.mmonitor3Win.length; i++) {
                                        if ($rootScope.mmonitor3Win[i] != null)
                                            $rootScope.mmonitor3Win[i].close();
                                        $rootScope.mmonitor3Win.splice(i, 1);
                                    }
                                }

                                var display = $scope.client.client.getDisplay();
                                display.setMediumResolution(false);
                                $rootScope.$broadcast('Resolution.BestResponsiveness');
                            }
                            else if (ribbonService.streamingMode == ribbonService.streamingModeOptions[1]) {
                                if ($window.innerWidth < ribbonService.MEDIUM_RESOLUTION_WIDTH || $window.innerHeight < ribbonService.MEDIUM_RESOLUTION_HEIGHT) {
                                    $rootScope.$broadcast('mediumResolutionWarning');
                                }
                                else {
                                    ribbonService.poorInternet = false;

                                    // If the 2nd monitor is opened, close it
                                    if ($rootScope.mmonitor2Win) {
                                        for (var i = 0; i < $rootScope.mmonitor2Win.length; i++) {
                                            if ($rootScope.mmonitor2Win[i] != null)
                                                $rootScope.mmonitor2Win[i].close();
                                            $rootScope.mmonitor2Win.splice(i, 1);
                                        }
                                    }

                                    // If the 3rd monitor is opened, close it
                                    if ($rootScope.mmonitor3Win) {
                                        for (var i = 0; i < $rootScope.mmonitor3Win.length; i++) {
                                            if ($rootScope.mmonitor3Win[i] != null)
                                                $rootScope.mmonitor3Win[i].close();
                                            $rootScope.mmonitor3Win.splice(i, 1);
                                        }
                                    }

                                    var display = $scope.client.client.getDisplay();
                                    display.setBestResponsiveness(false);
                                    $rootScope.$broadcast('Resolution.MediumResolution');
                                }
                            }
                            else if (ribbonService.streamingMode == ribbonService.streamingModeOptions[2]) {
                                ribbonService.poorInternet = false;
                                $scope.mainElementResized();

                                ribbonService.isOpenSecondMonitor = false;
                                ribbonService.isOpenThirdMonitor = false
                                displayContainer.style.position = "";
                                displayContainer.style.left = "";
                                displayContainer.style.top = "";

                                var display = $scope.client.client.getDisplay();
                                display.setBestResponsiveness(false);
                                display.setMediumResolution(false);
                            }
                        });

                        /**
                         * Ignores the given event.
                         *
                         * @param {Event} e The event to ignore.
                         */
                        function ignoreEvent(e) {
                           e.preventDefault();
                           e.stopPropagation();
                        }

                        // Handle and ignore dragenter/dragover
                        displayContainer.addEventListener("dragenter", ignoreEvent, false);
                        displayContainer.addEventListener("dragover",  ignoreEvent, false);

                        // File drop event handler
                        displayContainer.addEventListener("drop", function(e) {
                            e.preventDefault();
                            e.stopPropagation();

                            // Ignore file drops if no attached client
                            if (!$scope.client)
                                return;

                            // Upload each file
                            var files = e.dataTransfer.files;
                            ribbonService.fileUploadVisible = false;
                            // Wait until all processes are reset to upload files
                            $timeout(function() {
                                ribbonService.filesToUpload = files;
                                ribbonService.fileUploadVisible = true;
                            }, 2000);

                        }, false);

                        var userActionInterval = null;

                        $rootScope.$on("multiMonitorNotificationsStarted", function() {
                            /**
                             * Send the user action if there is any action within 3 mins.
                             */
                            if (ribbonService.licenses.hasMMonitorLicence || $routeParams.hasOwnProperty("mm")) {
                                userActionInterval = $interval(function() {
                                    if (hasAction) $scope.sendUserAction();
                                }, SEND_FREQ);
                            }
                        });

                        $rootScope.$on("multiMonitorNotificationsEnded", function() {
                            /**
                             * Stop sending the user action.
                             */
                            if (userActionInterval) {
                               $interval.cancel(userActionInterval);
                            }
                        });

                        /**
                         * Send the user action.
                         */
                        $scope.sendUserAction = function sendUserAction() {
                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            var datasource = encodeURIComponent(clientIdentifier.dataSource);

                            getClientId(datasource, clientIdentifier)
                            .then(function(clientId) {
                                $http({
                                    method: 'POST',
                                    url: "api/session/ext/" + datasource + "/mmonitornotify/sendUserAction"
                                         + "?sessionId=" + clientId
                                         + "&token=" + encodeURIComponent(authenticationService.getCurrentToken()),
                                    headers: {
                                        'Content-Type': 'application/json'
                                    }
                                })
                                .catch(function (error) {
                                    console.debug("Error sending the user action: ", error);
                                })
                                .finally(function() {
                                    // Reset after the action is sent.
                                    hasAction = false;
                                });
                            });
                        }

                        /**
                         * Client id is retrieved in a different way for a normal and shared session.
                         * This function unifies retrieval of the client id.
                         */
                        function getClientId(datasource, clientIdentifier) {
                            var deffered = $q.defer();

                            if (datasource === "encryptedurl-jdbc") {
                                deffered.resolve(clientIdentifier.id);
                            }
                            else {
                                if($location.path().indexOf("classroom") > -1) {
                                    return;
                                }

                                connectionService.getConnection(datasource, clientIdentifier.id)
                                .then(function connectionRetrieved(connection) {
                                    deffered.resolve(connection.name);
                                });
                            }

                            return deffered.promise;
                        };

                        /*
                         * END CLIENT DIRECTIVE
                         */

                    }
                ]

            return $delegate;
        }
    ]);
}]);
