/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

function AppParticipant(stream) {

  this.stream = stream;
  this.videoElement;
  this.thumbnailId;

  var that = this;

  this.getStream = function () {
    return this.stream;
  }

  this.setMain = function () {
    var mainVideo = document.getElementById("main-video");
    var oldVideo = mainVideo.firstChild;

    stream.playOnlyVideo("main-video", that.thumbnailId);

    if (oldVideo !== null) {
      mainVideo.removeChild(oldVideo);
    }
  }

  this.removeMain = function () {
    $(that.videoElement).removeClass("active-video");
  }

  this.remove = function () {
    if (that.videoElement !== undefined) {
      if (that.videoElement.parentNode !== null) {
        that.videoElement.parentNode.removeChild(that.videoElement);
      }
    }
  }

  function playVideo() {
    that.thumbnailId = "video-" + stream.getGlobalID();

    that.videoElement = document.createElement('div');
    that.videoElement.setAttribute("id", that.thumbnailId);
    that.videoElement.className = "video";

    var speakerSpeakingVolumen = document.createElement('div');
    speakerSpeakingVolumen.setAttribute("id", "speaker" + that.thumbnailId);
    speakerSpeakingVolumen.className = 'btn--m btn--green btn--fab mdi md-volume-up blinking';
    speakerSpeakingVolumen.style.position = "absolute";
    speakerSpeakingVolumen.style.left = "3%";
    speakerSpeakingVolumen.style.top = "60%";
    speakerSpeakingVolumen.style.zIndex = "100";
    speakerSpeakingVolumen.style.display = "none";
    that.videoElement.appendChild(speakerSpeakingVolumen);

    document.getElementById("participants").appendChild(that.videoElement);
    that.stream.playThumbnail(that.thumbnailId);
  }

  playVideo();
}

function Participants($rootScope, ribbonService) {
  var mainParticipant;
  var localParticipant;
  var mirrorParticipant;
  var participants = {};
  var roomName;
  var that = this;
  var connected = true;
  var displayingRelogin = false;
  var mainSpeaker = true;

  this.DECLINED = 'declined';
  this.MISSED = 'missed';
  this.BUSY = 'busy';
  this.DONTDISTURB = 'dontdisturb';
  this.UNAVAILABLE = 'unavailable';

  this.isConnected = function () {
    return connected;
  }

  this.messages = [];
  this.participants = [];

  this.getRoomName = function () {
    console.log("room - getRoom " + roomName);
    roomName = room.name;
    return roomName;
  };

  this.getMainParticipant = function () {
    return mainParticipant;
  }

  function updateVideoStyle() {
    var MAX_WIDTH = 14;
    var numParticipants = Object.keys(participants).length;
    var maxParticipantsWithMaxWidth = 98 / MAX_WIDTH;

    if (numParticipants > maxParticipantsWithMaxWidth) {
      $('.video').css({
        "width": (98 / numParticipants) + "%"
      });
    } else {
      $('.video').css({
        "width": MAX_WIDTH + "%"
      });
    }
  };

  function updateMainParticipant(participant) {
    if (mainParticipant) {
      mainParticipant.removeMain();
    }
    mainParticipant = participant;
    mainParticipant.setMain();
  }

  this.addLocalParticipant = function (stream) {
    localParticipant = that.addStream(stream);
    mainParticipant = localParticipant;
    mainParticipant.setMain();
  };

  this.addStream = function (stream) {
    var participant = new AppParticipant(stream);
    participants[stream.getGlobalID()] = participant;

    updateVideoStyle();
    updateMainParticipant(participant);

    return participant;
  }

  this.removeParticipantByStream = function (stream) {
    this.removeStream(stream.getGlobalID());
  };


  this.removeStream = function (streamId) {
    var participant = participants[streamId];
    delete participants[streamId];
    participant.remove();
    //setting main
    if (mainParticipant && mainParticipant === participant) {
      var mainIsLocal = false;
      if (localParticipant) {
        if (participant !== localParticipant && participant !== mirrorParticipant) {
          mainParticipant = localParticipant;
          mainIsLocal = true;
        } else {
          localParticipant = null;
          mirrorParticipant = null;
        }
      }
      if (!mainIsLocal) {
        var keys = Object.keys(participants);
        if (keys.length > 0) {
          mainParticipant = participants[keys[0]];
        } else {
          mainParticipant = null;
        }
      }
      if (mainParticipant) {
        mainParticipant.setMain();
      }
    }

    updateVideoStyle();
  };

  this.addParticipant = function (participant) {
    if (!this.participants) {
      this.participants = [];
    }

    var id = participant.getID();
    if (id.includes('/vclassroom')) {
      id = id.substr(0, id.indexOf('/vclassroom'));
    }

    participant.setId(id);

    var index = this.participants.findIndex(function (element) {
      return element.getID() == id;
    });

    if (index < 0) {
      this.participants.push(participant);
      $rootScope.$broadcast('participants:updated');
    }
  };

  this.removeParticipant = function (participant) {
    var id = participant.getID();
    if (id.includes('/vclassroom')) {
      id = id.substr(0, id.indexOf('/vclassroom'));
    }

    var index = this.participants.findIndex(function (element) {
      return element.getID() == id;
    });

    if (index > -1) {
      this.participants.splice(index, 1);
      $rootScope.$broadcast('participants:updated');
    }
  };

  this.updateParticipant = function(user, state) {
    if (user.includes('/vclassroom')) {
      user = user.substr(0, user.indexOf('/vclassroom'));
    }

    var index;
    if (this.participants) {
      index = this.participants.findIndex(function (element) {
        return element.getID() == user;
      });
    }

    if (index > -1) {
      this.participants[index].setState(state);
    }

    $rootScope.$broadcast('participants:updated');
  }

  //only called when leaving the room
  this.removeParticipants = function () {
    connected = false;
    for (var index in participants) {
      var participant = participants[index];
      participant.remove();
    }
  };

  this.getParticipants = function () {
    return participants;
  };

  this.enableMainSpeaker = function () {
    mainSpeaker = true;
  }

  this.disableMainSpeaker = function () {
    mainSpeaker = false;
  }

  // Open the chat automatically when a message is received
  function autoOpenChat() {
    var selectedEffect = "slide";
    var options = { direction: "right" };
    if ($("#effect").is(':hidden')) {
      $("#content").animate({ width: '80%' }, 500);
      $("#effect").toggle(selectedEffect, options, 500);
    }
  };

  function toHM(timeValue){
    var d = new Date(parseInt(timeValue, 10));
    var isPM = d.getHours() >= 12;
    var isMidday = d.getHours() == 12;
    var hours = ('0'+(d.getHours()-(isPM && !isMidday ? 12 : 0))).slice(-2);
    var minutes = ('0'+d.getMinutes()).slice(-2);
    var time = [hours, minutes].join(':') + (isPM ? ' PM' : ' AM');
    return time;
  }

  this.showMessage = function (id, room, user, message, dest, time, type) {
    if (type == ribbonService.SHARE_URL) {
      return ;
    }
    if (!dest) {
      if (!this.messages[room])
        this.messages[room] = [];

      this.messages[room].push({
        'user': user,
        'message': message,
        'time': toHM(time)
      });
    }
    else {
      if (!this.messages[user]) {
        this.messages[user] = [];
        this.messages[user][room] = [];
      }
      var last = true;
      var length = this.messages[user][room].length;

      if (length > 0 && this.messages[user][room][length-1]['user'] == user) {
        this.messages[user][room][length-1]['last'] = false;
      }

      this.messages[user][room].push({
        'id': id,
        'user': user,
        'message': message,
        'time': toHM(time),
        'last': last,
        'timestamp': time,
        'state': 'unread',
        'type': type,
      });
    }

    $rootScope.$broadcast('messages:updated', {
      id: id,
      room: room,
      user: user,
      message: message,
      type: type,
      state: 'unread',
    });
  };

  this.showHistory = function (history) {
    if (history.messages.length > 0) {
      var messages = history.messages;
      var user = history.user;
      var room = history.room;
      if (!this.messages[user]) {
        this.messages[user] = [];
        this.messages[user][room] = [];
      }

      for (var i = 0; i < messages.length; i++) {
        if (messages[i].histtype == ribbonService.SHARE_URL) {
          continue ;
        }
        var last = false;
        var length = this.messages[user][room].length;
        var message = messages[i].histmessage;

        if (length == 0) {
          last = true;
        }

        if (length > 0 && this.messages[user][room][0]['user'] != messages[i].histuser) {
          last = true;
        }

        this.messages[user][room].unshift({
          'id': messages[i].histid,
          'user': messages[i].histuser,
          'message': message,
          'time': toHM(messages[i].histtime),
          'last': last,
          'timestamp': messages[i].histtime,
          'state': messages[i].histstate,
          'type': messages[i].histtype,
        });
      }

      $rootScope.$broadcast('history:updated', {
        room: room,
        user: user
      });
    }
  };

  this.showSendMessage = function (room, user, message, dest, time, type, id) {
    if (type == ribbonService.SHARE_URL) {
      return ;
    }
    if (user.includes('vclassroom')) {
      var res = user.split('/');
      if (res[1] == 'vclassroom') {
        user = res[0];
      }
    }
    if (dest.includes('vclassroom')) {
      var res = dest.split('/');
      if (res[1] == 'vclassroom') {
        dest = res[0];
      }
    }
    if (!this.messages[user]) {
      this.messages[user] = [];
      this.messages[user][room] = [];
    }

    var last = true;
    var length = this.messages[user][room].length;

    if (length > 0 && this.messages[user][room][length-1]['user'] == dest) {
      this.messages[user][room][length-1]['last'] = false;
    }

    this.messages[user][room].push({
      'id': id,
      'user': dest,
      'message': message,
      'time': toHM(time),
      'last': last,
      'timestamp': time,
      'type': type,
      'state': 'unread'
    });

    $rootScope.$broadcast('messages:updated', {
      id: id,
      room: room,
      user: user,
      message: message,
      type: type,
      dest: dest,
      state: 'unread'
    });
  }

  this.sycRevMessage = function(user, id) {
    var messages = this.messages;
    for (var from in messages) {
      for (var group in messages[from]) {
        var index = messages[from][group].findIndex((obj) => obj.id == id);

        if (index > -1 && messages[from][group][index].user == user) {
          messages[from][group][index].state = 'read';

          $rootScope.$broadcast('messages:updated', {
            user: messages[from][group][index].user,
            state: 'read'
          });

          return;
        }
      }
    }
  }

  this.showError = function ($window, LxNotificationService, e) {
    if (displayingRelogin) {
      console.warn('Already displaying an alert that leads to relogin');
      return false;
    }
    displayingRelogin = true;
    that.removeParticipants();
    LxNotificationService.alert('Error!', e.error.message, 'Reconnect', function (answer) {
      displayingRelogin = false;
      $window.location.href = '/';
    });
  };

  this.forceClose = function ($window, LxNotificationService, msg) {
    if (displayingRelogin) {
      console.warn('Already displaying an alert that leads to relogin');
      return false;
    }
    displayingRelogin = true;
    that.removeParticipants();
    LxNotificationService.alert('Warning!', msg, 'Reload', function (answer) {
      displayingRelogin = false;
      $window.location.href = '/';
    });
  };

  this.alertMediaError = function ($window, LxNotificationService, msg, callback) {
    if (displayingRelogin) {
      console.warn('Already displaying an alert that leads to relogin');
      return false;
    }
    LxNotificationService.confirm('Warning!', 'Server media error: ' + msg
      + ". Please reconnect.", { cancel: 'Disagree', ok: 'Agree' },
      function (answer) {
        console.log("User agrees upon media error: " + answer);
        if (answer) {
          that.removeParticipants();
          $window.location.href = '/';
        }
        if (typeof callback === "function") {
          callback(answer);
        }
      });
  };

  this.streamSpeaking = function (participantId) {
    if (participants[participantId.participantId] != undefined)
      document.getElementById("speaker" + participants[participantId.participantId].thumbnailId).style.display = '';
  }

  this.streamStoppedSpeaking = function (participantId) {
    if (participants[participantId.participantId] != undefined)
      document.getElementById("speaker" + participants[participantId.participantId].thumbnailId).style.display = "none";
  }

  this.updateMainSpeaker = function (participantId) {
    if (participants[participantId.participantId] != undefined) {
      if (mainSpeaker)
        updateMainParticipant(participants[participantId.participantId]);
    }
  }
}
