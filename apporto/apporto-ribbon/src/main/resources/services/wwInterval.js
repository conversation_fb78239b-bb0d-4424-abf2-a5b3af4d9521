/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for managing file explorer.
 * 
 * This service provides function for getting the file browser url,
 * openning and closing file explorer.
 * 
 */
angular.module('ribbon').factory('wwInterval',
        [ '$injector', function wwInterval($injector) {

            var service = { 
                // Web worker to handle process that requires the strict time counting in inactive browsers
                // e.g. like calcuating the idle time
                timeTrackingWorker: undefined
            };

            service.wwCreate = function() {
                if (window.Worker) { // Check if <PERSON><PERSON><PERSON> supports the Worker API.
                    if (!this.timeTrackingWorker) {
                        this.timeTrackingWorker = new Worker("app/ext/ribbon/workers/TimeTrackingWorker.js");
                    }
                }
            }

            service.wwTerminate = function() {
                if (this.timeTrackingWorker) {
                    this.timeTrackingWorker.terminate();
                    this.timeTrackingWorker = undefined;
                }
            }

            service.wwSetCallback = function(callback) {
                if (this.timeTrackingWorker) {
                    this.timeTrackingWorker.onmessage = function(e) {
                        callback(e);
                    }
                }
            }

            service.wwSendMessage = function(message) {
                if (this.timeTrackingWorker) {
                    this.timeTrackingWorker.postMessage(message);
                }
            }

            return service;
        } ]);
