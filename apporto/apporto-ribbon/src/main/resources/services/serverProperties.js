/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for retrieving server properties
 */
angular.module('ribbon').factory('serverProperties',
        [ '$injector', function serverProperties($injector) {

    // Required services
    var $q                       = $injector.get('$q');
    var $http                    = $injector.get('$http');
    var ClientIdentifier         = $injector.get('ClientIdentifier');
    var $routeParams             = $injector.get('$routeParams');
    var authenticationService    = $injector.get('authenticationService');
    var $location                = $injector.get('$location');

    var service = {};

    service.getProperty = function getProperty(property) {
        if($location.path().indexOf("classroom") > -1) {
           return;
        }

        var deferred = $q.defer();

        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

        var httpParameters = {
            token   : authenticationService.getCurrentToken(),
            id      : clientIdentifier.id,
            name    : property
        };

        $http({
            method  : 'GET',
            url     : 'api/session/ext/' + authenticationService.getDataSource() + '/property',
            params  : httpParameters
        })
        .then(function(response) {
            console.debug("Retrieved property " + "'" + property + "'" + " = " + response.data[property]);
            deferred.resolve(response.data[property] || "");
        })
        .catch(function(response) {
            console.error("Failed to get property " + "'" + property + "'");
            deferred.reject("Failed to get property " + "'" + property + "'");
        });

        return deferred.promise;
    }
            
    return service;
} ]);

