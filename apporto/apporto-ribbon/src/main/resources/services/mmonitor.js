/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for managing multimonitor
 */
angular.module('ribbon').factory('mmonitorService',
        [ '$injector', '$timeout', '$interval', '$routeParams', 
        	
        function mmonitorService($injector, $timeout, $interval, $routeParams) {
        
        	var authenticationService = $injector.get('authenticationService');
            var ManagedClientState    = $injector.get('ManagedClientState');
            var ClientIdentifier      = $injector.get('ClientIdentifier');
            var connectionService     = $injector.get('connectionService');
            var $routeParams          = $injector.get('$routeParams');
            var $http                 = $injector.get('$http');
            var $q                    = $injector.get('$q');
            var $location             = $injector.get('$location');
            var $window               = $injector.get('$window');
            var ribbonService         = $injector.get('ribbonService'); 

            var monitorChecker = null;
            var client = null;
            var elNonPrimary = null;
            var monitorsProperties = null;
            var displayScale = 1;

            var service = {}

            service.getDisplayScale = function getDisplayScale() {
                return displayScale;
            }

            service.setDisplayScale = function setDisplayScale(scale) {
                displayScale = scale;
            }

            service.getMonitorsProperties = function getMonitorsProperties() {
                return monitorsProperties;
            }

            /**
             * Client id is retrieved in a different way for a normal and shared session.
             * This function unifies retrieval of the client id.
             */
            function getClientId(datasource, clientIdentifier) {
                var deffered = $q.defer();

                if (datasource === "encryptedurl-jdbc")
                    deffered.resolve(clientIdentifier.id);
                else {
                    if($location.path().indexOf("classroom") > -1) {
                        return;
                    }

                    connectionService.getConnection(datasource, clientIdentifier.id)
                        .then(function connectionRetrieved(connection) {
                            deffered.resolve(connection.name);
                        });
                }

                return deffered.promise;
            };

            /**
             * Initialization: disable auto scaling and setup css for multimonitor
             */
            service.initMultiMonitor = function initMultiMonitor(newClient) {
                client = newClient;
                client.clientProperties.autoFit = true;
                client.clientProperties.scale = 1 / ($window.devicePixelRatio || 1);
                client.clientProperties.minScale = 1 / ($window.devicePixelRatio || 1);
                client.clientProperties.maxScale = 3;

                $timeout(function() {
                    var el = document.getElementsByClassName("main ng-isolate-scope");
                    el[0] && (el[0].style.overflow = "hidden");

                    $timeout(function() {
                       if($routeParams.hasOwnProperty("key") && $routeParams.hasOwnProperty("mm")) {
                            elNonPrimary = document.getElementsByClassName("main ng-isolate-scope")[0];
                            elNonPrimary && (elNonPrimary.style.position = "absolute");
                        }
                        else if ($routeParams.hasOwnProperty("key")) {
                            elNonPrimary = null;
                        } 
                    }, 500);
                }, 0);
            }

            /**
             * Send monitor properties to the server.
             */
            service.sendMonitorsProperties = function sendMonitorsProperties(width, height, scale, monitor) {
                if($location.path().indexOf("classroom") > -1) {
                    return;
                }

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                getClientId(datasource, clientIdentifier)
                .then(function(clientId) {
                    var httpParameters = {
                        token: encodeURIComponent(authenticationService.getCurrentToken()),
                        id: encodeURIComponent(clientId),
                        width: width,
                        height: height,
                        scale: scale
                    };

                    var req = {
                        method: 'POST',
                        url: "api/session/ext/" + datasource + "/multimonitor/" + monitor,
                        params: httpParameters
                    };

                    $http(req)
                    .then(function(response) {
                        // Empty process
                    })
                    .catch(function(response) {
                        console.error("Error sending monitor dimensions: ", response.message);
                    })
                    .finally( function() { 
                        // Empty process
                    });
                });
            };

            service.startMonitorChecker = function startMonitorChecker() {
                if (monitorChecker == null)
                    monitorChecker = $interval(checkMonitorsSizes, 3000);
            }

            service.stopMonitorChecker = function stopMonitorChecker() {
                if (monitorChecker != null) {
                    $interval.cancel(monitorChecker);
                    monitorChecker = null;
                }
            }

            /**
             * Return true if this is primary monitor
             */
            service.isPrimary = function isPrimary() {
                return elNonPrimary == null;
            }

            function checkMonitorsSizes() {
                if (ManagedClientState.isDisconnectState() == true) {
                    return;
                }
                if(elNonPrimary===null && ribbonService.isOpenSecondMonitor===false){
                    return;
                }
                
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                getClientId(datasource, clientIdentifier)
                .then(function(clientId) {
                    var httpParameters = {
                        token: encodeURIComponent(authenticationService.getCurrentToken()),
                        id: encodeURIComponent(clientId),
                    };

                    var req = {
                        method: 'GET',
                        url: "api/session/ext/" + datasource + "/multimonitor/all",
                        params: httpParameters
                    };

                    $http(req)
                    .then(function(response) {
                        monitorsProperties = response.data;

                        adjustSecondaryMonitor();
                    })
                    .catch(function(response) {
                        console.error("Error retrieving monitor dimensions: ", response.message);
                    })
                    .finally( function() { 
                        // Empty process
                    });
                });
            }

            function adjustSecondaryMonitor() {
                if (elNonPrimary != null) {
                    if ($routeParams.mm == "1") {
                        elNonPrimary.style.left = (-1 * monitorsProperties.primWidth * monitorsProperties.primScale) * displayScale + "px";
                    }
                    else if ($routeParams.mm == "2") {
                        elNonPrimary.style.left = (-1 * (monitorsProperties.primWidth * monitorsProperties.primScale + monitorsProperties.secWidth * monitorsProperties.secScale) * displayScale) + "px";
                    }

                    if (elNonPrimary.style.width != (monitorsProperties.primWidth + monitorsProperties.secWidth) + "px")
                        elNonPrimary.style.width = "auto"; // force automatic resizing

                    var layer_h264 = elNonPrimary.getElementsByClassName("layer-h264");
                    if (!!layer_h264) {
                        if ($routeParams.mm == "1") {
                            layer_h264[0].style.left = monitorsProperties.primWidth * monitorsProperties.primScale + "px";
                        }
                        else if ($routeParams.mm == "2") {
                            layer_h264[0].style.left = (monitorsProperties.primWidth * monitorsProperties.primScale + monitorsProperties.secWidth * monitorsProperties.secScale) + "px";
                        }
                    }
                }
            }

            return service;
}]);
