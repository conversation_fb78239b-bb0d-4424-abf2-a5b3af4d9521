/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for managing chatbot.
 * 
 * This service currently has no use, it exists for future development
 * 
 */
angular.module('ribbon').factory('chatbotService',
    [ '$injector', function chatbotService($injector) {

        var authenticationService = $injector.get('authenticationService');
        var ClientIdentifier      = $injector.get('ClientIdentifier');
        var $routeParams          = $injector.get('$routeParams');
        var $http                 = $injector.get('$http')
        var $location             = $injector.get('$location');
        var $q                    = $injector.get('$q');
        var guacNotification      = $injector.get('guacNotification');
        var sce                   = $injector.get('$sce');

        var service = { };

        return service;
    } ]);
