/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

angular.module('ribbon').service('ServiceRoom', function () {

    var kurentos = [];
    var userName;
    var localStream;

    this.getKurento = function (roomName) {
        return kurentos[roomName];
    };

    this.setKurento = function (value, roomName) {
        kurentos[roomName] = value;
    };

    this.getKurentos = function () {
        return kurentos;
    };

    this.setKurentos = function (values) {
        kurentos = values;
    };

    this.getLocalStream = function () {
        return localStream;
    };

    this.setLocalStream = function (value) {
        localStream = value;
    };

    this.getUserName = function () {
        return userName;
    };

    this.setUserName = function (value) {
        userName = value;
    };

    this.removeKurento = function (roomName) {
        kurentos[roomName] = ''
    }
});
