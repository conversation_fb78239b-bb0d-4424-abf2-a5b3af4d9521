/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for managing actions performed on button click
 */
angular.module('ribbon').factory('ribbonService',
    [ function ribbonService() {

        var UNKNOWN = 'unknown';

        var ribbonService = {
            /**
             * Size of the statistics bar in pixels.
             */
            STATISTICS_BAR_SIZE: 30,

            // Add all the elements inside modal which you want to make focusable
            FOCUSABLE_ELEMENTS: 'a[href]:not(.ng-hide), area[href]:not(.ng-hide), input:not([disabled]):not(.ng-hide), button:not([disabled]):not(.ng-hide):not([tabindex]), select:not([disabled]):not(.ng-hide), textarea:not([disabled]):not(.ng-hide), iframe:not(.ng-hide), object:not(.ng-hide), embed:not(.ng-hide), *[tabindex]:not(.ng-hide):not([disabled]), *[contenteditable=true]:not(.ng-hide)',

            BEST_RESPONSIVENESS_WIDTH: 1024, // The width of the best responsiveness
            BEST_RESPONSIVENESS_HEIGHT: 768, // The height of the best responsiveness

            MEDIUM_RESOLUTION_WIDTH : 2560, // The width of the medium resolution
            MEDIUM_RESOLUTION_HEIGHT : 1440, // The height of the medium resolution

            H264_MAX_WIDTH: 4096,  // The default max width
            H264_MAX_HEIGHT: 4096, // The default max height

            browser : {
                // Opera 8.0+
                isOpera : false,
                // Firefox 1.0+
                isFirefox : false,
                // Safari 3.0+ "[object HTMLElementConstructor]"
                isSafari : false,
                // Internet Explorer 6-11
                isIE : false,
                // Edge 20+
                isEdge : false,
                // Chrome 1 - 71
                isChrome : false,
                // Blink engine detection
                isBlink : ((!!window.chrome && (!!window.chrome.webstore || !!window.chrome.runtime)) || // isChrome
                            ((!!window.opr && !!opr.addons) || !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0)) && // isOpera
                            !!window.CSS,
                majorVersion : UNKNOWN,
                detailVersion : UNKNOWN,
            },

			/**
             * Customer's browser
             */
            customerBrowser: "Chrome",

            /**
             * connection_id
             */
            connectionId: '',

            fileTransferMessage: "CLIENT.FILE_UPLOAD_TO_DESKTOP",

            /**
             * Licenses available for the current user
             *
             * Availability of ribbon features depends on the available licenses
             */
            licenses : {
                hasFullscreenLicence: false,
                hasUploadLicence: false,
                hasDownloadLicence: false,
                hasSharingLicence: false,
                hasAsyncCollaborationLicence: false,
                hasMessengerLicence: false,
                hasSnapshotsLicence: false,
                hasAnalyticsLicence: false,
                hasHighlightLicence: false,
                hasMounterLicence: false,
                hasClassroomLicence: false,
                hasPresenterLicence: false,
                hasActivityTrackLicence: false,
                hasClipboard: true,
                hasClipboardIn: true,
                hasClipboardOut: true,
                hasRebootLicence: false,
                hasMMonitorLicence: false,
                hasBackupLicence: false,
                hasMacOSLicence: false,
                hasLinuxLicence: false,
                hasH264Licence: false,
                hasFileBrowserLicence: false,
                hardWareAccelerated: false, // The flag for checking GPU available or not
                hasChattingLicence: false,
                hasCameraLicence: false,
                hasStatisticsLicence: false,
                hasSftpLicence: false,
                hasLTILicence: false,
                hasWatermarkLicence: false,
                monitorCount: 1,
                appname: '',
                industryType: 'Default',
                subdomain: '',
                cloudUsername: '',
                userId: '',
                payloadVersion: '',
                idleTimeout: '',
                hasLogoff: false,
                /**
                 * Stable size for RDP H264 in pixels.
                 * The Remote Desktop protocol restricts the max resolution at which it does AVC to HEVC level 5.1
                 * 3840 * 2160 = 8,294,400
                 */
                h264MaxResolution: 3840 * 2160,
                /**
                 * Prevent sending caps locak key code to the server.
                 * XRDP server has issue with handling caps lock.
                 */
                preventCapsLock: false,
                cloudProvider: '',
                /**
                 * LTI issuer
                 * this param can be one among canvas, d2l and blackboard in case if hasLTILicence set true.
                 */
                ltiIssuer: '',
                /**
                 * The threshold & interval value & stack size to check the latency
                 * latencyThreshold: millisecond
                 * latencyCheckInterval: second
                 */
                latencyThreshold: 120,
                latencyCheckInterval: 2,
                latencyCheckCount: 5,
                 /**
                 * The members of the parameters for the customization user guide.
                 */
                 userGuideMenuName: '',
                 userGuideMenuLink: '',
                /**
                 * The members of the URL parameters for the support links.
                 */
                supportMenuName: '',
                supportLink: '',
                supportEmail: '',
                /**
                 * The USB feature
                 */
                hasUSBLicence: false,
                default_username: '',
                /**
                 * The flag for being enable logging.
                 * Logging is started when setting this flag from false to true.
                 */
                loggingEnabled: false,
                /**
                 * User information for Support Request Form
                 */
                firstName: '',
                lastName: '',
                company: '',
                email: '',
                /**
                 * Latency information for Support Request Form
                 */
                latencyMin : 0,
                latencyMax : 0,
                latencyMedian : 0,
                /**
                 * Information for extended compute
                 */
                extendedCompute: false,
                remainingTime: 0,
                hostName: '',
                rdpPort: '',
                /**
                 * The flag for launching in the full screen mode
                 */
                launchFullScreen: false,
                /**
                 * The allowed usb device class codes for vUSB: "02,03,08" ...
                 */
                usbDevices: "",
                /**
                 * The remote apps list
                 */
                remoteApps: '',
                hasRemoteApps: false,
            },

            /**
             * Time to show the info message
             *
             * @type Integer
             */
            thresholdInfo: 10 * 1000,

            /**
             * Prevent grabbing keyboard if mouse is over ribbon
             */
            ribbonActive: false,

            /**
             * Current Network Status
             *
             * @type Integer
             */
            networkStatus: 0,

            /**
             * Network Threshold
             *
             * @type Integer
             */
            excellentThreshold       : 40,
            excellentMediumThreshold : 70,
            fairThreshold            : 100,
            fairMediumThreshold      : 130,
            poorThreshold            : 1000,

            /**
             * The average latency value in last 10 seconds
             *
             * @type Integer
             */
            avgLatency: 0,

            /**
             * The flag for poor internet
             *
             * @type Boolean
             */
            poorInternet: false,

            /**
             * Time since send ping till receive result
             *
             * @type Integer
             */
            latency: 0,

            /**
             * Current latency for 1 second
             *
             * @type Integer
             */
            currentLatency: 0,

            /**
             * Array latency for the lasted time(up to 1 minute)
             *
             * @type Array
             */
            arrayLatency: [],

            /**
             * Current bandwidth
             *
             * @type Integer
             */
            currentBandwidth: 0,

            /**
             * Average bandwidth
             *
             * @type Integer
             */
            averageBandwidth: 0,

            /**
             * Summary chart dialog is visible only when clicked on small ribbon chart.
             *
             * @type Boolean
             */
            summaryChartVisible: false,

            /**
             * The url that fill be used to open inner file browser
             */
            innerFileBrowserUrl: 'about:blank',

            /**
             * If true, new file browser (external) is visible in the inner frame
             */
            innerFileBrowserVisible: false,

            /**
             * Files to upload.
             *
             * @type Array
             */
            filesToUpload: [],

            /**
             * File explorer is visible when browser button is clicked.
             *
             * @type Boolean
             */
            fileExplorerVisible: false,

            /**
             * File explorer is ready when client and filesystems are accessible
             *
             * @type Boolean
             */
            fileExplorerReady: false,

            /**
             * File browser is ready when client and filesystems are accessible
             *
             * @type Boolean
             */
            fileBrowserReady: false,

            /**
             * File List is ready when client and filesystems are accessible
             *
             * @type Boolean
             */
            fileListReady: false,

            /**
             * Flag for direct to rdp host guac connection
             *
             * @type Boolean
             */
            normalConnection: false,

            /**
             * Session manager is visible when snapshots button is clicked
             *
             * @type Boolean
             */
            snapshotManagerVisible: false,

            /**
             * Hide dialog background
             *
             * @type Boolean
             */
            hideDialogBackground: false,

            /**
             * Screen share dialog is visible when ribbon share button is clicked
             *
             * @type Boolean
             */
            ribbonShareDialogVisible: false,

            /**
             * Indicates if next button on the first dialog of ribbon share dialogs is clicked
             *
             * @type Boolean
             */
            nextButtonClicked: false,

            /**
             * Network quality dialog is visible when network indicator button is clicked
             *
             * @type Boolean
             */
            networkQualityDialogVisible: false,

            /**
             * Screen share dialog is visible when messenger share button is clicked
             *
             * @type Boolean
             */
            messengerShareDialogVisible: false,

            /**
             * Presenter request dialog is visible when presenter button is clicked
             *
             * @type Boolean
             */
            presenterRequestDialogVisible: false,

            /**
             * LTI Assignment Publish Dialog is visible when the publish button is clicked
             *
             * @type Boolean
             */
            assignmentPublishVisible: false,

            /**
             * Sharing button enabled/disabled
             *
             * @type Boolean
             */
            sharingEnabled: false,

            /**
             * Multi-monitor button enabled/disabled
             *
             * @type Boolean
             */
            mMonitorEnabled: false,

            /**
             * File upload instruction for file uploading
             *
             * @type Boolean
             * */
            uploadInstructionVisible: false,

            /**
             * File explorer appearance for file uploading
             *
             * @type Boolean
             * */
            fileUploadVisible: false,

            /**
             * File explorer appearance for file downloading
             *
             * @type Boolean
             * */
            fileDownloadVisible: false,

            /**
             * Highlight button is visible only when there is plug-in to handle highlighting.
             *
             * @type Boolean
             */
            highlightAvailable: false,

            /**
             * Flag that shows whether highlight is activated.
             */
            highlightActivated: false,

            /**
             * highlight user name
             */
            highlightUserName: "Remote user",

            /**
             * remote server id
             */
            serverId: null,

            /**
             * Flag that indicates if sound is muted in browser
             * */
            soundMute: false,

            /**
             * Flag that show/hide the side menu
             * */
            sideMenu: false,

            /**
             * Flag that indicates if microphone device is enabled in browser
             * */
            micEnabled: true,

            /**
             * Flag that indicates if camera device is enabled in browser
             * */
            cameraEnabled: false,

            /**
             * Flag that indicates if clipboard is disabled in browser
             * */
            clipboardEnabled: true,

            /**
             * Flag that indicates if frame statistics is shown in browser
             * */
            frameStatistics: true,

            /**
             * Show/hide chat window
             *
             * @type Boolean
             */
            displayMessenger: false,

            /**
             * First show/hide chat window
             *
             * @type Boolean
             */
            firstStartMessenger: false,

            /**
             * Indicates that intro is running
             *
             * @type Boolean
             * */
            introRunning: false,

            noMsgAcnt: true,

            cloudMounterEnabled: false,

            /**
             *
             */
            shareKey: "undefined",

            /**
             * Indicates that exists one shared session
             */
            shareLinkCopied: false,

            /**
             * Host name of the messenger server
             */
            messengerHostname: null,

            /**
             * Messenger login/logout
             */
            toggleMessenger: null,

            /**
             * When true, the classroom button is enabled
             */
            classroomReady: false,

            /**
             * Shows/Hides Classrrom Group Picker
             */
            toggleClassroom: null,

            /**
             * Classroom view is enabled when classroom button is clicked
             */
            displayClassroomView: false,

            /**
             * The uuids of opened classroom
             */
            openedClassroom: [],

            /**
             * Shows/Hides absent students
             */
            showAbsent: false,

            /**
             * Function for retrieving classroom links
             */
            retrieveAllSharedLinks: null,

            /**
             * Indicates if classroom display is visible
             */
            visibleClassroomView: false,

            /**
             * To check thumbnail uploading when user login after classroom already launched
             */
            activeClass: false,

            /**
             * Prevent sending caps locak key code to the server.
             * XRDP server has issue with handling caps lock.
             */
            preventCapsLock: null,

            /**
             * Sharing links
             * */
            shareLinkVO: null,
            shareLinkFC: null,
            shareLinkMM: null,
            shareLinkMM3: null,

            /**
             * Flag for loading multi monitor resize
             * */
            mmLoad: false,

            /**
             * Flag for loading items
             */
            skeletonLoad: {
                assignmentList: false,
                assignmentDetail: false,
                assignmentManagementFile: false
            },

            /**
             * Flag for loading thumbnail iframe
             * */
            thumbnailLoad: false,

            /**
             * Info if the current session is shared
             * */
            isShared: false,

            /**
             * Info if the idle is expired
             * */
            isIdleExpired: false,

            /**
             * Presenter mode for faculty
             * @type Boolean
             */
            presenterVisible: false,

            /**
             * Show/Hide the presenter thumbnail
             * @type Boolean
             */
            presenterThumbnailVisible: false,

            /**
             * Presenter mode for student
             * @type Boolean
             */
            isPresenter: false,

            /**
             * The group dialog for presenter
             * @type Boolean
             */
            presenterDialogVisible: false,

            /**
             * Share/Stop screen
             * @type Boolean
             */
            isPresenterEnabled: false,

            /**
             * Show/Hide the Help GUide
             * @type Boolean
             */
            inPageVisible: false,

            /**
             * Help Guide
             * @type Boolean
             */
            isInPage: false,

            /**
             * Start/Stop the messenger for virtual classroom
             * @type Boolean
             */
            isMessengerEnabled: false,

            /**
             * Fullscreen mode
             */
            isFullscreen: false,


            /**
             * if screeSharing session is ready/shareable
             */
            shareableStarted: false,

            /**
             * Session manager is visible when snapshots button is clicked
             *
             * @type Boolean
             */
            vmManagerVisible: false,

            /**
             * chatbot open or not
             * 
             * @type Boolean
             */
            chatbotVisible: false,

            /**
             * chatbot url
             * 
             * @type String
             */
            chatbotUrl: "",

            /**
             * Number of servers behind active lb
             * */
            serverGroupCount: null,

            classroomDialogVisible: false,

            classroomGroups:[],
            authRequests: [],

            groups:[],
            userinfo: {
                windows_username: '',
                email: '',
                groups: [],
                roles: '',
                status: '',
                name: '',
                initials: '',
                sha256_username: '',
                state: '3'
            },
            roomState: {},
            userlist: {},
            groupMembers: [],
            activeMembers: [],
            apportoMembers: [],

            userName: '',
            roomName: '',

            kurentoHostName: '',
            updateSpeakerInterval: 1800,
            thresholdSpeaker: -50,

            /**
             * Chatting dialog is visible only when chat button is clicked.
             *
             * @type Boolean
             */
            chattingVisible: false,
            callVisible: true,
            chattingMinimized: true,
            chattingBoxMinimized: true,
            isFocus: true,
            isVirtualClassroomThumbnail: false,
            isVirtualClassroom: false,

            GLOBAL_ROOM_NAME    : 'apporto',
            SHARE_URL           : 'share_url',
            VOICE               : 'voice',
            CHAT                : 'chat',

            cursor_name: '',

            /**
             * Setting popup is visible when mouse is over the setting button of ribbon
             *
             * @type Boolean
             */
            settingPopupVisible: false,

            /**
             * Remote Apps popup is visible when mouse is over the remote apps button of ribbon
             *
             * @type Boolean
             */
            remoteAppsPopupVisible: false,

            /**
             * The count of max retry
             */
            MAX_RETRY_COUNT: 7,

            /**
             * HelpCenter popup is visible when mouse is over the HelpCenter button of ribbon
             *
             * @type Boolean
             */
            helpCenterPopupVisible: false,

            /** UUID
             *
             * @type string
             */
            uuid: '',

            /**
             * All available assignment list are shown when this variable is true
             *
             * @type Boolean
             */
            assignmentListDialogVisible: false,

            /**
             * Assignment Manager Screen will be shown when this variable is true
             * 
             * @type Boolean
             */
            assignmentManagementDialogVisible: false,

            /**
             * Course name
             * 
             * @type String
             */
            courseName: "",

            /**
             * All available assignment list
             *
             */
            assignmentList:[],

            /**
             * file manager ready or not 
             */
            assignmentManagmentFiles: false,

            /**
             * if there is any file in current directory
             * @type Boolean
             */
            currFileExist: true,

            /**
             * The flag indicates whether the current window is a primary screen
             *
             * @type Boolean
             */
            isPrimaryScreen: true,

            /**
             * The flag indicates whether the secondary window is opened
             *
             * @type Boolean
             */
            isOpenSecondMonitor: false,

            /**
             * The flag indicates whether the tertiary window is opened
             * @type Boolean
             */
            isOpenThirdMonitor: false,

            /**
             * USB list dialog is visible when the USB accessible button is clicked in setting popup
             *
             * @type Boolean
             */
            usbListDlgVisible: false,

            /**
             * Websocket port between the guacamole client and usb agent
             *
             * @type String
             */
            usb_agent_port: "19019",

            /**
             * The info indicating the platform
             */
            platform : {
                osName: UNKNOWN,
                osVersion: UNKNOWN,
            },

            /**
             * Attendance dialog to show the attendance status of the students in the group
             *
             * @type Boolean
             */
            attendanceVisible: false,

            /**
             * Show the dialog to confirm enable logging
             *
             * @type Boolean
             */
             enableLoggingDialogVisible: false,

            /**
             * Show the dialog to display the build version information
             *
             * @type Boolean
             */
             aboutApportoDialogVisible: false,

            /**
             * The options for resolution
             */
            resolutionOptions: [
                {
                    width: 2560,
                    height: 1440
                },
                {
                    width: 1920,
                    height: 1080
                },
                {
                    width: 1280,
                    height: 720
                },
                {
                    width: 1024,
                    height: 768
                },
                {
                    width: 800,
                    height: 600
                },
            ],

            /**
             * The resolution mode
             */
            resolution: {
                width: 0,
                height: 0
            },

            /**
             * The options for scale
             */
            scaleOptions: [
                100,
                125,
                150,
                175,
                200,
                225,
                250
            ],

            /**
             * The scale mode
             */
            scale: 100,

            assignmentStatus: Object.freeze({
                UNLOCKED: "unlocked",
                LOCKED_FUTURE: "locked_future",
                LOCKED_PAST_DUE: "locked_past_due",
                SUBMITTED: "submitted",
                SUBMITTED_LOCKED: "submitted_locked"
            }),

            /**
             * The options for streaming mode
             */
            streamingModeOptions: [
                "Faster streaming",
                "Medium resolution",
                "Better quality"
            ],

            /**
             * The streaming mode
             */
            streamingMode: "Better quality",

            /**
             * Check the low latency detected
             */
            isCheckLowLatency: false,

            /**
             * Enable/Disable the auto resize
             */
            isAutoResize: true,

            /**
             * The value of resize method
             */
            resizeMethod: 0,

            /**
             * Watermark Type
             */
            WATERMARKTYPE: {
                NONE: "none",
                TEXT: "text",
                PICTURE: "picture",
            },

            /**
             * Watermark Layout
             */
            WATERMARKLAYOUT: {
                DIAGONAL: "diagonal",
                HORIZONTAL: "horizontal",
            },

            /**
             * LTI Issuer Types
             */
            LTI_ISSUER: {
                CANVAS: "canvas",
                D2L: "d2l",
                BLACKBOARD: "blackboard"
            },

            /**
             * Resize method Types
             */
            RESIZE_METHOD: {
                NONE: 0,
                DISPLAY_UPDATE: 1,
                RECONNECT: 2
            },

            /**
             * The variable for sftp check needed
             */
            isSftpCheckNeeded: false,

            /**
             * Check the sftp available
             */
            isSftpAvailable: false,

            /**
             * The max timeout for checking the server capacity (in milliseconds)
             */
            max_hap_capacity_timeout: 0,

            /**
             * The max allowed failed value for checking the server capacity
             */
            max_allowed_failed_start: 0,
        };

        // Get the initial information for checking browser and platform
        var userAgent = navigator.userAgent;
        var browser = navigator.appName;
        var detailVersion = '' + parseFloat(navigator.appVersion);
        var majorVersion = parseInt(navigator.appVersion, 10);
        var nameOffset, verOffset, ix;

        // Opera
        if ((verOffset = userAgent.indexOf('Opera')) != -1) {
            ribbonService.browser.isOpera = true;
            detailVersion = userAgent.substring(verOffset + 6);
            if ((verOffset = userAgent.indexOf('Version')) != -1) {
                detailVersion = userAgent.substring(verOffset + 8);
            }
            ribbonService.customerBrowser = "Opera";
        }
        // Opera Next
        if ((verOffset = userAgent.indexOf('OPR')) != -1) {
            ribbonService.browser.isOpera = true;
            detailVersion = userAgent.substring(verOffset + 4);
            ribbonService.customerBrowser = "OPR";
        }
        // Legacy Edge
        else if ((verOffset = userAgent.indexOf('Edge')) != -1) {
            ribbonService.browser.isEdge = true;
            detailVersion = userAgent.substring(verOffset + 5);
            ribbonService.customerBrowser = "Edge";
        }
        // Edge (Chromium)
        else if ((verOffset = userAgent.indexOf('Edg')) != -1) {
            ribbonService.browser.isEdge = true;
            detailVersion = userAgent.substring(verOffset + 4);
            ribbonService.customerBrowser = "Edg";
        }
        // MSIE
        else if ((verOffset = userAgent.indexOf('MSIE')) != -1) {
            ribbonService.browser.isIE = true;
            detailVersion = userAgent.substring(verOffset + 5);
            ribbonService.customerBrowser = "MSIE";
        }
        // Chrome
        else if ((verOffset = userAgent.indexOf('Chrome')) != -1) {
            ribbonService.browser.isChrome = true;
            detailVersion = userAgent.substring(verOffset + 7);
            ribbonService.customerBrowser = "Chrome";
        }
        // Safari
        else if ((verOffset = userAgent.indexOf('Safari')) != -1) {
            ribbonService.browser.isSafari = true;
            detailVersion = userAgent.substring(verOffset + 7);
            if ((verOffset = userAgent.indexOf('Version')) != -1) {
                detailVersion = userAgent.substring(verOffset + 8);
            }
            ribbonService.customerBrowser = "Safari";
        }
        // Firefox
        else if ((verOffset = userAgent.indexOf('Firefox')) != -1) {
            ribbonService.browser.isFirefox = true;
            detailVersion = userAgent.substring(verOffset + 8);
            ribbonService.customerBrowser = "Firefox";
        }
        // MSIE 11+
        else if (userAgent.indexOf('Trident/') != -1) {
            ribbonService.browser.isIE = true;
            detailVersion = userAgent.substring(userAgent.indexOf('rv:') + 3);
        }
        // Other browsers
        else if ((nameOffset = userAgent.lastIndexOf(' ') + 1) < (verOffset = userAgent.lastIndexOf('/'))) {
            browser = userAgent.substring(nameOffset, verOffset);
            detailVersion = userAgent.substring(verOffset + 1);
            if (browser.toLowerCase() == browser.toUpperCase()) {
                browser = navigator.appName;
            }
        }

        // trim the detailVersion string
        if ((ix = detailVersion.indexOf(';')) != -1) detailVersion = detailVersion.substring(0, ix);
        if ((ix = detailVersion.indexOf(' ')) != -1) detailVersion = detailVersion.substring(0, ix);
        if ((ix = detailVersion.indexOf(')')) != -1) detailVersion = detailVersion.substring(0, ix);

        majorVersion = parseInt('' + detailVersion, 10);
        if (isNaN(majorVersion)) {
            detailVersion = '' + parseFloat(navigator.appVersion);
            majorVersion = parseInt(navigator.appVersion, 10);
        }

        // Set the browser version
        ribbonService.browser.majorVersion = majorVersion;
        ribbonService.browser.detailVersion = detailVersion;

        if (ribbonService.customerBrowser == "Chrome") {
            navigator.userAgentData
                .getHighEntropyValues(['uaFullVersion'])
                .then(ua => {
                    // uaFullVersion: "114.0.5735.198"
                    ribbonService.browser.detailVersion = ua.uaFullVersion;
                });
        }

        console.log("Customer browser: " + ribbonService.customerBrowser + ", version: " + detailVersion);

        // Get the initial information for checking platform
        var os = UNKNOWN;
        var clientStrings = [
            {s:'Windows 10', r:/(Windows 10.0|Windows NT 10.0)/},
            {s:'Windows 8.1', r:/(Windows 8.1|Windows NT 6.3)/},
            {s:'Windows 8', r:/(Windows 8|Windows NT 6.2)/},
            {s:'Windows 7', r:/(Windows 7|Windows NT 6.1)/},
            {s:'Windows Vista', r:/Windows NT 6.0/},
            {s:'Windows Server 2003', r:/Windows NT 5.2/},
            {s:'Windows XP', r:/(Windows NT 5.1|Windows XP)/},
            {s:'Windows 2000', r:/(Windows NT 5.0|Windows 2000)/},
            {s:'Windows ME', r:/(Win 9x 4.90|Windows ME)/},
            {s:'Windows 98', r:/(Windows 98|Win98)/},
            {s:'Windows 95', r:/(Windows 95|Win95|Windows_95)/},
            {s:'Windows NT 4.0', r:/(Windows NT 4.0|WinNT4.0|WinNT|Windows NT)/},
            {s:'Windows CE', r:/Windows CE/},
            {s:'Windows 3.11', r:/Win16/},
            {s:'Android', r:/Android/},
            {s:'Open BSD', r:/OpenBSD/},
            {s:'Sun OS', r:/SunOS/},
            {s:'Chrome OS', r:/CrOS/},
            {s:'Linux', r:/(Linux|X11(?!.*CrOS))/},
            {s:'iOS', r:/(iPhone|iPad|iPod)/},
            {s:'Mac OS X', r:/Mac OS X/},
            {s:'Mac OS', r:/(Mac OS|MacPPC|MacIntel|Mac_PowerPC|Macintosh)/},
            {s:'QNX', r:/QNX/},
            {s:'UNIX', r:/UNIX/},
            {s:'BeOS', r:/BeOS/},
            {s:'OS/2', r:/OS\/2/},
            {s:'Search Bot', r:/(nuhk|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask Jeeves\/Teoma|ia_archiver)/}
        ];

        // Check the os name
        for (var id in clientStrings) {
            var cs = clientStrings[id];
            if (cs.r.test(userAgent)) {
                os = cs.s;
                break;
            }
        }

        // Check the os detailVersion
        var osVersion = UNKNOWN;
        if (/Windows/.test(os)) {
            osVersion = /Windows (.*)/.exec(os)[1];
            os = 'Windows';
        }

        switch (os) {
            case 'Mac OS':
            case 'Mac OS X':
            case 'Android':
                osVersion = /(?:Android|Mac OS|Mac OS X|MacPPC|MacIntel|Mac_PowerPC|Macintosh) ([\.\_\d]+)/.exec(userAgent)[1];
                break;
            case 'iOS':
                osVersion = /OS (\d+)_(\d+)_?(\d+)?/.exec(userAgent);
                osVersion = osVersion[1] + '.' + osVersion[2] + '.' + (osVersion[3] | 0);
                break;
        }

        // Set the os name and version
        ribbonService.platform.osName = os;
        ribbonService.platform.osVersion = osVersion;

        return ribbonService;
    } ]);

