/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

angular.module('ribbon').service('ServiceCall', ['$rootScope', '$window', 'ribbonService', 'ServiceParticipant', 'ServiceRoom',
    function ($rootScope, $window, ribbonService, ServiceParticipant, ServiceRoom) {
        var ws;
        var user = '';
        var from = '';
        var username;
        var roomname;
        var callStartTime;
        var that = this;

        this.registerState = 0;
        const NOT_REGISTERED = 0;
        const REGISTERING = 1;
        const REGISTERED = 2;

        this.callState = 0;
        const NO_CALL = 0;
        const IN_CALL = 1;
        const INCOMING_CALL = 2;
        const OUTGOING_CALL = 3;
        const CALLING_NOW = 4;
        const CALLING_ACCEPTED = 5;

        const DECLINED = 'declined';
        const MISSED = 'missed';
        const BUSY = 'busy';

        this.infoText = '';
        this.userText = '';
        this.callDialogVisible = false;
        this.callConfirmDialogVisible = false;
        this.isAccept = false;
        this.ring = '';
        const INCOMING_RING = 'app/ext/ribbon/rings/calls_incoming_ring.mp3';
        const OUTGOING_RING = 'app/ext/ribbon/rings/calls_outgoing_ring.mp3';

        var videoInput;
        var videoOutput;
        var webRtcPeer;

        var timeout;

        function sendMessage(message) {
            var jsonMessage = JSON.stringify(message);
            // Websocket connection is open and ready to communicate.
            if (ws.readyState === 1) {
                ws.send(jsonMessage);
            }
        }

        function setRegisterState(nextState) {
            switch (nextState) {
                case NOT_REGISTERED:
                    ribbonService.callVisible = false;
                    setCallState(NO_CALL);
                    break;
                case REGISTERED:
                    setCallState(NO_CALL);
                    break;
                default:
                    return;
            }
            registerState = nextState;
            $rootScope.$broadcast('registerState:updated', registerState);
        }

        function setCallState(nextState) {
            switch (nextState) {
                case NO_CALL:
                    that.callConfirmDialogVisible = false;
                    that.callDialogVisible = false;
                    if ($rootScope.groupcallStream) {
                        $rootScope.groupcallStream.audioEnabled = true;
                    }
                    break;
                case IN_CALL:
                    callStartTime = new Date().getTime();
                case CALLING_ACCEPTED:
                case INCOMING_CALL:
                case OUTGOING_CALL:
                    that.callConfirmDialogVisible = true;
                    if ($rootScope.groupcallStream) {
                        $rootScope.groupcallStream.audioEnabled = false;
                    }
                    break;
                default:
                    break;
            }
            that.isAccept = false;
            that.callState = nextState;
            $rootScope.$broadcast('callState:updated', that.callState);
        }

        function onOfferCall(error, offerSdp) {
            if (error)
                return console.error('Error generating the offer');
            console.log('Invoking SDP offer callback function');
            var message = {
                id: 'call',
                from: user,
                to: from,
                room: roomname,
                sdpOffer: offerSdp,
            };
            sendMessage(message);
        }

        function registerResponse(message) {
            if (message.response == 'accepted') {
                setRegisterState(REGISTERED);
            }
            else {
                setRegisterState(NOT_REGISTERED);
                var errorMessage = message.message ? message.message : 'Unknown reason for register rejection.';
                console.log(errorMessage);
            }
        }

        function callResponse(message) {
            if (message.response != 'accepted') {
                console.info('Call not accepted by peer. Closing call');
                var errorMessage = message.message ? message.message : 'Unknown reason for call rejection.';
                console.log(errorMessage);
                if (message.response.includes('rejected') && message.response.includes('busy')) {
                    var kurento = ServiceRoom.getKurento(roomname);
                    kurento.sendMessage(roomname, user, BUSY, from, ribbonService.VOICE, function (response) {
                        var now = new Date().getTime();
                        ServiceParticipant.showSendMessage(roomname, from, BUSY, user, now, ribbonService.VOICE, response.id);
                    });
                    stop();
                }
                else if (message.response == 'rejected' && message.message == BUSY) {
                    stop();
                }
                else if (message.message == DECLINED) {
                    stop();
                }
                else if (that.callState != IN_CALL) {
                    setCallState(NO_CALL);
                }
            }
            else {
                setCallState(IN_CALL);
                webRtcPeer.processAnswer(message.sdpAnswer, function (error) {
                    if (error)
                        return console.error(error);
                });
            }
        }

        function callingNowResponse(message) {
            if (message.user != user) {
                if (message.response == 'busy') {
                    setCallState(CALLING_NOW);
                }
                else if (message.response == 'stop') {
                    setCallState(NO_CALL);
                }
                else if (message.response == 'accepted') {
                    setCallState(CALLING_ACCEPTED);
                    from = message.partner;
                }
            }
        }

        function startCommunication(message) {
            setCallState(IN_CALL);
            webRtcPeer.processAnswer(message.sdpAnswer, function (error) {
                if (error)
                    return console.error(error);
            });
        }

        function incomingCall(message) {
            // If bussy just reject without disturbing user
            if (that.callState != NO_CALL) {
                var response = {
                    id: 'incomingCallResponse',
                    from: message.from,
                    room: roomname,
                    callResponse: 'reject',
                    message: BUSY
                };
                var kurento = ServiceRoom.getKurento(roomname);
                kurento.sendMessage(roomname, user, BUSY, message.from, ribbonService.VOICE, function (response) {
                    var now = new Date().getTime();
                    ServiceParticipant.showSendMessage(roomname, message.from, BUSY, user, now, ribbonService.VOICE, response.id);
                });
                return sendMessage(response);
            }

            from = message.from;
            roomname = message.room;
            that.ring = INCOMING_RING;
            setCallState(INCOMING_CALL);
        }

        function onOfferIncomingCall(error, offerSdp) {
            if (error)
                return console.error("Error generating the offer");

            var response = {
                id: 'incomingCallResponse',
                from: from,
                room: roomname,
                callResponse: 'accept',
                sdpOffer: offerSdp
            };
            sendMessage(response);
        }

        function stop(message) {
            clearTimeout(timeout);

            setCallState(NO_CALL);
            if (webRtcPeer) {
                webRtcPeer.dispose();
                webRtcPeer = null;

                if (!message) {
                    var message = {
                        id: 'stop'
                    }
                    sendMessage(message);
                }
            }
            hideSpinner(videoInput, videoOutput);
        }

        function onIceCandidate(candidate) {
            console.log("Local candidate" + JSON.stringify(candidate));

            var message = {
                id: 'onIceCandidate',
                candidate: candidate
            };
            sendMessage(message);
        }

        function onError() {
            setCallState(NO_CALL);
        }

        function showSpinner(input, output) {
            input.style.opacity = 0;
            input.style.visibility = 'hidden';
            output.style.opacity = 0;
            output.style.visibility = 'hidden';
        }

        function hideSpinner(input, output) {
            input.src = '';
            input.srcObject = null;
            input.style.opacity = 0;
            input.style.visibility = 'hidden';
            output.src = '';
            output.srcObject = null;
            output.style.opacity = 0;
            output.style.visibility = 'hidden';
        }

        this.register = function (options) {
            user = options.user;
            if (!options.adu) {
                options.adu = '';
            }
            if (!options.session) {
                options.session = '';
            }

            var message = {
                id: 'register',
                name: user,
                adu: options.adu,
                session: options.session
            };

            sendMessage(message);
        }

        this.call = function (dest) {
            from = dest;
            setCallState(OUTGOING_CALL);
            that.ring = OUTGOING_RING;

            showSpinner(videoInput, videoOutput);

            var options = {
                localVideo: videoInput,
                remoteVideo: videoOutput,
                mediaConstraints: {
                    audio: true,
                    video: false
                },
                onicecandidate: onIceCandidate,
                onerror: onError
            }
            webRtcPeer = new kurentoUtils.WebRtcPeer.WebRtcPeerSendrecv(options,
                function (error) {
                    if (error) {
                        return console.error(error);
                    }
                    webRtcPeer.generateOffer(onOfferCall);
                });

            timeout = setTimeout(function () {
                if (that.callState == OUTGOING_CALL) {
                    that.reject();
                }
            }, 30000);
        }

        this.accept = function () {
            showSpinner(videoInput, videoOutput);

            var options = {
                localVideo: videoInput,
                remoteVideo: videoOutput,
                mediaConstraints: {
                    audio: true,
                    video: false
                },
                onicecandidate: onIceCandidate,
                onerror: onError
            }
            webRtcPeer = new kurentoUtils.WebRtcPeer.WebRtcPeerSendrecv(options,
                function (error) {
                    if (error) {
                        return console.error(error);
                    }
                    webRtcPeer.generateOffer(onOfferIncomingCall);
                });
        }

        this.reject = function () {
            var response = {
                id: 'incomingCallResponse',
                from: from,
                room: roomname,
                callResponse: 'reject',
                message: MISSED
            };
            sendMessage(response);
            stop();
            clearTimeout(timeout);
            var kurento = ServiceRoom.getKurento(roomname);
            kurento.sendMessage(roomname, user, MISSED, from, ribbonService.VOICE, function (response) {
                var now = new Date().getTime();
                ServiceParticipant.showSendMessage(roomname, from, MISSED, user, now, ribbonService.VOICE, responseid);
            });
        }

        this.terminate = function () {
            stop();
            clearTimeout(timeout);
            var callTime = new Date().getTime() - callStartTime;
            $rootScope.$broadcast('call:ended', {
                time: callTime
            });
        }

        this.decline = function () {
            var response = {
                id: 'incomingCallResponse',
                from: from,
                room: roomname,
                callResponse: 'reject',
                message: DECLINED
            };
            sendMessage(response);
            stop();
            clearTimeout(timeout);
            var kurento = ServiceRoom.getKurento(roomname);
            kurento.sendMessage(roomname, user, DECLINED, from, ribbonService.VOICE, function (response) {
                var now = new Date().getTime();
                ServiceParticipant.showSendMessage(roomname, from, DECLINED, user, now, ribbonService.VOICE, response.id);
            });
        }

        this.getWebSocket = function () {
            return ws;
        };

        this.setWebSocket = function (wsUri, options) {
            ws = new WebSocket(wsUri);
            user = options.user;
            videoInput = document.getElementById('videoInput');
            videoOutput = document.getElementById('videoOutput');

            ws.onopen = function () {
                that.register(options);
            }

            ws.onmessage = function (message) {
                var parsedMessage = JSON.parse(message.data);
                console.info('Received call message: ' + message.data);

                switch (parsedMessage.id) {
                    case 'registerResponse':
                        registerResponse(parsedMessage);
                        break;
                    case 'callResponse':
                        callResponse(parsedMessage);
                        break;
                    case 'callingNowResponse':
                        callingNowResponse(parsedMessage);
                        break;
                    case 'incomingCall':
                        incomingCall(parsedMessage);
                        break;
                    case 'startCommunication':
                        startCommunication(parsedMessage);
                        break;
                    case 'stopCommunication':
                        console.info('Communication ended by remote peer');
                        stop(true);
                        break;
                    case 'iceCandidate':
                        webRtcPeer.addIceCandidate(parsedMessage.candidate, function (error) {
                            if (error)
                                return console.error('Error adding candidate: ' + error);
                        });
                        break;
                    default:
                        console.error('Unrecognized message', parsedMessage);
                }
            }

            ws.onclose = function (e) {
                Rollbar.error("WebSocket is closed with Kurento call room" + e);
                console.log('Socket is closed. Reconnect will be attempted in 5 second.');
                setTimeout(function () {
                    that.setWebSocket(wsUri, options);
                }, 5000);
            }

            ws.onerror = function (err) {
                Rollbar.error("WebSocket connection failed with Kurento call room" + err);
                console.error('Socket encountered error: ', err, 'Closing socket');
                ws.close();
            };
        };

        this.getUser = function () {
            return user;
        };

        this.setUser = function (name) {
            user = name;
        };

        this.getFrom = function () {
            var fromName;

            for (room in ribbonService.groupMembers) {
                var index = ribbonService.groupMembers[room].findIndex(function (member) {
                    return from.includes(member['windows_name']);
                });

                if (index > -1) {
                    fromName = ribbonService.groupMembers[room][index]['name'];
                    break;
                }
            }
            return fromName;
        };

        this.setFrom = function (name) {
            from = name;
        };

        this.setUsername = function (name) {
            username = name;
        };

        this.getUsername = function () {
            return username;
        };

        this.setRoomname = function (name) {
            roomname = name;
        };
    }
]);
