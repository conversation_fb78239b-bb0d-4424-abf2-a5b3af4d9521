/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for managing file explorer.
 * 
 * This service provides function for getting the file browser url,
 * openning and closing file explorer.
 * 
 */
angular.module('ribbon').factory('fileBrowserService',
        [ '$injector', function fileBrowserService($injector) {

            var authenticationService = $injector.get('authenticationService');
            var ClientIdentifier      = $injector.get('ClientIdentifier');
            var $routeParams          = $injector.get('$routeParams');
            var $http                 = $injector.get('$http')
            var $location             = $injector.get('$location');
            var $q                    = $injector.get('$q');
            var guacNotification      = $injector.get('guacNotification');
            var sce                   = $injector.get('$sce');

            var service = { };

            function isValidUrl(url) {
                try {
                    new URL(url);
                    return true;
                } catch (e) {
                    return false;
                }
            }

            /**
             * Get file browser URL.
             * Do not return file browser URL if classroom is active.
             */
            service.getFileBrowserUrl = function getFileBrowserUrl() {
                if($location.path().indexOf("classroom") > -1) {
                    console.error("File browser is not supported in classroom session.") 
                    return;
                }

                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                if (datasource !== "encryptedurl-jdbc") {
                    console.error("File browser is supported only in main session.") 
                    return;
                }

                var deffered = $q.defer();
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };

                var req = {
                    method: 'GET',
                    url: "api/session/ext/" + datasource + "/filebrowser/url/",
                    params: httpParameters
                };

                $http(req)
                .then(function(response) {
                    if (isValidUrl(response.data.url))
                        deffered.resolve(response.data.url);
                    else
                        deffered.reject("Invalid file browser URL: " + response.data.url);
                })
                .catch(function(response) {
                    console.error("Error retrieving file browser url: ", response.message);
                    deffered.reject(response.message);
                })
                .finally( function() { 
                    // Empty process
                });

                return deffered.promise;
            }

            /**
             * Show error dialog related to file browser
             */
            service.showFileBrowserErrorDialog = function () {
                // Action for closing notification dialog
                var OK_ACTION = {
                    name      : "APP.ACTION_ACKNOWLEDGE",
                    callback  : function cancelCallback() {
                        guacNotification.showStatus(false);
                    }
                };

                // Build array of available actions
                var actions = [OK_ACTION];

                // Show the notification dialog
                guacNotification.showStatus({
                    title   : "CLIENT.MESSAGE_TITLE",
                    text    : {
                        key : "CLIENT.ERROR_FILE_BROWSER_START"
                    },
                    actions : actions
                });
            }
            
            service.logoutFB = function(url) {
                var fbUrl = new URL(url);
                var fbToken = encodeURIComponent(fbUrl.searchParams.get('token'));
            
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                var token = encodeURIComponent(authenticationService.getCurrentToken());
                var  id = encodeURIComponent(clientIdentifier.id);
            
                var targetUrl = `api/session/ext/${datasource}/filebrowser/logout/?fbToken=${fbToken}&token=${token}&id=${id}`;
            
                /*
                * Send a beacon request to logout from file browser.
                * This is necessary because this function may be called from window.unload event
                * and standard http request may fail if it is not finished before browser window closes.
                */
                var success = navigator.sendBeacon(targetUrl, null); 
                if (success) { 
                    console.debug('Logged out from file browser');
                } else {
                    console.error('Error during file browser logout');
                }
            };
            

            return service;
        } ]);
