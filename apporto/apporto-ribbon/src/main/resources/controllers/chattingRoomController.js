/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon').controller('chattingRoomController',
    ['$scope', '$timeout', '$injector', 'ServiceParticipant', 'ServiceRoom', 'ServiceCall',

    function chattingRoomController($scope, $timeout, $injector, ServiceParticipant, ServiceRoom, ServiceCall) {
        var ServiceParticipant = $injector.get('ServiceParticipant');
        var ServiceRoom        = $injector.get('ServiceRoom');
        var ServiceCall        = $injector.get('ServiceCall');
        var $rootScope         = $injector.get('$rootScope');
        var $window            = $injector.get('$window');

        $scope.ribbonService   = $injector.get('ribbonService');
        $scope.serviceShare    = $injector.get('ServiceShare');

        // Google Tag Manager
        var dataLayer = $window.dataLayer = $window.dataLayer || [];

        var roomName = $scope.ribbonService.GLOBAL_ROOM_NAME;
        var kurento = ServiceRoom.getKurento(roomName);

        var isScrollBottom;
        
        $scope.newMessage = '';
        $scope.registerState = ServiceCall.registerState;
        $scope.callState = ServiceCall.callState;

        $timeout(function() {
            var chatting_box = angular.element(document.querySelector("[id='room-"+$scope.roominfo.windows_name+"']"));

            if (!!chatting_box[0] && chatting_box[0].offsetHeight == chatting_box[0].scrollHeight) {
                if ($scope.messages) {
                    for (var i = 0; i < $scope.messages.length; i++) {
                        if ($scope.messages[i].user == $scope.roominfo['windows_name'] && $scope.messages[i].state != 'read') {
                            kurento.sendAckMessage($scope.messages[i].id, roomName, $scope.messages[i].user, $scope.messages[i].message, $scope.ribbonService.userinfo['windows_username'], $scope.messages[i].timestamp, $scope.messages[i].type);

                            $scope.messages[i].state = 'read';
                        }
                    }
                }
            }

            chatting_box.bind('scroll', function () {
                if (chatting_box.scrollTop() == 0) {
                    var time = $scope.messages[0]['timestamp'];
                    getHistory(time);
                    isScrollBottom = false;
                }
                else if (chatting_box[0].scrollTop + chatting_box[0].offsetHeight >= chatting_box[0].scrollHeight - 1) {
                    if ($scope.messages) {
                        for (var i = 0; i < $scope.messages.length; i++) {
                            if ($scope.messages[i].user == $scope.roominfo['windows_name'] && $scope.messages[i].state != 'read') {
                                kurento.sendAckMessage($scope.messages[i].id, roomName, $scope.messages[i].user, $scope.messages[i].message, $scope.ribbonService.userinfo['windows_username'], $scope.messages[i].timestamp, $scope.messages[i].type);
                                
                                $scope.messages[i].state = 'read';
                            }
                        }
                    }
                }
            })
        }, 100);

        var getCallMessage = function (msg) {
            var name = $scope.roominfo['name'];
            if (msg.user == $scope.ribbonService.userName) {
                switch (msg.message) {
                    case ServiceParticipant.DECLINED:
                        message = 'You' + ' declined a call.';
                        break;
                    case ServiceParticipant.MISSED:
                        message = name + ' missed a call.';
                        break;
                    case ServiceParticipant.BUSY:
                        message = name + ' is busy.';
                        break;
                    case ServiceParticipant.DONTDISTURB:
                        message = name + ' is in Do not disturb mode.';
                        break;
                    case ServiceParticipant.UNAVAILABLE:
                        message = name + ' is unavailable.';
                        break;
                }
            }
            else {
                switch (msg.message) {
                    case ServiceParticipant.DECLINED:
                        message = name + ' declined a call.';
                        break;
                    case ServiceParticipant.MISSED:
                        message = 'You' + ' missed a call.';
                        break;
                    case ServiceParticipant.BUSY:
                        message = 'You' + ' missed a call.';
                        break;
                    case ServiceParticipant.DONTDISTURB:
                        message = 'You' + ' missed a call.';
                        break;
                    case ServiceParticipant.UNAVAILABLE:
                        message = 'You' + ' missed a call.';
                        break;
                }
            }
            return message;
        }

        var getMessages = function () {
            if ($scope.roominfo['id'] && $scope.roominfo['id'] == 'group') {
                if (ServiceParticipant.messages && ServiceParticipant.messages[$scope.roominfo['windows_name']]) {
                    $scope.messages = ServiceParticipant.messages[$scope.roominfo['windows_name']];
                }
                else {
                    $scope.messages = [];
                }
            }
            else {
                if (ServiceParticipant.messages && ServiceParticipant.messages[$scope.roominfo['windows_name']]) {
                    $scope.messages = ServiceParticipant.messages[$scope.roominfo['windows_name']][$scope.ribbonService.GLOBAL_ROOM_NAME];
                }
                else {
                    $scope.messages = [];
                }

                for (var i = 0; i < $scope.messages.length; i++) {
                    if ($scope.messages[i].user == $scope.roominfo['windows_name'] && $scope.messages[i].state != 'read') {
                        kurento.sendAckMessage($scope.messages[i].id, roomName, $scope.messages[i].user, $scope.messages[i].message, $scope.ribbonService.userinfo['windows_username'], $scope.messages[i].timestamp, $scope.messages[i].type);
                        $scope.messages[i].state = 'read';
                    }

                    if ($scope.messages[i].type == $scope.ribbonService.VOICE) {
                        $scope.messages[i].callMessage = getCallMessage($scope.messages[i]);
                    }
                }
            }
        }

        var setScrollBottom = function() {
            $timeout(function() {
                var container = document.getElementById("room-" + $scope.roominfo.windows_name);
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            }, 100);
        }

        var getHistory = function(time) {
            index = $scope.ribbonService.apportoMembers.findIndex(function(member) {
                return member['windows_name'] == $scope.roominfo['windows_name'];
            });
            if (index > -1 && !$scope.ribbonService.apportoMembers[index]['isGetHistory']) {
                $scope.ribbonService.apportoMembers[index]['isGetHistory'] = true;
                kurento && kurento.getHistory($scope.ribbonService.GLOBAL_ROOM_NAME, $scope.ribbonService.userinfo['windows_username'], $scope.roominfo['windows_name'], 50, time, '');
            }
        }

        getMessages();
        setScrollBottom();
        isScrollBottom = true;
        if ($scope.messages.length == 0) {
            var now = new Date().getTime();
            getHistory(now);
        }
        else if ($scope.messages.length < 50) {
            var time = $scope.messages[0]['timestamp'];
            getHistory(time);
        }

        $scope.$on('registerState:updated', function (event, registerState) {
            $scope.registerState = registerState;
        });

        $scope.$on('callState:updated', function (event, callState) {
            $scope.callState = callState;
        });

        $scope.$on('messages:updated', function(event) {
            getMessages();
            setScrollBottom();
        });

        $scope.$on('history:updated', function(event) {
            getMessages();
            if (isScrollBottom) {
                setScrollBottom();
            }
        });

        $scope.closeChat = function closeChat() {
            var index = $scope.ribbonService.activeMembers.indexOf($scope.roominfo);
            $scope.ribbonService.activeMembers.splice(index, 1);
            $scope.ribbonService.ribbonActive = false;
            $rootScope.firstFocusableElement = null;
            $rootScope.lastFocusableElement = null;
            $rootScope.setFocusableElements('#chatting-dialog');
        }

        $scope.minimizeChat = function minimizeChat() {
            $scope.ribbonService.chattingBoxMinimized = true;
        }

        $scope.call = function call() {
            ServiceCall.setUsername($scope.roominfo['name']);
            ServiceCall.setRoomname(roomName);
            switch ($scope.roominfo['state']) {
                case '0': // Available
                case '2': // Away
                    ServiceCall.call($scope.roominfo['windows_name']);
                    break;
                case '1': // Do not disturb
                    ServiceCall.callConfirmDialogVisible = true;
                    ServiceCall.isAccept = true;
                    ServiceCall.callState = 3;
                    ServiceCall.setFrom($scope.roominfo['windows_name']);
                    var timeout = setTimeout(function() {
                        if (ServiceCall.callConfirmDialogVisible) {
                            ServiceCall.callConfirmDialogVisible = false;
                            kurento.sendMessage(roomName, $scope.ribbonService.userName, 'dontdisturb', $scope.roominfo['windows_name'], $scope.ribbonService.VOICE, function (response) {
                                var now = new Date().getTime();
                                ServiceParticipant.showSendMessage(roomName, $scope.roominfo['windows_name'], 'dontdisturb', $scope.ribbonService.userName, now, $scope.ribbonService.VOICE, response.id);
                            });
                            ServiceCall.callState = 0;
                        }
                        clearTimeout(timeout);
                    }, 5000);
                    break;
                default: // Offline
                    ServiceCall.callConfirmDialogVisible = true;
                    ServiceCall.isAccept = true;
                    ServiceCall.callState = 3;
                    ServiceCall.setFrom($scope.roominfo['windows_name']);
                    var timeout = setTimeout(function() {
                        if (ServiceCall.callConfirmDialogVisible) {
                            ServiceCall.callConfirmDialogVisible = false;
                            kurento.sendMessage(roomName, $scope.ribbonService.userName, 'unavailable', $scope.roominfo['windows_name'], $scope.ribbonService.VOICE, function (response) {
                                var now = new Date().getTime();
                                ServiceParticipant.showSendMessage(roomName, $scope.roominfo['windows_name'], 'unavailable', $scope.ribbonService.userName, now, $scope.ribbonService.VOICE, response.id);
                            });
                            ServiceCall.callState = 0;
                        }
                        clearTimeout(timeout);
                    }, 5000);
                    break;
            }
        }

        $scope.terminate = function terminate() {
            ServiceCall.terminate();
        }

        $scope.createMessage = function createMessage() {
            if ($scope.newMessage.length >= 500) {
                return ;
            }
            if (!$scope.newMessage) return ;

            // Google Tag Manager
            dataLayer.push({
                event: 'Number of chats',
                button_name: 'btn_send_message',
                sub_domain: $scope.ribbonService.licenses.subdomain
            });

            if ($scope.roominfo['id'] && $scope.roominfo['id'] == 'group') {
                kurento.sendMessage(roomName, $scope.ribbonService.userName, $scope.newMessage, '', $scope.ribbonService.CHAT);
                $scope.newMessage  = '';
            }
            else {
                kurento.sendMessage(roomName, $scope.ribbonService.userName, $scope.newMessage, $scope.roominfo['windows_name'], $scope.ribbonService.CHAT, function (response) {
                    var now = new Date().getTime();
                    ServiceParticipant.showSendMessage(roomName, $scope.roominfo['windows_name'], $scope.newMessage, $scope.ribbonService.userName, now, $scope.ribbonService.CHAT, response.id);
                    $scope.newMessage  = '';
                });
            }
        }

        $scope.shareDialog = function shareDialog() {
            $scope.ribbonService.messengerShareDialogVisible = true;
            $scope.serviceShare.user = $scope.roominfo;
        }
    }
]);

