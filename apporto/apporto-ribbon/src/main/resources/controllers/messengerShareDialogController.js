/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon').controller('messengerShareDialogController',
    ['$scope', '$location', '$injector',

        function messengerShareDialogController($scope, $location, $injector) {
            var ManagedClient           = $injector.get('ManagedClient');
            var guacClientManager       = $injector.get('guacClientManager');
            var tunnelService           = $injector.get('tunnelService');
            var $routeParams            = $injector.get('$routeParams');
            var $interval               = $injector.get('$interval');
            var ClientIdentifier        = $injector.get('ClientIdentifier');
            var $window                 = $injector.get('$window');
            var $rootScope              = $injector.get('$rootScope');
            var ServiceParticipant      = $injector.get('ServiceParticipant');
            var ServiceRoom             = $injector.get('ServiceRoom');
            var guacNotification        = $injector.get('guacNotification');
            var authenticationService   = $injector.get('authenticationService');
            var $http                   = $injector.get('$http');
            var ManagedShareLink        = $injector.get('ManagedShareLink');

            $scope.ribbonService        = $injector.get('ribbonService');
            $scope.serviceShare         = $injector.get('ServiceShare');

            var roomName = $scope.ribbonService.GLOBAL_ROOM_NAME;
            var kurento = ServiceRoom.getKurento(roomName);

            var checkAvailable;
            $scope.counter = 0;

            // When started, periodically query client until it is possible to retrieve sharing links.
            var queryClient;

            if ($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                return;
            }

            // This service is required by directive used in ribbon template html
            $scope.infoService = $injector.get('infoService');

            $scope.client = null;

            $scope.isRemoteControl = false;

            $scope.shareKey = "undefined";
            $scope.shareKeys = [];

            // to remove sharing profile  when close dialog
            $scope.enableRMProfile = false;
            $scope.loadingProfile = false;

            $scope.onCloseWindow = function () {
                if ($scope.shareLinkVO !== DEFAULT_VO_LINK) {
                    $scope.shareKey = $scope.shareLinkVO;
                }

                if ($scope.shareLinkFC !== DEFAULT_FC_LINK) {
                    $scope.shareKey = $scope.shareLinkFC;
                }

                angular.forEach($scope.shareKeys, function (profile) {
                    $rootScope.stopSharing(profile);
                });
                $scope.shareKeys = [];
            }

            // Get client reference when route changes
            $scope.$on('$routeChangeSuccess', getClient);

            // Trigger receive share_url event
            var ACCEPT_ACTION = {
                name: "SHARE_SUBMENU.SHARE_ACCEPT",
                // Handle action
                callback: function acknowledgeCallback() {
                    var index = $scope.serviceShare.from.findIndex(function(user) {
                        return user.isOpen == false;
                    })
                    $scope.serviceShare.from[index].isOpen = true;
                    var q = {
                        presenter_url: $scope.serviceShare.from[index].share_url,
                        faculty_name: $scope.serviceShare.from[index].name,
                        isRemoteControl: $scope.serviceShare.from[index].isRemoteControl,
                        isSharingUrl: true,
                        id: $scope.serviceShare.from[index].id,
                        token: $scope.serviceShare.from[index].token,
                        server_count: $scope.serviceShare.from[index].server_count,
                    }
                    $window.open($window.location.origin + $window.location.pathname + '#/classroom?q=' + btoa(JSON.stringify(q)), '_blank');

                    kurento.sendMessage(roomName, $scope.ribbonService.userName, $scope.serviceShare.ACCEPT, $scope.serviceShare.from[index].user, $scope.ribbonService.SHARE_URL);
                    guacNotification.showStatus(false);
                }
            };
            var DECLINE_ACTION = {
                name: "SHARE_SUBMENU.SHARE_DECLINE",
                className: "fClose",
                // Handle action
                callback: function acknowledgeCallback() {
                    var index = $scope.serviceShare.from.findIndex(function(user) {
                        return user.isOpen == false;
                    });
                    kurento.sendMessage(roomName, $scope.ribbonService.userName, $scope.serviceShare.DECLINE, $scope.serviceShare.from[index].user, $scope.ribbonService.SHARE_URL, function (response) {
                        var now = new Date().getTime();
                        ServiceParticipant.showSendMessage(roomName, $scope.serviceShare.from[index].user, 'You declined screen sharing.', $scope.ribbonService.userName, now, $scope.ribbonService.CHAT, response.id);
                    });

                    $scope.serviceShare.from.splice(index, 1);
                    guacNotification.showStatus(false);
                }
            };

            $scope.$on("share_url", function (event, data) {
                var now = new Date().getTime();
                var userlist = $scope.ribbonService.userlist;
                var index = -1;

                for (var group in userlist) {
                    index = userlist[group].findIndex(function (user) {
                        return data.user == user.windows_name;
                    })

                    if (index >= 0) {
                        break;
                    }
                }

                $scope.ribbonService.cursor_name = '';
                guacNotification.showStatus(false);
                switch (data.message) {
                    case $scope.serviceShare.ACCEPT:
                        $scope.ribbonService.cursor_name = userlist[group][index].name;
                        $scope.serviceShare.state = $scope.serviceShare.SHARING;
                        $scope.counter = 0;
                        $interval.cancel(checkAvailable);
                        $scope.ribbonService.messengerShareDialogVisible = false;
                        $scope.startShare();
                        window.open($scope.shareLinkVO+'&isSharingScreenWith='+$scope.serviceShare.user.name);
                        break;
                    case $scope.serviceShare.DECLINE:
                        if (index >= 0) {
                            ServiceParticipant.showSendMessage(roomName, data.user, userlist[group][index].name + ' declined screen sharing.', data.dest, now, $scope.ribbonService.CHAT);
                        }
                        $scope.serviceShare.state = $scope.serviceShare.NOT_SHARE;
                        $scope.counter = 0;
                        $interval.cancel(checkAvailable);
                        break;
                    case $scope.serviceShare.UNAVAILABLE:
                        if (index >= 0) {
                            kurento.sendMessage(roomName, $scope.ribbonService.userName, $scope.serviceShare.STOP, $scope.serviceShare.user.windows_name, $scope.ribbonService.SHARE_URL, function (response) {
                                ServiceParticipant.showSendMessage(roomName, data.user, userlist[group][index].name + ' is not available for screen sharing.', data.dest, now, $scope.ribbonService.CHAT, response.id);
                            });
                        }
                        $scope.serviceShare.state = $scope.serviceShare.NOT_SHARE;
                        break;
                    case $scope.serviceShare.STOP:
                        guacNotification.showStatus(false);
                        ServiceParticipant.showSendMessage(roomName, data.user, 'You missed screen sharing.', data.dest, now, $scope.ribbonService.CHAT);
                        break;
                    default:
                        if (index >= 0) {
                            var share_data;
                            try {
                                share_data = JSON.parse(atob(data.message));
                            }
                            catch(e) {
                                return;
                            }

                            $scope.serviceShare.from.push({
                                user: data.user,
                                name: userlist[group][index].name,
                                share_url: share_data.share_url,
                                isRemoteControl: share_data.isRemoteControl,
                                isOpen: false,
                                id: share_data.id,
                                token: share_data.token,
                                server_count: $scope.ribbonService.serverGroupCount,
                            });

                            guacNotification.showStatus({
                                title: "SHARE_SUBMENU.SHARE_TITLE",
                                text: {
                                    key: "SHARE_SUBMENU.SHARE_CONFIRM_RECIEVE_MESSAGE",
                                    variables: { NAME: userlist[group][index].name }
                                },
                                actions: [DECLINE_ACTION, ACCEPT_ACTION]
                            });
                            return;
                        }
                        break;
                }
            })

            // When dialog is presented, get client reference (maybe it is not needed, TBC)
            $scope.$watch('ribbonService.messengerShareDialogVisible', function (visible) {
                if (visible && $scope.serviceShare.state == $scope.serviceShare.NOT_SHARE) {
                    $('input[type=checkbox]').attr('disabled', true);
                    $('.buttons > button').attr('disabled', true);
                    getClient();

                    $scope.loadingProfile = true;
                    retrieveSharingProfiles($scope.client.tunnel.uuid);
                }
            });

            // When generated the share url, enable the button and checkbox
            $scope.$watch('shareLinkVO', function (share_url) {
                if (!!share_url && share_url != DEFAULT_VO_LINK && share_url != DEFAULT_FC_LINK && share_url != ERROR_SHARE_LINK) {
                    if ($('.buttons > button').is(":disabled") && $scope.serviceShare.state != $scope.serviceShare.WAITING) {
                        $('.buttons > button').attr('disabled', false);
                    }

                    if ($scope.serviceShare.state == $scope.serviceShare.NOT_SHARE) {
                        if ($('input[type=checkbox]').is(":disabled")) {
                            $('input[type=checkbox]').attr('disabled', false);
                        }
                    }
                }
            });

            // Check the unavailable time
            $scope.$watch('counter', function (counter) {
                if (counter == 30) {
                    $scope.$broadcast('share_url', {
                        message: $scope.serviceShare.UNAVAILABLE,
                        user: $scope.serviceShare.user.windows_name,
                        dest: $scope.ribbonService.userName
                    })
                }
            })

            // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
            // try to get client after 10 seconds.
            queryClient = $interval(function () {
                if ($scope.client == null)
                    getClient();
                else {
                    $interval.cancel(queryClient);
                    queryClient = null;
                }
            }, 5 * 1000);

            function getClient() {
                if ($routeParams.id) {
                    var cl = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                    if (cl == null || cl.tunnel.uuid == null) {
                        console.debug("Sharing: Client or tunnel not yet available.");
                        return;
                    }

                    $scope.client = cl;
                    console.debug("Sharing: current uuid: " + $scope.client.tunnel.uuid);

                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    var datasource = clientIdentifier.dataSource;
                    if (datasource === 'encryptedurl-jdbc')
                        $scope.ribbonService.sharingEnabled = true;
                }
            };

            var ERROR_SHARE_LINK = '--- Sharing is not possible at the moment, please reload the page ---';

            /**
             * The view only share link and name of the profile.
             *
             * @type String
             */
            var DEFAULT_VO_LINK = '--- Generating share link... ---';
            var GENERATED_VO_LINK = '--- Generated share link ---';
            var PROFILE_VO = "share-VO";
            $scope.shareLinkVO = DEFAULT_VO_LINK;
            $scope.shortShareLinkVO = DEFAULT_VO_LINK;
            $scope.labelShareLinkVO = DEFAULT_VO_LINK;

            /**
             * The full control share link.
             *
             * @type String
             */
            var DEFAULT_FC_LINK = '--- Generating share link... ---';
            var GENERATED_FC_LINK = '--- Generated share link ---';
            var PROFILE_FC = "share-FC";
            $scope.shareLinkFC = DEFAULT_FC_LINK;
            $scope.shortShareLinkFC = DEFAULT_FC_LINK;
            $scope.labelShareLinkFC = DEFAULT_FC_LINK;

            function retrieveSharingProfiles(uuid) {
                // Only pull sharing profiles if tunnel UUID is actually available
                if (!uuid || $routeParams.hasOwnProperty("key")) {
                    console.debug("Sharing: Cannot create profiles - uuid is undefined or shared session");
                    return;
                }

                $scope.shareLinkVO = DEFAULT_VO_LINK;
                $scope.shortShareLinkVO = DEFAULT_VO_LINK;
                $scope.labelShareLinkVO = DEFAULT_VO_LINK;
                $scope.shareLinkFC = DEFAULT_FC_LINK;
                $scope.shortShareLinkFC = DEFAULT_FC_LINK;
                $scope.labelShareLinkFC = DEFAULT_FC_LINK;

                // Pull sharing profiles for the current connection
                // Make a delay, to allow tunnel to form completely.
                let profileCreator =
                    $interval(function () {
                        var sharingProfile = tunnelService.getSharingProfiles(uuid);
                        if (sharingProfile === undefined) {
                            console.debug("Sharing: Cannot create profiles - tunnel is not yet availabale. Will retry in 2s.");
                            return;
                        }
                        sharingProfile.then(async function sharingProfilesRetrieved(sharingProfiles) {
                            // create sharing profiles related to "share-FC" and "share-VO"
                            for (var str in sharingProfiles) {
                                if (sharingProfiles[str].name == PROFILE_FC || sharingProfiles[str].name == PROFILE_VO) {
                                    var sharingCredentials = await ManagedClient.createShareLink($scope.client, sharingProfiles[str]);

                                    $scope.client.shareLinks[sharingProfiles[str].identifier] =
                                        ManagedShareLink.getInstance(sharingProfiles[str], sharingCredentials);
                                    console.debug("Sharing: Created link: " + sharingProfiles[str].name);
                                }
                            }

                            angular.forEach($scope.client.shareLinks, function (profile) {
                                if (profile.name == PROFILE_FC || profile.name == PROFILE_VO) {
                                    $scope.shareKeys.push(profile.href);
                                }
                            });

                            createShareLinks()
                        })
                        .catch(function (err) {
                            console.error("Sharing: " + err);
                            $scope.shareLinkVO = ERROR_SHARE_LINK;
                            $scope.shareLinkFC = ERROR_SHARE_LINK;
                        });

                        $interval.cancel(profileCreator);
                    }, 2 * 1000);
            }

            function createShareLinks() {
                // Only process share links if they are available (skip setting initial value to null/nothing etc....)
                if (!$scope.client || !$scope.client.shareLinks || $scope.ribbonService.serverId == null)
                    return;

                $scope.enableRMProfile = true;
                $scope.loadingProfile = false;

                for (var id in $scope.client.shareLinks) {
                    var str = $scope.client.shareLinks[id].href.replace(
                        /hyperstream\/#/,
                        ($scope.ribbonService.serverId != "" ? $scope.ribbonService.serverId + "/" : "") +
                        "hyperstream/#");
                    str += "&connection_type=messenger"

                    if ($scope.client.shareLinks[id].name === PROFILE_VO) {
                        $scope.shareLinkVO = str;
                        $scope.shortShareLinkVO = str.substr(0, 70) + "...";
                        $scope.labelShareLinkVO = GENERATED_VO_LINK;
                        $scope.ribbonService.shareLinkVO = str;
                    }

                    if ($scope.client.shareLinks[id].name === PROFILE_FC) {
                        $scope.shareLinkFC = str;
                        $scope.shortShareLinkFC = str.substr(0, 70) + "...";
                        $scope.labelShareLinkFC = GENERATED_FC_LINK;
                        $scope.ribbonService.shareLinkFC = str;
                    }
                }
            }

            $scope.btnShareScreen = function btnShareScreen() {
                var data = {};
                switch ($scope.serviceShare.state) {
                    case $scope.serviceShare.NOT_SHARE:
                        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                        data.id = encodeURIComponent(clientIdentifier.id),
                        data.token = encodeURIComponent(authenticationService.getCurrentToken())
                        if ($scope.isRemoteControl) {
                            data.isRemoteControl = true;
                            data.share_url = $scope.shareLinkFC;
                        }
                        else {
                            data.isRemoteControl = false;
                            data.share_url = $scope.shareLinkVO;
                        }

                        $scope.shareKey = data.share_url;
                        kurento.sendMessage(roomName, $scope.ribbonService.userName, btoa(JSON.stringify(data)), $scope.serviceShare.user.windows_name, $scope.ribbonService.SHARE_URL);
                        $scope.serviceShare.state = $scope.serviceShare.WAITING;
                        $interval.cancel(checkAvailable);
                        $scope.counter = 0;

                        checkAvailable = $interval(function () {
                            $scope.counter ++;
                        }, 1000);
                        $scope.enableRMProfile = false;
                        break;

                    case $scope.serviceShare.SHARING:
                        $scope.serviceShare.state = $scope.serviceShare.NOT_SHARE;
                        angular.forEach($scope.shareKeys, function (profile) {
                            $rootScope.stopSharing(profile);
                        });
                        $scope.shareKeys = [];

                        kurento.sendMessage(roomName, $scope.ribbonService.userName, $scope.serviceShare.SESSION_STOP, $scope.serviceShare.user.windows_name, $scope.ribbonService.SHARE_URL);
                        $scope.ribbonService.messengerShareDialogVisible = false;
                        $scope.stopShare();
                        break;

                    default:
                        break;
                }
            }

            $scope.btnClose = function btnClose() {
                $scope.isRemoteControl = false;
                $scope.ribbonService.messengerShareDialogVisible = false;

                if ($scope.enableRMProfile) {
                    angular.forEach($scope.shareKeys, function (profile) {
                        $rootScope.stopSharing(profile);
                    });
                    $scope.shareKeys = [];
                    $scope.enableRMProfile = false;
                }
            }

            $scope.btnCloseWait = function btnCloseWait() {
                $scope.isRemoteControl = false;
                $scope.ribbonService.messengerShareDialogVisible = false;
            }

            $scope.toggleRemoteControl = function toggleRemoteControl() {
                $scope.isRemoteControl = !$scope.isRemoteControl;
            }

            //When first user click the share button, set the share flag.
            $scope.startShare = function startShare() {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };
                var req = {
                    method: 'POST',
                    url: "api/session/ext/" + datasource + "/classroom/startshare",
                    params: httpParameters,
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
                };

                $http(req)
                .then(function () {
                    var notify = new XMLHttpRequest();

                    var waitResponse = $interval(function () {
                        var responseRequest = '?id=' + encodeURIComponent(clientIdentifier.id) + "&token=" + encodeURIComponent(authenticationService.getCurrentToken());
                        notify.open('GET', 'api/session/ext/encryptedurl-jdbc/classroom/checkshare' + responseRequest);
                        notify.send();

                        notify.onreadystatechange = function () {
                            if (notify.readyState === 4) {
                                if (notify.response === "STOPPED") {
                                    $scope.isRemoteControl = false;
                                    $scope.serviceShare.state = $scope.serviceShare.NOT_SHARE;
                                    $interval.cancel(waitResponse);
                                }
                            }
                        }
                    }, 3000);
                })
                .catch(function () {
                })
                .finally(function () {
                    console.debug("Started sharing screen");
                });
            }

            //When first user clicks the stop sharing button, remove the share flag.
            $scope.stopShare = function stopShare() {
                $scope.isRemoteControl = false;
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);
                var httpParameters = {
                    token: encodeURIComponent(authenticationService.getCurrentToken()),
                    id: encodeURIComponent(clientIdentifier.id)
                };

                if($scope.ribbonService.serverGroupCount > 1) {
                    var req = {
                        method: 'POST',
                        url: "/"+ $scope.ribbonService.serverId + "/hyperstream/api/session/ext/" + datasource + "/classroom/stopshare",
                        params: httpParameters
                    };
                }
                else {
                    var req = {
                        method: 'POST',
                        url: "api/session/ext/" + datasource + "/classroom/stopshare",
                        params: httpParameters
                    };
                }
                $http(req).then(function () {});
            }

            //Close the dialog for screen sharing
            $scope.closeDialog = function closeDialog() {
                $scope.isRemoteControl = false;
                $scope.ribbonService.messengerShareDialogVisible = false;
                $scope.ribbonService.ribbonActive = false;

                if ($scope.enableRMProfile) {
                    angular.forEach($scope.shareKeys, function (profile) {
                        $rootScope.stopSharing(profile);
                    });
                    $scope.shareKeys = [];
                    $scope.enableRMProfile = false;
                }
            }
        }
    ]); // end messenger share dialog controller
