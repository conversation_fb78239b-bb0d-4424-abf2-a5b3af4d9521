/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon').controller('sessionTimeController', ['$scope', '$interval', '$injector',
    function sessionTimeController($scope, $interval, $injector) {
        // Required services
        var $location           = $injector.get('$location');
        var $timeout            = $injector.get('$timeout');
        var $routeParams        = $injector.get('$routeParams');
        var $rootScope          = $injector.get('$rootScope');
        var $translate          = $injector.get('$translate');
        var guacClientManager   = $injector.get('guacClientManager');
        var ManagedClientState  = $injector.get('ManagedClientState');
        var infoService         = $injector.get('infoService');

        $scope.ribbonService    = $injector.get('ribbonService');

        if ($location.path().indexOf("classroom") > -1) {
            return;
        }

        // Timer to calculate the session time
        var sessionTimeInterval = null;

        // Timer to calculate remaining time
        var remainingTimeInterval = null;

        // Interval to check remaining time
        const REMAINING_INTERVAL = 60*1000; // one minute 60 * 1000

        // Express if a specific notification was displayed
        var remaining_warning = {
            isSixtyMinutes: false,
            isThirtyMinutes: false,
            isFiveMinutes: false,
        }

        // Is synchronized with remaining time in payload and API
        isSyncRemainingTime = false;

        // Specified times in minutes to display a warning message
        const WARNING_TIMES = {
            SIXTY_MINUTES: 60,
            THIRTY_MINUTES: 30,
            FIVE_MINUTES: 5
        };

        // Identifier to save the remaining time to local storage
        var appIdentifier;

        // Interval between API calls to get the remaining time in milliseconds
        const API_REMAINING_INTERVAL_MS = 5 * 60 * 1000;

        // Interval between API calls to get the remaining time in minutes
        const API_REMAINING_INTERVAL_M = 5;

        // current client instance
        $scope.client         = null;
        $scope.managedClients = null;

        $scope.hours   = "00";
        $scope.minutes = "00";
        $scope.seconds = "00";

        var counter = 0;

        $scope.$on('$routeChangeSuccess', function (event, current, previous) {
            if ($routeParams.id) {
                $scope.managedClients = guacClientManager.getManagedClients();
            }
        });

        // try to get licence after 10 seconds.
        $timeout(function () {
            if ($routeParams.id && !$scope.managedClients) {
                $scope.managedClients = guacClientManager.getManagedClients();
            }
        }, 10 * 1000);

        $scope.$watchCollection('managedClients', function () {
            if ($routeParams.id) {
                $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);
            }
        });

        var startSessionTimer = function startSessionTimer() {
            // Reset the session time
            $scope.hours   = "00";
            $scope.minutes = "00";
            $scope.seconds = "00";

            sessionTimeInterval = $interval(function () {
                counter++;

                var hours   = Math.floor(counter / 3600);
                var minutes = Math.floor((counter - (hours * 3600)) / 60);
                var seconds = counter - (hours * 3600) - (minutes * 60);

                $scope.hours   = (hours < 10)   ? "0" + hours   : hours;
                $scope.minutes = (minutes < 10) ? "0" + minutes : minutes;
                $scope.seconds = (seconds < 10) ? "0" + seconds : seconds;
            }, 1000);
        }

        // Remove items created one day ago from localStorage
        var removeExpiredStorage = function removeExpiredStorage() {
            const keys = Object.keys(localStorage);
            const oneDayMilliseconds = 24 * 60 * 60 * 1000; // 1 day in milliseconds

            keys.forEach(function(key) {
            const item = JSON.parse(localStorage.getItem(key));
            if (item && (new Date().getTime() - item.timestamp) > oneDayMilliseconds) {
                localStorage.removeItem(key);
            }
            });
        }

        $scope.$on('remainingTime', function(event, remainingTime) {
            if (!isSyncRemainingTime && $scope.ribbonService.licenses.remainingTime - remainingTime <= 5) {
                isSyncRemainingTime = true;
                appIdentifier = "GUAC_" + $scope.ribbonService.licenses.default_username + "_" + $scope.ribbonService.licenses.appname +
                                "_" + $scope.ribbonService.licenses.hostName + "_" + $scope.ribbonService.licenses.rdpPort;
                startRemainingTimer();
            }

            if (remainingTime > 0) {
                var item = {
                    remainingTime: remainingTime,
                    timestamp: new Date().getTime() // Store the current timestamp
                };
                localStorage.setItem(appIdentifier, JSON.stringify(item));
            }
            else if (remainingTimeInterval) {
                localStorage.removeItem(appIdentifier);
                $interval.cancel(remainingTimeInterval);
                remainingTimeInterval = null;
            }

        });

        const checkStartableRemaining = function checkStartableRemaining() {
            $rootScope.$broadcast('checkStartableRemaining');
        }

        var startRemainingTimer = function startRemainingTimer() {
            removeExpiredStorage();
            remainingTimeInterval = $interval(function() {
                var itemObj = JSON.parse(localStorage.getItem(appIdentifier));
                var remainingTime = itemObj && itemObj.remainingTime ? itemObj.remainingTime : 0;

                // API call to get the remaining time failed.
                // Then, update the ramaining time manually.
                if (itemObj && (new Date().getTime() - itemObj.timestamp) > API_REMAINING_INTERVAL_MS) {
                    remainingTime -= 5;
                }

                if (remainingTime > 0) {
                    // API call to get the remaining time failed.
                    // Then, update the ramaining time manually.
                    if (itemObj && (new Date().getTime() - itemObj.timestamp) > API_REMAINING_INTERVAL_MS) {
                        itemObj.remainingTime = remainingTime;
                        itemObj.timestamp = new Date().getTime(); // Store the current timestamp
                        localStorage.setItem(appIdentifier, JSON.stringify(itemObj));
                    }

                    if (remainingTime >= WARNING_TIMES.SIXTY_MINUTES && remainingTime < WARNING_TIMES.SIXTY_MINUTES + API_REMAINING_INTERVAL_M &&
                        !remaining_warning.isSixtyMinutes) {
                        $translate('CLIENT.TEXT_REMAINING_TIME_ONE_HOUR').then(function setTitle(text) {
                            infoService.top = true;
                            infoService.infoText = text;
                            infoService.infoDialogVisible = true;
                        });
                        remaining_warning.isSixtyMinutes = true;
                    }

                    if (remainingTime >= WARNING_TIMES.THIRTY_MINUTES && remainingTime < WARNING_TIMES.THIRTY_MINUTES + API_REMAINING_INTERVAL_M &&
                        !remaining_warning.isThirtyMinutes) {
                        $translate('CLIENT.TEXT_REMAINING_TIME_THIRTY_MINUTES').then(function setTitle(text) {
                            infoService.top = true;
                            infoService.infoText = text;
                            infoService.infoDialogVisible = true;
                        });
                        remaining_warning.isThirtyMinutes = true;
                    }

                    if (remainingTime >= WARNING_TIMES.FIVE_MINUTES && remainingTime < WARNING_TIMES.FIVE_MINUTES + API_REMAINING_INTERVAL_M &&
                        !remaining_warning.isFiveMinutes) {
                        $translate('CLIENT.TEXT_REMAINING_TIME_FIVE_MINUTES').then(function setTitle(text) {
                            infoService.top = true;
                            infoService.infoText = text;
                            infoService.infoDialogVisible = true;
                        });
                        remaining_warning.isFiveMinutes = true;
                    }
                }
                else {
                    localStorage.removeItem(appIdentifier);
                    $interval.cancel(remainingTimeInterval);
                    remainingTimeInterval = null;
                }
            }, REMAINING_INTERVAL);

        }

        $scope.$watch('client.clientState.connectionState', function clientStateChanged(connectionState) {
            // This is called whenever connection is established.
            switch (connectionState) {
                case ManagedClientState.ConnectionState.CONNECTED:
                    if (sessionTimeInterval == null) {
                        startSessionTimer();
                    }
                    if (remainingTimeInterval == null && $scope.ribbonService.licenses.extendedCompute &&
                        $scope.ribbonService.licenses.remainingTime > 0) {
                        checkStartableRemaining();
                    }
                    break;
                case ManagedClientState.ConnectionState.DISCONNECTED:
                case ManagedClientState.ConnectionState.CLIENT_ERROR:
                case ManagedClientState.ConnectionState.TUNNEL_ERROR:
                    if (sessionTimeInterval) {
                        // Reset the session timer.
                        $interval.cancel(sessionTimeInterval);
                        sessionTimeInterval = null;

                        // Reset the counter.
                        counter = 0;
                    }
                    if (remainingTimeInterval) {
                        // Reset timer to check the remaining time.
                        $interval.cancel(remainingTimeInterval);
                        remainingTimeInterval = null;
                    }
                    break;
            }
        });
    }
]);
