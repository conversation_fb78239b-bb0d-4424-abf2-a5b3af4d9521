/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon').controller('usageChartController',
        [ '$scope', '$element', '$injector', '$interval', '$timeout',

        function usageChartController($scope, $element, $injector, $interval, $timeout) {

            // requiredServices
        	var $document           = $injector.get('$document');
            var statsCollectService = $injector.get('statsCollectService');
            $scope.ribbonService    = $injector.get('ribbonService');
            var $location           = $injector.get('$location');

            if($location.path().indexOf("classroom") > -1) {
                return;
            }
           
            var TIME_CHART    = 0;
            var ONLINE_CHART  = 1;

            var startTime = Date.now();

            var ribbonChart = document.getElementById("ribbonChart");
            var ctx = ribbonChart.getContext("2d");

            // Gradient
            var gradient = ctx.createLinearGradient(0, 0, 0, ribbonChart.getBoundingClientRect().height);
            gradient.addColorStop(0, 'rgba(255,255,255,1)');
            gradient.addColorStop(1, 'rgba(224,232,243,1)');

            $scope.ribbonChart = new Chart($element[0].querySelector("#ribbonChart"), {
                type: 'bar',
                data: {
                    datasets: [
                    {
                        label: 'time',
                        borderColor: gradient,
                        borderWidth: 1,
                        data: [],
                        yAxisID: 'timeScale'
                    },

                    {
                        label: 'Activity',
                        borderColor: 'rgb(89,136,187)',
                        borderWidth: 1,
                        data: [{x: startTime, y: 0}],
                        yAxisID: 'activityScale'
                    }]
                },
                options: {
                    animation: false,
                    maintainAspectRatio: false,
                    legend: {
                        display: false
                    },
                    tooltips: {
                         enabled: false
                    },
                    scales: {
                        yAxes: [{
                            id: 'timeScale',
                            display: false,
                            stacked: false,
                            ticks: {
                                min: 0,
                                max: 1
                                }
                        },
                        {
                            id: 'activityScale',
                            display: false,
                            stacked: false,
                            ticks: {
                                beginAtZero: true
                                }
                        }],
                        xAxes: [{
                            barThickness: 1,
                            display: false,
                            stacked: true,
                            type: "time",
                            time: {
                            	distribution: 'linear',
                            	min: startTime,
                            	max: startTime + 3600 * 1000
                            }
                        }]
                    },
                    
                    events: ["click", "mouseup", "mouseout"],

                    /**
                     * When ribbon chart is clicked, detailed chart is displayed.
                     */
                    onClick: function() {
                        $scope.ribbonService.summaryChartVisible = true;
                    },
               }
            });
            /**
             * Adjusting different display for desktop and mobile devices
             */
            $document.ready(function(){
                var ua = navigator.userAgent;
                var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS|CrOS/i.test(ua);

                if (!isMobile && navigator.userAgent.match(/Mac/) && navigator.maxTouchPoints && navigator.maxTouchPoints > 2) { // Check iOS 13
                    isMobile = true;
                }

                if (isMobile) {
                   $scope.desktop = false;
                   $scope.tablet = true;
            	}
                else {
                   $scope.desktop = true;
                   $scope.tablet = false;
                }
            });

            /**
             * Handler called when new event data is generated.
             * 
             * @param int eventNum - new number of events
             */
            $scope.updateSessionData = function updateSessionData(eventNum) {
                var timestamp = new Date();

                var onlineData = $scope.ribbonChart.data.datasets[ONLINE_CHART].data;
                var timeData = $scope.ribbonChart.data.datasets[TIME_CHART].data;
                
                // Insert new activity value
                onlineData.push({x: timestamp.getTime(), y: eventNum});

                // Move time line
                timeData.shift();

                $scope.ribbonChart.update();
            }
          
            /**
             * Extend maximum value on chart every hour 
             */
            if($scope.ribbonService.licenses.hasActivityTrackLicence){
	            $interval(function extendChart() {
	                $scope.fillTimeline($scope.ribbonChart.options.scales.xAxes[0].time.max);
	                
	                $scope.ribbonChart.options.scales.xAxes[0].time.max += 3600 * 1000;
	
	                $scope.ribbonChart.update();
	            }, 3600 * 1000)
            }
            
            /**
             * Fill time chart with lines; lines will be removed from begin as time passes.
             * 
             * This is a workaround for having chart beyond current time in different color.
             */
            $scope.fillTimeline = function fillTimeline(start) {
                for(i=20; i<3600; i+=5) {
                    $scope.ribbonChart.data.datasets[TIME_CHART].data.push({x: start+i*1000, y:1});
                }
            }
            if($scope.ribbonService.licenses.hasActivityTrackLicence){
            	$scope.fillTimeline(startTime);
            }

            /**
             * Set handler which is called when new event count is available
             */
            if($scope.ribbonService.licenses.hasActivityTrackLicence){
            	statsCollectService.counterUpdateHandler = $scope.updateSessionData;
            }
    } ]);
