/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon', ['angular-web-notification']).controller('chattingController',
    ['$scope', '$routeParams', '$injector', 'ServiceParticipant', 'ServiceRoom', 'webNotification',

    function chattingController($scope, $routeParams, $injector, ServiceParticipant, ServiceRoom, webNotification) {
        var ServiceParticipant       = $injector.get('ServiceParticipant');
        var ManagedClientState       = $injector.get('ManagedClientState');
        $scope.ribbonService         = $injector.get('ribbonService');
        var userInfoService          = $injector.get('userInfoService');
        $scope.ServiceParticipant    = $injector.get('ServiceParticipant');
        var $rootScope               = $injector.get('$rootScope');
        var authenticationService    = $injector.get('authenticationService');
        var ClientIdentifier         = $injector.get('ClientIdentifier');
        var $http                    = $injector.get('$http');
        var $q                       = $injector.get('$q');
        var $translate               = $injector.get('$translate');
        var $timeout                 = $injector.get('$timeout');
        $scope.groups = [];
        $scope.stateContextMenuVisible = false;
        this.chatState = $scope.ribbonService.userinfo.state;

        for (var i = 0; i < $scope.ribbonService.groups.length; i++) {
            if (i == 0) {
                $scope.groups.push({
                    'name': $scope.ribbonService.groups[i],
                    'isCollapsed': false
                });
            }
            else {
                $scope.groups.push({
                    'name': $scope.ribbonService.groups[i],
                    'isCollapsed': true
                });
            }
        }

        var getUserList = function getUserList() {
            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            var httpParameters = {
                token: authenticationService.getCurrentToken(),
                id: clientIdentifier.id,
            };

            var req = {
                method: 'GET',
                url: "api/session/ext/" + authenticationService.getDataSource() + "/messenger/getUserList",
                params: httpParameters
            };

            $http(req)
                .then(function (response) {
                    var data = response.data;
                    var groups = [];
                    $scope.ribbonService.userlist = data;

                    $translate('MESSENGER.FACULTY').then(function setTitle(text) {
                        for (var group in data) {
                            var index = data[group].findIndex(function (user) {
                                return user['email'] == $scope.ribbonService.userinfo['email'];
                            });
                            if (index > -1) {
                                data[group].splice(index, 1);
                            }

                            if (data[group].length == 0) {
                                continue;
                            }

                            groups.push(group);

                            for (var i = 0; i < data[group].length; i++) {
                                var fullName = userInfoService.getFullName(data[group][i]['first name'], data[group][i]['last name']);
                                fullName = fullName ? fullName : data[group][i]['email'];
                                var initials = userInfoService.getInitials(fullName);
                                initials = initials ? initials : userInfoService.getInitials(data[group][i]['email']);
                                $scope.ribbonService.userlist[group][i]['name'] = fullName;
                                $scope.ribbonService.userlist[group][i]['initials'] = initials;
                                $scope.ribbonService.userlist[group][i]['windows_name'] = data[group][i]['email'];
                                $scope.ribbonService.userlist[group][i].sha256_username = sha256(data[group][i]['windows_name']);

                                if ($scope.ribbonService.userlist[group][i].Role == 'Faculty Admin') {
                                    $scope.ribbonService.userlist[group][i].name += " (" + text + ")";
                                }

                                var index = $scope.ribbonService.apportoMembers.findIndex(function (member) {
                                    return member['windows_name'] == $scope.ribbonService.userlist[group][i]['windows_name'];
                                });

                                if (index == -1) {
                                    $scope.ribbonService.apportoMembers.push($scope.ribbonService.userlist[group][i]);
                                }
                            }
                        }

                        if (!$scope.ribbonService.userlist || $scope.ribbonService.userlist.length == 0) {
                            $scope.ribbonService.licenses.hasChattingLicence = false;
                            $scope.ribbonService.chattingVisible = false;
                            $scope.ribbonService.groups = [];
                            console.log('Either the userlist is undefined or userlist length is zero, which makes chat button disable');
                        } else {
                            $scope.ribbonService.licenses.hasChattingLicence = true;
                            $scope.ribbonService.groups = groups;
                            $scope.ribbonService.groups.sort();

                            index = $scope.groups.findIndex(function (group) {
                                return group.isCollapsed == false;
                            });

                            $scope.groups = [];
                            for (var i = 0; i < $scope.ribbonService.groups.length; i++) {
                                if (i == index) {
                                    $scope.groups.push({
                                        'name': $scope.ribbonService.groups[i],
                                        'isCollapsed': false
                                    });
                                }
                                else {
                                    $scope.groups.push({
                                        'name': $scope.ribbonService.groups[i],
                                        'isCollapsed': true
                                    });
                                }
                            }

                            setGroupMembers();
                            checkUserlist();
                        }
                    });
                }).catch(function (response) {
                    console.error("Chatting error group fetch: ", response);
                    $scope.ribbonService.userlist = [];
                    $scope.ribbonService.licenses.hasChattingLicence = false;
                    checkUserlist();
                }).finally(function () { })
        }

        function setGroupMembers() {
            var participants = ServiceParticipant.participants;
            var userlist = $scope.ribbonService.userlist;

            for (room in userlist) {
                for (var i = 0; i < userlist[room].length; i++) {
                    if (!!userlist[room][i]['windows_name'] && userlist[room][i]['windows_name'].includes('vclassroom')) {
                        userlist[room].splice(i, 1);
                        i --;
                        continue;
                    }
                    var index = participants.findIndex(function (participant) {
                        return userlist[room][i]['windows_name'] == participant.getID()
                    });

                    if (index > -1) {
                        userlist[room][i].isOnline = true;
                        var state = participants[index].getState();
                        switch (state) {
                            case 'available':
                                userlist[room][i].state = '0';
                                break;
                            case 'dontdisturb':
                                userlist[room][i].state = '1';
                                break;
                            case 'away':
                                userlist[room][i].state = '2';
                                break;
                            default:
                                userlist[room][i].isOnline = false;
                                userlist[room][i].state = '3';
                                break;
                        }
                    }
                    else {
                        userlist[room][i].isOnline = false;
                        userlist[room][i].state = '3';
                    }
                }
            }

            for (room in userlist) {
                userlist[room].sort(function (x, y) {
                    if (x.isOnline === y.isOnline) {
                        return x.name > y.name ? 1 : -1;
                    }
                    return x.isOnline ? -1 : 1;
                });
            }

            $scope.ribbonService.groupMembers = userlist;
        }

        function countUnreadMessage() {
            var messages = ServiceParticipant.messages;

            for (var from in messages) {
                for (var group in messages[from]) {
                    if (!!messages[from][group]) {
                        let unreadMessagesArray = messages[from][group].filter((obj) => obj.state != "read" && obj.user == from);
                        if (unreadMessagesArray.length > 0) {
                            for (var groupName in $scope.ribbonService.groupMembers) {
                                var group = $scope.ribbonService.groupMembers[groupName];
                                for (var i = 0; i < group.length; i++) {
                                    var user = group[i];
                                    if (user.windows_name === from) {
                                        user.unreadMezCount = unreadMessagesArray.length;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        function getCallMessage (msg) {
            var name = msg.name;
            if (msg.user == $scope.ribbonService.userName) {
                switch (msg.message) {
                    case ServiceParticipant.DECLINED:
                        message = 'You' + ' declined a call.';
                        break;
                    case ServiceParticipant.MISSED:
                        message = name + ' missed a call.';
                        break;
                    case ServiceParticipant.BUSY:
                        message = name + ' is busy.';
                        break;
                    case ServiceParticipant.DONTDISTURB:
                        message = name + ' is in Do not disturb mode.';
                        break;
                }
            }
            else {
                switch (msg.message) {
                    case ServiceParticipant.DECLINED:
                        message = name + ' declined a call.';
                        break;
                    case ServiceParticipant.MISSED:
                        message = 'You' + ' missed a call.';
                        break;
                    case ServiceParticipant.BUSY:
                        message = 'You' + ' missed a call.';
                        break;
                    case ServiceParticipant.DONTDISTURB:
                        message = 'You' + ' missed a call.';
                        break;
                }
            }
            return message;
        }

        function checkUserlist() {
            $scope.checkUserlist = $timeout(function () {
                if (ManagedClientState.isDisconnectState() == true) {
                    return;
                }

                if (!$scope.ribbonService.isVirtualClassroom && $scope.ribbonService.licenses.hasMessengerLicence) {

                    $q.all([
                        userInfoService.getUserInfo()
                    ])
                    .then(function() {
                        getUserList();
                    })
                }
            }, 5 * 60000);
        }

        setGroupMembers();
        checkUserlist();

        $scope.$watch("ribbonService.groups", function (groups) {
            $scope.groups = [];
            for (var i = 0; i < groups.length; i++) {
                if (i == 0) {
                    $scope.groups.push({
                        'name': groups[i],
                        'isCollapsed': false
                    });
                }
                else {
                    $scope.groups.push({
                        'name': groups[i],
                        'isCollapsed': true
                    });
                }
            }
        });

        $scope.$on('participants:updated', function(event) {
            setGroupMembers();
        });

        $scope.$on('messages:updated', function (event, data) {
            var indexActive = $scope.ribbonService.activeMembers.findIndex(function (member) {
                return member.windows_name == data.user;
            })

            if (indexActive == -1  && data.user != $scope.ribbonService.userinfo.windows_username) {
                for (roomName in $scope.ribbonService.groupMembers) {
                    index = $scope.ribbonService.groupMembers[roomName].findIndex(function (member) {
                        return member.windows_name == data.user;
                    });

                    if (index > -1) {
                        if (data.state != 'read'){
                            $scope.ribbonService.groupMembers[roomName][index].isUnread = true;
                            countUnreadMessage();
                        }
                        else{
                            $scope.ribbonService.groupMembers[roomName][index].isUnread = false;
                            $scope.ribbonService.groupMembers[roomName][index].unreadMezCount = 0
                        }
                    }
                }
            }

            if ((indexActive == -1 || !$scope.ribbonService.isFocus) && $scope.ribbonService.userinfo.state == '0' && data.user != $scope.ribbonService.userinfo.windows_username && !data.dest && data.message) {
                for (roomName in $scope.ribbonService.groupMembers) {
                    index = $scope.ribbonService.groupMembers[roomName].findIndex(function (member) {
                        return member.windows_name == data.user;
                    });

                    if (index > -1) {
                        var title = $scope.ribbonService.groupMembers[roomName][index].name;
                        data.name = title;
                        if (data.type == $scope.ribbonService.CHAT)
                            body = data.message;
                        else
                            body = getCallMessage (data);

                        if (data.id)
                            tag = data.id;
                        else
                            tag = title + body;

                        if (navigator.serviceWorker) {
                            webNotification && webNotification.showNotification(title, {
                                body: body,
                                tag: tag,
                                requireInteraction: true,
                                onClick: function onNotificationClicked() {
                                    console.log('Notification clicked.');
                                    var index = $scope.ribbonService.groupMembers[room].findIndex(function (member) {
                                        return member['windows_name'] == data.user;
                                    });

                                    if (index > -1) {
                                        $scope.openChat($scope.ribbonService.groupMembers[room][index], roomName);
                                    }
                                    try {
                                        window.focus();
                                        this.cancel();
                                    }
                                    catch (ex) {
                                    }
                                },
                                icon: 'app/ext/ribbon/images/favicons/apple-icon-60x60.png'
                            }, function onShow(error, hide) {
                                if (error) {
                                    console.log('Unable to show notification: ' + error.message);
                                } else {
                                    console.log('Notification Shown.');
                                }
                            });
                        }
                        break;
                    }
                }
            }
        });

        $scope.$on('history:updated', function (event, data) {
            var unreadMessages = ServiceParticipant.messages[data.user][$scope.ribbonService.GLOBAL_ROOM_NAME].filter(function (item) {
                return item['state'] != 'read' && item['user'] == data['user'];
            });

            if (unreadMessages.length > 0) {
                for (groupName in $scope.ribbonService.groupMembers) {
                    index = $scope.ribbonService.groupMembers[groupName].findIndex(function (member) {
                        return member.windows_name == data.user;
                    });

                    if (index > -1) {
                        $scope.ribbonService.groupMembers[groupName][index].isUnread = true;
                        $scope.ribbonService.groupMembers[groupName][index].unreadMezCount += ($scope.ribbonService.groupMembers[groupName][index].unreadMezCount ? $scope.ribbonService.groupMembers[groupName][index].unreadMezCount : 0);
                    }
                }
            }
        });

        $scope.saveState = function saveState(data) {
            this.chatState = data;
            var room = $scope.ribbonService.GLOBAL_ROOM_NAME;
            var user = $scope.ribbonService.userinfo.windows_username;
            var kurento = ServiceRoom.getKurento(room);
            var state;
            switch (this.chatState) {
                case '0':
                    state = 'available';
                    break;
                case '1':
                    state = 'dontdisturb';
                    break;
                case '2':
                    state = 'away';
                    break;
            }

            kurento.stateChange(room, user, state);
            $scope.ribbonService.userinfo.state = this.chatState;
            $scope.stateContextMenuVisible = false;
        }

        $scope.closeChat = function closeChat() {
            $scope.ribbonService.chattingVisible = false;
            $scope.ribbonService.chattingMinimized = true;
            $scope.ribbonService.ribbonActive = false;
            var room = $scope.ribbonService.GLOBAL_ROOM_NAME;
            var kurento = ServiceRoom.getKurento(room);
            kurento.close();
            ServiceRoom.removeKurento(room);
            $scope.ribbonService.userinfo.state = '3';
            $timeout.cancel($scope.checkUserlist);

            $rootScope.firstFocusableElement = null;
            $rootScope.lastFocusableElement = null;
        }

        $scope.openContext = function openContext() {
            this.chatState = $scope.ribbonService.userinfo.state;
            $scope.stateContextMenuVisible = !$scope.stateContextMenuVisible;
            window.onclick = function (event) {
                if (!event.target.matches('.context-menu.edit')) {
                    $scope.stateContextMenuVisible = false;
                }
            }
        }

        $scope.closeContext = function closeContext() {
            this.chatState = $scope.ribbonService.userinfo.state;
            $scope.stateContextMenuVisible = false;
        }

        $scope.toggleChatGroup = function(event, index) {
            if ($scope.groups[index].isCollapsed && event.key === 'Tab') {
                event.preventDefault();
                $scope.groups[index].isCollapsed = false;
            }
        };

        $scope.minimizeChat = function minimizeChat() {
            $scope.ribbonService.chattingMinimized = true;
        }

        $scope.openChat = function openChat(member, group) {
            if (member == 'group') {
                setTimeout(function () {
                    $rootScope.setFocusableElements("chatting-room");
                }, 500);
                var index = $scope.ribbonService.activeMembers.findIndex(function(element) {
                    return element['id'] == 'group';
                });
                if (index == -1) {
                    $scope.ribbonService.activeMembers.push({
                        'id': 'group',
                        'windows_name': group.name,
                        'group': group.name,
                        'isOnline': true
                    });
                }
                else {
                    $scope.ribbonService.activeMembers.splice(index, 1);
                }
            }
            else {
                setTimeout(function () {
                    $rootScope.setFocusableElements("#chatting-room-" + member['uid']);
                }, 500);
                member.group = group.name;
                var index = $scope.ribbonService.activeMembers.findIndex(function(element) {
                    return element['windows_name'] == member['windows_name'];
                });
                if (index == -1) {
                    for (groupName in $scope.ribbonService.groupMembers) {
                        var index = $scope.ribbonService.groupMembers[groupName].findIndex(function(element) {
                            return element['windows_name'] == member['windows_name'];
                        });

                        if (index > -1) {
                            $scope.ribbonService.groupMembers[groupName][index].isUnread = false;
                            $scope.ribbonService.groupMembers[groupName][index].unreadMezCount = 0
                        }
                    }
                    $scope.ribbonService.activeMembers.push(member);
                }
            }
        }
    }
]);

