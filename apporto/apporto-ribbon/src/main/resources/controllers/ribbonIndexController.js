/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * The controller for the root of the application.
 *
 * This controller is changed to allow keyboard to be attached to the content
 * element, instead of attaching to document element.
 */
angular.module('index').controller('ribbonIndexController', ['$scope', '$injector', '$routeParams',
        function ribbonIndexController($scope, $injector, $routeParams) {

    // Required services
    var $document        = $injector.get('$document');
    var $window          = $injector.get('$window');
    var clipboardService = $injector.get('clipboardService');
    var guacNotification = $injector.get('guacNotification');
    var $rootScope   	 = $injector.get('$rootScope');
    var $route           = $injector.get('$route');

    $scope.ribbonService     = $injector.get('ribbonService');
    $rootScope.ribbonService = $injector.get('ribbonService');

    /**
     * The notification service.
     */
    $scope.guacNotification = guacNotification;

    /**
     * The message to display to the user as instructions for the login
     * process.
     *
     * @type TranslatableMessage
     */
    $scope.loginHelpText = null;

    /**
     * The credentials that the authentication service is has already accepted,
     * pending additional credentials, if any. If the user is logged in, or no
     * credentials have been accepted, this will be null. If credentials have
     * been accepted, this will be a map of name/value pairs corresponding to
     * the parameters submitted in a previous authentication attempt.
     *
     * @type Object.<String, String>
     */
    $scope.acceptedCredentials = null;

    /**
     * The credentials that the authentication service is currently expecting,
     * if any. If the user is logged in, this will be null.
     *
     * @type Field[]
     */
    $scope.expectedCredentials = null;

    /**
     * Basic page-level information.
     */
    $scope.page = {

        /**
         * The title of the page.
         *
         * @type String
         */
        title: '',

        /**
         * The name of the CSS class to apply to the page body, if any.
         *
         * @type String
         */
        bodyClassName: ''

    };

    /**
     * Action which replaces the current client with a newly-connected client.
     */
    function reconnectAction() {
        $route.reload();
    }

    $scope.goAppStore = function() {
        var appStore = "";

        // Redirect to the home page
        if ($rootScope.homepage) {
            if (!$rootScope.homepage.includes(".apporto.com")) { // The subdomain of the payload is not contain ".apporto.com"
                appStore = $rootScope.homepage  + ".apporto.com/home";
            } else {
                appStore = $rootScope.homepage + "/home" ;
            }
        } else {
            appStore = "apporto.com";
        }

        $window.location.href = 'https://' + appStore; // Adjust the URL based on your routing
    };

    $scope.goBackHome = function() {
        var homePage = "";

        // Redirect to the home page
        if ($rootScope.homepage) {
            if (!$rootScope.payloadVersion) {
                if (!$rootScope.homepage.includes(".apporto.com")) { // The subdomain of the payload is not contain ".apporto.com"
                    homePage = $rootScope.homepage  + ".apporto.com/user/logout";
                } else {
                    homePage = $rootScope.homepage + "/user/logout" ;
                }
            } else {
                if (!$rootScope.homepage.includes(".apporto.com")) { // The subdomain of the payload is not contain ".apporto.com"
                    homePage = $rootScope.homepage  + ".apporto.com/api/auth/logout";
                } else {
                    homePage = $rootScope.homepage + "/api/auth/logout" ;
                }
            }
        } else {
            homePage = "apporto.com";
        }

        $window.location.href = 'https://' + homePage; // Adjust the URL based on your routing
    };

    // Add default destination for input events
    var sink = new Guacamole.InputSink();
    $document[0].body.appendChild(sink.getElement());

    // Create event listeners at the global level
    var keyboard = new Guacamole.Keyboard($document[0]);
    keyboard.listenTo(sink.getElement());

    // Broadcast keydown events
    keyboard.onkeydown = function onkeydown(keysym) {

        // Do not handle key events if not logged in
        if ($scope.expectedCredentials)
            return true;

        // Warn of pending keydown
        var guacBeforeKeydownEvent = $scope.$broadcast('guacBeforeKeydown', keysym, keyboard);
        if (guacBeforeKeydownEvent.defaultPrevented)
            return true;

        // If not prevented via guacBeforeKeydown, fire corresponding keydown event
        var guacKeydownEvent = $scope.$broadcast('guacKeydown', keysym, keyboard);
        return !guacKeydownEvent.defaultPrevented;

    };

    // Broadcast keyup events
    keyboard.onkeyup = function onkeyup(keysym) {

        // Do not handle key events if not logged in
        if ($scope.expectedCredentials)
            return;

        // Warn of pending keyup
        var guacBeforeKeydownEvent = $scope.$broadcast('guacBeforeKeyup', keysym, keyboard);
        if (guacBeforeKeydownEvent.defaultPrevented)
            return;

        // If not prevented via guacBeforeKeyup, fire corresponding keydown event
        $scope.$broadcast('guacKeyup', keysym, keyboard);

    };

    // Blur handler
    $window.onblur = function () {

        // Set the flag indicating the blur of the window
        $scope.ribbonService.isFocus = false;

        // Release all keys when window loses focus
        keyboard.reset();

    };

    // Focus handler
    $window.onfocus = function () {

        // Set the flag indicating the focus of the window
        $scope.ribbonService.isFocus = true;

    };

    /**
     * Checks whether the clipboard data has changed, firing a new
     * "guacClipboard" event if it has.
     */
    var checkClipboard = function checkClipboard(event) {

        if ($scope.ribbonService.licenses.hasClipboard) {
            if (!$scope.ribbonService.licenses.hasClipboardIn) {
                if (event.type == 'focus')
                    return; 
            }
            var cliprdr = clipboardService.getLocalClipboard();
            if (cliprdr === null)
                return;

            cliprdr.then(function clipboardRead(data) {
                $scope.$broadcast('guacClipboard', data);
            }, angular.noop);
        }

    };

    // Attempt to read the clipboard if it may have changed
    $window.addEventListener('load',  function(event) {
        checkClipboard(event);
    }, true);
    $window.addEventListener('copy',  function(event) {
        checkClipboard(event);
    }, true);
    $window.addEventListener('cut',   function(event) {
        checkClipboard(event);
    }, true);
    $window.addEventListener('focus', function focusGained(event) {

        // Only recheck clipboard if it's the window itself that gained focus
        if (event.target === $window) {
            checkClipboard(event);
        }

    }, true);

    // Display login screen if a whole new set of credentials is needed
    $scope.$on('guacInvalidCredentials', function loginInvalid(error) {
        $scope.page.title = 'APP.NAME';
        $scope.page.bodyClassName = '';
        $scope.fatalError = error;
        $rootScope.homepage = error.subdomain;
        $rootScope.payloadVersion = error.payloadVersion;
    });

    // Display session expired page
    $scope.$on('guacExpiredUrl', function urlExpired(event, parameters, error) {
        $scope.ribbonService.displayMessenger = false;
        $scope.page.title = 'APP.NAME';
        $scope.page.bodyClassName = '';
        $scope.loginHelpText = null;
        $scope.acceptedCredentials = {};
        $scope.expectedCredentials = error.expected;
        $rootScope.loginHidden = true;
        $rootScope.homepage = error.subdomain;
        $rootScope.payloadVersion = error.payloadVersion;
        $rootScope.notifyNoCredentials = true;
    });

    // Prompt for remaining credentials if provided credentials were not enough
    $scope.$on('guacInsufficientCredentials', function loginInsufficient(event, parameters, error) {
        $scope.page.title = 'APP.NAME';
        $scope.page.bodyClassName = '';
        $scope.loginHelpText = error.translatableMessage;
        $scope.acceptedCredentials = parameters;
        $scope.expectedCredentials = error.expected;
        $rootScope.homepage = error.subdomain;
        $rootScope.payloadVersion = error.payloadVersion;
    });

    // Replace absolutely all content with an error message if the page itself
    // cannot be displayed due to an error
    $scope.$on('guacFatalPageError', function fatalPageError(error) {
        $scope.page.title = 'APP.NAME';
        $scope.page.bodyClassName = '';
        $scope.fatalError = error;
        $rootScope.homepage = error.subdomain;
        $rootScope.payloadVersion = error.payloadVersion;
    });

    // Display error notification
    $scope.$on('guacDuplicatedUser', function duplicatedUser(error) {
        title = "CLIENT.DIALOG_HEADER_CONNECTION_ERROR";

        var RECONNECT_ACTION = {
            name      : "CLIENT.ACTION_RECONNECT",
            className : "reconnect button",
            callback  : function reconnectCallback() {

                reconnectAction();

            }
        };

        var LOGIN_PAGE_ACTION = {
            name      : "CLIENT.LOGIN_PAGE",
            className : "loginPage button",
            callback  : function loginPageCallback() {
    
                $rootScope.goLoginPage();
    
            }
        };

        // Show error status
        if ($rootScope.isKioskMode) {
            guacNotification.showStatus({
                className : "error",
                title     : title,
                text      : {
                    key : "CLIENT.ERROR_TUNNEL_201",
                },
                actions   : [ RECONNECT_ACTION, LOGIN_PAGE_ACTION ]
            });
        }
        else {
            guacNotification.showStatus({
                className : "error",
                title     : title,
                text      : {
                    key : "CLIENT.ERROR_TUNNEL_201",
                },
                actions   : [ RECONNECT_ACTION ]
            });
        }

    });

    // Redirect the `sorry` page when getting many connnections error
    $scope.$on('guacManyConnections', function fatalPageError(error) {
        $window.location.href = '#/sorry';
        $rootScope.homepage = error.subdomain;
    });

    function uuidv4() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    // Update title and CSS class upon navigation
    $scope.$on('$routeChangeSuccess', function(event, current, previous) {
        $scope.ribbonService.uuid = uuidv4();

        // If the current route is available
        if (current.$$route) {

            // Clear login screen if route change was successful (and thus
            // login was either successful or not required)
            $scope.loginHelpText = null;
            $scope.acceptedCredentials = null;
            $scope.expectedCredentials = null;

            // Set title
            var title = current.$$route.title;
            if (title)
                $scope.page.title = title;

            // Set body CSS class
            $scope.page.bodyClassName = current.$$route.bodyClassName || '';

            if ($routeParams.hasOwnProperty("name")) {
                $rootScope.ribbonService.cursor_name = $routeParams.name;
            }
        }

    });

}]);
