/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing ribbon bar.
 */
angular.module('ribbon').controller('summaryChartController',
    ['$scope', '$routeParams', '$element', '$injector',

        function summaryChartController($scope, $routeParams, $element, $injector) {

            // requiredServices
            var $q = $injector.get('$q');
            var $http = $injector.get('$http');
            var authenticationService = $injector.get('authenticationService');
            var $translate = $injector.get('$translate');
            var $location = $injector.get('$location');

            // Required types
            var ClientIdentifier = $injector.get('ClientIdentifier');

            if ($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                return;
            }
            $scope.STATISTICS_API = {
                USER: {
                    chartActivityID: 0, activityField: "user_activity",
                    chartDurationID: 1, durationField: "cumulative_duration",
                    url: "api/chartdata/user"
                },
                GROUP: {
                    chartActivityID: 2, activityField: "average_activity",
                    chartDurationID: 3, durationField: "cumulative_duration",
                    url: "api/chartdata/group"
                }
            }

            // When set to true, activates demo features.
            $scope.demo = false;

            $scope.ribbonService = $injector.get('ribbonService');

            var d = new Date();
            $scope.tzOffset = -d.getTimezoneOffset();

            var c = document.querySelector(".bar.bar-blue")
            var ctx = c.getContext("2d");
            ctx.rect(0, 0, 40, 15);
            ctx.fillStyle = '#78c88c';
            ctx.fill();

            c = document.querySelector(".bar.bar-red")
            ctx = c.getContext("2d");
            ctx.rect(0, 0, 40, 15);
            ctx.fillStyle = '#5a90cf';
            ctx.fill();

            $scope.summaryChart = new Chart($element[0].querySelector("#summaryChart"), {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        borderColor: 'rgba(190,211,236,255)',
                        backgroundColor : '#5a90cf',
                        borderWidth: 1,
                        data: [],
                        hidden: false,
                        pointRadius: 0,
                        yAxisID: 'ACTIVITY'
                    },

                    {
                        type: 'scatter',
                        borderColor: '#5a90cf',
                        borderWidth: 2,
                        data: [],
                        fill: false,
                        hidden: true,
                        lineTension: 0,
                        pointRadius: 0,
                        spanGaps: true,
                        yAxisID: 'DURATION'
                    },

                    {
                        borderColor: 'rgba(175,222,187,255)',
                        backgroundColor: '#78c88c',
                        borderWidth: 1,
                        data: [],
                        hidden: false,
                        pointRadius: 0,
                        yAxisID: 'ACTIVITY'
                    },

                    {
                        type: 'scatter',
                        borderColor: '#78c88c',
                        borderDash: [5, 10],
                        borderWidth: 2,
                        data: [],
                        fill: false,
                        hidden: true,
                        lineTension: 0,
                        pointRadius: 0,
                        spanGaps: true,
                        yAxisID: 'DURATION'
                    }]

                },
                options: {
                    legend: {
                        display: false
                    },
                    scales: {
                        yAxes: [{
                            id: 'ACTIVITY',
                            display: true,
                            gridLines: {
                                display: true
                            },
                            position: 'left',
                            scaleLabel: {
                                display: true
                            },
                            ticks: {
                                beginAtZero: true,
                                display: true
                            },
                            type: 'linear'
                        },
                        {
                            id: 'DURATION',
                            display: false,
                            gridLines: {
                                display: false
                            },
                            position: 'right',
                            scaleLabel: {
                                display: true
                            },
                            ticks: {
                                beginAtZero: true,
                                callback: function (label, index, labels) {
                                    return Math.trunc(label / 3600) + ' h';
                                },
                                display: true
                            },
                            type: 'linear'
                        }],
                        xAxes: [{
                            ticks: {
                                userCallback: function (label, index, labels) {
                                    return moment((+label - $scope.tzOffset * 60) * 1000).format("MMM DD, YYYY");
                                }
                            },
                            barThickness: 15,
                            display: true,
                            scaleLabel: {
                                display: true
                            }
                        }]
                    }
                }
            });

            $translate('CHARTS.USER_ACTIVITY').then(function textReceived(text) {
                $scope.summaryChart.data.datasets[0].label = text;
            })
            $translate('CHARTS.USER_DURATION').then(function textReceived(text) {
                $scope.summaryChart.data.datasets[1].label = text;
            })
            $translate('CHARTS.GROUP_ACTIVITY').then(function textReceived(text) {
                $scope.summaryChart.data.datasets[2].label = text;
            })
            $translate('CHARTS.GROUP_DURATION').then(function textReceived(text) {
                $scope.summaryChart.data.datasets[3].label = text;
            })

            $scope.closeDialog = function closeDialog() {
                $scope.ribbonService.summaryChartVisible = false;
            }

            $scope.chartState = 'Activity';

            $scope.toggleActivity = function toggleActivity(){
               if($scope.chartState === 'Duration'){
                $scope.chartState = 'Activity';
                $scope.toggleChart(0); 
                $scope.toggleChart(1); 
                $scope.toggleChart(2); 
                $scope.toggleChart(3); 
                $scope.summaryChart.options.scales.yAxes[1].display = false;
                $scope.summaryChart.data.datasets[0].hidden = false;
                $scope.summaryChart.data.datasets[1].hidden = true;
                $scope.summaryChart.data.datasets[2].hidden = false;
                $scope.summaryChart.data.datasets[3].hidden = true;
               }
            }
            $scope.toggleDuration = function toggleDuration(){
                if($scope.chartState === 'Activity'){
                $scope.chartState = 'Duration';
                $scope.toggleChart(0); 
                $scope.toggleChart(1); 
                $scope.toggleChart(2); 
                $scope.toggleChart(3); 
                $scope.summaryChart.options.scales.yAxes[0].display = false;
                $scope.summaryChart.options.scales.yAxes[1].gridLines.display = true;
                $scope.summaryChart.data.datasets[0].hidden = true;
                $scope.summaryChart.data.datasets[1].hidden = false;
                $scope.summaryChart.data.datasets[2].hidden = true;
                $scope.summaryChart.data.datasets[3].hidden = false;
                }
            }

            /**
             * Toogles chart visibility.
             * 
             * @param  id       id of a chart
             */
            $scope.toggleChart = function toggleChart(id) {
                $scope.summaryChart.data.datasets[id].hidden = !$scope.summaryChart.data.datasets[id].hidden;

                // Hide activity axis when activities are not shown
                $scope.summaryChart.options.scales.yAxes[0].display =
                    !($scope.summaryChart.data.datasets[$scope.STATISTICS_API.USER.chartActivityID].hidden &&
                        $scope.summaryChart.data.datasets[$scope.STATISTICS_API.GROUP.chartActivityID].hidden)

                // Hide duration axis when durations are not shown
                $scope.summaryChart.options.scales.yAxes[1].display =
                    !($scope.summaryChart.data.datasets[$scope.STATISTICS_API.USER.chartDurationID].hidden &&
                        $scope.summaryChart.data.datasets[$scope.STATISTICS_API.GROUP.chartDurationID].hidden)

                // Show grid lines on the time axis only if activity axis is not displayed (activity axes always
                // display grid lines.
                $scope.summaryChart.options.scales.yAxes[1].gridLines.display = !$scope.summaryChart.options.scales.yAxes[0].display;

                $scope.summaryChart.update();
            }

            /**
             * User data and group data read from database.
             */
            $scope.user_data = null;
            $scope.group_data = null;

            /**
             * Activates when summaryChartVisible is set; this is also used as ng-class parameter
             * which shows whole dialog.
             * Show summary chart in the dialog.
             */
            $scope.$watch('ribbonService.summaryChartVisible', function (newVal) {
                if (newVal) {
                    $scope.clearHistory();
                    $scope.readHistory($scope.STATISTICS_API.USER).
                        then(function () {
                            $scope.readHistory($scope.STATISTICS_API.GROUP).
                                then(function () {
                                    $scope.generateChart().
                                        then($scope.summaryChart.update());
                                });
                        });
                }
            })

            /**
             * Generates chart with categories on the X axis that matches the date range
             * of the charts. Beginning date is set as the minimum value of all charts, while
             * end date is set to maximum value of all charts.
             * 
             * Next, labels are generated in the range min to max date. Labels will be used as
             * X coordinates as well (this is done automatically by chartjs). Labels are generated
             * using timestamps, this allows ordering of the categories. X axis has a callback that
             * creates a human readable date used for a ticks.
             * 
             * Next, categories and lines are populated:
             * 
             * Categories are charted as bar chart. Each category is placed in a category array on a
             * position equal to the position of a category date in the labels array. Missing array
             * members are simply skipped, and only non-null and non-undefined array values are plotted.
             * Because of that, category array is populated with zeros on the position where no bars exists.
             * 
             * Lines are plotted as scattered chart. Each point is stored in the array with X coordinate
             * equal to the timestamp and Y coordinate equals to the value.
             * 
             */
            $scope.generateChart = function () {
                return $q(function (resolve, reject) {
                    /**
                     * Check whether the user_data or group_data is null/undefined
                     */
                    if (!$scope.user_data.data || !$scope.group_data.data)
                        return;

                    $scope.user_data.data = $scope.user_data.data.map((value) => {
                        var date = new Date(value.daystring * 1000);
                        date.setUTCHours(0, 0, 0, 0);
                        value.daystring = date.getTime() / 1000;

                        return value;
                    });

                    $scope.group_data.data = $scope.group_data.data.map((value) => {
                        var date = new Date(value.daystring * 1000);
                        date.setUTCHours(0, 0, 0, 0);
                        value.daystring = date.getTime() / 1000;

                        return value;
                    });

                    /**
                     * Find min and max data in arrays
                     */
                    var min_ts_user = +$scope.user_data.data.reduce(function (prev, curr) { return prev["daystring"] < curr["daystring"] ? prev : curr; }).daystring;
                    var max_ts_user = +$scope.user_data.data.reduce(function (prev, curr) { return prev["daystring"] > curr["daystring"] ? prev : curr; }).daystring;

                    var min_ts_group = +$scope.group_data.data.reduce(function (prev, curr) { return prev["daystring"] < curr["daystring"] ? prev : curr; }).daystring;
                    var max_ts_group = +$scope.group_data.data.reduce(function (prev, curr) { return prev["daystring"] > curr["daystring"] ? prev : curr; }).daystring;

                    var min_ts = min_ts_user < min_ts_group ? min_ts_user : min_ts_group;
                    var max_ts = max_ts_user > max_ts_group ? max_ts_user : max_ts_group;

                    /**
                     * Generate label for X axis in the range min-max using timestamps
                     */
                    var labels = $scope.summaryChart.data.labels;
                    var counter = min_ts;
                    for (counter = min_ts; counter <= max_ts; counter += 60 * 60 * 24)
                        if (labels.indexOf(counter) == -1)
                            labels.push(counter);

                    /**
                     * Populate data for bars and lines for user data.
                     * Bars must have all categories populated, not only one with values. So, first populate array values
                     * on the position determined by timestamp, and later populate remaining positions with zero.
                     * Charts are scattered, so they can be determined with array of (x,y) coordinates (timestamp, value).
                     */
                    var activityData = $scope.summaryChart.data.datasets[$scope.STATISTICS_API.USER.chartActivityID].data;
                    var durationData = $scope.summaryChart.data.datasets[$scope.STATISTICS_API.USER.chartDurationID].data;

                    // Populate values
                    for (i = 0; i < $scope.user_data.data.length; i++) {
                        activityData[labels.indexOf(+$scope.user_data.data[i].daystring)] = $scope.user_data.data[i][$scope.STATISTICS_API.USER.activityField];
                        durationData[labels.indexOf(+$scope.user_data.data[i].daystring)] = {
                            x: $scope.user_data.data[i].daystring,
                            y: $scope.user_data.data[i][$scope.STATISTICS_API.USER.durationField]
                        };
                    }
                    // Fill missing categories for bars with zeros.
                    for (i = 0; i < activityData.length; i++)
                        if (activityData[i] == null || activityData[i] == undefined)
                            activityData[i] = "0";

                    /**
                     * Populate data for bars and lines for group data.
                     * Do the same as for the user data.
                     */
                    activityData = $scope.summaryChart.data.datasets[$scope.STATISTICS_API.GROUP.chartActivityID].data;
                    durationData = $scope.summaryChart.data.datasets[$scope.STATISTICS_API.GROUP.chartDurationID].data;

                    // Populate values
                    for (i = 0; i < $scope.group_data.data.length; i++) {
                        activityData[labels.indexOf(+$scope.group_data.data[i].daystring)] = $scope.group_data.data[i][$scope.STATISTICS_API.GROUP.activityField];
                        durationData[labels.indexOf(+$scope.group_data.data[i].daystring)] = {
                            x: $scope.group_data.data[i].daystring,
                            y: $scope.group_data.data[i][$scope.STATISTICS_API.GROUP.durationField]
                        };
                    }
                    // Fill missing categories for bars with zeros.
                    for (i = 0; i < activityData.length; i++)
                        if (activityData[i] == null || activityData[i] == undefined)
                            activityData[i] = "0";
                });
            }

            /**
             * Read history data for a current user from the database.
             * 
             * @param STATISTICS_API api to be used, user or group
             * 
             * @returns {Promise}
             *     A promise which succeeds when the data is received from the server
             *     and filtered.
             */
            $scope.readHistory = function readHistory(api) {
                var historyData = $q.defer();
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

                /**
                 * Read historical data
                 */
                var httpParameters = {
                    token: authenticationService.getCurrentToken(),
                    id: clientIdentifier.id,
                    datasource: clientIdentifier.dataSource,
                    timezone: ($scope.tzOffset < 0 ? "" : "+") + $scope.tzOffset
                };

                $http({
                    method: 'GET',
                    url: api.url,
                    params: httpParameters
                })
                    .then(function (avg_data) {
                        if (api == $scope.STATISTICS_API.USER)
                            $scope.user_data = avg_data.data;
                        if (api == $scope.STATISTICS_API.GROUP)
                            $scope.group_data = avg_data.data;

                        historyData.resolve();

                        return;
                    });

                return historyData.promise;
            }

            /**
             * Clear all data from historical charts
             */
            $scope.clearHistory = function clearHistory() {
                var datasets = $scope.summaryChart.data.datasets;

                datasets[0].data.length = 0;
                datasets[1].data.length = 0;
                datasets[2].data.length = 0;
                datasets[3].data.length = 0;
            }

        }
    ]);
