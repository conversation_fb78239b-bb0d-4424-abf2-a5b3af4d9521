!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var t;t="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,t.SimpleWebsocket=e()}}(function(){return function e(t,r,n){function i(s,a){if(!r[s]){if(!t[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(o)return o(s,!0);var f=new Error("Cannot find module '"+s+"'");throw f.code="MODULE_NOT_FOUND",f}var h=r[s]={exports:{}};t[s][0].call(h.exports,function(e){var r=t[s][1][e];return i(r?r:e)},h,h.exports,e,t,r,n)}return r[s].exports}for(var o="function"==typeof require&&require,s=0;s<n.length;s++)i(n[s]);return i}({1:[function(e,t,r){},{}],2:[function(e,t,r){(function(t){"use strict";function n(){try{var e=new Uint8Array(1);return e.foo=function(){return 42},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(t){return!1}}function i(){return o.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e){return this instanceof o?(o.TYPED_ARRAY_SUPPORT||(this.length=0,this.parent=void 0),"number"==typeof e?s(this,e):"string"==typeof e?a(this,e,arguments.length>1?arguments[1]:"utf8"):u(this,e)):arguments.length>1?new o(e,arguments[1]):new o(e)}function s(e,t){if(e=g(e,0>t?0:0|v(t)),!o.TYPED_ARRAY_SUPPORT)for(var r=0;t>r;r++)e[r]=0;return e}function a(e,t,r){"string"==typeof r&&""!==r||(r="utf8");var n=0|b(t,r);return e=g(e,n),e.write(t,r),e}function u(e,t){if(o.isBuffer(t))return f(e,t);if(Z(t))return h(e,t);if(null==t)throw new TypeError("must start with number, buffer, array or string");if("undefined"!=typeof ArrayBuffer){if(t.buffer instanceof ArrayBuffer)return c(e,t);if(t instanceof ArrayBuffer)return l(e,t)}return t.length?d(e,t):p(e,t)}function f(e,t){var r=0|v(t.length);return e=g(e,r),t.copy(e,0,0,r),e}function h(e,t){var r=0|v(t.length);e=g(e,r);for(var n=0;r>n;n+=1)e[n]=255&t[n];return e}function c(e,t){var r=0|v(t.length);e=g(e,r);for(var n=0;r>n;n+=1)e[n]=255&t[n];return e}function l(e,t){return t.byteLength,o.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=o.prototype):e=c(e,new Uint8Array(t)),e}function d(e,t){var r=0|v(t.length);e=g(e,r);for(var n=0;r>n;n+=1)e[n]=255&t[n];return e}function p(e,t){var r,n=0;"Buffer"===t.type&&Z(t.data)&&(r=t.data,n=0|v(r.length)),e=g(e,n);for(var i=0;n>i;i+=1)e[i]=255&r[i];return e}function g(e,t){o.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=o.prototype):e.length=t;var r=0!==t&&t<=o.poolSize>>>1;return r&&(e.parent=G),e}function v(e){if(e>=i())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i().toString(16)+" bytes");return 0|e}function y(e,t){if(!(this instanceof y))return new y(e,t);var r=new o(e,t);return delete r.parent,r}function b(e,t){"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"binary":case"raw":case"raws":return r;case"utf8":case"utf-8":return F(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return $(e).length;default:if(n)return F(e).length;t=(""+t).toLowerCase(),n=!0}}function w(e,t,r){var n=!1;if(t=0|t,r=void 0===r||r===1/0?this.length:0|r,e||(e="utf8"),0>t&&(t=0),r>this.length&&(r=this.length),t>=r)return"";for(;;)switch(e){case"hex":return T(this,t,r);case"utf8":case"utf-8":return x(this,t,r);case"ascii":return M(this,t,r);case"binary":return B(this,t,r);case"base64":return k(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function m(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n),n>i&&(n=i)):n=i;var o=t.length;if(o%2!==0)throw new Error("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;n>s;s++){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))throw new Error("Invalid hex string");e[r+s]=a}return s}function _(e,t,r,n){return J(F(t,e.length-r),e,r,n)}function E(e,t,r,n){return J(z(t),e,r,n)}function R(e,t,r,n){return E(e,t,r,n)}function S(e,t,r,n){return J($(t),e,r,n)}function A(e,t,r,n){return J(H(t,e.length-r),e,r,n)}function k(e,t,r){return 0===t&&r===e.length?X.fromByteArray(e):X.fromByteArray(e.slice(t,r))}function x(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;r>i;){var o=e[i],s=null,a=o>239?4:o>223?3:o>191?2:1;if(r>=i+a){var u,f,h,c;switch(a){case 1:128>o&&(s=o);break;case 2:u=e[i+1],128===(192&u)&&(c=(31&o)<<6|63&u,c>127&&(s=c));break;case 3:u=e[i+1],f=e[i+2],128===(192&u)&&128===(192&f)&&(c=(15&o)<<12|(63&u)<<6|63&f,c>2047&&(55296>c||c>57343)&&(s=c));break;case 4:u=e[i+1],f=e[i+2],h=e[i+3],128===(192&u)&&128===(192&f)&&128===(192&h)&&(c=(15&o)<<18|(63&u)<<12|(63&f)<<6|63&h,c>65535&&1114112>c&&(s=c))}}null===s?(s=65533,a=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=a}return L(n)}function L(e){var t=e.length;if(Q>=t)return String.fromCharCode.apply(String,e);for(var r="",n=0;t>n;)r+=String.fromCharCode.apply(String,e.slice(n,n+=Q));return r}function M(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;r>i;i++)n+=String.fromCharCode(127&e[i]);return n}function B(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;r>i;i++)n+=String.fromCharCode(e[i]);return n}function T(e,t,r){var n=e.length;(!t||0>t)&&(t=0),(!r||0>r||r>n)&&(r=n);for(var i="",o=t;r>o;o++)i+=N(e[o]);return i}function P(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function C(e,t,r){if(e%1!==0||0>e)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function j(e,t,r,n,i,s){if(!o.isBuffer(e))throw new TypeError("buffer must be a Buffer instance");if(t>i||s>t)throw new RangeError("value is out of bounds");if(r+n>e.length)throw new RangeError("index out of range")}function O(e,t,r,n){0>t&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);o>i;i++)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function U(e,t,r,n){0>t&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);o>i;i++)e[r+i]=t>>>8*(n?i:3-i)&255}function D(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("index out of range");if(0>r)throw new RangeError("index out of range")}function I(e,t,r,n,i){return i||D(e,t,r,4,3.4028234663852886e38,-3.4028234663852886e38),K.write(e,t,r,n,23,4),r+4}function Y(e,t,r,n,i){return i||D(e,t,r,8,1.7976931348623157e308,-1.7976931348623157e308),K.write(e,t,r,n,52,8),r+8}function q(e){if(e=W(e).replace(V,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function W(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function N(e){return 16>e?"0"+e.toString(16):e.toString(16)}function F(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],s=0;n>s;s++){if(r=e.charCodeAt(s),r>55295&&57344>r){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(56320>r){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,128>r){if((t-=1)<0)break;o.push(r)}else if(2048>r){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(65536>r){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(1114112>r))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function z(e){for(var t=[],r=0;r<e.length;r++)t.push(255&e.charCodeAt(r));return t}function H(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);s++)r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}function $(e){return X.toByteArray(q(e))}function J(e,t,r,n){for(var i=0;n>i&&!(i+r>=t.length||i>=e.length);i++)t[i+r]=e[i];return i}var X=e("base64-js"),K=e("ieee754"),Z=e("isarray");r.Buffer=o,r.SlowBuffer=y,r.INSPECT_MAX_BYTES=50,o.poolSize=8192;var G={};o.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:n(),o._augment=function(e){return e.__proto__=o.prototype,e},o.TYPED_ARRAY_SUPPORT?(o.prototype.__proto__=Uint8Array.prototype,o.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&o[Symbol.species]===o&&Object.defineProperty(o,Symbol.species,{value:null,configurable:!0})):(o.prototype.length=void 0,o.prototype.parent=void 0),o.isBuffer=function(e){return!(null==e||!e._isBuffer)},o.compare=function(e,t){if(!o.isBuffer(e)||!o.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,s=Math.min(r,n);s>i&&e[i]===t[i];)++i;return i!==s&&(r=e[i],n=t[i]),n>r?-1:r>n?1:0},o.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"raw":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},o.concat=function(e,t){if(!Z(e))throw new TypeError("list argument must be an Array of Buffers.");if(0===e.length)return new o(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;r++)t+=e[r].length;var n=new o(t),i=0;for(r=0;r<e.length;r++){var s=e[r];s.copy(n,i),i+=s.length}return n},o.byteLength=b,o.prototype._isBuffer=!0,o.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?x(this,0,e):w.apply(this,arguments)},o.prototype.equals=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:0===o.compare(this,e)},o.prototype.inspect=function(){var e="",t=r.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},o.prototype.compare=function(e){if(!o.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?0:o.compare(this,e)},o.prototype.indexOf=function(e,t){function r(e,t,r){for(var n=-1,i=0;r+i<e.length;i++)if(e[r+i]===t[-1===n?0:i-n]){if(-1===n&&(n=i),i-n+1===t.length)return r+n}else n=-1;return-1}if(t>2147483647?t=2147483647:-2147483648>t&&(t=-2147483648),t>>=0,0===this.length)return-1;if(t>=this.length)return-1;if(0>t&&(t=Math.max(this.length+t,0)),"string"==typeof e)return 0===e.length?-1:String.prototype.indexOf.call(this,e,t);if(o.isBuffer(e))return r(this,e,t);if("number"==typeof e)return o.TYPED_ARRAY_SUPPORT&&"function"===Uint8Array.prototype.indexOf?Uint8Array.prototype.indexOf.call(this,e,t):r(this,[e],t);throw new TypeError("val must be string, number or Buffer")},o.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t=0|t,isFinite(r)?(r=0|r,void 0===n&&(n="utf8")):(n=r,r=void 0);else{var i=n;n=t,t=0|r,r=i}var o=this.length-t;if((void 0===r||r>o)&&(r=o),e.length>0&&(0>r||0>t)||t>this.length)throw new RangeError("attempt to write outside buffer bounds");n||(n="utf8");for(var s=!1;;)switch(n){case"hex":return m(this,e,t,r);case"utf8":case"utf-8":return _(this,e,t,r);case"ascii":return E(this,e,t,r);case"binary":return R(this,e,t,r);case"base64":return S(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,r);default:if(s)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),s=!0}},o.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Q=4096;o.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,0>e?(e+=r,0>e&&(e=0)):e>r&&(e=r),0>t?(t+=r,0>t&&(t=0)):t>r&&(t=r),e>t&&(t=e);var n;if(o.TYPED_ARRAY_SUPPORT)n=this.subarray(e,t),n.__proto__=o.prototype;else{var i=t-e;n=new o(i,void 0);for(var s=0;i>s;s++)n[s]=this[s+e]}return n.length&&(n.parent=this.parent||this),n},o.prototype.readUIntLE=function(e,t,r){e=0|e,t=0|t,r||C(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},o.prototype.readUIntBE=function(e,t,r){e=0|e,t=0|t,r||C(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},o.prototype.readUInt8=function(e,t){return t||C(e,1,this.length),this[e]},o.prototype.readUInt16LE=function(e,t){return t||C(e,2,this.length),this[e]|this[e+1]<<8},o.prototype.readUInt16BE=function(e,t){return t||C(e,2,this.length),this[e]<<8|this[e+1]},o.prototype.readUInt32LE=function(e,t){return t||C(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},o.prototype.readUInt32BE=function(e,t){return t||C(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},o.prototype.readIntLE=function(e,t,r){e=0|e,t=0|t,r||C(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return i*=128,n>=i&&(n-=Math.pow(2,8*t)),n},o.prototype.readIntBE=function(e,t,r){e=0|e,t=0|t,r||C(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},o.prototype.readInt8=function(e,t){return t||C(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},o.prototype.readInt16LE=function(e,t){t||C(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},o.prototype.readInt16BE=function(e,t){t||C(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},o.prototype.readInt32LE=function(e,t){return t||C(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},o.prototype.readInt32BE=function(e,t){return t||C(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},o.prototype.readFloatLE=function(e,t){return t||C(e,4,this.length),K.read(this,e,!0,23,4)},o.prototype.readFloatBE=function(e,t){return t||C(e,4,this.length),K.read(this,e,!1,23,4)},o.prototype.readDoubleLE=function(e,t){return t||C(e,8,this.length),K.read(this,e,!0,52,8)},o.prototype.readDoubleBE=function(e,t){return t||C(e,8,this.length),K.read(this,e,!1,52,8)},o.prototype.writeUIntLE=function(e,t,r,n){e=+e,t=0|t,r=0|r,n||j(this,e,t,r,Math.pow(2,8*r),0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},o.prototype.writeUIntBE=function(e,t,r,n){e=+e,t=0|t,r=0|r,n||j(this,e,t,r,Math.pow(2,8*r),0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},o.prototype.writeUInt8=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,1,255,0),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},o.prototype.writeUInt16LE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},o.prototype.writeUInt16BE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,2,65535,0),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},o.prototype.writeUInt32LE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):U(this,e,t,!0),t+4},o.prototype.writeUInt32BE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,4,4294967295,0),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):U(this,e,t,!1),t+4},o.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t=0|t,!n){var i=Math.pow(2,8*r-1);j(this,e,t,r,i-1,-i)}var o=0,s=1,a=0>e?1:0;for(this[t]=255&e;++o<r&&(s*=256);)this[t+o]=(e/s>>0)-a&255;return t+r},o.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t=0|t,!n){var i=Math.pow(2,8*r-1);j(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0>e?1:0;for(this[t+o]=255&e;--o>=0&&(s*=256);)this[t+o]=(e/s>>0)-a&255;return t+r},o.prototype.writeInt8=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,1,127,-128),o.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),0>e&&(e=255+e+1),this[t]=255&e,t+1},o.prototype.writeInt16LE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):O(this,e,t,!0),t+2},o.prototype.writeInt16BE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,2,32767,-32768),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):O(this,e,t,!1),t+2},o.prototype.writeInt32LE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,4,2147483647,-2147483648),o.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):U(this,e,t,!0),t+4},o.prototype.writeInt32BE=function(e,t,r){return e=+e,t=0|t,r||j(this,e,t,4,2147483647,-2147483648),0>e&&(e=4294967295+e+1),o.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):U(this,e,t,!1),t+4},o.prototype.writeFloatLE=function(e,t,r){return I(this,e,t,!0,r)},o.prototype.writeFloatBE=function(e,t,r){return I(this,e,t,!1,r)},o.prototype.writeDoubleLE=function(e,t,r){return Y(this,e,t,!0,r)},o.prototype.writeDoubleBE=function(e,t,r){return Y(this,e,t,!1,r)},o.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&r>n&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(0>t)throw new RangeError("targetStart out of bounds");if(0>r||r>=this.length)throw new RangeError("sourceStart out of bounds");if(0>n)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,s=n-r;if(this===e&&t>r&&n>t)for(i=s-1;i>=0;i--)e[i+t]=this[i+r];else if(1e3>s||!o.TYPED_ARRAY_SUPPORT)for(i=0;s>i;i++)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+s),t);return s},o.prototype.fill=function(e,t,r){if(e||(e=0),t||(t=0),r||(r=this.length),t>r)throw new RangeError("end < start");if(r!==t&&0!==this.length){if(0>t||t>=this.length)throw new RangeError("start out of bounds");if(0>r||r>this.length)throw new RangeError("end out of bounds");var n;if("number"==typeof e)for(n=t;r>n;n++)this[n]=e;else{var i=F(e.toString()),o=i.length;for(n=t;r>n;n++)this[n]=i[n%o]}return this}};var V=/[^+\/0-9A-Za-z-_]/g}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"base64-js":3,ieee754:4,isarray:5}],3:[function(e,t,r){"use strict";function n(){var e,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=t.length;for(e=0;r>e;e++)u[e]=t[e];for(e=0;r>e;++e)f[t.charCodeAt(e)]=e;f["-".charCodeAt(0)]=62,f["_".charCodeAt(0)]=63}function i(e){var t,r,n,i,o,s,a=e.length;if(a%4>0)throw new Error("Invalid string. Length must be a multiple of 4");o="="===e[a-2]?2:"="===e[a-1]?1:0,s=new h(3*a/4-o),n=o>0?a-4:a;var u=0;for(t=0,r=0;n>t;t+=4,r+=3)i=f[e.charCodeAt(t)]<<18|f[e.charCodeAt(t+1)]<<12|f[e.charCodeAt(t+2)]<<6|f[e.charCodeAt(t+3)],s[u++]=(16711680&i)>>16,s[u++]=(65280&i)>>8,s[u++]=255&i;return 2===o?(i=f[e.charCodeAt(t)]<<2|f[e.charCodeAt(t+1)]>>4,s[u++]=255&i):1===o&&(i=f[e.charCodeAt(t)]<<10|f[e.charCodeAt(t+1)]<<4|f[e.charCodeAt(t+2)]>>2,s[u++]=i>>8&255,s[u++]=255&i),s}function o(e){return u[e>>18&63]+u[e>>12&63]+u[e>>6&63]+u[63&e]}function s(e,t,r){for(var n,i=[],s=t;r>s;s+=3)n=(e[s]<<16)+(e[s+1]<<8)+e[s+2],i.push(o(n));return i.join("")}function a(e){for(var t,r=e.length,n=r%3,i="",o=[],a=16383,f=0,h=r-n;h>f;f+=a)o.push(s(e,f,f+a>h?h:f+a));return 1===n?(t=e[r-1],i+=u[t>>2],i+=u[t<<4&63],i+="=="):2===n&&(t=(e[r-2]<<8)+e[r-1],i+=u[t>>10],i+=u[t>>4&63],i+=u[t<<2&63],i+="="),o.push(i),o.join("")}r.toByteArray=i,r.fromByteArray=a;var u=[],f=[],h="undefined"!=typeof Uint8Array?Uint8Array:Array;n()},{}],4:[function(e,t,r){r.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,f=u>>1,h=-7,c=r?i-1:0,l=r?-1:1,d=e[t+c];for(c+=l,o=d&(1<<-h)-1,d>>=-h,h+=a;h>0;o=256*o+e[t+c],c+=l,h-=8);for(s=o&(1<<-h)-1,o>>=-h,h+=n;h>0;s=256*s+e[t+c],c+=l,h-=8);if(0===o)o=1-f;else{if(o===u)return s?NaN:(d?-1:1)*(1/0);s+=Math.pow(2,n),o-=f}return(d?-1:1)*s*Math.pow(2,o-n)},r.write=function(e,t,r,n,i,o){var s,a,u,f=8*o-i-1,h=(1<<f)-1,c=h>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,g=0>t||0===t&&0>1/t?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=h):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+c>=1?l/u:l*Math.pow(2,1-c),t*u>=2&&(s++,u/=2),s+c>=h?(a=0,s=h):s+c>=1?(a=(t*u-1)*Math.pow(2,i),s+=c):(a=t*Math.pow(2,c-1)*Math.pow(2,i),s=0));i>=8;e[r+d]=255&a,d+=p,a/=256,i-=8);for(s=s<<i|a,f+=i;f>0;e[r+d]=255&s,d+=p,s/=256,f-=8);e[r+d-p]|=128*g}},{}],5:[function(e,t,r){var n={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},{}],6:[function(e,t,r){function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function i(e){return"function"==typeof e}function o(e){return"number"==typeof e}function s(e){return"object"==typeof e&&null!==e}function a(e){return void 0===e}t.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(e){if(!o(e)||0>e||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},n.prototype.emit=function(e){var t,r,n,o,u,f;if(this._events||(this._events={}),"error"===e&&(!this._events.error||s(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;throw TypeError('Uncaught, unspecified "error" event.')}if(r=this._events[e],a(r))return!1;if(i(r))switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:o=Array.prototype.slice.call(arguments,1),r.apply(this,o)}else if(s(r))for(o=Array.prototype.slice.call(arguments,1),f=r.slice(),n=f.length,u=0;n>u;u++)f[u].apply(this,o);return!0},n.prototype.addListener=function(e,t){var r;if(!i(t))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,i(t.listener)?t.listener:t),this._events[e]?s(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,s(this._events[e])&&!this._events[e].warned&&(r=a(this._maxListeners)?n.defaultMaxListeners:this._maxListeners,r&&r>0&&this._events[e].length>r&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace())),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(e,t){function r(){this.removeListener(e,r),n||(n=!0,t.apply(this,arguments))}if(!i(t))throw TypeError("listener must be a function");var n=!1;return r.listener=t,this.on(e,r),this},n.prototype.removeListener=function(e,t){var r,n,o,a;if(!i(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(r=this._events[e],o=r.length,n=-1,r===t||i(r.listener)&&r.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(s(r)){for(a=o;a-- >0;)if(r[a]===t||r[a].listener&&r[a].listener===t){n=a;break}if(0>n)return this;1===r.length?(r.length=0,delete this._events[e]):r.splice(n,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},n.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r=this._events[e],i(r))this.removeListener(e,r);else if(r)for(;r.length;)this.removeListener(e,r[r.length-1]);return delete this._events[e],this},n.prototype.listeners=function(e){var t;return t=this._events&&this._events[e]?i(this._events[e])?[this._events[e]]:this._events[e].slice():[]},n.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(i(t))return 1;if(t)return t.length}return 0},n.listenerCount=function(e,t){return e.listenerCount(t)}},{}],7:[function(e,t,r){t.exports=function(e){return!(null==e||!(e._isBuffer||e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)))}},{}],8:[function(e,t,r){function n(){h=!1,a.length?f=a.concat(f):c=-1,f.length&&i()}function i(){if(!h){var e=setTimeout(n);h=!0;for(var t=f.length;t;){for(a=f,f=[];++c<t;)a&&a[c].run();c=-1,t=f.length}a=null,h=!1,clearTimeout(e)}}function o(e,t){this.fun=e,this.array=t}function s(){}var a,u=t.exports={},f=[],h=!1,c=-1;u.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];f.push(new o(e,t)),1!==f.length||h||setTimeout(i,0)},o.prototype.run=function(){this.fun.apply(null,this.array)},u.title="browser",u.browser=!0,u.env={},u.argv=[],u.version="",u.versions={},u.on=s,u.addListener=s,u.once=s,u.off=s,u.removeListener=s,u.removeAllListeners=s,u.emit=s,u.binding=function(e){throw new Error("process.binding is not supported")},u.cwd=function(){return"/"},u.chdir=function(e){throw new Error("process.chdir is not supported")},u.umask=function(){return 0}},{}],9:[function(e,t,r){function n(){return"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31}function i(){var e=arguments,t=this.useColors;if(e[0]=(t?"%c":"")+this.namespace+(t?" %c":" ")+e[0]+(t?"%c ":" ")+"+"+r.humanize(this.diff),!t)return e;var n="color: "+this.color;e=[e[0],n,"color: inherit"].concat(Array.prototype.slice.call(e,1));var i=0,o=0;return e[0].replace(/%[a-z%]/g,function(e){"%%"!==e&&(i++,"%c"===e&&(o=i))}),e.splice(o,0,n),e}function o(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function s(e){try{null==e?r.storage.removeItem("debug"):r.storage.debug=e}catch(t){}}function a(){var e;try{e=r.storage.debug}catch(t){}return e}function u(){try{return window.localStorage}catch(e){}}r=t.exports=e("./debug"),r.log=o,r.formatArgs=i,r.save=s,r.load=a,r.useColors=n,r.storage="undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage?chrome.storage.local:u(),r.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],r.formatters.j=function(e){return JSON.stringify(e)},r.enable(a())},{"./debug":10}],10:[function(e,t,r){function n(){return r.colors[h++%r.colors.length]}function i(e){function t(){}function i(){var e=i,t=+new Date,o=t-(f||t);e.diff=o,e.prev=f,e.curr=t,f=t,null==e.useColors&&(e.useColors=r.useColors()),null==e.color&&e.useColors&&(e.color=n());var s=Array.prototype.slice.call(arguments);s[0]=r.coerce(s[0]),"string"!=typeof s[0]&&(s=["%o"].concat(s));var a=0;s[0]=s[0].replace(/%([a-z%])/g,function(t,n){if("%%"===t)return t;a++;var i=r.formatters[n];if("function"==typeof i){var o=s[a];t=i.call(e,o),s.splice(a,1),a--}return t}),"function"==typeof r.formatArgs&&(s=r.formatArgs.apply(e,s));var u=i.log||r.log||console.log.bind(console);u.apply(e,s)}t.enabled=!1,i.enabled=!0;var o=r.enabled(e)?i:t;return o.namespace=e,o}function o(e){r.save(e);for(var t=(e||"").split(/[\s,]+/),n=t.length,i=0;n>i;i++)t[i]&&(e=t[i].replace(/\*/g,".*?"),"-"===e[0]?r.skips.push(new RegExp("^"+e.substr(1)+"$")):r.names.push(new RegExp("^"+e+"$")))}function s(){r.enable("")}function a(e){var t,n;for(t=0,n=r.skips.length;n>t;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;n>t;t++)if(r.names[t].test(e))return!0;return!1}function u(e){return e instanceof Error?e.stack||e.message:e}r=t.exports=i,r.coerce=u,r.disable=s,r.enable=o,r.enabled=a,r.humanize=e("ms"),r.names=[],r.skips=[],r.formatters={};var f,h=0},{ms:11}],11:[function(e,t,r){function n(e){if(e=""+e,!(e.length>1e4)){var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]),n=(t[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*c;case"days":case"day":case"d":return r*h;case"hours":case"hour":case"hrs":case"hr":case"h":return r*f;case"minutes":case"minute":case"mins":case"min":case"m":return r*u;case"seconds":case"second":case"secs":case"sec":case"s":return r*a;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r}}}}function i(e){return e>=h?Math.round(e/h)+"d":e>=f?Math.round(e/f)+"h":e>=u?Math.round(e/u)+"m":e>=a?Math.round(e/a)+"s":e+"ms"}function o(e){return s(e,h,"day")||s(e,f,"hour")||s(e,u,"minute")||s(e,a,"second")||e+" ms"}function s(e,t,r){return t>e?void 0:1.5*t>e?Math.floor(e/t)+" "+r:Math.ceil(e/t)+" "+r+"s"}var a=1e3,u=60*a,f=60*u,h=24*f,c=365.25*h;t.exports=function(e,t){return t=t||{},"string"==typeof e?n(e):t["long"]?o(e):i(e)}},{}],12:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}},{}],13:[function(e,t,r){"use strict";function n(e){return this instanceof n?(f.call(this,e),h.call(this,e),e&&e.readable===!1&&(this.readable=!1),e&&e.writable===!1&&(this.writable=!1),this.allowHalfOpen=!0,e&&e.allowHalfOpen===!1&&(this.allowHalfOpen=!1),void this.once("end",i)):new n(e)}function i(){this.allowHalfOpen||this._writableState.ended||a(o,this)}function o(e){e.end()}var s=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=n;var a=e("process-nextick-args"),u=e("core-util-is");u.inherits=e("inherits");var f=e("./_stream_readable"),h=e("./_stream_writable");u.inherits(n,f);for(var c=s(h.prototype),l=0;l<c.length;l++){var d=c[l];n.prototype[d]||(n.prototype[d]=h.prototype[d])}},{"./_stream_readable":15,"./_stream_writable":17,"core-util-is":18,inherits:12,"process-nextick-args":20}],14:[function(e,t,r){"use strict";function n(e){return this instanceof n?void i.call(this,e):new n(e)}t.exports=n;var i=e("./_stream_transform"),o=e("core-util-is");o.inherits=e("inherits"),o.inherits(n,i),n.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":16,"core-util-is":18,inherits:12}],15:[function(e,t,r){(function(r){"use strict";function n(t,r){j=j||e("./_stream_duplex"),t=t||{},this.objectMode=!!t.objectMode,r instanceof j&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var n=t.highWaterMark,i=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:i,this.highWaterMark=~~this.highWaterMark,this.buffer=[],this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.ranOut=!1,this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(C||(C=e("string_decoder/").StringDecoder),this.decoder=new C(t.encoding),this.encoding=t.encoding)}function i(t){return j=j||e("./_stream_duplex"),this instanceof i?(this._readableState=new n(t,this),this.readable=!0,t&&"function"==typeof t.read&&(this._read=t.read),void L.call(this)):new i(t)}function o(e,t,r,n,i){var o=f(t,r);if(o)e.emit("error",o);else if(null===r)t.reading=!1,h(e,t);else if(t.objectMode||r&&r.length>0)if(t.ended&&!i){var a=new Error("stream.push() after EOF");e.emit("error",a)}else if(t.endEmitted&&i){var a=new Error("stream.unshift() after end event");e.emit("error",a)}else{var u;!t.decoder||i||n||(r=t.decoder.write(r),u=!t.objectMode&&0===r.length),i||(t.reading=!1),u||(t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&c(e))),d(e,t)}else i||(t.reading=!1);return s(t)}function s(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}function a(e){return e>=O?e=O:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function u(e,t){return 0===t.length&&t.ended?0:t.objectMode?0===e?0:1:null===e||isNaN(e)?t.flowing&&t.buffer.length?t.buffer[0].length:t.length:0>=e?0:(e>t.highWaterMark&&(t.highWaterMark=a(e)),e>t.length?t.ended?t.length:(t.needReadable=!0,0):e)}function f(e,t){var r=null;return x.isBuffer(t)||"string"==typeof t||null===t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk")),
r}function h(e,t){if(!t.ended){if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,c(e)}}function c(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(P("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?A(l,e):l(e))}function l(e){P("emit readable"),e.emit("readable"),w(e)}function d(e,t){t.readingMore||(t.readingMore=!0,A(p,e,t))}function p(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(P("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function g(e){return function(){var t=e._readableState;P("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&M(e,"data")&&(t.flowing=!0,w(e))}}function v(e){P("readable nexttick read 0"),e.read(0)}function y(e,t){t.resumeScheduled||(t.resumeScheduled=!0,A(b,e,t))}function b(e,t){t.reading||(P("resume read 0"),e.read(0)),t.resumeScheduled=!1,e.emit("resume"),w(e),t.flowing&&!t.reading&&e.read(0)}function w(e){var t=e._readableState;if(P("flow",t.flowing),t.flowing)do var r=e.read();while(null!==r&&t.flowing)}function m(e,t){var r,n=t.buffer,i=t.length,o=!!t.decoder,s=!!t.objectMode;if(0===n.length)return null;if(0===i)r=null;else if(s)r=n.shift();else if(!e||e>=i)r=o?n.join(""):1===n.length?n[0]:x.concat(n,i),n.length=0;else if(e<n[0].length){var a=n[0];r=a.slice(0,e),n[0]=a.slice(e)}else if(e===n[0].length)r=n.shift();else{r=o?"":new x(e);for(var u=0,f=0,h=n.length;h>f&&e>u;f++){var a=n[0],c=Math.min(e-u,a.length);o?r+=a.slice(0,c):a.copy(r,u,0,c),c<a.length?n[0]=a.slice(c):n.shift(),u+=c}}return r}function _(e){var t=e._readableState;if(t.length>0)throw new Error("endReadable called on non-empty stream");t.endEmitted||(t.ended=!0,A(E,t,e))}function E(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function R(e,t){for(var r=0,n=e.length;n>r;r++)t(e[r],r)}function S(e,t){for(var r=0,n=e.length;n>r;r++)if(e[r]===t)return r;return-1}t.exports=i;var A=e("process-nextick-args"),k=e("isarray"),x=e("buffer").Buffer;i.ReadableState=n;var L,M=(e("events"),function(e,t){return e.listeners(t).length});!function(){try{L=e("stream")}catch(t){}finally{L||(L=e("events").EventEmitter)}}();var x=e("buffer").Buffer,B=e("core-util-is");B.inherits=e("inherits");var T=e("util"),P=void 0;P=T&&T.debuglog?T.debuglog("stream"):function(){};var C;B.inherits(i,L);var j,j;i.prototype.push=function(e,t){var r=this._readableState;return r.objectMode||"string"!=typeof e||(t=t||r.defaultEncoding,t!==r.encoding&&(e=new x(e,t),t="")),o(this,r,e,t,!1)},i.prototype.unshift=function(e){var t=this._readableState;return o(this,t,e,"",!0)},i.prototype.isPaused=function(){return this._readableState.flowing===!1},i.prototype.setEncoding=function(t){return C||(C=e("string_decoder/").StringDecoder),this._readableState.decoder=new C(t),this._readableState.encoding=t,this};var O=8388608;i.prototype.read=function(e){P("read",e);var t=this._readableState,r=e;if(("number"!=typeof e||e>0)&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return P("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?_(this):c(this),null;if(e=u(e,t),0===e&&t.ended)return 0===t.length&&_(this),null;var n=t.needReadable;P("need readable",n),(0===t.length||t.length-e<t.highWaterMark)&&(n=!0,P("length less than watermark",n)),(t.ended||t.reading)&&(n=!1,P("reading or ended",n)),n&&(P("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1),n&&!t.reading&&(e=u(r,t));var i;return i=e>0?m(e,t):null,null===i&&(t.needReadable=!0,e=0),t.length-=e,0!==t.length||t.ended||(t.needReadable=!0),r!==e&&t.ended&&0===t.length&&_(this),null!==i&&this.emit("data",i),i},i.prototype._read=function(e){this.emit("error",new Error("not implemented"))},i.prototype.pipe=function(e,t){function n(e){P("onunpipe"),e===c&&o()}function i(){P("onend"),e.end()}function o(){P("cleanup"),e.removeListener("close",u),e.removeListener("finish",f),e.removeListener("drain",v),e.removeListener("error",a),e.removeListener("unpipe",n),c.removeListener("end",i),c.removeListener("end",o),c.removeListener("data",s),y=!0,!l.awaitDrain||e._writableState&&!e._writableState.needDrain||v()}function s(t){P("ondata");var r=e.write(t);!1===r&&(1!==l.pipesCount||l.pipes[0]!==e||1!==c.listenerCount("data")||y||(P("false write response, pause",c._readableState.awaitDrain),c._readableState.awaitDrain++),c.pause())}function a(t){P("onerror",t),h(),e.removeListener("error",a),0===M(e,"error")&&e.emit("error",t)}function u(){e.removeListener("finish",f),h()}function f(){P("onfinish"),e.removeListener("close",u),h()}function h(){P("unpipe"),c.unpipe(e)}var c=this,l=this._readableState;switch(l.pipesCount){case 0:l.pipes=e;break;case 1:l.pipes=[l.pipes,e];break;default:l.pipes.push(e)}l.pipesCount+=1,P("pipe count=%d opts=%j",l.pipesCount,t);var d=(!t||t.end!==!1)&&e!==r.stdout&&e!==r.stderr,p=d?i:o;l.endEmitted?A(p):c.once("end",p),e.on("unpipe",n);var v=g(c);e.on("drain",v);var y=!1;return c.on("data",s),e._events&&e._events.error?k(e._events.error)?e._events.error.unshift(a):e._events.error=[a,e._events.error]:e.on("error",a),e.once("close",u),e.once("finish",f),e.emit("pipe",c),l.flowing||(P("pipe resume"),c.resume()),e},i.prototype.unpipe=function(e){var t=this._readableState;if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this),this);if(!e){var r=t.pipes,n=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;n>i;i++)r[i].emit("unpipe",this);return this}var o=S(t.pipes,e);return-1===o?this:(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this),this)},i.prototype.on=function(e,t){var r=L.prototype.on.call(this,e,t);if("data"===e&&!1!==this._readableState.flowing&&this.resume(),"readable"===e&&!this._readableState.endEmitted){var n=this._readableState;n.readableListening||(n.readableListening=!0,n.emittedReadable=!1,n.needReadable=!0,n.reading?n.length&&c(this,n):A(v,this))}return r},i.prototype.addListener=i.prototype.on,i.prototype.resume=function(){var e=this._readableState;return e.flowing||(P("resume"),e.flowing=!0,y(this,e)),this},i.prototype.pause=function(){return P("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(P("pause"),this._readableState.flowing=!1,this.emit("pause")),this},i.prototype.wrap=function(e){var t=this._readableState,r=!1,n=this;e.on("end",function(){if(P("wrapped end"),t.decoder&&!t.ended){var e=t.decoder.end();e&&e.length&&n.push(e)}n.push(null)}),e.on("data",function(i){if(P("wrapped data"),t.decoder&&(i=t.decoder.write(i)),(!t.objectMode||null!==i&&void 0!==i)&&(t.objectMode||i&&i.length)){var o=n.push(i);o||(r=!0,e.pause())}});for(var i in e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));var o=["error","close","destroy","pause","resume"];return R(o,function(t){e.on(t,n.emit.bind(n,t))}),n._read=function(t){P("wrapped _read",t),r&&(r=!1,e.resume())},n},i._fromList=m}).call(this,e("_process"))},{"./_stream_duplex":13,_process:8,buffer:2,"core-util-is":18,events:6,inherits:12,isarray:19,"process-nextick-args":20,"string_decoder/":21,util:1}],16:[function(e,t,r){"use strict";function n(e){this.afterTransform=function(t,r){return i(e,t,r)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function i(e,t,r){var n=e._transformState;n.transforming=!1;var i=n.writecb;if(!i)return e.emit("error",new Error("no writecb in Transform class"));n.writechunk=null,n.writecb=null,null!==r&&void 0!==r&&e.push(r),i(t);var o=e._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&e._read(o.highWaterMark)}function o(e){if(!(this instanceof o))return new o(e);a.call(this,e),this._transformState=new n(this);var t=this;this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.once("prefinish",function(){"function"==typeof this._flush?this._flush(function(e){s(t,e)}):s(t)})}function s(e,t){if(t)return e.emit("error",t);var r=e._writableState,n=e._transformState;if(r.length)throw new Error("calling transform done when ws.length != 0");if(n.transforming)throw new Error("calling transform done when still transforming");return e.push(null)}t.exports=o;var a=e("./_stream_duplex"),u=e("core-util-is");u.inherits=e("inherits"),u.inherits(o,a),o.prototype.push=function(e,t){return this._transformState.needTransform=!1,a.prototype.push.call(this,e,t)},o.prototype._transform=function(e,t,r){throw new Error("not implemented")},o.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},o.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0}},{"./_stream_duplex":13,"core-util-is":18,inherits:12}],17:[function(e,t,r){(function(r){"use strict";function n(){}function i(e,t,r){this.chunk=e,this.encoding=t,this.callback=r,this.next=null}function o(t,r){M=M||e("./_stream_duplex"),t=t||{},this.objectMode=!!t.objectMode,r instanceof M&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var n=t.highWaterMark,i=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:i,this.highWaterMark=~~this.highWaterMark,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1;var o=t.decodeStrings===!1;this.decodeStrings=!o,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){p(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new E(this),this.corkedRequestsFree.next=new E(this)}function s(t){return M=M||e("./_stream_duplex"),this instanceof s||this instanceof M?(this._writableState=new o(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev)),void x.call(this)):new s(t)}function a(e,t){var r=new Error("write after end");e.emit("error",r),R(t,r)}function u(e,t,r,n){var i=!0;if(!A.isBuffer(r)&&"string"!=typeof r&&null!==r&&void 0!==r&&!t.objectMode){var o=new TypeError("Invalid non-string/buffer chunk");e.emit("error",o),R(n,o),i=!1}return i}function f(e,t,r){return e.objectMode||e.decodeStrings===!1||"string"!=typeof t||(t=new A(t,r)),t}function h(e,t,r,n,o){r=f(t,r,n),A.isBuffer(r)&&(n="buffer");var s=t.objectMode?1:r.length;t.length+=s;var a=t.length<t.highWaterMark;if(a||(t.needDrain=!0),t.writing||t.corked){var u=t.lastBufferedRequest;t.lastBufferedRequest=new i(r,n,o),u?u.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else c(e,t,!1,s,r,n,o);return a}function c(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function l(e,t,r,n,i){--t.pendingcb,r?R(i,n):i(n),e._writableState.errorEmitted=!0,e.emit("error",n)}function d(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function p(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(d(r),t)l(e,r,n,t,i);else{var o=b(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||y(e,r),n?S(g,e,r,o,i):g(e,r,o,i)}}function g(e,t,r,n){r||v(e,t),t.pendingcb--,n(),m(e,t)}function v(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function y(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var s=0;r;)i[s]=r,r=r.next,s+=1;c(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,t.corkedRequestsFree=o.next,o.next=null}else{for(;r;){var a=r.chunk,u=r.encoding,f=r.callback,h=t.objectMode?1:a.length;if(c(e,t,!1,h,a,u,f),r=r.next,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequestCount=0,t.bufferedRequest=r,t.bufferProcessing=!1}function b(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function w(e,t){t.prefinished||(t.prefinished=!0,e.emit("prefinish"))}function m(e,t){var r=b(t);return r&&(0===t.pendingcb?(w(e,t),t.finished=!0,e.emit("finish")):w(e,t)),r}function _(e,t,r){t.ending=!0,m(e,t),r&&(t.finished?R(r):e.once("finish",r)),t.ended=!0,e.writable=!1}function E(e){var t=this;this.next=null,this.entry=null,this.finish=function(r){var n=t.entry;for(t.entry=null;n;){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}}t.exports=s;var R=e("process-nextick-args"),S=!r.browser&&["v0.10","v0.9."].indexOf(r.version.slice(0,5))>-1?setImmediate:R,A=e("buffer").Buffer;s.WritableState=o;var k=e("core-util-is");k.inherits=e("inherits");var x,L={deprecate:e("util-deprecate")};!function(){try{x=e("stream")}catch(t){}finally{x||(x=e("events").EventEmitter)}}();var A=e("buffer").Buffer;k.inherits(s,x);var M;o.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(o.prototype,"buffer",{get:L.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.")})}catch(e){}}();var M;s.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe. Not readable."))},s.prototype.write=function(e,t,r){var i=this._writableState,o=!1;return"function"==typeof t&&(r=t,t=null),A.isBuffer(e)?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof r&&(r=n),i.ended?a(this,r):u(this,i,e,r)&&(i.pendingcb++,o=h(this,i,e,t,r)),o},s.prototype.cork=function(){var e=this._writableState;e.corked++},s.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||y(this,e))},s.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);this._writableState.defaultEncoding=e},s.prototype._write=function(e,t,r){r(new Error("not implemented"))},s.prototype._writev=null,s.prototype.end=function(e,t,r){var n=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!==e&&void 0!==e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||_(this,n,r)}}).call(this,e("_process"))},{"./_stream_duplex":13,_process:8,buffer:2,"core-util-is":18,events:6,inherits:12,"process-nextick-args":20,"util-deprecate":22}],18:[function(e,t,r){(function(e){function t(e){return Array.isArray?Array.isArray(e):"[object Array]"===v(e)}function n(e){return"boolean"==typeof e}function i(e){return null===e}function o(e){return null==e}function s(e){return"number"==typeof e}function a(e){return"string"==typeof e}function u(e){return"symbol"==typeof e}function f(e){return void 0===e}function h(e){return"[object RegExp]"===v(e)}function c(e){return"object"==typeof e&&null!==e}function l(e){return"[object Date]"===v(e)}function d(e){return"[object Error]"===v(e)||e instanceof Error}function p(e){return"function"==typeof e}function g(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||"undefined"==typeof e}function v(e){return Object.prototype.toString.call(e)}r.isArray=t,r.isBoolean=n,r.isNull=i,r.isNullOrUndefined=o,r.isNumber=s,r.isString=a,r.isSymbol=u,r.isUndefined=f,r.isRegExp=h,r.isObject=c,r.isDate=l,r.isError=d,r.isFunction=p,r.isPrimitive=g,r.isBuffer=e.isBuffer}).call(this,{isBuffer:e("../../../../browserify/node_modules/insert-module-globals/node_modules/is-buffer/index.js")})},{"../../../../browserify/node_modules/insert-module-globals/node_modules/is-buffer/index.js":7}],19:[function(e,t,r){arguments[4][5][0].apply(r,arguments)},{dup:5}],20:[function(e,t,r){(function(e){"use strict";function r(t){for(var r=new Array(arguments.length-1),n=0;n<r.length;)r[n++]=arguments[n];e.nextTick(function(){t.apply(null,r)})}!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports=r:t.exports=e.nextTick}).call(this,e("_process"))},{_process:8}],21:[function(e,t,r){function n(e){if(e&&!u(e))throw new Error("Unknown encoding: "+e)}function i(e){return e.toString(this.encoding)}function o(e){this.charReceived=e.length%2,this.charLength=this.charReceived?2:0}function s(e){this.charReceived=e.length%3,this.charLength=this.charReceived?3:0}var a=e("buffer").Buffer,u=a.isEncoding||function(e){switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}},f=r.StringDecoder=function(e){switch(this.encoding=(e||"utf8").toLowerCase().replace(/[-_]/,""),n(e),this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=o;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=s;break;default:return void(this.write=i)}this.charBuffer=new a(6),this.charReceived=0,this.charLength=0};f.prototype.write=function(e){for(var t="";this.charLength;){var r=e.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:e.length;if(e.copy(this.charBuffer,this.charReceived,0,r),this.charReceived+=r,this.charReceived<this.charLength)return"";e=e.slice(r,e.length),t=this.charBuffer.slice(0,this.charLength).toString(this.encoding);var n=t.charCodeAt(t.length-1);if(!(n>=55296&&56319>=n)){if(this.charReceived=this.charLength=0,0===e.length)return t;break}this.charLength+=this.surrogateSize,t=""}this.detectIncompleteChar(e);var i=e.length;this.charLength&&(e.copy(this.charBuffer,0,e.length-this.charReceived,i),i-=this.charReceived),t+=e.toString(this.encoding,0,i);var i=t.length-1,n=t.charCodeAt(i);if(n>=55296&&56319>=n){var o=this.surrogateSize;return this.charLength+=o,this.charReceived+=o,this.charBuffer.copy(this.charBuffer,o,0,o),e.copy(this.charBuffer,0,0,o),t.substring(0,i)}return t},f.prototype.detectIncompleteChar=function(e){for(var t=e.length>=3?3:e.length;t>0;t--){var r=e[e.length-t];if(1==t&&r>>5==6){this.charLength=2;break}if(2>=t&&r>>4==14){this.charLength=3;break}if(3>=t&&r>>3==30){this.charLength=4;break}}this.charReceived=t},f.prototype.end=function(e){var t="";if(e&&e.length&&(t=this.write(e)),this.charReceived){var r=this.charReceived,n=this.charBuffer,i=this.encoding;t+=n.slice(0,r).toString(i)}return t}},{buffer:2}],22:[function(e,t,r){(function(e){function r(e,t){function r(){if(!i){if(n("throwDeprecation"))throw new Error(t);n("traceDeprecation")?console.trace(t):console.warn(t),i=!0}return e.apply(this,arguments)}if(n("noDeprecation"))return e;var i=!1;return r}function n(t){try{if(!e.localStorage)return!1}catch(r){return!1}var n=e.localStorage[t];return null==n?!1:"true"===String(n).toLowerCase()}t.exports=r}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],23:[function(e,t,r){var n=function(){try{return e("stream")}catch(t){}}();r=t.exports=e("./lib/_stream_readable.js"),r.Stream=n||r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":13,"./lib/_stream_passthrough.js":14,"./lib/_stream_readable.js":15,"./lib/_stream_transform.js":16,"./lib/_stream_writable.js":17}],"/":[function(e,t,r){(function(r,n){function i(e,t){var n=this;if(!(n instanceof i))return new i(e,t);t||(t={}),o("new websocket: %s %o",e,t),t.allowHalfOpen=!1,null==t.highWaterMark&&(t.highWaterMark=1048576),a.Duplex.call(n,t),n.url=e,n.connected=!1,n.destroyed=!1,n._maxBufferedAmount=t.highWaterMark,n._chunk=null,n._cb=null,n._interval=null;try{"undefined"==typeof WebSocket?n._ws=new f(n.url,t):n._ws=new f(n.url)}catch(s){return void r.nextTick(function(){n._onError(s)})}n._ws.binaryType="arraybuffer",n._ws.onopen=function(){n._onOpen()},n._ws.onmessage=function(e){n._onMessage(e)},n._ws.onclose=function(){n._onClose()},n._ws.onerror=function(){n._onError(new Error("connection error to "+n.url))},n.on("finish",function(){n.connected?setTimeout(function(){n._destroy()},100):n.once("connect",function(){setTimeout(function(){n._destroy()},100)})})}t.exports=i;var o=e("debug")("simple-websocket"),s=e("inherits"),a=e("readable-stream"),u=e("ws"),f="undefined"!=typeof WebSocket?WebSocket:u;s(i,a.Duplex),i.WEBSOCKET_SUPPORT=!!f,i.prototype.send=function(e){var t=this,r=e.length||e.byteLength||e.size;t._ws.send(e),o("write: %d bytes",r)},i.prototype.destroy=function(e){var t=this;t._destroy(null,e)},i.prototype._destroy=function(e,t){var r=this;if(!r.destroyed){if(t&&r.once("close",t),o("destroy (error: %s)",e&&e.message),this.readable=this.writable=!1,r._readableState.ended||r.push(null),r._writableState.finished||r.end(),r.connected=!1,r.destroyed=!0,clearInterval(r._interval),r._interval=null,r._chunk=null,r._cb=null,r._ws){var n=r._ws,i=function(){n.onclose=null,r.emit("close")};if(n.readyState===f.CLOSED)i();else try{n.onclose=i,n.close()}catch(e){i()}n.onopen=null,n.onmessage=null,n.onerror=null}r._ws=null,e&&r.emit("error",e)}},i.prototype._read=function(){},i.prototype._write=function(e,t,r){var n=this;if(n.destroyed)return r(new Error("cannot write after socket is destroyed"));if(n.connected){try{n.send(e)}catch(i){return n._onError(i)}"function"!=typeof u&&n._ws.bufferedAmount>n._maxBufferedAmount?(o("start backpressure: bufferedAmount %d",n._ws.bufferedAmount),n._cb=r):r(null)}else o("write before connect"),n._chunk=e,n._cb=r},i.prototype._onMessage=function(e){var t=this;if(!t.destroyed){var r=e.data;o("read: %d bytes",r.byteLength||r.length),r instanceof ArrayBuffer&&(r=new n(r)),t.push(r)}},i.prototype._onOpen=function(){var e=this;if(!e.connected&&!e.destroyed){if(e.connected=!0,e._chunk){try{e.send(e._chunk)}catch(t){return e._onError(t)}e._chunk=null,o('sent chunk from "write before connect"');var r=e._cb;e._cb=null,r(null)}"function"!=typeof u&&(e._interval=setInterval(function(){if(e._cb&&e._ws&&!(e._ws.bufferedAmount>e._maxBufferedAmount)){o("ending backpressure: bufferedAmount %d",e._ws.bufferedAmount);var t=e._cb;e._cb=null,t(null)}},150),e._interval.unref&&e._interval.unref()),o("connect"),e.emit("connect")}},i.prototype._onClose=function(){var e=this;e.destroyed||(o("on close"),e._destroy())},i.prototype._onError=function(e){var t=this;t.destroyed||(o("error: %s",e.message||e),t._destroy(e))}}).call(this,e("_process"),e("buffer").Buffer)},{_process:8,buffer:2,debug:9,inherits:12,"readable-stream":23,ws:1}]},{},[])("/")});
