(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.RpcBuilder = f()}})(function(){var define,module,exports;return (function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r})()({1:[function(require,module,exports){
  function Mapper() {
    var sources = {};

    this.forEach = function (callback) {
      for (var key in sources) {
        var source = sources[key];

        for (var key2 in source)
          callback(source[key2]);
      };
    };

    this.get = function (id, source) {
      var ids = sources[source];
      if (ids == undefined)
        return undefined;

      return ids[id];
    };

    this.remove = function (id, source) {
      var ids = sources[source];
      if (ids == undefined)
        return;

      delete ids[id];

      // Check it's empty
      for (var i in ids) {
        return false
      }

      delete sources[source];
    };

    this.set = function (value, id, source) {
      if (value == undefined)
        return this.remove(id, source);

      var ids = sources[source];
      if (ids == undefined)
        sources[source] = ids = {};

      ids[id] = value;
    };
  };

  Mapper.prototype.pop = function (id, source) {
    var value = this.get(id, source);
    if (value == undefined)
      return undefined;

    this.remove(id, source);

    return value;
  };

  module.exports = Mapper;

  }, {}], 2: [function (require, module, exports) {
    /*
     * (C) Copyright 2014 Kurento (http://kurento.org/)
     *
     * Licensed under the Apache License, Version 2.0 (the "License");
     * you may not use this file except in compliance with the License.
     * You may obtain a copy of the License at
     *
     *   http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     *
     */

    var JsonRpcClient = require('./jsonrpcclient');

    exports.JsonRpcClient = JsonRpcClient;

  }, {
    "./jsonrpcclient": 3
  }], 3: [function (require, module, exports) {
    /*
     * (C) Copyright 2014 Kurento (http://kurento.org/)
     *
     * Licensed under the Apache License, Version 2.0 (the "License");
     * you may not use this file except in compliance with the License.
     * You may obtain a copy of the License at
     *
     *   http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     *
     */

    var RpcBuilder = require('../..');
    var WebSocketWithReconnection = require(
      './transports/webSocketWithReconnection');

    Date.now = Date.now || function () {
      return +new Date;
    };

    var PING_INTERVAL = 5000;

    var RECONNECTING = 'RECONNECTING';
    var CONNECTED = 'CONNECTED';
    var DISCONNECTED = 'DISCONNECTED';

    var Logger = console;

    /**
     *
     * heartbeat: interval in ms for each heartbeat message,
     * sendCloseMessage : true / false, before closing the connection, it sends a closeSession message
     * <pre>
     * ws : {
     * 	uri : URI to conntect to,
     *  useSockJS : true (use SockJS) / false (use WebSocket) by default,
     * 	onconnected : callback method to invoke when connection is successful,
     * 	ondisconnect : callback method to invoke when the connection is lost,
     * 	onreconnecting : callback method to invoke when the client is reconnecting,
     * 	onreconnected : callback method to invoke when the client succesfully reconnects,
     * 	onerror : callback method to invoke when there is an error
     * },
     * rpc : {
     * 	requestTimeout : timeout for a request,
     * 	sessionStatusChanged: callback method for changes in session status,
     * 	mediaRenegotiation: mediaRenegotiation
     * }
     * </pre>
     */
    function JsonRpcClient(configuration) {

      var self = this;

      var wsConfig = configuration.ws;

      var notReconnectIfNumLessThan = -1;

      var pingNextNum = 0;
      var enabledPings = true;
      var pingPongStarted = false;
      var pingInterval;

      var status = DISCONNECTED;

      var onreconnecting = wsConfig.onreconnecting;
      var onreconnected = wsConfig.onreconnected;
      var onconnected = wsConfig.onconnected;
      var onerror = wsConfig.onerror;

      configuration.rpc.pull = function (params, request) {
        request.reply(null, "push");
      }

      wsConfig.onreconnecting = function () {
        Logger.debug("--------- ONRECONNECTING -----------");
        if (status === RECONNECTING) {
          Logger.error(
            "Websocket already in RECONNECTING state when receiving a new ONRECONNECTING message. Ignoring it"
          );
          return;
        }

        status = RECONNECTING;
        if (onreconnecting) {
          onreconnecting();
        }
      }

      wsConfig.onreconnected = function () {
        Logger.debug("--------- ONRECONNECTED -----------");
        if (status === CONNECTED) {
          Logger.error(
            "Websocket already in CONNECTED state when receiving a new ONRECONNECTED message. Ignoring it"
          );
          return;
        }
        status = CONNECTED;

        enabledPings = true;
        updateNotReconnectIfLessThan();
        usePing();

        if (onreconnected) {
          onreconnected();
        }
      }

      wsConfig.onconnected = function () {
        Logger.debug("--------- ONCONNECTED -----------");
        if (status === CONNECTED) {
          Logger.error(
            "Websocket already in CONNECTED state when receiving a new ONCONNECTED message. Ignoring it"
          );
          return;
        }
        status = CONNECTED;

        enabledPings = true;
        usePing();

        if (onconnected) {
          onconnected();
        }
      }

      wsConfig.onerror = function (error) {
        Logger.debug("--------- ONERROR -----------");

        status = DISCONNECTED;

        if (onerror) {
          onerror(error);
        }
      }

      var ws = new WebSocketWithReconnection(wsConfig);

      Logger.debug('Connecting websocket to URI: ' + wsConfig.uri);

      var rpcBuilderOptions = {
        request_timeout: configuration.rpc.requestTimeout,
        ping_request_timeout: configuration.rpc.heartbeatRequestTimeout
      };

      var rpc = new RpcBuilder(RpcBuilder.packers.JsonRPC, rpcBuilderOptions, ws,
        function (request) {

          try {
            var func = configuration.rpc[request.method];

            if (func === undefined) {
              Logger.error("Method " + request.method +
                " not registered in client");
            } else {
              func(request.params, request);
            }
          } catch (err) {
            Logger.error('Exception processing request: ' + JSON.stringify(
              request));
            Logger.error(err);
          }
        });

      this.send = function (method, params, callback) {
        var requestTime = Date.now();

        rpc.encode(method, params, function (error, result) {
          if (error) {
            try {
              Logger.error("ERROR:" + error.message + " in Request: method:" +
                method + " params:" + JSON.stringify(params) + " request:" +
                error.request);
              if (error.data) {
                Logger.error("ERROR DATA:" + JSON.stringify(error.data));
              }
            } catch (e) {}
            error.requestTime = requestTime;
          }
          if (callback) {
            callback(error, result);
          }
        });
      }

      function updateNotReconnectIfLessThan() {
        Logger.debug("notReconnectIfNumLessThan = " + pingNextNum + ' (old=' +
          notReconnectIfNumLessThan + ')');
        notReconnectIfNumLessThan = pingNextNum;
      }

      function sendPing() {
        if (enabledPings) {
          var params = null;
          if (pingNextNum == 0 || pingNextNum == notReconnectIfNumLessThan) {
            params = {
              interval: configuration.heartbeat || PING_INTERVAL
            };
          }
          pingNextNum++;

          self.send('ping', params, (function (pingNum) {
            return function (error, result) {
              if (error) {
                Logger.debug("Error in ping request #" + pingNum + " (" +
                  error.message + ")");
                if (pingNum > notReconnectIfNumLessThan) {
                  enabledPings = false;
                  updateNotReconnectIfLessThan();
                  Logger.debug("Server did not respond to ping message #" +
                    pingNum + ". Reconnecting... ");
                  ws.reconnectWs();
                }
              }
            }
          })(pingNextNum));
        } else {
          Logger.debug("Trying to send ping, but ping is not enabled");
        }
      }

      /*
       * If configuration.hearbeat has any value, the ping-pong will work with the interval
       * of configuration.hearbeat
       */
      function usePing() {
        if (!pingPongStarted) {
          Logger.debug("Starting ping (if configured)")
          pingPongStarted = true;

          if (configuration.heartbeat != undefined) {
            pingInterval = setInterval(sendPing, configuration.heartbeat);
            sendPing();
          }
        }
      }

      this.close = function () {
        Logger.debug("Closing jsonRpcClient explicitly by client");

        if (pingInterval != undefined) {
          Logger.debug("Clearing ping interval");
          clearInterval(pingInterval);
        }
        pingPongStarted = false;
        enabledPings = false;

        if (configuration.sendCloseMessage) {
          Logger.debug("Sending close message")
          this.send('closeSession', null, function (error, result) {
            if (error) {
              Logger.error("Error sending close message: " + JSON.stringify(
                error));
            }
            ws.close();
          });
        } else {
          ws.close();
        }
      }

      // This method is only for testing
      this.forceClose = function (millis) {
        ws.forceClose(millis);
      }

      this.reconnect = function () {
        ws.reconnectWs();
      }
    }

    module.exports = JsonRpcClient;

  }, {
    "../..": 6,
    "./transports/webSocketWithReconnection": 5
  }], 4: [function (require, module, exports) {
    /*
     * (C) Copyright 2014 Kurento (http://kurento.org/)
     *
     * Licensed under the Apache License, Version 2.0 (the "License");
     * you may not use this file except in compliance with the License.
     * You may obtain a copy of the License at
     *
     *   http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     *
     */

    var WebSocketWithReconnection = require('./webSocketWithReconnection');

    exports.WebSocketWithReconnection = WebSocketWithReconnection;

  }, {
    "./webSocketWithReconnection": 5
  }], 5: [function (require, module, exports) {
    (function (global) {
      /*
       * (C) Copyright 2013-2015 Kurento (http://kurento.org/)
       *
       * Licensed under the Apache License, Version 2.0 (the "License");
       * you may not use this file except in compliance with the License.
       * You may obtain a copy of the License at
       *
       *   http://www.apache.org/licenses/LICENSE-2.0
       *
       * Unless required by applicable law or agreed to in writing, software
       * distributed under the License is distributed on an "AS IS" BASIS,
       * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
       * See the License for the specific language governing permissions and
       * limitations under the License.
       */

      "use strict";

      var BrowserWebSocket = global.WebSocket || global.MozWebSocket;

      var Logger = console;

      /**
       * Get either the `WebSocket` or `MozWebSocket` globals
       * in the browser or try to resolve WebSocket-compatible
       * interface exposed by `ws` for Node-like environment.
       */

      var WebSocket = BrowserWebSocket;
      if (!WebSocket && typeof window === 'undefined') {
        try {
          WebSocket = require('ws');
        } catch (e) {}
      }

      //var SockJS = require('sockjs-client');

      var MAX_RETRIES = 2000; // Forever...
      var RETRY_TIME_MS = 20000; // FIXME: Implement exponential wait times...

      var CONNECTING = 0;
      var OPEN = 1;
      var CLOSING = 2;
      var CLOSED = 3;

      /*
      config = {
          uri : wsUri,
          useSockJS : true (use SockJS) / false (use WebSocket) by default,
          onconnected : callback method to invoke when connection is successful,
          ondisconnect : callback method to invoke when the connection is lost,
          onreconnecting : callback method to invoke when the client is reconnecting,
          onreconnected : callback method to invoke when the client succesfully reconnects,
        };
      */
      function WebSocketWithReconnection(config) {

        var closing = false;
        var registerMessageHandler;
        var wsUri = config.uri;
        var useSockJS = config.useSockJS;
        var reconnecting = false;

        var forcingDisconnection = false;

        var ws;

        if (useSockJS) {
          ws = new SockJS(wsUri);
        } else {
          ws = new WebSocket(wsUri);
        }

        ws.onopen = function () {
          logConnected(ws, wsUri);
          if (config.onconnected) {
            config.onconnected();
          }
        };

        ws.onerror = function (error) {
          Logger.error("Could not connect to " + wsUri +
            " (invoking onerror if defined)", error);
          if (config.onerror) {
            config.onerror(error);
          }
        };

        function logConnected(ws, wsUri) {
          try {
            Logger.debug("WebSocket connected to " + wsUri);
          } catch (e) {
            Logger.error(e);
          }
        }

        var reconnectionOnClose = function () {
          if (ws.readyState === CLOSED) {
            if (closing) {
              Logger.debug("Connection closed by user");
            } else {
              Logger.debug("Connection closed unexpectecly. Reconnecting...");
              reconnectToSameUri(MAX_RETRIES, 1);
            }
          } else {
            Logger.debug("Close callback from previous websocket. Ignoring it");
          }
        };

        ws.onclose = reconnectionOnClose;

        function reconnectToSameUri(maxRetries, numRetries) {
          Logger.debug("reconnectToSameUri (attempt #" + numRetries + ", max=" +
            maxRetries + ")");

          if (numRetries === 1) {
            if (reconnecting) {
              Logger.warn(
                "Trying to reconnectToNewUri when reconnecting... Ignoring this reconnection."
              )
              return;
            } else {
              reconnecting = true;
            }

            if (config.onreconnecting) {
              config.onreconnecting();
            }
          }

          if (forcingDisconnection) {
            reconnectToNewUri(maxRetries, numRetries, wsUri);

          } else {
            if (config.newWsUriOnReconnection) {
              config.newWsUriOnReconnection(function (error, newWsUri) {

                if (error) {
                  Logger.debug(error);
                  setTimeout(function () {
                    reconnectToSameUri(maxRetries, numRetries + 1);
                  }, RETRY_TIME_MS);
                } else {
                  reconnectToNewUri(maxRetries, numRetries, newWsUri);
                }
              })
            } else {
              reconnectToNewUri(maxRetries, numRetries, wsUri);
            }
          }
        }

        // TODO Test retries. How to force not connection?
        function reconnectToNewUri(maxRetries, numRetries, reconnectWsUri) {
          Logger.debug("Reconnection attempt #" + numRetries);

          ws.close();

          wsUri = reconnectWsUri || wsUri;

          var newWs;
          if (useSockJS) {
            newWs = new SockJS(wsUri);
          } else {
            newWs = new WebSocket(wsUri);
          }

          newWs.onopen = function () {
            Logger.debug("Reconnected after " + numRetries + " attempts...");
            logConnected(newWs, wsUri);
            reconnecting = false;
            registerMessageHandler();
            if (config.onreconnected()) {
              config.onreconnected();
            }

            newWs.onclose = reconnectionOnClose;
          };

          var onErrorOrClose = function (error) {
            Logger.warn("Reconnection error: ", error);

            if (numRetries === maxRetries) {
              if (config.ondisconnect) {
                config.ondisconnect();
              }
            } else {
              setTimeout(function () {
                reconnectToSameUri(maxRetries, numRetries + 1);
              }, RETRY_TIME_MS);
            }
          };

          newWs.onerror = onErrorOrClose;

          ws = newWs;
        }

        this.close = function () {
          closing = true;
          ws.close();
        };

        // This method is only for testing
        this.forceClose = function (millis) {
          Logger.debug("Testing: Force WebSocket close");

          if (millis) {
            Logger.debug("Testing: Change wsUri for " + millis +
              " millis to simulate net failure");
            var goodWsUri = wsUri;
            wsUri = "wss://21.234.12.34.4:443/";

            forcingDisconnection = true;

            setTimeout(function () {
              Logger.debug("Testing: Recover good wsUri " + goodWsUri);
              wsUri = goodWsUri;

              forcingDisconnection = false;

            }, millis);
          }

          ws.close();
        };

        this.reconnectWs = function () {
          Logger.debug("reconnectWs");
          reconnectToSameUri(MAX_RETRIES, 1, wsUri);
        };

        this.send = function (message) {
          // Websocket connection is open and ready to communicate.
          if (ws.readyState === 1) {
            ws.send(message);
          }
        };

        this.addEventListener = function (type, callback) {
          registerMessageHandler = function () {
            ws.addEventListener(type, callback);
          };

          registerMessageHandler();
        };
      }

      module.exports = WebSocketWithReconnection;

    }).call(this, typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})

  }, {
    "ws": undefined
  }], 6: [function (require, module, exports) {
    /*
     * (C) Copyright 2014 Kurento (http://kurento.org/)
     *
     * Licensed under the Apache License, Version 2.0 (the "License");
     * you may not use this file except in compliance with the License.
     * You may obtain a copy of the License at
     *
     *   http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     *
     */

    var defineProperty_IE8 = false
    if (Object.defineProperty) {
      try {
        Object.defineProperty({}, "x", {});
      } catch (e) {
        defineProperty_IE8 = true
      }
    }

    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function/bind
    if (!Function.prototype.bind) {
      Function.prototype.bind = function (oThis) {
        if (typeof this !== 'function') {
          // closest thing possible to the ECMAScript 5
          // internal IsCallable function
          throw new TypeError(
            'Function.prototype.bind - what is trying to be bound is not callable'
          );
        }

        var aArgs = Array.prototype.slice.call(arguments, 1),
          fToBind = this,
          fNOP = function () {},
          fBound = function () {
            return fToBind.apply(this instanceof fNOP && oThis ?
              this :
              oThis,
              aArgs.concat(Array.prototype.slice.call(arguments)));
          };

        fNOP.prototype = this.prototype;
        fBound.prototype = new fNOP();

        return fBound;
      };
    }

    var EventEmitter = require('events').EventEmitter;

    var inherits = require('inherits');

    var packers = require('./packers');
    var Mapper = require('./Mapper');

    var BASE_TIMEOUT = 5000;

    function unifyResponseMethods(responseMethods) {
      if (!responseMethods) return {};

      for (var key in responseMethods) {
        var value = responseMethods[key];

        if (typeof value == 'string')
          responseMethods[key] = {
            response: value
          }
      };

      return responseMethods;
    };

    function unifyTransport(transport) {
      if (!transport) return;

      // Transport as a function
      if (transport instanceof Function)
        return {
          send: transport
        };

      // WebSocket & DataChannel
      if (transport.send instanceof Function)
        return transport;

      // Message API (Inter-window & WebWorker)
      if (transport.postMessage instanceof Function) {
        transport.send = transport.postMessage;
        return transport;
      }

      // Stream API
      if (transport.write instanceof Function) {
        transport.send = transport.write;
        return transport;
      }

      // Transports that only can receive messages, but not send
      if (transport.onmessage !== undefined) return;
      if (transport.pause instanceof Function) return;

      throw new SyntaxError("Transport is not a function nor a valid object");
    };

    /**
     * Representation of a RPC notification
     *
     * @class
     *
     * @constructor
     *
     * @param {String} method -method of the notification
     * @param params - parameters of the notification
     */
    function RpcNotification(method, params) {
      if (defineProperty_IE8) {
        this.method = method
        this.params = params
      } else {
        Object.defineProperty(this, 'method', {
          value: method,
          enumerable: true
        });
        Object.defineProperty(this, 'params', {
          value: params,
          enumerable: true
        });
      }
    };

    /**
     * @class
     *
     * @constructor
     *
     * @param {object} packer
     *
     * @param {object} [options]
     *
     * @param {object} [transport]
     *
     * @param {Function} [onRequest]
     */
    function RpcBuilder(packer, options, transport, onRequest) {
      var self = this;

      if (!packer)
        throw new SyntaxError('Packer is not defined');

      if (!packer.pack || !packer.unpack)
        throw new SyntaxError('Packer is invalid');

      var responseMethods = unifyResponseMethods(packer.responseMethods);

      if (options instanceof Function) {
        if (transport != undefined)
          throw new SyntaxError("There can't be parameters after onRequest");

        onRequest = options;
        transport = undefined;
        options = undefined;
      };

      if (options && options.send instanceof Function) {
        if (transport && !(transport instanceof Function))
          throw new SyntaxError("Only a function can be after transport");

        onRequest = transport;
        transport = options;
        options = undefined;
      };

      if (transport instanceof Function) {
        if (onRequest != undefined)
          throw new SyntaxError("There can't be parameters after onRequest");

        onRequest = transport;
        transport = undefined;
      };

      if (transport && transport.send instanceof Function)
        if (onRequest && !(onRequest instanceof Function))
          throw new SyntaxError("Only a function can be after transport");

      options = options || {};

      EventEmitter.call(this);

      if (onRequest)
        this.on('request', onRequest);

      if (defineProperty_IE8)
        this.peerID = options.peerID
      else
        Object.defineProperty(this, 'peerID', {
          value: options.peerID
        });

      var max_retries = options.max_retries || 0;

      function transportMessage(event) {
        self.decode(event.data || event.toString());
      };

      this.getTransport = function () {
        return transport;
      }
      this.setTransport = function (value) {
        // Remove listener from old transport
        if (transport) {
          // W3C transports
          if (transport.removeEventListener)
            transport.removeEventListener('message', transportMessage);

          // Node.js Streams API
          else if (transport.removeListener)
            transport.removeListener('data', transportMessage);
        };

        // Set listener on new transport
        if (value) {
          // W3C transports
          if (value.addEventListener)
            value.addEventListener('message', transportMessage);

          // Node.js Streams API
          else if (value.addListener)
            value.addListener('data', transportMessage);
        };

        transport = unifyTransport(value);
      }

      if (!defineProperty_IE8)
        Object.defineProperty(this, 'transport', {
          get: this.getTransport.bind(this),
          set: this.setTransport.bind(this)
        })

      this.setTransport(transport);

      var request_timeout = options.request_timeout || BASE_TIMEOUT;
      var ping_request_timeout = options.ping_request_timeout || request_timeout;
      var response_timeout = options.response_timeout || BASE_TIMEOUT;
      var duplicates_timeout = options.duplicates_timeout || BASE_TIMEOUT;

      var requestID = 0;

      var requests = new Mapper();
      var responses = new Mapper();
      var processedResponses = new Mapper();

      var message2Key = {};

      /**
       * Store the response to prevent to process duplicate request later
       */
      function storeResponse(message, id, dest) {
        var response = {
          message: message,
          /** Timeout to auto-clean old responses */
          timeout: setTimeout(function () {
              responses.remove(id, dest);
            },
            response_timeout)
        };

        responses.set(response, id, dest);
      };

      /**
       * Store the response to ignore duplicated messages later
       */
      function storeProcessedResponse(ack, from) {
        var timeout = setTimeout(function () {
            processedResponses.remove(ack, from);
          },
          duplicates_timeout);

        processedResponses.set(timeout, ack, from);
      };

      /**
       * Representation of a RPC request
       *
       * @class
       * @extends RpcNotification
       *
       * @constructor
       *
       * @param {String} method -method of the notification
       * @param params - parameters of the notification
       * @param {Integer} id - identifier of the request
       * @param [from] - source of the notification
       */
      function RpcRequest(method, params, id, from, transport) {
        RpcNotification.call(this, method, params);

        this.getTransport = function () {
          return transport;
        }
        this.setTransport = function (value) {
          transport = unifyTransport(value);
        }

        if (!defineProperty_IE8)
          Object.defineProperty(this, 'transport', {
            get: this.getTransport.bind(this),
            set: this.setTransport.bind(this)
          })

        var response = responses.get(id, from);

        /**
         * @constant {Boolean} duplicated
         */
        if (!(transport || self.getTransport())) {
          if (defineProperty_IE8)
            this.duplicated = Boolean(response)
          else
            Object.defineProperty(this, 'duplicated', {
              value: Boolean(response)
            });
        }

        var responseMethod = responseMethods[method];

        this.pack = packer.pack.bind(packer, this, id)

        /**
         * Generate a response to this request
         *
         * @param {Error} [error]
         * @param {*} [result]
         *
         * @returns {string}
         */
        this.reply = function (error, result, transport) {
          // Fix optional parameters
          if (error instanceof Function || error && error
            .send instanceof Function) {
            if (result != undefined)
              throw new SyntaxError("There can't be parameters after callback");

            transport = error;
            result = null;
            error = undefined;
          } else if (result instanceof Function ||
            result && result.send instanceof Function) {
            if (transport != undefined)
              throw new SyntaxError("There can't be parameters after callback");

            transport = result;
            result = null;
          };

          transport = unifyTransport(transport);

          // Duplicated request, remove old response timeout
          if (response)
            clearTimeout(response.timeout);

          if (from != undefined) {
            if (error)
              error.dest = from;

            if (result)
              result.dest = from;
          };

          var message;

          // New request or overriden one, create new response with provided data
          if (error || result != undefined) {
            if (self.peerID != undefined) {
              if (error)
                error.from = self.peerID;
              else
                result.from = self.peerID;
            }

            // Protocol indicates that responses has own request methods
            if (responseMethod) {
              if (responseMethod.error == undefined && error)
                message = {
                  error: error
                };

              else {
                var method = error ?
                  responseMethod.error :
                  responseMethod.response;

                message = {
                  method: method,
                  params: error || result
                };
              }
            } else
              message = {
                error: error,
                result: result
              };

            message = packer.pack(message, id);
          }

          // Duplicate & not-overriden request, re-send old response
          else if (response)
            message = response.message;

          // New empty reply, response null value
          else
            message = packer.pack({
              result: null
            }, id);

          // Store the response to prevent to process a duplicated request later
          storeResponse(message, id, from);

          // Return the stored response so it can be directly send back
          transport = transport || this.getTransport() || self.getTransport();

          if (transport)
            return transport.send(message);

          return message;
        }
      };
      inherits(RpcRequest, RpcNotification);

      function cancel(message) {
        var key = message2Key[message];
        if (!key) return;

        delete message2Key[message];

        var request = requests.pop(key.id, key.dest);
        if (!request) return;

        clearTimeout(request.timeout);

        // Start duplicated responses timeout
        storeProcessedResponse(key.id, key.dest);
      };

      /**
       * Allow to cancel a request and don't wait for a response
       *
       * If `message` is not given, cancel all the request
       */
      this.cancel = function (message) {
        if (message) return cancel(message);

        for (var message in message2Key)
          cancel(message);
      };

      this.close = function () {
        // Prevent to receive new messages
        var transport = this.getTransport();
        if (transport && transport.close)
          transport.close();

        // Request & processed responses
        this.cancel();

        processedResponses.forEach(clearTimeout);

        // Responses
        responses.forEach(function (response) {
          clearTimeout(response.timeout);
        });
      };

      /**
       * Generates and encode a JsonRPC 2.0 message
       *
       * @param {String} method -method of the notification
       * @param params - parameters of the notification
       * @param [dest] - destination of the notification
       * @param {object} [transport] - transport where to send the message
       * @param [callback] - function called when a response to this request is
       *   received. If not defined, a notification will be send instead
       *
       * @returns {string} A raw JsonRPC 2.0 request or notification string
       */
      this.encode = function (method, params, dest, transport, callback) {
        // Fix optional parameters
        if (params instanceof Function) {
          if (dest != undefined)
            throw new SyntaxError("There can't be parameters after callback");

          callback = params;
          transport = undefined;
          dest = undefined;
          params = undefined;
        } else if (dest instanceof Function) {
          if (transport != undefined)
            throw new SyntaxError("There can't be parameters after callback");

          callback = dest;
          transport = undefined;
          dest = undefined;
        } else if (transport instanceof Function) {
          if (callback != undefined)
            throw new SyntaxError("There can't be parameters after callback");

          callback = transport;
          transport = undefined;
        };

        if (self.peerID != undefined) {
          params = params || {};

          params.from = self.peerID;
        };

        if (dest != undefined) {
          params = params || {};

          params.dest = dest;
        };

        // Encode message
        var message = {
          method: method,
          params: params
        };

        if (callback) {
          var id = requestID++;
          var retried = 0;

          message = packer.pack(message, id);

          function dispatchCallback(error, result) {
            self.cancel(message);

            callback(error, result);
          };

          var request = {
            message: message,
            callback: dispatchCallback,
            responseMethods: responseMethods[method] || {}
          };

          var encode_transport = unifyTransport(transport);

          function sendRequest(transport) {
            var rt = (method === 'ping' ? ping_request_timeout : request_timeout);
            request.timeout = setTimeout(timeout, rt * Math.pow(2, retried++));
            message2Key[message] = {
              id: id,
              dest: dest
            };
            requests.set(request, id, dest);

            transport = transport || encode_transport || self.getTransport();
            if (transport)
              return transport.send(message);

            return message;
          };

          function retry(transport) {
            transport = unifyTransport(transport);

            console.warn(retried + ' retry for request message:', message);

            var timeout = processedResponses.pop(id, dest);
            clearTimeout(timeout);

            return sendRequest(transport);
          };

          function timeout() {
            if (retried < max_retries)
              return retry(transport);

            var error = new Error('Request has timed out');
            error.request = message;

            error.retry = retry;

            dispatchCallback(error)
          };

          return sendRequest(transport);
        };

        // Return the packed message
        message = packer.pack(message);

        transport = transport || this.getTransport();
        if (transport)
          return transport.send(message);

        return message;
      };

      /**
       * Decode and process a JsonRPC 2.0 message
       *
       * @param {string} message - string with the content of the message
       *
       * @returns {RpcNotification|RpcRequest|undefined} - the representation of the
       *   notification or the request. If a response was processed, it will return
       *   `undefined` to notify that it was processed
       *
       * @throws {TypeError} - Message is not defined
       */
      this.decode = function (message, transport) {
        if (!message)
          throw new TypeError("Message is not defined");

        try {
          message = packer.unpack(message);
        } catch (e) {
          // Ignore invalid messages
          return console.debug(e, message);
        };

        var id = message.id;
        var ack = message.ack;
        var method = message.method;
        var params = message.params || {};

        var from = params.from;
        var dest = params.dest;

        // Ignore messages send by us
        if (self.peerID != undefined && from == self.peerID) return;

        // Notification
        if (id == undefined && ack == undefined) {
          var notification = new RpcNotification(method, params);

          if (self.emit('request', notification)) return;
          return notification;
        };

        function processRequest() {
          // If we have a transport and it's a duplicated request, reply inmediatly
          transport = unifyTransport(transport) || self.getTransport();
          if (transport) {
            var response = responses.get(id, from);
            if (response)
              return transport.send(response.message);
          };

          var idAck = (id != undefined) ? id : ack;
          var request = new RpcRequest(method, params, idAck, from, transport);

          if (self.emit('request', request)) return;
          return request;
        };

        function processResponse(request, error, result) {
          request.callback(error, result);
        };

        function duplicatedResponse(timeout) {
          console.warn("Response already processed", message);

          // Update duplicated responses timeout
          clearTimeout(timeout);
          storeProcessedResponse(ack, from);
        };

        // Request, or response with own method
        if (method) {
          // Check if it's a response with own method
          if (dest == undefined || dest == self.peerID) {
            var request = requests.get(ack, from);
            if (request) {
              var responseMethods = request.responseMethods;

              if (method == responseMethods.error)
                return processResponse(request, params);

              if (method == responseMethods.response)
                return processResponse(request, null, params);

              return processRequest();
            }

            var processed = processedResponses.get(ack, from);
            if (processed)
              return duplicatedResponse(processed);
          }

          // Request
          return processRequest();
        };

        var error = message.error;
        var result = message.result;

        // Ignore responses not send to us
        if (error && error.dest && error.dest != self.peerID) return;
        if (result && result.dest && result.dest != self.peerID) return;

        // Response
        var request = requests.get(ack, from);
        if (!request) {
          var processed = processedResponses.get(ack, from);
          if (processed)
            return duplicatedResponse(processed);

          return console.warn("No callback was defined for this message",
            message);
        };

        // Process response
        processResponse(request, error, result);
      };
    };
    inherits(RpcBuilder, EventEmitter);

    RpcBuilder.RpcNotification = RpcNotification;

    module.exports = RpcBuilder;

    var clients = require('./clients');
    var transports = require('./clients/transports');

    RpcBuilder.clients = clients;
    RpcBuilder.clients.transports = transports;
    RpcBuilder.packers = packers;

  }, {
    "./Mapper": 1,
    "./clients": 2,
    "./clients/transports": 4,
    "./packers": 9,
    "events": 10,
    "inherits": 11
  }], 7: [function (require, module, exports) {
    /**
     * JsonRPC 2.0 packer
     */

    /**
     * Pack a JsonRPC 2.0 message
     *
     * @param {Object} message - object to be packaged. It requires to have all the
     *   fields needed by the JsonRPC 2.0 message that it's going to be generated
     *
     * @return {String} - the stringified JsonRPC 2.0 message
     */
    function pack(message, id) {
      var result = {
        jsonrpc: "2.0"
      };

      // Request
      if (message.method) {
        result.method = message.method;

        if (message.params)
          result.params = message.params;

        // Request is a notification
        if (id != undefined)
          result.id = id;
      }

      // Response
      else if (id != undefined) {
        if (message.error) {
          if (message.result !== undefined)
            throw new TypeError("Both result and error are defined");

          result.error = message.error;
        } else if (message.result !== undefined)
          result.result = message.result;
        else
          throw new TypeError("No result or error is defined");

        result.id = id;
      };

      return JSON.stringify(result);
    };

    /**
     * Unpack a JsonRPC 2.0 message
     *
     * @param {String} message - string with the content of the JsonRPC 2.0 message
     *
     * @throws {TypeError} - Invalid JsonRPC version
     *
     * @return {Object} - object filled with the JsonRPC 2.0 message content
     */
    function unpack(message) {
      var result = message;

      if (typeof message === 'string' || message instanceof String) {
        result = JSON.parse(message);
      }

      // Check if it's a valid message

      var version = result.jsonrpc;
      if (version !== '2.0')
        throw new TypeError("Invalid JsonRPC version '" + version + "': " +
          message);

      // Response
      if (result.method == undefined) {
        if (result.id == undefined)
          throw new TypeError("Invalid message: " + message);

        var result_defined = result.result !== undefined;
        var error_defined = result.error !== undefined;

        // Check only result or error is defined, not both or none
        if (result_defined && error_defined)
          throw new TypeError("Both result and error are defined: " + message);

        if (!result_defined && !error_defined)
          throw new TypeError("No result or error is defined: " + message);

        result.ack = result.id;
        delete result.id;
      }

      // Return unpacked message
      return result;
    };

    exports.pack = pack;
    exports.unpack = unpack;

  }, {}], 8: [function (require, module, exports) {
    function pack(message) {
      throw new TypeError("Not yet implemented");
    };

    function unpack(message) {
      throw new TypeError("Not yet implemented");
    };

    exports.pack = pack;
    exports.unpack = unpack;

  }, {}], 9: [function (require, module, exports) {
    var JsonRPC = require('./JsonRPC');
    var XmlRPC = require('./XmlRPC');

    exports.JsonRPC = JsonRPC;
    exports.XmlRPC = XmlRPC;

  }, {
    "./JsonRPC": 7,
    "./XmlRPC": 8
  }], 10: [function (require, module, exports) {
    // Copyright Joyent, Inc. and other Node contributors.
    //
    // Permission is hereby granted, free of charge, to any person obtaining a
    // copy of this software and associated documentation files (the
    // "Software"), to deal in the Software without restriction, including
    // without limitation the rights to use, copy, modify, merge, publish,
    // distribute, sublicense, and/or sell copies of the Software, and to permit
    // persons to whom the Software is furnished to do so, subject to the
    // following conditions:
    //
    // The above copyright notice and this permission notice shall be included
    // in all copies or substantial portions of the Software.
    //
    // THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
    // OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
    // MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
    // NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
    // DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
    // OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
    // USE OR OTHER DEALINGS IN THE SOFTWARE.

    var objectCreate = Object.create || objectCreatePolyfill
    var objectKeys = Object.keys || objectKeysPolyfill
    var bind = Function.prototype.bind || functionBindPolyfill

    function EventEmitter() {
      if (!this._events || !Object.prototype.hasOwnProperty.call(this, '_events')) {
        this._events = objectCreate(null);
        this._eventsCount = 0;
      }

      this._maxListeners = this._maxListeners || undefined;
    }
    module.exports = EventEmitter;

    // Backwards-compat with node 0.10.x
    EventEmitter.EventEmitter = EventEmitter;

    EventEmitter.prototype._events = undefined;
    EventEmitter.prototype._maxListeners = undefined;

    // By default EventEmitters will print a warning if more than 10 listeners are
    // added to it. This is a useful default which helps finding memory leaks.
    var defaultMaxListeners = 10;

    var hasDefineProperty;
    try {
      var o = {};
      if (Object.defineProperty) Object.defineProperty(o, 'x', {
        value: 0
      });
      hasDefineProperty = o.x === 0;
    } catch (err) {
      hasDefineProperty = false
    }
    if (hasDefineProperty) {
      Object.defineProperty(EventEmitter, 'defaultMaxListeners', {
        enumerable: true,
        get: function () {
          return defaultMaxListeners;
        },
        set: function (arg) {
          // check whether the input is a positive number (whose value is zero or
          // greater and not a NaN).
          if (typeof arg !== 'number' || arg < 0 || arg !== arg)
            throw new TypeError('"defaultMaxListeners" must be a positive number');
          defaultMaxListeners = arg;
        }
      });
    } else {
      EventEmitter.defaultMaxListeners = defaultMaxListeners;
    }

    // Obviously not all Emitters should be limited to 10. This function allows
    // that to be increased. Set to zero for unlimited.
    EventEmitter.prototype.setMaxListeners = function setMaxListeners(n) {
      if (typeof n !== 'number' || n < 0 || isNaN(n))
        throw new TypeError('"n" argument must be a positive number');
      this._maxListeners = n;
      return this;
    };

    function $getMaxListeners(that) {
      if (that._maxListeners === undefined)
        return EventEmitter.defaultMaxListeners;
      return that._maxListeners;
    }

    EventEmitter.prototype.getMaxListeners = function getMaxListeners() {
      return $getMaxListeners(this);
    };

    // These standalone emit* functions are used to optimize calling of event
    // handlers for fast cases because emit() itself often has a variable number of
    // arguments and can be deoptimized because of that. These functions always have
    // the same number of arguments and thus do not get deoptimized, so the code
    // inside them can execute faster.
    function emitNone(handler, isFn, self) {
      if (isFn)
        handler.call(self);
      else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          listeners[i].call(self);
      }
    }

    function emitOne(handler, isFn, self, arg1) {
      if (isFn)
        handler.call(self, arg1);
      else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          listeners[i].call(self, arg1);
      }
    }

    function emitTwo(handler, isFn, self, arg1, arg2) {
      if (isFn)
        handler.call(self, arg1, arg2);
      else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          listeners[i].call(self, arg1, arg2);
      }
    }

    function emitThree(handler, isFn, self, arg1, arg2, arg3) {
      if (isFn)
        handler.call(self, arg1, arg2, arg3);
      else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          listeners[i].call(self, arg1, arg2, arg3);
      }
    }

    function emitMany(handler, isFn, self, args) {
      if (isFn)
        handler.apply(self, args);
      else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i = 0; i < len; ++i)
          listeners[i].apply(self, args);
      }
    }

    EventEmitter.prototype.emit = function emit(type) {
      var er, handler, len, args, i, events;
      var doError = (type === 'error');

      events = this._events;
      if (events)
        doError = (doError && events.error == null);
      else if (!doError)
        return false;

      // If there is no 'error' event listener then throw.
      if (doError) {
        if (arguments.length > 1)
          er = arguments[1];
        if (er instanceof Error) {
          throw er; // Unhandled 'error' event
        } else {
          // At least give some kind of context to the user
          var err = new Error('Unhandled "error" event. (' + er + ')');
          err.context = er;
          throw err;
        }
        return false;
      }

      handler = events[type];

      if (!handler)
        return false;

      var isFn = typeof handler === 'function';
      len = arguments.length;
      switch (len) {
        // fast cases
        case 1:
          emitNone(handler, isFn, this);
          break;
        case 2:
          emitOne(handler, isFn, this, arguments[1]);
          break;
        case 3:
          emitTwo(handler, isFn, this, arguments[1], arguments[2]);
          break;
        case 4:
          emitThree(handler, isFn, this, arguments[1], arguments[2], arguments[3]);
          break;
          // slower
        default:
          args = new Array(len - 1);
          for (i = 1; i < len; i++)
            args[i - 1] = arguments[i];
          emitMany(handler, isFn, this, args);
      }

      return true;
    };

    function _addListener(target, type, listener, prepend) {
      var m;
      var events;
      var existing;

      if (typeof listener !== 'function')
        throw new TypeError('"listener" argument must be a function');

      events = target._events;
      if (!events) {
        events = target._events = objectCreate(null);
        target._eventsCount = 0;
      } else {
        // To avoid recursion in the case that type === "newListener"! Before
        // adding it to the listeners, first emit "newListener".
        if (events.newListener) {
          target.emit('newListener', type,
            listener.listener ? listener.listener : listener);

          // Re-assign `events` because a newListener handler could have caused the
          // this._events to be assigned to a new object
          events = target._events;
        }
        existing = events[type];
      }

      if (!existing) {
        // Optimize the case of one listener. Don't need the extra array object.
        existing = events[type] = listener;
        ++target._eventsCount;
      } else {
        if (typeof existing === 'function') {
          // Adding the second element, need to change to array.
          existing = events[type] =
            prepend ? [listener, existing] : [existing, listener];
        } else {
          // If we've already got an array, just append.
          if (prepend) {
            existing.unshift(listener);
          } else {
            existing.push(listener);
          }
        }

        // Check for listener leak
        if (!existing.warned) {
          m = $getMaxListeners(target);
          if (m && m > 0 && existing.length > m) {
            existing.warned = true;
            var w = new Error('Possible EventEmitter memory leak detected. ' +
              existing.length + ' "' + String(type) + '" listeners ' +
              'added. Use emitter.setMaxListeners() to ' +
              'increase limit.');
            w.name = 'MaxListenersExceededWarning';
            w.emitter = target;
            w.type = type;
            w.count = existing.length;
            if (typeof console === 'object' && console.warn) {
              console.warn('%s: %s', w.name, w.message);
            }
          }
        }
      }

      return target;
    }

    EventEmitter.prototype.addListener = function addListener(type, listener) {
      return _addListener(this, type, listener, false);
    };

    EventEmitter.prototype.on = EventEmitter.prototype.addListener;

    EventEmitter.prototype.prependListener =
      function prependListener(type, listener) {
        return _addListener(this, type, listener, true);
      };

    function onceWrapper() {
      if (!this.fired) {
        this.target.removeListener(this.type, this.wrapFn);
        this.fired = true;
        switch (arguments.length) {
          case 0:
            return this.listener.call(this.target);
          case 1:
            return this.listener.call(this.target, arguments[0]);
          case 2:
            return this.listener.call(this.target, arguments[0], arguments[1]);
          case 3:
            return this.listener.call(this.target, arguments[0], arguments[1],
              arguments[2]);
          default:
            var args = new Array(arguments.length);
            for (var i = 0; i < args.length; ++i)
              args[i] = arguments[i];
            this.listener.apply(this.target, args);
        }
      }
    }

    function _onceWrap(target, type, listener) {
      var state = {
        fired: false,
        wrapFn: undefined,
        target: target,
        type: type,
        listener: listener
      };
      var wrapped = bind.call(onceWrapper, state);
      wrapped.listener = listener;
      state.wrapFn = wrapped;
      return wrapped;
    }

    EventEmitter.prototype.once = function once(type, listener) {
      if (typeof listener !== 'function')
        throw new TypeError('"listener" argument must be a function');
      this.on(type, _onceWrap(this, type, listener));
      return this;
    };

    EventEmitter.prototype.prependOnceListener =
      function prependOnceListener(type, listener) {
        if (typeof listener !== 'function')
          throw new TypeError('"listener" argument must be a function');
        this.prependListener(type, _onceWrap(this, type, listener));
        return this;
      };

    // Emits a 'removeListener' event if and only if the listener was removed.
    EventEmitter.prototype.removeListener =
      function removeListener(type, listener) {
        var list, events, position, i, originalListener;

        if (typeof listener !== 'function')
          throw new TypeError('"listener" argument must be a function');

        events = this._events;
        if (!events)
          return this;

        list = events[type];
        if (!list)
          return this;

        if (list === listener || list.listener === listener) {
          if (--this._eventsCount === 0)
            this._events = objectCreate(null);
          else {
            delete events[type];
            if (events.removeListener)
              this.emit('removeListener', type, list.listener || listener);
          }
        } else if (typeof list !== 'function') {
          position = -1;

          for (i = list.length - 1; i >= 0; i--) {
            if (list[i] === listener || list[i].listener === listener) {
              originalListener = list[i].listener;
              position = i;
              break;
            }
          }

          if (position < 0)
            return this;

          if (position === 0)
            list.shift();
          else
            spliceOne(list, position);

          if (list.length === 1)
            events[type] = list[0];

          if (events.removeListener)
            this.emit('removeListener', type, originalListener || listener);
        }

        return this;
      };

    EventEmitter.prototype.removeAllListeners =
      function removeAllListeners(type) {
        var listeners, events, i;

        events = this._events;
        if (!events)
          return this;

        // not listening for removeListener, no need to emit
        if (!events.removeListener) {
          if (arguments.length === 0) {
            this._events = objectCreate(null);
            this._eventsCount = 0;
          } else if (events[type]) {
            if (--this._eventsCount === 0)
              this._events = objectCreate(null);
            else
              delete events[type];
          }
          return this;
        }

        // emit removeListener for all listeners on all events
        if (arguments.length === 0) {
          var keys = objectKeys(events);
          var key;
          for (i = 0; i < keys.length; ++i) {
            key = keys[i];
            if (key === 'removeListener') continue;
            this.removeAllListeners(key);
          }
          this.removeAllListeners('removeListener');
          this._events = objectCreate(null);
          this._eventsCount = 0;
          return this;
        }

        listeners = events[type];

        if (typeof listeners === 'function') {
          this.removeListener(type, listeners);
        } else if (listeners) {
          // LIFO order
          for (i = listeners.length - 1; i >= 0; i--) {
            this.removeListener(type, listeners[i]);
          }
        }

        return this;
      };

    function _listeners(target, type, unwrap) {
      var events = target._events;

      if (!events)
        return [];

      var evlistener = events[type];
      if (!evlistener)
        return [];

      if (typeof evlistener === 'function')
        return unwrap ? [evlistener.listener || evlistener] : [evlistener];

      return unwrap ? unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);
    }

    EventEmitter.prototype.listeners = function listeners(type) {
      return _listeners(this, type, true);
    };

    EventEmitter.prototype.rawListeners = function rawListeners(type) {
      return _listeners(this, type, false);
    };

    EventEmitter.listenerCount = function (emitter, type) {
      if (typeof emitter.listenerCount === 'function') {
        return emitter.listenerCount(type);
      } else {
        return listenerCount.call(emitter, type);
      }
    };

    EventEmitter.prototype.listenerCount = listenerCount;

    function listenerCount(type) {
      var events = this._events;

      if (events) {
        var evlistener = events[type];

        if (typeof evlistener === 'function') {
          return 1;
        } else if (evlistener) {
          return evlistener.length;
        }
      }

      return 0;
    }

    EventEmitter.prototype.eventNames = function eventNames() {
      return this._eventsCount > 0 ? Reflect.ownKeys(this._events) : [];
    };

    // About 1.5x faster than the two-arg version of Array#splice().
    function spliceOne(list, index) {
      for (var i = index, k = i + 1, n = list.length; k < n; i += 1, k += 1)
        list[i] = list[k];
      list.pop();
    }

    function arrayClone(arr, n) {
      var copy = new Array(n);
      for (var i = 0; i < n; ++i)
        copy[i] = arr[i];
      return copy;
    }

    function unwrapListeners(arr) {
      var ret = new Array(arr.length);
      for (var i = 0; i < ret.length; ++i) {
        ret[i] = arr[i].listener || arr[i];
      }
      return ret;
    }

    function objectCreatePolyfill(proto) {
      var F = function () {};
      F.prototype = proto;
      return new F;
    }

    function objectKeysPolyfill(obj) {
      var keys = [];
      for (var k in obj)
        if (Object.prototype.hasOwnProperty.call(obj, k)) {
          keys.push(k);
        }
      return k;
    }

    function functionBindPolyfill(context) {
      var fn = this;
      return function () {
        return fn.apply(context, arguments);
      };
    }

  }, {}], 11: [function (require, module, exports) {
    if (typeof Object.create === 'function') {
      // implementation from standard node.js 'util' module
      module.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
          ctor.super_ = superCtor
          ctor.prototype = Object.create(superCtor.prototype, {
            constructor: {
              value: ctor,
              enumerable: false,
              writable: true,
              configurable: true
            }
          })
        }
      };
    } else {
      // old school shim for old browsers
      module.exports = function inherits(ctor, superCtor) {
        if (superCtor) {
          ctor.super_ = superCtor
          var TempCtor = function () {}
          TempCtor.prototype = superCtor.prototype
          ctor.prototype = new TempCtor()
          ctor.prototype.constructor = ctor
        }
      }
    }

  }, {}]
  }, {}, [6])(6)
  });