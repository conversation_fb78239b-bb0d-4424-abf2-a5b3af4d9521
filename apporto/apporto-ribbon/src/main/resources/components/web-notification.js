!function t(n,o){"use strict";const e=n.Notification||window.Notification,i=o(e);return i.initWebNotificationFromContext=function(n){return t(n,o)},"function"==typeof define&&define.amd?define(function(){return i}):"object"==typeof module&&module.exports?module.exports=i:n.webNotification=i,i}(this,function(t){"use strict";let n=0;const o={};o.lib=t,o.allowRequest=!0,Object.defineProperty(o,"permissionGranted",{get:function(){let n;if(!(t&&"permission"in t))return!1;let o=!1;return"granted"===(n=t.permission)&&(o=!0),o}});const e=function(){},i=function(){return o.permissionGranted};return o.requestPermission=function(n){n&&"function"==typeof n&&(i()?n(!0):t?t.requestPermission(function(){n(i())}):n(!1))},o.showNotification=function(){const i=Array.prototype.slice.call(arguments,0);if(i.length>=1&&i.length<=3){const c=function(t){let n=e;t.length&&"function"==typeof t[t.length-1]&&(n=t.pop());let o=null,i=null;if(2===t.length)o=t[0],i=t[1];else if(1===t.length){const n=t.pop();"string"==typeof n?(o=n,i={}):(o="",i=n)}return{callback:n,title:o=o||"",options:i=i||{}}}(i),r=c.callback,s=c.title,l=c.options;o.requestPermission(function(o){o?function(o,e,i){let c=0;e.autoClose&&"number"==typeof e.autoClose&&(c=e.autoClose),e.icon||(e.icon="/favicon.ico");const r=function(t){e.onClick&&t&&(t.onclick=e.onClick);const n=function(){t.close()};c&&setTimeout(n,c),i(null,n)},s=e.serviceWorkerRegistration;if(s){delete e.serviceWorkerRegistration,e.tag||(n++,e.tag="webnotification-"+Date.now()+"-"+n);const t=e.tag;s.showNotification(o,e).then(function(){s.getNotifications({tag:t}).then(function(t){t&&t.length?r(t[0]):i(new Error("Unable to find notification."))}).catch(i)}).catch(i)}else{let n;try{n=new t(o,e)}catch(t){i(t)}n&&r(n)}}(s,l,r):r(new Error("Notifications are not enabled."),null)})}},o});
