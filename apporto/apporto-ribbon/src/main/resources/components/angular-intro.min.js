/*! angular-intro.js - v3.3.0 - 2017-06-16 */

!function(a,b){"function"==typeof window.define&&window.define.amd?window.define(["angular","intro.js"],b):"object"==typeof window.exports?window.module.exports=b(window.require("angular"),window.require("intro.js")):a.angularIntroJs=b(a.angular,a.introJs)}(this,function(a,b){var c={open:"open",closed:"closed"},d="angular-intro",e={},f=function(){function d(){this.intro=b()}return d.prototype.addListener=function(b,c){a.isFunction(c)&&(e[b]=c)},d.prototype.removeListener=function(a){delete e[a]},d.prototype.notifyListeners=function(b){for(var c in e)e.hasOwnProperty(c)&&a.isFunction(e[c])&&e[c](b)},d.prototype.setOptions=function(a){return this.intro.setOptions(a)},d.prototype.start=function(a){return"number"==typeof a?this.intro.start().goToStep(a):this.intro.start(),this.notifyListeners(c.open),this.intro},d.prototype.exit=function(){return this.notifyListeners(c.closed),this.intro.exit()},d.prototype.clear=function(d){return"undefined"!=typeof this.intro&&this.intro.exit(),this.intro=b(),this.notifyListeners(c.closed),a.isFunction(d)&&d(),this.intro},d.prototype.goToStepNumber=function(a){return this.intro.goToStepNumber(a)},d.prototype.addHints=function(){return this.intro.addHints()},d.prototype.showHint=function(a){return this.intro.showHint(a)},d.prototype.showHints=function(){return this.intro.showHints()},d.prototype.hideHint=function(a){return this.intro.hideHint(a)},d.prototype.hideHints=function(){return this.intro.hideHints()},d.prototype.removeHint=function(a){return this.intro.removeHint(a)},d.prototype.removeHints=function(){return this.intro.removeHints()},d.prototype.previous=function(){return this.notifyListeners(c.open),this.intro.previousStep()},d.prototype.next=function(){return this.notifyListeners(c.open),this.intro.nextStep()},d.prototype.refresh=function(){return this.intro.refresh()},d.prototype.onComplete=function(b){var d=this;return this.intro.oncomplete(function(){a.isFunction(b)&&b(),d.notifyListeners(c.closed)})},d.prototype.onExit=function(b){var d=this;return this.intro.onexit(function(){d.notifyListeners(c.closed),a.isFunction(b)&&b()})},d.prototype.onBeforeChange=function(b){return this.intro.onbeforechange(function(c){a.isFunction(b)&&b(c)})},d.prototype.onChange=function(b){return this.intro.onchange(function(c){a.isFunction(b)&&b(c)})},d.prototype.onAfterChange=function(b){return this.intro.onafterchange(function(c){a.isFunction(b)&&b(c)})},d.prototype.onHintClick=function(b){return this.intro.onhintclick(function(){a.isFunction(b)&&b()})},d.prototype.onHintClose=function(b){return this.intro.onhintclose(function(){a.isFunction(b)&&b()})},d.prototype.onHintsAdded=function(b){return this.intro.onhintclose(function(){a.isFunction(b)&&b()})},d}(),g=function(){function b(b,c){var d=this;this.restrict="A",this.scope={ngIntroMethod:"=",ngIntroExitMethod:"=?",ngIntroNextMethod:"=?",ngIntroPreviousMethod:"=?",ngIntroRefreshMethod:"=?",ngIntroOptions:"=",ngIntroOncomplete:"=",ngIntroOnexit:"=",ngIntroOnchange:"=",ngIntroOnbeforechange:"=",ngIntroOnafterchange:"=",ngIntroAutostart:"=",ngIntroAutorefresh:"=",ngIntroHintsMethod:"=?",ngIntroOnhintsadded:"=",ngIntroOnhintclick:"=?",ngIntroOnhintclose:"=?",ngIntroShowHint:"=?",ngIntroShowHints:"=?",ngIntroHideHint:"=?",ngIntroHideHints:"=?"},this.destroy=[],this.link=function(e,f,g){e.ngIntroOncomplete&&b.onComplete(e.ngIntroOncomplete),e.ngIntroOnexit&&b.onExit(e.ngIntroOnexit),e.ngIntroOnbeforechange&&b.onBeforeChange(e.ngIntroOnbeforechange),e.ngIntroOnchange&&b.onChange(e.ngIntroOnchange),e.ngIntroOnafterchange&&b.onAfterChange(e.ngIntroOnafterchange),e.ngIntroMethod=function(a){b.setOptions(e.ngIntroOptions),b.start(a)},e.ngIntroHintsMethod=function(a){b.setOptions(e.ngIntroOptions),b.start(a),e.ngIntroOnhintsadded&&b.onHintsAdded(e.ngIntroOnbeforechange),e.ngIntroOnhintclick&&b.onHintClick(e.ngIntroOnbeforechange),e.ngIntroOnhintclose&&b.onHintClick(e.ngIntroOnbeforechange),b.addHints()},e.ngIntroShowHint=function(a){b.showHint(a)},e.ngIntroShowHints=function(){b.showHints()},e.ngIntroHideHint=function(a){b.hideHint(a)},e.ngIntroHideHints=function(){b.hideHints()},e.ngIntroNextMethod=function(){b.next()},e.ngIntroPreviousMethod=function(){b.previous()},e.ngIntroExitMethod=function(c){b.exit(),a.isFunction(c)&&c()},e.ngIntroRefreshMethod=function(){b.refresh()};var h=e.$watch("ngIntroAutostart",function(){e.ngIntroAutostart&&c(function(){e.ngIntroMethod()}),h()});d.destroy.push(e.$on("$locationChangeStart",function(){b.exit()})),d.destroy.push(e.$on("$locationChangeSuccess",function(){b.exit()})),e.ngIntroAutorefresh&&d.destroy.push(e.$watch(function(){b.refresh()})),d.destroy.push(e.$on("$destroy",function(){b.exit()})),e.$on("$destroy",function(){i()});var i=function(){for(var a=0,b=d.destroy;a<b.length;a++){var c=b[a];c()}}}}return b.factory=function(){var a=function(a,c){return new b(a,c)};return a.$inject=["ngIntroService","$timeout"],a},b}();a.module(d,[]).service("ngIntroService",f).directive("ngIntroOptions",g.factory()).directive("ngIntroDisableButton",["ngIntroService",function(a){var b=0;return{restrict:"A",priority:1,link:function(d,e,f){var g="disabledBtn"+b++;a.addListener(g,function(a){a===c.open?f.$set("disabled","disabled"):(delete f.disabled,e.removeAttr("disabled"))}),d.$on("$destroy",function(){a.removeListener(g)})}}}])});

