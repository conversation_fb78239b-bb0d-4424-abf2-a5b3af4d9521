/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

 .help-center-popup {
    width: 200px;
    height: 0;
    display: inline-block;
    top: 45px !important;
    left: auto;
}

.help-center-popup-content {
    position: absolute;
    width: 200px;
    background: white;
    box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.6);
    top: 0px;
    visibility: visible;
    opacity: 1;
    border-radius: 10px;
    border-radius: 10px;
    padding: 10px;
}

.help-center-popup-content div {
    max-width: 210px;
}

.help-center-popup .header h2 {
    font-size: 1.2em;
    text-transform: none;
}

.help-center-popup .header {
    -ms-flex-align:      left;
    -moz-box-align:      left;
    -webkit-box-align:   left;
    -webkit-align-items: left;
    align-items:         left;
    margin-bottom: 0;
}

.help-center-popup ul {
    overflow: visible;
    width: auto;
    margin: 0;
    padding: 0;
}

.help-center-popup li {
    display: block;
    width: 100%;
    overflow: visible;
    margin: 0;
    padding: 0;
}

.help-center-popup li {
    border-bottom: 1px solid lightgrey;
    border: 0px;
}

.help-center-popup li:last-child {
    border-bottom: unset;
}

.help-center-popup li:only-child {
    border-bottom: 0px;
}

.help-center-popup a {
    width: auto;
    height: 100%;
    display: block;
    justify-content: flex-start;
    align-items: initial;
    white-space: nowrap;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 15px;
    padding-right: 15px;
    cursor: pointer;
    text-overflow: ellipsis;
    overflow: hidden;
}

.help-center-popup a:hover {
    background-color: rgba(184, 226, 255,0.2);
    border-radius: 10px;
}

#ribbonHelpCenterPopup{
    left: none !important;
}
