/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.center-fb {
    left: 10vw;
    top: 50px;
    width: auto;
    height: auto;
}

.file-browser-external {
    display: flex;
    flex-direction: column;
    height: 80vh;
    width: 80vw;
    overflow: visible;
	background: #f8fafc;
    border-radius: 6px;
    position: relative;
}

.file-browser-external > iframe {
    border: 0;
    flex-grow: 1;
    height: 100%;
    margin-top: 10px;
    min-width: auto;
    width: 100%;
}

.minmax-tooltip {
    width: 75px !important;
    margin-left: -37px !important;
}

.minmax-tooltip::after {
    content: '';
    position: absolute;
    top: 100%; /* Bottom of the tooltip */
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent; /* Arrow pointing down */
}

.file-browser-external .file-browser-close:hover .close-tooltip {   
    visibility: visible;
    opacity: 1;
}

.file-browser-external .file-browser-close:focus .close-tooltip {
    visibility: visible;
    opacity: 1;
}

.file-browser-external .file-browser-minmax:hover .minmax-tooltip {   
    visibility: visible;
    opacity: 1;
}

.file-browser-external .file-browser-minmax:focus .minmax-tooltip {
    visibility: visible;
    opacity: 1;
}