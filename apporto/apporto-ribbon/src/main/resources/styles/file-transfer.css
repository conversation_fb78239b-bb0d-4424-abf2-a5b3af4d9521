/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */


.cancel-transfer {
    background-position: 0 0;
}
.close-transfer {
    display: initial;
    color: #000;
    font-weight: bold;
    line-height: 15px;
    text-align: center;
    text-decoration: none;
    width: 20px;
    height: 20px;
    position: absolute;
    background-image: url("app/ext/ribbon/images/close_icon.svg");
    background-color: transparent;
    padding: 0px;
    min-width: 20px;
    cursor: pointer;
    box-shadow: none;
    background-size: 15px;
    background-position: center;
    border-radius: 50%;
    border: 2px solid red;
}

.success-transfer {
    background-image: url("app/ext/ribbon/images/check_circle.svg") !important;
    border: none;
    background-size: 24px;
}


button.close-transfer:hover:enabled {
    background-color: transparent;
    box-shadow: none;
    border-radius: 50%;
}

button.close-transfer:active {
    background-color: transparent;
    box-shadow: none;
}