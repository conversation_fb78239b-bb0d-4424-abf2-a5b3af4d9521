/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.network-quality-dialog {
    width: min-content;
    min-width: 180px;
    overflow: visible;
    position: relative;
    margin-top: 10px;
    border-radius: 6px;
}

.network-quality-dialog::before {
    content: "";
    position: absolute;
    height: 5px;
    top: -5px;
    left: 4px;
    right: 4px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

#dlg-network-quality>* {
    margin: initial;
    padding: 10px 10px 0px 10px;
}

.dialog-initial {
    width: 100%;
    text-align: center;
}

.bandwidth {
    justify-content: space-between;
    align-items: center;
    display: flex;
    flex-direction: column;
}

.label-item {
    margin-bottom: 15px;
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    margin-left: 5%;
    margin-right: 5%;
}

.label-value {
    border: none !important;
    width: 100% !important;
    color: #696969;
    font-size: 1em !important;
    max-width: unset !important;
    display: block;
}

.network-quality-dialog span.close {
    color: #808080;
    cursor: pointer;
    float: right;
    font-weight: bold;
    font-size: 28px;
    line-height: 25px;
    position: absolute;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
}

.status-value {
    font-size: 0.9em;
    text-align: right;
    display: block;
}

.label-title {
    display: block;
}

.network-quality {
    text-transform: none;
}

#move-network-dlg {
    
}