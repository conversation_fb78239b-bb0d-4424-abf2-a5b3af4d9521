/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.dialog-outer {
    visibility: hidden;
    opacity: 0;
    transition: opacity, visibility;
    transition-duration: 0.25s;
    z-index: 10;
}

.shown.dialog-outer {
    visibility: visible;
    opacity: 1;
}

.dialog-outer-attendance {
    visibility: hidden;
    opacity: 0;
    transition: opacity, visibility;
    transition-duration: 0.25s;
    z-index: 10;
}

.shown.dialog-outer-attendance {
    visibility: visible;
    opacity: 1;
}

.hide-bg.dialog-outer {
    background: rgba(0,0,0,1);
}

.raise.dialog-outer {
    z-index: 20;
}

.dialog {
    background: white;
}

.login-ui .logo {
    display: block;
    margin: 0.5em auto;
    width: 48px;
    height: 48px;
    background-image: url("app/ext/ribbon/images/session-expire.png");
}

.login-ui .session-expire {
    font-family: Lato;
    font-size: 24px;
    font-weight: 600;
    line-height: 28.8px;
    letter-spacing: 0.01em;
    text-align: center;
    color: #1A1A1A;
}

.login-ui .back-home {
    font-family: Lato;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
    color: #1A1A1A;
}

.login-ui .back-home-btn {
    height: 44px;
    padding: 12px;
    border-radius: 8px;
    opacity: 1;
    background: #22538F;
    color: white;
    border: none;
    cursor: pointer;
}

.login-ui .back-home-btn img.btn-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    display: inline-block;
}

.login-ui .back-home-btn:hover {
    border-radius: 8px;
    background: #1a3d6a;
}

.apporto-logo {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: auto;
}

