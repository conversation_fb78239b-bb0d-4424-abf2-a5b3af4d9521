/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.delete-confirmation-dialog {
    background-color: rgb(224, 232, 243);
    width: 30vw;
    overflow: visible;
}

#dlg-delete-confirmation>* {
    margin: initial;
}

/* The Close Button */
.delete-confirmation-dialog span.close {
    background: #606061;
    color: #FFFFFF;
    cursor: default;
    float: right;
    font-weight: bold;
    line-height: 25px;
    position: relative;
    right: -12px;
    text-align: center;
    text-decoration: none;
    top: -10px;
    width: 24px;
    
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px;

    -moz-box-shadow: 1px 1px 3px #000;
    -webkit-box-shadow: 1px 1px 3px #000;
    box-shadow: 1px 1px 3px #000;
}

.delete-confirmation-dialog #content {
    display: -ms-flexbox;
    -ms-flex-align: stretch;
    -ms-flex-direction: column;
    display: -moz-box;
    -moz-box-align: stretch;
    -moz-box-orient: vertical;
    display: -webkit-box;
    -webkit-box-align: stretch;
    -webkit-box-orient: vertical;
    display: -webkit-flex;
    -webkit-align-items: stretch;
    -webkit-flex-direction: column;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}

.delete-confirmation-dialog #content li {
    align-items: center;
    display: flex;
}

.delete-confirmation-dialog #content span {
    flex: 1 1 auto;
    font-size: 1.1em;
    font-weight: bold;
    padding: 0.75em;
}

.delete-confirmation-dialog #content a {
    color: black;
    font-weight: initial;
    margin-left: 5px;
    margin-right: 20px;
    text-decoration: none;
}

.delete-confirmation-dialog #content button {
    background-color: rgba(54, 162, 235, 0.4);
    border: 0;

    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 12px;

    -moz-box-shadow: 1px 1px 3px #000;
    -webkit-box-shadow: 1px 1px 3px #000;/
    box-shadow: 1px 1px 3px #000;

    color: black;
    margin: 0 1em;
    text-shadow: none;
    width: 120px;
}

.delete-confirmation-dialog #content button:hover {
    background-color: rgba(54,162,235,0.2);
}

.delete-confirmation-dialog .footer {
    padding: 0.75em;
}

