/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.ribbon-bar {
    background-color: white;
    height: 40px;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 101 !important;
    transition: 0.5s;
    margin-block: 0;
    padding-inline: 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    padding-inline: 8px;
    width: max-content;
    min-width: 868px;
    margin-inline: auto;
}

header {
    width: 100%;
}

.flex-grow {
    flex-grow: 1;
}

.hide-ribbon-bar {
    height: 0px !important;
}

.introjs-disabled {
    display: none;
}

.ribbon-button {
    background-color: transparent;
    box-shadow: none;
    height: 30px;
    background-position: center;
    background-repeat: no-repeat;
    min-width: 4em;
    cursor: pointer;
    font-weight: 400;
}

.ribbon-bar-safari {
    height: 37px;
}

.ribbon-button:hover, .ribbon-button:visited, .ribbon-button:active {
    background-color: #eff6ff;
}

.ribbon-button:focus {
    outline: 2px solid black;
    border-radius: 5px;
    outline-offset: 2px;
}

.ribbon-button-active {
    background-color: rgb(175, 197, 222);
}

button.ribbon-button:disabled {
	background-color: transparent;
	opacity: 0.1;
    color: black;
}

.btn-file-browser {
	background-image: url(app/ext/ribbon/images/file-browser.png);
    background-size: 20px;
}

.btn-chatbot {
    background-image: url(app/ext/ribbon/images/ai-icon.svg);
    background-size: 20px;
    color: #3592c9 !important;
}

#fileUploadIcon{
    background-image: url(app/ext/ribbon/images/backup.svg);
    background-size: 24px;
    width: 24px;
    height: 24px;
}

.btn-file-download {
    margin-left: 12px;
}

#downloadIcon{
    background-image: url(app/ext/ribbon/images/cloud_download.svg);
}

.btn-file-explorer {
    background-image: url(app/ext/ribbon/images/folder-black.png);
}

.btn-full-screen {
	background-image: url(app/ext/ribbon/images/fullscreen.svg);
    background-size: 24px;
    padding: 4px 2px !important;
    width: min-content;
    min-width: 40px !important;
    margin: 0px !important;
}

.btn-minimize-screen {
    background-image: url(app/ext/ribbon/images/fullscreen_exit.svg);
    background-size: 24px;
    min-width: 40px;
}

.ribbon-btn-common-class{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2px 8px !important;
    gap: 8px;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    color: #1A1A1A !important;
}

#shareScreenIcon{
    background-image: url(app/ext/ribbon/images/shareScreenIcon.svg);
}

.btn-snapshot-manager {
    background-image: url(app/ext/ribbon/images/settings_backup_restore.svg);
    background-position: left !important;
    width: 200px;
    color: black;
    border-radius: 5px;
}

#chatIcon{
    background-image: url(app/ext/ribbon/images/chat_bubble.svg);
}

.ribbon-bar-btn{
    background-size: 24px;
    width: 24px;
    height: 24px;
}

#blue_dot{
    position: absolute;
    width: 11px;
    height: 10px;
    min-width: 2px;
    margin: 0;
    padding: 0;
    background: #0081f9;
    border-radius: 50%;
    left: 23px;
    top: 3px;
    border: 2px solid white;
}

.btn-osk {
    background-image: url(app/ext/ribbon/images/keyboard-black-32x32.png);
    margin-bottom: 3px;
}

.btn-analytics {
	background-image: url(app/ext/ribbon/images/bar_chart_4_bars.svg);
    background-position: left !important;
    width: 200px;
    color: black;
    border-radius: 5px;
}

.btn-speaker {
	background-image: url(app/ext/ribbon/images/speaker.png);
}

.btn-mute {
	background-image: url(app/ext/ribbon/images/mute.png);
}

.btn-sound-on {
	background-image: url(app/ext/ribbon/images/volume_up.svg);
}

.btn-sound-off {
	background-image: url(app/ext/ribbon/images/icons8-mute.png);
    background-size: 22px !important;
}

.btn-cloud {
	background-image: url(app/ext/ribbon/images/plus_icon.png);
}

.btn-classroom-mandatory {
	background-image: url(app/ext/ribbon/images/classroom-madatory.svg);
    color: black;
}

.btn-classroom-optional {
	background-image: url(app/ext/ribbon/images/classroom-optional.png);
}

.btn-hand-off {
    background-image: url(app/ext/ribbon/images/raiseHand.svg);
}

.btn-hand-on {
    background-image: url(app/ext/ribbon/images/raiseHand.svg);
}

.btn-exit {
	background-image: url(app/ext/ribbon/images/logout.png);
}

.btn-mmonitor {
	background-image: url(app/ext/ribbon/images/second_monitor.svg);
    background-position: left !important;
    width: 200px;
    color: black;
    border-radius: 5px;
}

.btn-side-menu {
	background-image: url(app/ext/ribbon/images/side-menu.png);
	background-size: 24px;
}

.btn-backup {
	background-image: url(app/ext/ribbon/images/backup-restore.svg);
}

.btn-reset-non-window-session {
     background-image: url(app/ext/ribbon/images/session-on-off.png);
}

.btn-presenter {
	background-image: url(app/ext/ribbon/images/presenter_mode.svg);
    color: black;
}

#assignmentIcon {
	background-image: url(app/ext/ribbon/images/assignment.svg);
    background-size: contain;
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
}

.btn-groupcall {
	background-image: url(app/ext/ribbon/images/microphone.png);
}

.btn-groupcall-mute {
	background-image: url(app/ext/ribbon/images/mute-microphone.png);
}

.btn-excellent {
	background-position: -120px 0;
}

.btn-excellent-medium {
	background-position: -96px 0;
}

.btn-fair {
	background-position: -72px 0;
}

.btn-fair-medium {
	background-position: -48px 0;
}

.btn-poor {
	background-position: -24px 0;
}

.btn-unstable {
	background-position: 0 0;
}

.network-indicator {
    float: left;
    width: 24px;
    height: 24px;
    background-image: url(app/ext/ribbon/images/network-indicator-nw.png );
}

.btn__badge {
    color: #1A1A1A;
    font-size: 16px;
}

.btn-gear {
	background-image: url(app/ext/ribbon/images/settings.svg);
}

.btn-help {
	background-image: url(app/ext/ribbon/images/help.svg);
}

.ribbon-checkbox {
    padding: 7px 0 7px 0px;
    margin-left: 20px;
    position: relative;
}

.ribbon-checkbox input {
	position: relative;
	top: 2px;
}

.ribbon-checkbox span {
	color: white;
}

.ribbon-stats {
	display: flex;
	margin-left: 15px;
}

.ribbon-stats span {
	display: flex;
    align-items: center;
    margin-right: 10px;
    margin-left: 10px;
}

.ribbon-chart-container {
	height: 30px;
}

.ribbon-chart {
	background: linear-gradient(0deg, rgba(224,232,243,1) 0%, rgba(255,255,255,1) 100%);
	height: 30px;
	width: 96px;
    cursor: pointer;
}

.ribbon-session-time {

}

.ribbon-text span {
	color: black;
}

.ribbon-checkbox label {
    border: 1px solid #000;
    padding: 3px;
    border-radius: 3px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    position: absolute;
    left: 5px;
    top: 7px;
    background: #000000;
}
.ribbon-checkbox label:after {
    content: '';
    width: 9px;
    height: 5px;
    position: absolute;
    top: 4px;
    left: 4px;
    border: 3px solid #fcfff4;
    border-top: none;
    border-right: none;
    background: transparent;
    opacity: 0;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}
.ribbon-checkbox input {
    opacity: 0;
    left: 8px;
    top: 0;
    z-index: 1;
    cursor: pointer;
}

.ribbon-checkbox input:checked + label:after {
    opacity: 1;
}

/* messenger */

.messenger {
    display: inline-block;
    position: relative;
    border-radius: 0.2em;
}

.messenger::after {
    font-family: Arial;
    font-size: 0.7em;
    font-weight: 400;
    position: absolute;
    top: -1px;
    right: -1px;
    padding: 1px 5px;
    border: 2px #fff solid;
    border-radius: 60px;
    background: #3498db;
    opacity: 0;
    content: attr(data-count);
    opacity: 0;
    transition: transform, opacity;
    transition-duration: 0.3s;
    transition-timing-function: ease-out;
    color: #fff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
}

.ribbon-network-indicator {
    margin-left: auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.cvox_indicator_container {
    display: none;
  }

  h1.title.ng-binding {
    font-size: 1.25em;
    font-weight: bold;
    text-transform: uppercase;
    text-align: left;
    padding: 0;
}

.undock-box {
    top: 30px;
    left: 35vw;
    z-index: 1;
    width: auto;
    height: auto;
}

.box-hidden {
    background: unset;
    width: auto;
    height: auto;
}

.more-li{
    border-bottom: 0px !important;
    padding: 5px;
    border-radius: 5px;
    display: flex !important;
    align-items: center;
}

.more-li:hover {
    background-color: #eff6ff;
}

.more-li:active {
    background-color: #eff6ff;
    box-shadow: inset 1px 1px 0.25em rgba(0,0,0,0.25), -1px -1px 0.25em rgba(0,0,0,0.25), 1px 1px 0.25em rgba(255,255,255,0.25);
}

.btn-more{
    display: flex;
    justify-content: center;
    align-items: center;
    color: #1A1A1A;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    gap: 8px;
    padding: 2px 8px;
}

#moreIcon{
    color: black;
    background-image: url(app/ext/ribbon/images/arrow_down.png);
    background-size: 15px;
    width: 15px;
    height: 15px;
    transition: 0.3s ease;
    transform: rotate(0deg);
}

.morePopupBtn{
    padding-left: 40px;
    display: flex;
    align-items: center;
    flex-direction: row;
    height: 34px;
    background-position: 6px !important;
    color: black;
}

.highlightButton{
    border-radius: 4px;
    background-color: #eff6ff !important;
}

.morePopupBtn:active{
    box-shadow: none !important;
}

.last-btns{
    padding: 0.35em 18px !important;
    margin-inline: 0px !important;
    min-width: 1.5em;
    background-size: 26px;
}

.btn-name{
 
}

#network-indicator-btn{
    padding: 0.35em 6px !important;
    margin-left: 50px !important;
}

btn-gap{
padding: .35em 1.4em 0.35em 0em !important;
margin: .25em 0.25em 0.25em 1em !important;
}

.add_line{
    height: 27px;
    width: 1px;
    border: 0;
    background-color: #D9D9D9;
    margin: 0px 10px;
    border: 0.1px solid #D9D9D9;
}

.compressed_ribbon_bar{
    transform: translate(-50% , 0px) !important;
}

.ribbon_bar_shown{
   top: -40px;
   box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
   animation: show_ribbon_animate 0.3s linear forwards;
}

@keyframes show_ribbon_animate {
   0%{
       top: -40px;
   }
   100%{
       top: 0px;
   }
}

.ribbon_bar_hidden{
       top: 0px;
   animation: hide_ribbon_animate 0.3s linear forwards;
}

@keyframes hide_ribbon_animate {
   0%{
       top: 0px;
    }
     100%{
       top : -40px
     }

}

.compressed_ribbon_bar_btn{
    background-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSIjMDAwMDAwIiBoZWlnaHQ9IjE4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHdpZHRoPSIxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4gICAgPHBhdGggZD0iTTcuNDEgNy44NEwxMiAxMi40Mmw0LjU5LTQuNThMMTggOS4yNWwtNiA2LTYtNnoiLz4gICAgPHBhdGggZD0iTTAtLjc1aDI0djI0SDB6IiBmaWxsPSJub25lIi8+PC9zdmc+);
    background-position: center;
    background-repeat: no-repeat;
    background-size: 24px;
    transform: rotate(180deg);
    min-width: max-content;
    margin: 0px;
}

#compressed_ribbon_bar_block{
    width: 150px;
    height: 24px;
    position: absolute;
    top: 0px;
    background: rgba(255, 255, 255, 0.5);
    z-index: 233;
    left: 50%;
    transform: translate(-50% , -36px);
    border-radius: 0px 0px 4px 4px;
    transition: all 0.5s;
    background-position: center;
    background-image: url(data:image/svg+xml;base64,PHN2ZyBmaWxsPSIjMDAwMDAwIiBoZWlnaHQ9IjE4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIHdpZHRoPSIxOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4gICAgPHBhdGggZD0iTTcuNDEgNy44NEwxMiAxMi40Mmw0LjU5LTQuNThMMTggOS4yNWwtNiA2LTYtNnoiLz4gICAgPHBhdGggZD0iTTAtLjc1aDI0djI0SDB6IiBmaWxsPSJub25lIi8+PC9zdmc+);
    background-repeat: no-repeat;
    background-size: 24px;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.20);
    cursor: pointer;
    position: absolute;
    padding-inline: 5px;
}

#stopSharingModal{
    position: fixed;
    bottom: 75px;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    border-radius: 8px;
    height: 56px;
    padding: 0px 8px;
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    gap: 8px;
    width: max-content;
}

#co_present{
    width: 28px;
    height: 24px;
    background-image: url(app/ext/ribbon/images/co_present.svg);
    background-repeat: no-repeat;
    background-size: 24px;
}

#stopSharingBtn{
    height: 24px;
    padding: 12px;
    border-radius: 8px;
    background: #D53737;
    width: max-content;
    margin: 0px;
    display: flex;
    align-items: center;
    cursor: pointer;
}

#stopSharingBtn:hover{
    background: #f11f1f;
}

.raiseHand{
    color: black;
    background-position: 8px;
    padding-left: 35px;
}
#close_network_tab_button{
    position: absolute;
    z-index: 3434;
    right: 1px;
    top: 10px;
}

.ribbon-tooltip {
    font-size: 14px;
    font-weight: 400;
    visibility: hidden;
    background-color: #323c47;
    border: 1px solid #ccc;
    color: #ccc;
    text-align: center;
    border-radius: 5px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    top: 150%;
    left: 50%;
    margin-left: -25px;
    opacity: 0;
    transition: opacity .3s;
}

.ribbon-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent #555 transparent;
}

#compressed_ribbon_bar_block:hover .ribbon-tooltip {
    visibility: visible;
    opacity: 1;
}

#compressed_ribbon_bar_block:focus .ribbon-tooltip {
    visibility: visible;
    opacity: 1;
}

.remoteapps-popup {
    width: 250px;
    max-height: calc(100vh - 50px);
    overflow-y: auto;
    overflow-x: hidden;
}

.remoteapps-popup::-webkit-scrollbar {
    width: 10px;
}

.remoteapps-popup::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 10px;
}

.remoteapps-popup::-webkit-scrollbar-thumb:hover {
    background-color: #aaa;
}

.remoteapps-popup::-webkit-scrollbar-track {
    background-color: #f3f3f3;
    border-radius: 10px;
}

.btn-remoteapps {
	background-image: url(app/ext/ribbon/images/remote_apps.svg);
    height: 34px;
}

.remoteapps-icon {
    background-size: 18px 18px;
}

.btn-remoteapps-gridview {
    background-image: url(app/ext/ribbon/images/remote_apps_gridview.svg);
    background-repeat: no-repeat;
    background-color: white;
    box-shadow: none;
    min-width: 16px!important;
    background-position: center;
    margin: 0px;
}

.btn-remoteapps-listview {
    background-image: url(app/ext/ribbon/images/remote_apps_listview.svg);
    background-repeat: no-repeat;
    background-color: white;
    box-shadow: none;
    min-width: 16px!important;
    background-position: center;
    margin: 0px;
}

.btn-remoteapps-search {
    background-image: url(app/ext/ribbon/images/remote_apps_search.svg);
    background-repeat: no-repeat;
    background-color: white;
    box-shadow: none;
    min-width: 16px!important;
    background-position: center;
    margin: 0px;
}

.btn-remoteapps-pin {
    background-image: url(app/ext/ribbon/images/remote_apps_pin.svg);
}

.btn-remoteapps-unpin {
    background-image: url(app/ext/ribbon/images/remote_apps_unpin.svg);
}

.remoteapps-li {
    border-bottom: 0px !important;
    padding: 5px;
    border-radius: 5px;
    display: flex !important;
    align-items: center;
}

.availableapps-title-li {
    display: flex;
    justify-content: space-between;
}

.listview-li {
    display: flex;
    justify-content: space-between;
}

.listview-li:hover {
    background-color: rgb(184 226 255 / 20%);
    border-radius: 6px;
}

.gridview-li {
    height: auto!important;
}

.gridview-li > button {
    min-width: 38px!important;
}

.search-box-li {
    border: none!important;
}

.remoteapps-search {
    display: flex;
    align-items: center;
    justify-content: left;
    width: 100%;
    border: 1px solid black;
    border-radius: 5px;
    height: 35px;
    margin: 5px 0px;
}

.remoteapps-search img {
    width: 25px;
    height: 25px;
}

.search-value {
    height: 100%;
    outline-color: white;
    border: none!important;
    font-size: 16px!important;
    outline: none;
}

.pined-apps {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pined-apps > button:active, .pined-apps > button:hover, .pined-apps > button:visited {
    background-color: transparent;
    box-shadow: none;
    outline: none;
    cursor: pointer;
}

.remoteapps-li > button:active, .remoteapps-li > button:hover, .remoteapps-li > button:visited {
    background-color: transparent;
    box-shadow: none;
    outline: none;
    cursor: pointer;
}

.remoteapps-li > button {
    min-width: 38px!important;
    margin: 0.25rem!important;
}

.pined-apps > button {
    min-width: 32px!important;
    margin: 0px!important;
}

.remoteapps-li .btn-remoteapps-unpin {
    display: none; /* Hide the buttons by default */
}

.remoteapps-li:hover .btn-remoteapps-unpin {
    display: inline-block; /* Show the buttons when the <li> is hovered */
}

.no-search-result {
    color: grey;
    text-align: center;
}