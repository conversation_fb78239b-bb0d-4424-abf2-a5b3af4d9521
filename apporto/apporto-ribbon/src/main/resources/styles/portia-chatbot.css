/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.portia-chatbot .dialog-box {
    background: white;
    width: 500px;
    border-radius: 8px;
    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.portia-chatbot .dialog-header {
    background: #007bff;
    color: white;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.portia-chatbot .dialog-title {
    font-size: 16px;
    font-weight: bold;
}

.portia-chatbot .close-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    background-image: url(app/ext/ribbon/images/chats/close.svg);
    background-size: contain;
    padding: 0px;
    background-repeat: no-repeat;
    width: 30px;
    min-width: unset;
    height: 30px;
    box-shadow: none;
    margin: 5px;
}

.portia-chatbot .chat-iframe {
    width: 100%;
    height: 600px;
    border: none;
    min-width: auto;
}
