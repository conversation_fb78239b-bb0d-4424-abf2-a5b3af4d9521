/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/* Buttons */
input[type="submit"], button, a.button {
    background-color: rgba(0, 123, 255, 1);
    color: rgb(255, 255, 255);
    border: 0;
    text-shadow: none;
}

input[type="submit"]:hover:enabled, button:hover:enabled, a.button:hover:enabled {
    background-color: #eff6ff;
}

input[type="submit"]:disabled, button:disabled, button.danger:disabled {
    background-color: rgba(74, 144, 226, 1);
}

input[type="submit"], button.upload, button.download, button.publish, button.reconnect, button.launch, button.tClose, a.button {
    background-color: #22538F;
    color: white;
    border-radius: 8px;
    height: 40px;
    text-shadow: none;
}

input[type="submit"], button.refresh, a.button {
    background-color: white;
    color: white;
    border-radius: 6px;
    height: 2em;
    text-shadow: none;
}

input[type="submit"]:hover:enabled, button.upload:hover:enabled, button.download:hover:enabled, button.publish:hover:enabled, button.reconnect:hover:enabled, button.launch:hover:enabled, button.tClose:hover:enabled, a.button:hover:enabled {
    background-color: rgba(26, 87, 158, 0.8);
}

input[type="submit"]:hover:enabled, button.refresh:hover:enabled, a.button:hover:enabled {
    background-image: url(app/ext/ribbon/images/circle-lightblue.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: white;
    box-shadow: none;
    border-radius: 5px;
    min-width: unset;
    font-size: 0;
    height: 27px;
    width: 27px;
}

input[type="submit"]:after:enabled, button.refresh:after:enabled, a.button:after:enabled {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top:0;
    width: 100%;
    height: 100%;
    opacity: 0;
}

input[type="submit"]:active:after:enabled, button.refresh:active:after:enabled, a.button:active:after:enabled {
    position: absolute;
    left: 0;
    top:0;
    opacity: 1;
    transition: 0s;
}

input[type="submit"]:active:enabled, button.refresh:active:enabled, a.button:active:enabled {
    top: 1px;
}

input[type="submit"]:disabled, button.upload:disabled, button.download:disabled, button.publish:disabled, button.reconnect:disabled, button.launch:disabled, button.tClose:disabled, button.danger:disabled {
    background-color: rgba(74, 144, 226, 1);
}

input[type="submit"]:disabled, button.refresh:disabled, button.danger:disabled {
    background-image: url(app/ext/ribbon/images/circle-lightblue.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: white;
    box-shadow: none;
    border-radius: 5px;
    min-width: unset;
    font-size: 0;
    height: 27px;
    width: 27px;
}

input[type="submit"], button.fClose, button.delete, a.button {
    background-color: rgba(0, 123, 255, 1);
    border-radius: 6px;
    color: white;
    height: 2em;
    text-shadow: none;
}

input[type="submit"]:hover:enabled, button.fClose:hover:enabled, button.delete:hover:enabled, a.button:hover:enabled {
    background-color: rgba(74, 144, 226, 0.8);
}

input[type="submit"]:disabled, button.fClose:disabled, button.delete:disabled, button.danger:disabled {
    background-color: rgba(74, 144, 226, 1);
}

.button.reconnect::before,
button.reconnect::before {
    background-image: url('app/ext/ribbon/images/circle-arrows.png');
}

/* Notifications */
.notification.error {
    background: white;
}

.notification .body p.text {
    white-space: pre-wrap;
    margin-block-start: 0px;
}

.status-middle .notification {
	width: 558px;
    border-radius: 6px;
    position: relative;
    overflow: visible;
}

.status-middle .notification .buttons {
    margin: 10px;
    margin-top: 0px;
    padding-bottom: 10px;
    text-align: right;
}

.div-disable {
    pointer-events: none;
}

#file-transfer-dialog {
    font-size: 1em;
}

#file-transfer-dialog .transfer-manager {
    border-radius: 6px;
    position: relative;
}

#file-transfer-dialog .transfer-manager::before {
    content: "";
    position: absolute;
    height: 5px;
    top: -5px;
    left: 4px;
    right: 4px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

body {
    font-family: Lato !important;
}