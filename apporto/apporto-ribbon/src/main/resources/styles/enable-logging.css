/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.logging-dialog {
    border-radius: 8px;
    width: 35vw;
    position: relative;
    overflow: visible;
    padding: 32px;
}

.logging-dialog>* {
    margin: initial;
}

.title-logging {
    margin-block-end: 0;
    text-transform: unset;
    text-align: left;
    padding: 0;
    margin-bottom: 8px !important;
}

.logging-dialog .body .text {
    text-align: left;
}

.logging-dialog .body .text p {
    font-weight: normal;
}

.logging-dialog .body .text p > span {
    color: #22538F;
}

.logging-dialog .footer {
    padding: 0px;
    text-align: left !important;
}

.logging-dialog .footer button {
    background-color: #22538F;
    color: white;
    height: max-content;
    width: max-content;
    text-shadow: none;
    padding: 12px;
}