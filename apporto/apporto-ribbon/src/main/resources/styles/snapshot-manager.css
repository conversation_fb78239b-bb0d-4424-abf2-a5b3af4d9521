/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.snapshot-manager {
	width: 35em;
    overflow: visible;
    border-radius: 6px;
}

.snapshot-manager .menu-content {
	/* height: 12em;  */
}

.snapshot-manager.dialog>* {
	margin: 0;
}

/*.snapshot-manager.dialog .header h2 {
    font-size: 1.2em;
    text-transform: none;
    text-align: center;
}*/

.snapshot-manager.dialog .snapshot-header {
   /* -ms-flex-align:      center;
    -moz-box-align:      center;
    -webkit-box-align:   center;
    -webkit-align-items: center;
    align-items:         center;*/
    /*height: 4em;*/
    /*border-bottom: 1px solid rgba(0,0,0,0.125);*/
}

.snapshot-manager.dialog .menu-body {
    padding: 0.25em;
    position: relative;
    overflow: hidden;
    gap: 24px;
}


.snapshot-manager div.footer {
	padding: 10px;
    padding-top: 20px;
}

.snapshot-manager button {
    -webkit-border-radius: 12px;
    -moz-border-radius: 12px;
    border-radius: 12px;
    margin: 0 1em;
    width: 120px;
}

.snapshot-manager button.close {
    margin: 0em;
    width: 24px;
    height: 24px;
    font-size: 28px;
}

.snapshot-manager div.line {
    border-top: 1px solid rgba(0,0,0,0.125);
    margin: 0 1em;
}

.snapshot-manager .list-item .caption {
    white-space: nowrap;
    border: 2px solid #C7C7C7;
    padding-left: 2vw;
    display: flex;
    align-items: center;
}

.snapshot-manager .list-item.focused .caption {
    border: 1px dotted rgba(0, 0, 0, 0.5);
}

.snapshot-manager circle-loader {
    position: absolute;
    top: 0;
    background: rgba(0,0,0,0.5);
    width: 100%;
    height: 100%;
    left: 0;
    padding: 4em 13em 1em 13em;
}

.snapshot-manager .snapshots-message-container {
    height: 100%;
    position: absolute;
    width: 100%;
}

.snapshot-manager .snapshots-message-text {
    font-size: 1.5em;
    /*text-shadow: 1px 1px;*/
    opacity: 0.3;
    position: relative;
    top:2%;
    bottom: 2.1em;
    margin-left: .5em;
    color: #606770;
}

.snapshot-manager.snapshot-title{
	margin-block-end: 0px;
}

.snapshot-manager.subtitle {
	margin-block-start: 1px;
	font-weight: normal;
	margin-block-end: 1em;
	padding-right: 5.5em;
}

.restore.button, .save.button {
	width: 7.5em;
    height: 2em;
    margin-left: 5px;
    color: black !important;
    width: max-content !important;
    gap: 8px;
}

#save_snap_button{
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-size: 24px;
    background-image: url(app/ext/ribbon/images/snapshot_save.svg);
}

#restore_snap_button{
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
    background-size: 24px;
    background-image: url(app/ext/ribbon/images/restore_snap.svg);
}

.radio {
	vertical-align: baseline;
}

.list-item {
	height: 2em;
    /*bottom: 1.4em;*/
}

.list-item:not(.selected) .caption:hover  {
	background: transparent;
}

.circle-loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    opacity: .25;
}

h1.snapshot-manager.snapshot-title {
    display: block;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    text-align: left;
    padding-bottom: 0px;
    margin: 0px 0px 8px 0px;
    font-size: 24px;
    letter-spacing: 0.24px;
    padding-left: 2px;
}

p.snapshot-manager.subtitle.ng-binding {
    display: block;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    font-size: 1.2em;
}

.status-outer {
    z-index: 100;
}

#select_snapshpshot_state{
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-top: 24px;
}

#snapshot_options{
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.snapshot_option{
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    padding: 24px;
    border-radius: 8px;
    border: 2px solid #C7C7C7;
    cursor: pointer;
}

.snapshot_option p{
    margin: 0px;
}

#save_snapshot_bg{
    background-image: url(app/ext/ribbon/images/save_snapshot.svg);
    background-size: 24px;
    width: 24px;
    height: 24px;
}

#restore_snapshot_bg{
    background-image: url(app/ext/ribbon/images/restore_snapshot.svg);
    background-size: 24px;
    width: 24px;
    height: 24px;
    filter: brightness(0);
}

.snapshot_buttons{
    margin: 0 !important;
    height: 40px !important;
    padding: 12px;
    border-radius: 8px !important;
    background: #22538f !important;
    display: flex;
    justify-content: center;
    align-items: center;
}

.snapshot_buttons:hover {
    background-color: rgba(74, 144, 226, 0.8) !important;
}

.snapshot_buttons:disabled:hover {
    pointer-events: none;
    background-color: #22538f !important;
}

.disable_snappshot_option p{
 color: #C7C7C7;
}

.disable_snappshot_option div{
    filter: brightness(1) !important;
}

#snapshot_info{
    color: #757575;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
    text-align: left;
}

.res_snap{
    padding: 24px !important;
    border-radius: 8px;
    border: 2px solid #22538F;
    background: white;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

#current_time_block{
    border-radius: 8px;
    border: 2px solid #22538F;
    background: var(--Accent-Light, #F0F6FF);
    display: flex;
    padding: 24px;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
}

#save_snapshot_cont{
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.txt_alg_lt{
    text-align: left;
}

#save_snapshot_cont .txt_alg_lt {
    color:#1A1A1A;
    line-height: 20px;
}

.menu-body .txt_alg_lt:first-child {
    color:#1A1A1A;
    line-height: 20px;
}

.menu-body .txt_alg_lt:nth-child(3) {
    color:#777;
    line-height: 20px; 
}
