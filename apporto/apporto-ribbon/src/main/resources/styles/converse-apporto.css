/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
 
#conversejs button.close {
    box-shadow: none;
    min-width: auto;
    top: auto;
    position: static;
    right: auto;
    width: auto;
}

#conversejs #controlbox .controlbox-pane .d-flex.controlbox-padded {
    padding-top: 1em;
}

#conversejs #converse-roster .roster-filter-form span {
	margin: 1em 0em .4em 0em;
}

#conversejs #converse-roster .roster-contacts .roster-group li a.list-item-action.remove-xmpp-contact.far.fa-trash-alt {
    overflow: visible;
}

#conversejs #controlbox .controlbox-section .controlbox-heading__btn {
	min-width: 0.25em;
}

#conversejs .chatbox.chatroom a.chatbox-btn.toggle-bookmark.fa.fa-bookmark.button-on {
	display: none;
}