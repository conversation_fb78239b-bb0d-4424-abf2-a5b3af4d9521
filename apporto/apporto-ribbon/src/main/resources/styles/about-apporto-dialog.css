/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.about-apporto-dialog {
    width: 750px;
    height: 470px;
    border-radius: 8px;
    overflow: visible;
    position: relative;
}

#dlg-about-apporto>* {
    margin: initial;
}

/* The Close Button */
.about-apporto-dialog span.close {
    line-height: 25px;
    right: 12px;
    top: 10px;
    width: 24px;
    position: absolute;
    text-align: center;
    text-decoration: none;
    font-weight: bold;
    color: #808080;
    cursor: default;
    float: right;
}

.about-apporto-dialog .enable-close {
    height: 24px;
    background-image: url(app/ext/ribbon/images/share_close.svg) !important;
    background-size: 24px !important;
    background-repeat: no-repeat !important;
}

.about-apporto-dialog .enable-close:hover .close-tooltip{
    visibility: visible;
    opacity: 1;
}

.about-apporto-dialog .enable-close:focus .close-tooltip{
    visibility: visible;
    opacity: 1;
}

.title-about-apporto {
    margin-block-end: 0px;
    padding: 0px !important;
}

.block-title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 5px;
}

.block-info {
    width: 650px;
    text-align: left;
}

.item-info {
    margin-left: 30px;
    margin-right: 30px;
    font-size: 18px;
    padding: 5px;
    display: flex;
    justify-content: space-between;
}
