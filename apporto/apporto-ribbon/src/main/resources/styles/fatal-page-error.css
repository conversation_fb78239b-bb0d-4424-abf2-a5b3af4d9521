/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.fatal-page-error {
    display: inline-block;
    width: 100%;
    padding: 1em;
    max-width: 15in !important;
    text-align: left;
}

.fatal-page-error-outer .logo {
    display: block;
    margin: 0.5em auto;
    width: 10em;
    height: 3em;
    background-size:         10em 3em;
    -moz-background-size:    10em 3em;
    -webkit-background-size: 10em 3em;
    -khtml-background-size:  10em 3em;
    background-image: url("app/ext/ribbon/images/apporto-logo.png");
    background-repeat: no-repeat;
}


.fatal-page-error-outer .fatal-dogs {
    display: block;
    margin: 0.5em auto;
    width: 15em;
    height: 15em;
    background-size:         10em 3em;
    -moz-background-size:    10em 3em;
    -webkit-background-size: 10em 3em;
    -khtml-background-size:  10em 3em;
    background-image: url("app/ext/ribbon/images/fatal-dogs.gif");
    background-repeat: no-repeat;
    background-size: contain;
}

.fatal-page-error-outer .back-home {
    font-family: Lato;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
    color: #1A1A1A;
}

.fatal-page-error-outer .back-home-btn {
    height: 44px;
    padding: 12px;
    border-radius: 8px;
    opacity: 1;
    background: #22538F;
    color: white;
    border: none;
    cursor: pointer;
    display: block;
    margin: .5em auto;
}

.fatal-page-error-outer .back-home-btn img.btn-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    display: inline-block;
}

.fatal-page-error-outer .back-home-btn:hover {
    border-radius: 8px;
    background: #1a3d6a;
}

.fatal-page-error-outer p {
    font-family: 'Kanit', sans-serif;
    display: block;
    margin: 1em auto;
    font-size: 24px;
    font-weight: 200;
    text-align: center;
}
