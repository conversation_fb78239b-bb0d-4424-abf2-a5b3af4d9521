/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.split-dropdown-button {
  margin: .25em;
  padding: 0 .3em;
  border-radius: 6px;
}

.split-dropdown-button:hover {
  background-color: #f0f6ff;
}

.split-button {
    background-color: rgba(0,0,0,0);
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: none;
    height: 30px;
    min-width: 2.15em;
    margin: 0;
    color: #1A1A1A;
    padding-right: 5px;
    padding-left: 35px;
    background-position-x: 5px;
    cursor: pointer;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.split-dropdown-button > .split-button:hover {
    background-color: rgba(0,0,0,0);
}

.dropdown-menu-button {
    background-color: transparent;
    background-position: center;
    background-repeat: no-repeat;
    border-left: 1px solid #d9d9d9;
    box-shadow: none;
    height: 27px;
    margin: 0;
    margin-top: 1px;
    min-width: auto;
    padding: .5em 0 .2em 0;
    width: 30px;
    background-image: url(app/ext/ribbon/images/arrow_down.png);
    background-size: 17px;
    background-position-y: 6px;
}

.split-dropdown-button > .dropdown-menu > .dropdown-menu-button:hover {
    background-color: rgba(0,0,0,0);
}

.btn-caret-down {
  color: black;
  font-size: medium;
}

.dropdown-menu {
  display: inline-block;
  vertical-align: top;
}

.dropdown-menu-content {
  position: absolute;
  bottom: -110px;
  background-color: #fff;
  min-width: 160px;
  z-index: 1;
  padding: 10px;
  border-radius: 6px;
}

.dropdown-menu-content div {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
  border-radius: 6px;
}

.dropdown-menu-content div:hover {
  background-color: #f0f6ff;
}
