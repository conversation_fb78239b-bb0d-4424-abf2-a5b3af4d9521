/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
 
 .chatting-room {
  width: 240px;
  height: 320px;
  min-height: 320px;
  background: transparent;
  opacity: 1 !important;
  visibility: visible !important;
  position: fixed;
  left: unset;
  top: unset;
  right: 250px;
  bottom: 30px;
  z-index: 100;;
}

#chatting-room {
  border-radius: 8px;
  background: #ffffff;
  -moz-box-shadow: 0.1em 0.1em 0.2em #00000099;
  -webkit-box-shadow: 0.1em 0.1em 0.2em #00000099;
  box-shadow: 0.1em 0.1em 0.2em #00000099
}

#chatting-room h1 {
  margin: 0;
  padding: 0 10px;
  white-space: nowrap;
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

#chatting-room ul {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin: 0;
  list-style: none;
}

#chatting-room li {
  width: 100%;
}

#chatting-room .list-item-text {
  display: flex;
  flex-direction: column;
  padding: 0 10px; 
  margin-bottom: 5px;
}

#chatting-room .context-menu {
  color: #000000;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  position: relative;
  text-align: center;
  text-decoration: none;
  margin: 3px 0;
  padding: 0 5px;
}

#chatting-room button.context-menu {
  background-color: transparent;
  padding: 0px;
  min-width: 25px;
  box-shadow: none;
}

#chatting-room button.context-menu:hover:enabled {
  background-color: transparent;
  box-shadow: none;
}

#chatting-room button.context-menu:active {
  background-color: transparent;
  box-shadow: none;
}

#chatting-room .context-menu.icon {
  background-size: contain;
  height: 100%;
  background-size: 24px;
  top: -6px;
}

#chatting-room .context-menu.icon.minus {
  background-image: url(app/ext/ribbon/images/chats/minimize.svg);
  margin-left: 10px;
}

#chatting-room .context-menu.icon.times {
  background-image: url(app/ext/ribbon/images/chats/close.svg);
  margin-left: 10px;
}

#chatting-room .context-menu.icon.call {
  background-image: url(app/ext/ribbon/images/chats/call.svg);
  margin-left: 10px;
}

#chatting-room .context-menu.icon.share {
  background-image: url(app/ext/ribbon/images/chats/present_to_all.svg);
}

#chatting-room span.disabled {
  pointer-events: none;
  color: #CCCCCC;
}

#chatting-room .fa-phone.call {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

#chatting-room .fa-phone.terminate {
  -webkit-transform: rotate(225deg);
  -moz-transform: rotate(225deg);
  -ms-transform: rotate(225deg);
  -o-transform: rotate(225deg);
  transform: rotate(225deg);
}

#chatting-room .message-userinfo {
  margin-bottom: 3px;
  white-space: nowrap;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
}

#chatting-room .message-content-receive{
  font-size: 16px;
  border-radius: 0px 10px 10px 10px;
  background: #E9E9EB;
  border: #E9E9EB;
  padding: 8px 12px;
  margin-right: auto;
  position: relative;
  word-break: break-all;
  /* -moz-box-shadow: 0.15em 0.15em 0.2em rgba(0,0,0,0.2);
  -webkit-box-shadow: 0.15em 0.15em 0.2em rgba(0,0,0,0.2);
  box-shadow: 0.15em 0.15em 0.2em rgba(0,0,0,0.2); */
}

#chatting-room .message-content-send{
  font-size: 16px;
  border-radius: 10px 0px 10px 10px;
  background: #2595FB;
  border: #2595FB;
  padding: 8px 12px;
  margin-left: auto;
  position: relative;
  word-break: break-all;
  color: white;
  /* -moz-box-shadow: 0.15em 0.15em 0.2em rgba(0,0,0,0.2);
  -webkit-box-shadow: 0.15em 0.15em 0.2em rgba(0,0,0,0.2);
  box-shadow: 0.15em 0.15em 0.2em rgba(0,0,0,0.2); */
}

#chatting-room .message-input {
  height: 40px;
  margin:0;
  background: white;
  max-width: unset;
  border-radius: 4px;
  border: none;
  width: 280px;
  border: 1px solid #757575;
  padding: 0px 16px;
  font-size: 16px;
  border-radius: 8px;
}

#chatting-room .message-input:focus {
  outline: none;
  border: 1px solid #fafafa;
  -webkit-box-shadow: 0 0 6px #acacac;
  -moz-box-shadow: 0 0 5px #acacac;
  box-shadow: 0 0 5px #acacac;
}

#chatting-room .message-input::placeholder {
  color: #757575;
}

#chatting-room .btn-send {
  position: absolute;
  font-size: 0;
  right: 12px;
}

#chatting-room .send-btn-tooltip {
  margin-left: -20px !important;
  bottom: 125%;
  left: 50%;
}

#chatting-room .btn-send:hover .send-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#chatting-room .btn-send:focus .send-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#chatting-room .btn-image {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-image: url(app/ext/ribbon/images/chats/send.svg);
  background-repeat: no-repeat;
  background-size: contain;
  cursor: pointer;
  background-color: #156CD5;
  padding: 12px;
  background-position: center;
  background-size: 24px;
}

#chatting-room .btn-send-group {
  position: relative;
  padding: 5px 10px;
  width: 348px;
  border-top: 2px solid #fff;
  border-radius: 8px;
}

#chatting-room .avatar {
  position: relative;
  display: inline-block;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: visible;
  align-items: stretch;
  width: 36px !important;
  height: 36px !important;
  justify-content: center;
  border: none !important;
  border-radius: 18px !important;
}

#chatting-room .avatar .avatar-text {
  position: relative;
    display: flex;
    flex-direction: row;
    flex-grow: 0;
    flex-shrink: 0;
    overflow: hidden;
    align-items: center;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    justify-content: center;
    background-color: #757575;
    width: 100%;
    height: 100%;
}

#chatting-room .avatar .avatar-text .avatar-short-text {
  position: relative;
  display: inline;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: hidden;
  white-space: pre;
  text-overflow: ellipsis;
  font-size: 14px;
  color: white;
  font-weight: 600;
  background-color: rgba(0, 0, 0, 0);
  cursor: inherit;
}

.chatting_room_text_white_color{
  color: white !important;
  font-size: 14px !important;
  font-weight: 400 !important;
}

#chatting-room .avatar span {
  position: absolute;
  bottom: -6px;
  right: -6px;
  font-size: 12px;
  border: 3px solid #156CD5;
  border-radius: 50%;
}

#chatting-room .user-state {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: auto;
}

#chatting-room .user-state p {
  color: #7F7F7F;
  padding: 5px 12px;
  font-size: 16px;
  line-height: 1;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#chatting-room .username {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  overflow: hidden;
  align-self: stretch;
  justify-content: center;
  margin-left: 10px;
  width: 120px;
}

#chatting-room .username p {
  overflow: hidden;
  white-space: pre;
  text-overflow: ellipsis;
  font-size: 18px;
  margin-bottom: 0px;
  color: #222124;
  font-weight: bold;
  line-height: 18px;
}

#chatting-room .chat {
  display: flex;
  flex-direction: column;
  padding: 10px;
}

#chatting-room .messages {
  display: flex;
  flex-direction: column;
}

#chatting-room .message {
  border-radius: 8px;
  padding: 8px 15px;
  margin-top: 3px;
  margin-bottom: 3px;
  display: inline-block;
  overflow-wrap: anywhere;
}

#chatting-room .yours {
  align-items: flex-start;
}

#chatting-room .yours .message {
  margin-right: 25%;
  background-color: #F1F1F1;
  position: relative;
  color: var(--text-color);
  text-align: left;
}

#chatting-room .yours .message.last:before {
  position: absolute;
  z-index: 0;
  bottom: 0;
  left: -7px;
  height: 20px;
  width: 20px;
  background: #eee;
  border-bottom-right-radius: 15px;
}
#chatting-room .yours .message.last:after {
  content: "";
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: -10px;
  width: 10px;
  height: 20px;
  background: white;
  border-bottom-right-radius: 10px;
}

#chatting-room .mine {
  align-items: flex-end;
}

#chatting-room .mine .message {
  color: white;
  margin-left: 25%;
  background-attachment: fixed;
  position: relative;
  text-align: left;
  padding-right: 25px;
  background-color: #156CD5;
}

#chatting-room .mine .message.last:before {
  position: absolute;
  z-index: 0;
  bottom: 0;
  right: -8px;
  height: 20px;
  width: 20px;
  background: linear-gradient(to bottom, #00D0EA 0%, #0085D1 100%);
  background-attachment: fixed;
  border-bottom-left-radius: 15px;
}

#chatting-room .mine .message.last:after {
  content: "";
  position: absolute;
  z-index: 1;
  bottom: 0;
  right: -10px;
  width: 10px;
  height: 20px;
  background: white;
  border-bottom-left-radius: 10px;
}

#chatting-room .message-state {
  height: 15px;
  width: 15px;
  right: 8px;
  bottom: 8px;
  position: absolute;
  z-index: 10;
}

#chatting-room .message-state.unread {
  color: #173040;
}

#chatting-room .message-state.read {
  color: #23f12a;
}

#chatting-room .close-btn-tooltip {
  margin-left:-25px !important;
  bottom: 125%;
  left: 50%;
}

#chatroom-close-btn:hover>.close-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#chatroom-close-btn:focus>.close-btn-tooltip {
  visibility: visible;
  opacity: 1;
}