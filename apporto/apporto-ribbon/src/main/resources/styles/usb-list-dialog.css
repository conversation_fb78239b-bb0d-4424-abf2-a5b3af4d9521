/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.usb-list-dialog {
    background-color: white;
    overflow: visible;
    position: relative;
    width: 470px;
    min-width: 470px;
    min-height: 235px;
    border-radius: 6px;
}

.usb-list-dialog::before {
    content: "";
    background: linear-gradient(to right,#00dcff,#00f);
    position: absolute;
    height: 5px;
    top: -5px;
    left: 4px;
    right: 4px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

#dlg-usb-list>* {
    margin: initial;
}

/* The Close Button */
.usb-list-dialog span.close {
    color: #808080;
    cursor: pointer !important;
    float: right;
    font-weight: bold;
    font-size: 28px;
    line-height: 25px;
    position: absolute;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
}

.title-usb-list {
    margin-block-end: 0px;
    margin-top: 15px;
}

.dialog .usb-dlg-footer {
    text-align: right;
    margin: 10px !important;
    margin-top: 23px !important;
}

.dialog .usb-dlg-footer button {
    margin-bottom: 15px;
}

#selected-usb {
    width: 405px;
    min-width: 405px;
    border-radius: 2em;
    height: 2.3em;
    outline: none;
    text-align: center;
}

#usb-form {
    display: inline-flex;
    align-items: center;
    text-align: center;
}

.status-form {
    margin-top: 20px;
    display: inline-flex;
    align-items: center;
}

.status-form span {
    display: inline-block;
    overflow-wrap: break-word;
    white-space: normal;
}

.usb-connect-status {
    width: 375px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    color: blue;
    font-weight: bold;
    white-space: nowrap;
}

.usb-disconnect-status {
    width: 375px;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    color: #EB0000;
    font-weight: bold;
    white-space: nowrap;
}

.usb-title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.usb-subtitle {
    display: flex;
    align-items: center;
    justify-content: center;
}

.fetch-usb-list {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    margin-left: 5px;
}

button.btn-fetch {
    background-image: url(app/ext/ribbon/images/circle-arrows-blue.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-color: white;
    box-shadow: none;
    border-radius: 5px;
    min-width: unset;
    font-size: 0;
    height: 27px;
    width: 27px;
    position: relative;
}

.text-noselect {
    -webkit-touch-callout: none; /* iOS Safari */
      -webkit-user-select: none; /* Safari */
       -khtml-user-select: none; /* Konqueror HTML */
         -moz-user-select: none; /* Old versions of Firefox */
          -ms-user-select: none; /* Internet Explorer/Edge */
              user-select: none; /* Non-prefixed version, currently
                                    supported by Chrome, Edge, Opera and Firefox */
}

#move-usb-dlg {
    cursor: move;
}