/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
 
 .file-explorer {
    width: 75vh;
    min-width: 722px;
    min-height: 431px;
    border-radius: 6px;
    position: relative;
}

.file-explorer::before {
    content: "";
    position: absolute;
    height: 5px;
    top: -5px;
    left: 4px;
    right: 4px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

.msg-middle {
	position: absolute;
    left: 0px;
    top: 50%;
    width: 100%;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
}

.file-explorer .menu-content {
    max-height: 500px;
}

.file-explorer.dialog>* {
	margin: 0;
}

.file-explorer.dialog .header h2 {
    font-size: 1.3em;
    text-transform: none;
}

.file-explorer.dialog .header {
    -ms-flex-align:      center;
    -moz-box-align:      center;
    -webkit-box-align:   center;
    -webkit-align-items: center;
    align-items:         center;
    margin-top:			  -25px;
}

.file-explorer.dialog .menu-body {
    padding: 0.25em;
}

.file-explorer.dialog .header.breadcrumbs {
    display: block;
    background: rgba(0,0,0,0.0125);
    box-shadow: none;
    margin-top: 0;
    border-top: none;
    text-align: left;
    width: 100%;
    padding: 0px 24px !important;
    border-bottom: 0 solid #edebeb !important;
    background: white !important;
}

.file-explorer.dialog .header.breadcrumbs .breadcrumb {
    color: #22538F;
    display: inline-block;
    font-size: 1.1em;
    font-weight: bold;
    padding: 7px 7px 7px 3px;
    border-radius: 4px;
    margin: 0px;

    min-width: auto !important;
    background-color: rgb(0 0 0 / 0%) !important;
    box-shadow: none !important;
    text-align: left !important;
}

.file-explorer.dialog .header.breadcrumbs .breadcrumb:hover {
    background-color: #ebebeb !important;
    cursor: pointer;
}

.file-explorer.dialog .header.breadcrumbs .breadcrumb.root {
    padding: 7px 7px 7px 7px;
}

.file-browser .directory>.caption .icon {
    background-image: url(app/ext/ribbon/images/folder.png);
}

.file-browser .directory>.caption .icon.disabled {
    background-image: url(app/ext/ribbon/images/folder-grey.png);
}

.file-explorer div.footer {
	padding: 10px 24px;
    display: flex;
    margin-top: 34px;
    gap: 10px;
}


.assignment-management-dialog .file-explorer div.footer{
    padding-left: 0px;
}

.file-explorer button {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
    cursor: pointer;
}
.file-explorer button.close {
    width: 25px;
    height: 25px;
    margin: 0px;
    font-size: 28px;
}

button.upload.button:disabled {
    opacity: .2;
}

.file-explorer div.line {
    border-top: 2px solid rgba(0,0,0,0.125);
    margin: 0 1em;
}

.file-explorer.dialog .header.breadcrumbs .breadcrumb .next {
    background-image: url('app/ext/ribbon/images/next.png');
    background-size:         0.75em 0.75em;
    -moz-background-size:    0.75em 0.75em;
    -webkit-background-size: 0.75em 0.75em;
    -khtml-background-size:  0.75em 0.75em;
    background-repeat: no-repeat;
    background-position: center center;
    width: 1em;
    height: 1em;
    padding: 0;
    vertical-align: middle;
    display: inline-block;
}

.file-explorer.dialog .header.breadcrumbs .breadcrumb .directory {
    display: inline-block;
    padding-left: 0.5em;
    font-weight: bold;
}

.file-explorer.dialog .header.breadcrumbs .instruction { 
    font-size: 1.1em;
    margin-top: 1vh;
    word-break: break-word;
    text-align: center;
    white-space: pre-line;
}

input[type="submit"]:disabled, button:disabled, button.danger:disabled {
	background-color: #c6c6c6;
}

.menu-body .circle-loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    opacity: .25;
    /* top: 50%; */
    margin: 0;
    position: absolute;
    top: 40%;
    left: 46%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

circle-loader.menu-body.file {
	background-color: rgba(1, 1, 1, 0.7);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
}


.fname {
	overflow: hidden;
	width: 60%;
	text-align: left;
    cursor: pointer;
    padding-left: 7px;
}

.fsize {
	overflow: hidden;
	width: 15%;
	text-align: center;
	cursor: pointer;
}

.fmodify {
	overflow: hidden;
	width: 15%;
	text-align: center;
	cursor: pointer;
}


.upload.button:disabled::after, .upload.button:disabled::before {
    position: absolute;
    display: none;
}

.upload.button:disabled::after {
    content: attr(data-tooltip);
    top: -25px;
    left: -5px;
    background: #ffe4b5;
    border-radius: 4px;
    padding: 2px 6px;
    white-space: nowrap;
    color: black;
}

.upload.button:disabled::before {
    border-left: 5px solid transparent;
    border-top: 5px solid #ffe4b5;
    border-right: 5px solid transparent;
    height: 0px;
    width: 0px;
    content: '';
    top: -2px;
    left: 6px;

    display: none;
}

.upload.button:disabled::after {
    content: attr(data-tooltip);
    top: -25px;
    left: -5px;
    background: #ffe4b5;
    border-radius: 4px;
    padding: 2px 6px;
    white-space: nowrap;
    color: black;
}

.upload.button:disabled::before {
    border-left: 5px solid transparent;
    border-top: 5px solid #ffe4b5;
    border-right: 5px solid transparent;
    height: 0px;
    width: 0px;
    content: '';
    top: -2px;
    left: 6px;
}

.upload.button:disabled:hover::after, .upload.button:disabled:hover::before {
    display: block;
}

.colorLoader {
    background-color: rgba(1, 1, 1, 0.7);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
}

.transfer .filename {
    font-family: Lato;
    width: 330px;
    max-width: 90%;
    left: 21px;
    margin-top: 1px;
    margin-left: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#fileDownLoadTitle{
    color: #1a1a1a;
    font-family: Lato;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: .24px;
    padding-left: 24px;
    padding-top: 24px;
    padding-bottom: 8px;
}

#titleHeader{
    text-align: start;
    border: 0px;
    box-shadow: none;
    background: transparent;
}

#instructionText{
    text-align: start;
    padding-top: 0;
    margin: 0;
    line-height: 6px;
    height: 20px;
}

#line-gap{
    width: 100%;
    height: 1px;
    background: #edebeb;
    margin: 16px 0px 16px 0px;
    border: 0.1px solid #edebeb;
}

#menuBody{
    margin-left: 20px;
    margin-right: 20px;
    padding-left: 0px;
    min-height: 164px !important;
}

.assignment-management-dialog #menuBody{
    margin-left: 0px;
    max-height: 400px;
}

#download_btn_inFileExplorer{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 12px !important;
    gap: 8px;
}

#download_file_btn{
    background-image: url('app/ext/ribbon/images/cloud_download_icon.svg');
    background-repeat: no-repeat;
    background-position: 12px;
    padding-left: 42px;
    background-size: 25px;
    background-color: #22538F;
    border-radius: 4px;
}

#download_file_btn:hover{
    background-color: #1e63b7;
}

.disable_download_btn{
    background-color: #EDEBEB !important;
    cursor: not-allowed !important;
    color: #C7C7C7 !important;
}


#sharing_screen_text{
    position: absolute;
    top: 0px;
    left: 50%;
    padding: 4px 16px;
    transform: translateX(-50%);
    border-radius: 0px 0px 8px 8px;
    border-right: 1px solid #038C21;
    border-bottom: 1px solid #038C21;
    border-left: 1px solid #038C21;
    background: #EDFEEC;
    color: #053003;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.close.file {
    position: relative;
    width: 25px;
    height: 25px;
    margin: 0px;
    font-size: 28px;
    cursor: pointer;
}
  
.dialog-tooltip {
    font-size: 14px;
    font-weight: 400;
    visibility: hidden;
    background-color: #323c47;
    border: 1px solid #ccc;
    color: #ccc;
    text-align: center;
    border-radius: 5px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 150%; /* Position above the button */
    left: 50%;
    margin-left: -30px; /* Center the tooltip */
    opacity: 0;
    transition: opacity 0.3s;
    width: 60px; /* Set a fixed width for better appearance */
}

.close-tooltip::after {
    content: '';
    position: absolute;
    top: 100%; /* Bottom of the tooltip */
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #555 transparent transparent transparent; /* Arrow pointing down */
}

.file-explorer .close.file:hover .close-tooltip {   
    visibility: visible;
    opacity: 1;
}

.file-explorer .close.file:focus .close-tooltip{
    visibility: visible;
    opacity: 1;
}

.assignment-management-dialog .file-explorer{
	height: auto;
    min-height: 350px;
    width: 650px;
    min-width:650px;
}

.assignment-management-dialog .file-explorer .menu-content {
	height: auto;
    max-height: 400px;
} 

.assignment-management-dialog .file-browser .dialog {
    box-shadow: none;
}

.assignment-management-dialog .file-explorer.dialog {
    box-shadow: none;
    margin-left: 0px;
}

.assignment-management-dialog .file-explorer.dialog .header.breadcrumbs {
    border-bottom: none;
    box-shadow: none;
}

.file-checkbox {
    margin-right: 8px;
    cursor: pointer;
}

.file-checkbox[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}

.file-explorer .submit-loader-container {
    width: 200px;
    height: 4px;
    position: relative;
    overflow: hidden;
}

.file-explorer .submit-loader {
    width: 100%;
    height: 100%;
    background-color: lightblue;
    position: relative;
}

.file-explorer .submit-progress {
    width: 40px;
    height: 100%;
    background-color: #22538f;
    position: absolute;
    animation: moveLoader 3s infinite cubic-bezier(0.4, 0.0, 0.2, 1);
}

@keyframes moveLoader {
    0% { transform: translateX(-80px); }
    100% { transform: translateX(200px); }
}