/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.classroom-dialog {
    background-color: rgb(224, 232, 243);
    width: 40vw;
    overflow: visible;
    border-radius: 6px;
    position: relative;
}

#dlg-classroom>* {
    margin: initial;
}

/* The Close Button */
.classroom-dialog span.close {
    color: #808080;
    cursor: default;
    float: right;
    font-weight: bold;
    line-height: 25px;
    position: relative;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
}

.classroom-dialog #content {
    display: -ms-flexbox;
    -ms-flex-align: stretch;
    -ms-flex-direction: column;
    display: -moz-box;
    -moz-box-align: stretch;
    -moz-box-orient: vertical;
    display: -webkit-box;
    -webkit-box-align: stretch;
    -webkit-box-orient: vertical;
    display: -webkit-flex;
    -webkit-align-items: stretch;
    -webkit-flex-direction: column;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}

.classroom-dialog #content li {
	align-items: center;
    display: flex;
}

.classroom-dialog #content span {
	font-size: 14px;
	font-weight: bold;
    width: 10em;
    text-align: left;
}

.classroom-dialog #content a {
	color: black;
    font-weight: initial;
    margin-left: 5px;
    margin-right: 20px;
    text-decoration: none;
    overflow: hidden;
}

.classroom-dialog #content button {
    background-color: rgba(54, 162, 235, 0.4);
    border: 0;

    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 12px;

    -moz-box-shadow: 1px 1px 3px #000;
    -webkit-box-shadow: 1px 1px 3px #000;
    box-shadow: 1px 1px 3px #000;

    color: black;
    margin-left: auto;
    text-shadow: none;
    width: 7vw;
}

.classroom-dialog #content button:hover {
    background-color: rgba(54,162,235,0.2);
}

li {
    text-align: left;
}

.subtitle-classroom {
	margin-block-start: 1px;
    margin-top: 10px;
	font-weight: normal;
}

.title-classroom {
	margin-block-end: 0px;
}

.list-classroom {
list-style-type: none;
}

a.disabled {
  pointer-events: none;
  cursor: default;
  text-decoration: none;
}

.label-classroom {
  display: block;
  width: 95%;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
  padding-left: 5%;
}

div.footer {
    padding: 10px;
    padding-top: 20px;
}

.dialog .footer {
    text-align: right;
}

div.line {
    border-top: 1px solid rgba(0,0,0,0.125);
    margin: 0 1em;
}

.viewOnly.classroom {
	width: 8em;
    height: 2em;
    border-radius: 12px;
}

#selectedGroupClassroom {
    width: 240px;
    min-width: 240px;
    text-align-last: center;
    border-radius: 2em;
    height: 2.3em;
    outline: none;
}

a.btn-classroom-mandatory.ribbon-button.ng-scope[disabled='disabled'] {
    pointer-events: none;
    background-color: transparent;
    opacity: .1;
}
