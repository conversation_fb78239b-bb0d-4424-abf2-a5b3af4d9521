/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.chart-dialog.dialog {
    width: 890px;
    overflow: visible;
	background: #f8fafc;
    border-radius: 6px;
    position: relative;
}

.chart-dialog.dialog>* {
	margin: initial;
}

/* The Close Button */
.close {
    color: #808080;
    cursor: pointer !important;
    float: right;
    font-weight: bold;
    font-size: 28px;
    line-height: 25px;
    position: relative;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
    background-color: transparent;
    padding: 0px;
    margin: 0px;
    min-width: 25px;
    box-shadow: none;
}

button.close:hover:enabled {
    background-color: transparent;
    box-shadow: none;
}

button.close:active {
    background-color: transparent;
    box-shadow: none;
}

.enable-close {
    color: #808080;
    cursor: pointer !important;
    float: right;
    font-weight: bold;
    font-size: 28px;
    line-height: 25px;
    position: relative;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
    background-color: transparent;
    padding: 0px;
    margin: 0px;
    min-width: 25px;
    box-shadow: none;
}

button.enable-close:hover:enabled {
    background-color: transparent;
    box-shadow: none;
}

button.enable-close:active {
    background-color: transparent;
    box-shadow: none;
}

div#summaryChartLegend {
    padding: 12px 0 0;
    text-align: center;
}

div#summaryChartLegend ul {
    display: flex;
    list-style: none;
    padding-left: 0px;
    box-shadow: none;
    padding: 0px 52px;
}

#summaryChartLegend ul li {
    display: inline-block;
    margin-right: 35px;
    cursor: pointer;
    border: 0px;
    width: max-content;
}

#summaryChartLegend ul li:last-child {
    margin-right: 0;
}

#summaryChartLegend ul li canvas.bar{
    width: 40px;
    height: 15px;
    display: inline-block;
    vertical-align: -3px;
    margin-right: 5px;
    border-radius: 4px;	
}

.bar-red {
	background-color: rgba(200, 60, 60, 0.7);
}

.bar-blue {
	background-color: rgba(45, 100, 200, 0.7);
}

#summaryChartLegend ul li span.line{
    width: 40px;
    height: 2px;
    display: inline-block;
    vertical-align: 4px;
    margin-right: 5px;
}

.line-blue {
	background-color: #5a90cf;
}

.line-dashed {
    border: dashed 2px #78c88c;
}

#summaryChartLegend ul li.inactive {
	opacity: 0.3;
}

#summaryChartLegend ul li span:last-child {
    cursor: pointer;
}

.beta-notice {
    font-size: 0.875em;
    padding: 5px 0 10px;
}

.beta-notice span:first-child {
	float: left;
	margin-left: 20px;
}

.beta-notice span:nth-child(2) {
	float: right;
}

h1.title-analytics {
    font-size: 1.5em;
}

.canvasParentElem {
    position: relative;
    display: block;
}

#summaryChart {
    top: 0px;
    left: 0px;
}

.canvasImg {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

#chartCateg{
    display: flex;
    gap: 20px;
    margin-inline: 50px;
    position: relative;
}

.chartUnderLine{
    background: black;
    width: 70px;
    height: 2px;
    position: absolute;
    bottom: -6px;
    left: -8px;
    transition: all 0.5s;
}

.onDurationCateg{
    width: 80px;
    left: 70px;
}