/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.ribbon-share-dialog {
    width: 35vw;
    overflow: visible;
    border-radius: 8px;
    position: relative;
    min-width: max-content;
    padding: 32px;
}

#dlg-share>* {
    margin: initial;
}

/* The Close Button */
.ribbon-share-dialog  .enable-close {
    height: 24px;
    background-image: url(app/ext/ribbon/images/share_close.svg) !important;
    background-size: 24px !important;
    background-repeat: no-repeat !important;
}

.ribbon-share-dialog .enable-close:hover .close-tooltip{
    visibility: visible;
    opacity: 1;
}

.ribbon-share-dialog .enable-close:focus .close-tooltip{
    visibility: visible;
    opacity: 1;
}

.disable-close {
    cursor: not-allowed!important;
}

.ribbon-share-dialog #content {
    display: -ms-flexbox;
    -ms-flex-align: stretch;
    -ms-flex-direction: column;
    display: -moz-box;
    -moz-box-align: stretch;
    -moz-box-orient: vertical;
    display: -webkit-box;
    -webkit-box-align: stretch;
    -webkit-box-orient: vertical;
    display: -webkit-flex;
    -webkit-align-items: stretch;
    -webkit-flex-direction: column;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}

.ribbon-share-dialog #content li {
    align-items: center;
    display: flex;
}

.ribbon-share-dialog #content span {
    font-size: 14px;
    font-weight: bold;
    width: 10em;
    text-align: left;
}

.ribbon-share-dialog #content a {
    color: black;
    font-weight: initial;
    margin-left: 5px;
    margin-right: 20px;
    text-decoration: none;
    overflow: hidden;
}

.ribbon-share-dialog #content button {
    background-color: rgba(54, 162, 235, 0.4);
    border: 0;

    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;

    color: black;
    margin-left: auto;
    text-shadow: none;
    width: 7vw;
}

.ribbon-share-dialog #content button:hover {
    background-color: rgba(54,162,235,0.2);
}

li {
    text-align: left;
}

.subtitle-share {
    margin-block-start: 1px;
    font-weight: normal;
    text-align: left;
}

.subtitle-collaborate-share {
    margin-block-start: 1px;
    font-weight: normal;
    font-size: 13px;
}

.title-share {
    margin-block-end: 0px;
    text-transform: unset;
    text-align: left;
    padding: 0px;
    margin-bottom: 8px !important;
}

.list-share {
list-style-type: none;
}

span.disabled {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
    width: 286px;
    color: #777;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}

.label-share {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-align: left;
  text-overflow: ellipsis;
}

.ribbon-share-dialog .label-share {
    display: flex;
    flex-direction: row;
    white-space: normal;
    padding: 24px;
    border-radius: 8px;
    border: 2px solid #C7C7C7;
    justify-content: space-between;
}

.label-padding {
    width: max-content;
    text-align: center;
}

div.footer {
    padding: 10px;
    padding-top: 20px;
}

.dialog .footer {
    text-align: right;
}

div.line {
    border-top: 1px solid rgba(0,0,0,0.125);
    margin: 0 1em;
}

.viewOnly.share{
    border-radius: 8px;
    background: #22538F;
    padding: 12px;
    padding-left: 44px;
    height: max-content;
    width: max-content;
    background-image: url(app/ext/ribbon/images/link.svg);
    background-size: 24px;
    background-repeat: no-repeat;
    background-position-y: center;
    background-position-x: 12px;
    cursor: pointer;
    margin: 0px;
}

.show_bg_in_btn{
    background-image: none !important;
    padding-left: 12px !important;
}

.viewOnly.share:hover{
    border-radius: 8px;
}

.collaborateOffline.share {
    background-color: #22538F;
    border-radius: 8px;
    padding: 12px;
    height: max-content;
    width: max-content;
    margin: 0px;
}

.collaboration.select {
    background-color: #22538F;
    border-radius: 8px;
    padding: 12px;
    height: max-content;
    width: max-content;
}

.collaborateOffline.share:hover,
.collaboration.select:hover {
    background-color: rgba(26, 87, 158, 0.8);
    border-radius: 8px;
}

input[type="submit"], button.share, a.button {
    background-color: #22538F;
    color: white;
    border: 0;
    height: 2em;
    text-shadow: none;
}

input[type="submit"]:hover:enabled, button.share:hover:enabled, a.button:hover:enabled {
    background-color: rgba(74, 144, 226, 0.8);
}

input[type="submit"]:disabled, button.share:disabled, button.danger:disabled {
    background-color: rgba(74, 144, 226, 1);
}

.selected_share_url{
    border: 2px solid #22538F !important;
    background: #F0F6FF !important;
}

.selected_share_url .disabled{
    color: #22538F;
}

.warning_mez_text{
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    color: #777;
}

.disable_text .disable_url{
    background: #FFF !important;
}

.disable_url span{
    color: #C7C7C7 !important;
}

.disable_text span{
    color: #C7C7C7 !important;
}

.ribbon-share-dialog .footer .stop{
    background-image: none;
    padding: 12px;
}