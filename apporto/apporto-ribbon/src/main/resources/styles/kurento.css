@font-face {
  font-family: 'Baumans';
  font-style: normal;
  font-weight: 400;
  src: local("Baumans Regular"), local("Baumans-Regular"), url(app/ext/ribbon/styles/webfonts/baumans.ttf) format("truetype");
}

@font-face {
  font-family: 'Muli';
  font-style: normal;
  font-weight: 400;
  src: local("Muli Regular"), local("Muli-Regular"), url(app/ext/ribbon/styles/webfonts/muli.ttf) format("truetype");
}

@font-face {
  font-family: 'kurentoFontAwesomeBrands';
  font-style: normal;
  font-weight: normal;
  src: url("app/ext/ribbon/styles/webfonts/fa-brands-400.eot");
  src: url("app/ext/ribbon/styles/webfonts/fa-brands-400.eot?#iefix") format("embedded-opentype"), url("app/ext/ribbon/styles/webfonts/fa-brands-400.woff2") format("woff2"), url("app/ext/ribbon/styles/webfonts/fa-brands-400.woff") format("woff"), url("app/ext/ribbon/styles/webfonts/fa-brands-400.ttf") format("truetype"), url("app/ext/ribbon/styles/webfonts/fa-brands-400.svg#fontawesome") format("svg");
}

@font-face {
  font-family: 'kurentoFontAwesomeRegular';
  font-style: normal;
  font-weight: 400;
  src: url("app/ext/ribbon/styles/webfonts/fa-regular-400.eot");
  src: url("app/ext/ribbon/styles/webfonts/fa-regular-400.eot?#iefix") format("embedded-opentype"), url("app/ext/ribbon/styles/webfonts/fa-regular-400.woff2") format("woff2"), url("app/ext/ribbon/styles/webfonts/fa-regular-400.woff") format("woff"), url("app/ext/ribbon/styles/webfonts/fa-regular-400.ttf") format("truetype"), url("app/ext/ribbon/styles/webfonts/fa-regular-400.svg#fontawesome") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'kurentoFontAwesomeSolid';
  font-style: normal;
  font-weight: 900;
  src: url("app/ext/ribbon/styles/webfonts/fa-solid-900.eot");
  src: url("app/ext/ribbon/styles/webfonts/fa-solid-900.eot?#iefix") format("embedded-opentype"), url("app/ext/ribbon/styles/webfonts/fa-solid-900.woff2") format("woff2"), url("app/ext/ribbon/styles/webfonts/fa-solid-900.woff") format("woff"), url("app/ext/ribbon/styles/webfonts/fa-solid-900.ttf") format("truetype"), url("app/ext/ribbon/styles/webfonts/fa-solid-900.svg#fontawesome") format("svg");
}

.far {
  font-family: 'kurentoFontAwesomeRegular' !important;
  font-weight: 400;
}

.fa,
.fas {
  font-family: 'kurentoFontAwesomeSolid' !important;
  font-weight: 900;
}

.fab {
  font-family: 'kurentoFontAwesomeBrands';
}

.fa,
.far,
.fas,
.fab {
  display: inline-block;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa,
.fas,
.far,
.fal,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -.0667em;
}

.fa-xs {
  font-size: .75em;
}

.fa-sm {
  font-size: .875em;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}

.fa-ul>li {
  position: relative;
}

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit;
}

.fa-border {
  border: solid 0.08em #eee;
  border-radius: .1em;
  padding: .2em .25em .15em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: .3em;
}

.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: .3em;
}

.fa-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  animation: fa-spin 1s infinite steps(8);
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(1, -1);
}

.fa-flip-horizontal.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  transform: scale(-1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  filter: none;
}

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2em;
}

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

.fa-angle-down {
  background-image: url(app/ext/ribbon/images/chats/arrow_drop_down.svg);
  background-size: 24px;
  width: 24px;
  height: 24px;
  background-position: center;
  filter: brightness(0);
}

.fa-angle-right {
  transform: rotate(-90deg) !important;
}


/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
.fa-500px:before {
  content: "\f26e";
}

.fa-accessible-icon:before {
  content: "\f368";
}

.fa-accusoft:before {
  content: "\f369";
}

.fa-ad:before {
  content: "\f641";
}

.fa-address-book:before {
  content: "\f2b9";
}

.fa-address-card:before {
  content: "\f2bb";
}

.fa-adjust:before {
  content: "\f042";
}

.fa-adn:before {
  content: "\f170";
}

.fa-adversal:before {
  content: "\f36a";
}

.fa-affiliatetheme:before {
  content: "\f36b";
}

.fa-air-freshener:before {
  content: "\f5d0";
}

.fa-algolia:before {
  content: "\f36c";
}

.fa-align-center:before {
  content: "\f037";
}

.fa-align-justify:before {
  content: "\f039";
}

.fa-align-left:before {
  content: "\f036";
}

.fa-align-right:before {
  content: "\f038";
}

.fa-alipay:before {
  content: "\f642";
}

.fa-allergies:before {
  content: "\f461";
}

.fa-amazon:before {
  content: "\f270";
}

.fa-amazon-pay:before {
  content: "\f42c";
}

.fa-ambulance:before {
  content: "\f0f9";
}

.fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

.fa-amilia:before {
  content: "\f36d";
}

.fa-anchor:before {
  content: "\f13d";
}

.fa-android:before {
  content: "\f17b";
}

.fa-angellist:before {
  content: "\f209";
}

.fa-angle-double-down:before {
  content: "\f103";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-angle-double-up:before {
  content: "\f102";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-angle-up:before {
  content: "\f106";
}

.fa-angry:before {
  content: "\f556";
}

.fa-angrycreative:before {
  content: "\f36e";
}

.fa-angular:before {
  content: "\f420";
}

.fa-ankh:before {
  content: "\f644";
}

.fa-app-store:before {
  content: "\f36f";
}

.fa-app-store-ios:before {
  content: "\f370";
}

.fa-apper:before {
  content: "\f371";
}

.fa-apple:before {
  content: "\f179";
}

.fa-apple-alt:before {
  content: "\f5d1";
}

.fa-apple-pay:before {
  content: "\f415";
}

.fa-archive:before {
  content: "\f187";
}

.fa-archway:before {
  content: "\f557";
}

.fa-arrow-alt-circle-down:before {
  content: "\f358";
}

.fa-arrow-alt-circle-left:before {
  content: "\f359";
}

.fa-arrow-alt-circle-right:before {
  content: "\f35a";
}

.fa-arrow-alt-circle-up:before {
  content: "\f35b";
}

.fa-arrow-circle-down:before {
  content: "\f0ab";
}

.fa-arrow-circle-left:before {
  content: "\f0a8";
}

.fa-arrow-circle-right:before {
  content: "\f0a9";
}

.fa-arrow-circle-up:before {
  content: "\f0aa";
}

.fa-arrow-down:before {
  content: "\f063";
}

.fa-arrow-left:before {
  content: "\f060";
}

.fa-arrow-right:before {
  content: "\f061";
}

.fa-arrow-up:before {
  content: "\f062";
}

.fa-arrows-alt:before {
  content: "\f0b2";
}

.fa-arrows-alt-h:before {
  content: "\f337";
}

.fa-arrows-alt-v:before {
  content: "\f338";
}

.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

.fa-asterisk:before {
  content: "\f069";
}

.fa-asymmetrik:before {
  content: "\f372";
}

.fa-at:before {
  content: "\f1fa";
}

.fa-atlas:before {
  content: "\f558";
}

.fa-atom:before {
  content: "\f5d2";
}

.fa-audible:before {
  content: "\f373";
}

.fa-audio-description:before {
  content: "\f29e";
}

.fa-autoprefixer:before {
  content: "\f41c";
}

.fa-avianex:before {
  content: "\f374";
}

.fa-aviato:before {
  content: "\f421";
}

.fa-award:before {
  content: "\f559";
}

.fa-aws:before {
  content: "\f375";
}

.fa-backspace:before {
  content: "\f55a";
}

.fa-backward:before {
  content: "\f04a";
}

.fa-balance-scale:before {
  content: "\f24e";
}

.fa-ban:before {
  content: "\f05e";
}

.fa-band-aid:before {
  content: "\f462";
}

.fa-bandcamp:before {
  content: "\f2d5";
}

.fa-barcode:before {
  content: "\f02a";
}

.fa-bars:before {
  content: "\f0c9";
}

.fa-baseball-ball:before {
  content: "\f433";
}

.fa-basketball-ball:before {
  content: "\f434";
}

.fa-bath:before {
  content: "\f2cd";
}

.fa-battery-empty:before {
  content: "\f244";
}

.fa-battery-full:before {
  content: "\f240";
}

.fa-battery-half:before {
  content: "\f242";
}

.fa-battery-quarter:before {
  content: "\f243";
}

.fa-battery-three-quarters:before {
  content: "\f241";
}

.fa-bed:before {
  content: "\f236";
}

.fa-beer:before {
  content: "\f0fc";
}

.fa-behance:before {
  content: "\f1b4";
}

.fa-behance-square:before {
  content: "\f1b5";
}

.fa-bell:before {
  content: "\f0f3";
}

.fa-bell-slash:before {
  content: "\f1f6";
}

.fa-bezier-curve:before {
  content: "\f55b";
}

.fa-bible:before {
  content: "\f647";
}

.fa-bicycle:before {
  content: "\f206";
}

.fa-bimobject:before {
  content: "\f378";
}

.fa-binoculars:before {
  content: "\f1e5";
}

.fa-birthday-cake:before {
  content: "\f1fd";
}

.fa-bitbucket:before {
  content: "\f171";
}

.fa-bitcoin:before {
  content: "\f379";
}

.fa-bity:before {
  content: "\f37a";
}

.fa-black-tie:before {
  content: "\f27e";
}

.fa-blackberry:before {
  content: "\f37b";
}

.fa-blender:before {
  content: "\f517";
}

.fa-blind:before {
  content: "\f29d";
}

.fa-blogger:before {
  content: "\f37c";
}

.fa-blogger-b:before {
  content: "\f37d";
}

.fa-bluetooth:before {
  content: "\f293";
}

.fa-bluetooth-b:before {
  content: "\f294";
}

.fa-bold:before {
  content: "\f032";
}

.fa-bolt:before {
  content: "\f0e7";
}

.fa-bomb:before {
  content: "\f1e2";
}

.fa-bone:before {
  content: "\f5d7";
}

.fa-bong:before {
  content: "\f55c";
}

.fa-book:before {
  content: "\f02d";
}

.fa-book-open:before {
  content: "\f518";
}

.fa-book-reader:before {
  content: "\f5da";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-bowling-ball:before {
  content: "\f436";
}

.fa-box:before {
  content: "\f466";
}

.fa-box-open:before {
  content: "\f49e";
}

.fa-boxes:before {
  content: "\f468";
}

.fa-braille:before {
  content: "\f2a1";
}

.fa-brain:before {
  content: "\f5dc";
}

.fa-briefcase:before {
  content: "\f0b1";
}

.fa-briefcase-medical:before {
  content: "\f469";
}

.fa-broadcast-tower:before {
  content: "\f519";
}

.fa-broom:before {
  content: "\f51a";
}

.fa-brush:before {
  content: "\f55d";
}

.fa-btc:before {
  content: "\f15a";
}

.fa-bug:before {
  content: "\f188";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-bullhorn:before {
  content: "\f0a1";
}

.fa-bullseye:before {
  content: "\f140";
}

.fa-burn:before {
  content: "\f46a";
}

.fa-buromobelexperte:before {
  content: "\f37f";
}

.fa-bus:before {
  content: "\f207";
}

.fa-bus-alt:before {
  content: "\f55e";
}

.fa-business-time:before {
  content: "\f64a";
}

.fa-buysellads:before {
  content: "\f20d";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-calendar:before {
  content: "\f133";
}

.fa-calendar-alt:before {
  content: "\f073";
}

.fa-calendar-check:before {
  content: "\f274";
}

.fa-calendar-minus:before {
  content: "\f272";
}

.fa-calendar-plus:before {
  content: "\f271";
}

.fa-calendar-times:before {
  content: "\f273";
}

.fa-camera:before {
  content: "\f030";
}

.fa-camera-retro:before {
  content: "\f083";
}

.fa-cannabis:before {
  content: "\f55f";
}

.fa-capsules:before {
  content: "\f46b";
}

.fa-car:before {
  content: "\f1b9";
}

.fa-car-alt:before {
  content: "\f5de";
}

.fa-car-battery:before {
  content: "\f5df";
}

.fa-car-crash:before {
  content: "\f5e1";
}

.fa-car-side:before {
  content: "\f5e4";
}

.fa-caret-down:before {
  content: "\f0d7";
}

.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

.fa-caret-square-down:before {
  content: "\f150";
}

.fa-caret-square-left:before {
  content: "\f191";
}

.fa-caret-square-right:before {
  content: "\f152";
}

.fa-caret-square-up:before {
  content: "\f151";
}

.fa-caret-up:before {
  content: "\f0d8";
}

.fa-cart-arrow-down:before {
  content: "\f218";
}

.fa-cart-plus:before {
  content: "\f217";
}

.fa-cc-amazon-pay:before {
  content: "\f42d";
}

.fa-cc-amex:before {
  content: "\f1f3";
}

.fa-cc-apple-pay:before {
  content: "\f416";
}

.fa-cc-diners-club:before {
  content: "\f24c";
}

.fa-cc-discover:before {
  content: "\f1f2";
}

.fa-cc-jcb:before {
  content: "\f24b";
}

.fa-cc-mastercard:before {
  content: "\f1f1";
}

.fa-cc-paypal:before {
  content: "\f1f4";
}

.fa-cc-stripe:before {
  content: "\f1f5";
}

.fa-cc-visa:before {
  content: "\f1f0";
}

.fa-centercode:before {
  content: "\f380";
}

.fa-certificate:before {
  content: "\f0a3";
}

.fa-chalkboard:before {
  content: "\f51b";
}

.fa-chalkboard-teacher:before {
  content: "\f51c";
}

.fa-charging-station:before {
  content: "\f5e7";
}

.fa-chart-area:before {
  content: "\f1fe";
}

.fa-chart-bar:before {
  content: "\f080";
}

.fa-chart-line:before {
  content: "\f201";
}

.fa-chart-pie:before {
  content: "\f200";
}

.fa-check:before {
  content: "\f00c";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-check-double:before {
  content: "\f560";
}

.fa-check-square:before {
  content: "\f14a";
}

.fa-chess:before {
  content: "\f439";
}

.fa-chess-bishop:before {
  content: "\f43a";
}

.fa-chess-board:before {
  content: "\f43c";
}

.fa-chess-king:before {
  content: "\f43f";
}

.fa-chess-knight:before {
  content: "\f441";
}

.fa-chess-pawn:before {
  content: "\f443";
}

.fa-chess-queen:before {
  content: "\f445";
}

.fa-chess-rook:before {
  content: "\f447";
}

.fa-chevron-circle-down:before {
  content: "\f13a";
}

.fa-chevron-circle-left:before {
  content: "\f137";
}

.fa-chevron-circle-right:before {
  content: "\f138";
}

.fa-chevron-circle-up:before {
  content: "\f139";
}

.fa-chevron-down:before {
  content: "\f078";
}

.fa-chevron-left:before {
  content: "\f053";
}

.fa-chevron-right:before {
  content: "\f054";
}

.fa-chevron-up:before {
  content: "\f077";
}

.fa-child:before {
  content: "\f1ae";
}

.fa-chrome:before {
  content: "\f268";
}

.fa-church:before {
  content: "\f51d";
}

.fa-circle:before {
  content: "\f111";
}

.fa-circle-notch:before {
  content: "\f1ce";
}

.fa-city:before {
  content: "\f64f";
}

.fa-clipboard:before {
  content: "\f328";
}

.fa-clipboard-check:before {
  content: "\f46c";
}

.fa-clipboard-list:before {
  content: "\f46d";
}

.fa-clock:before {
  content: "\f017";
}

.fa-clone:before {
  content: "\f24d";
}

.fa-closed-captioning:before {
  content: "\f20a";
}

.fa-cloud:before {
  content: "\f0c2";
}

.fa-cloud-download-alt:before {
  content: "\f381";
}

.fa-cloud-upload-alt:before {
  content: "\f382";
}

.fa-cloudscale:before {
  content: "\f383";
}

.fa-cloudsmith:before {
  content: "\f384";
}

.fa-cloudversify:before {
  content: "\f385";
}

.fa-cocktail:before {
  content: "\f561";
}

.fa-code:before {
  content: "\f121";
}

.fa-code-branch:before {
  content: "\f126";
}

.fa-codepen:before {
  content: "\f1cb";
}

.fa-codiepie:before {
  content: "\f284";
}

.fa-coffee:before {
  content: "\f0f4";
}

.fa-cog:before {
  content: "\f013";
}

.fa-cogs:before {
  content: "\f085";
}

.fa-coins:before {
  content: "\f51e";
}

.fa-columns:before {
  content: "\f0db";
}

.fa-comment:before {
  content: "\f075";
}

.fa-comment-alt:before {
  content: "\f27a";
}

.fa-comment-dollar:before {
  content: "\f651";
}

.fa-comment-dots:before {
  content: "\f4ad";
}

.fa-comment-slash:before {
  content: "\f4b3";
}

.fa-comments:before {
  content: "\f086";
}

.fa-comments-dollar:before {
  content: "\f653";
}

.fa-compact-disc:before {
  content: "\f51f";
}

.fa-compass:before {
  content: "\f14e";
}

.fa-compress:before {
  content: "\f066";
}

.fa-concierge-bell:before {
  content: "\f562";
}

.fa-connectdevelop:before {
  content: "\f20e";
}

.fa-contao:before {
  content: "\f26d";
}

.fa-cookie:before {
  content: "\f563";
}

.fa-cookie-bite:before {
  content: "\f564";
}

.fa-copy:before {
  content: "\f0c5";
}

.fa-copyright:before {
  content: "\f1f9";
}

.fa-couch:before {
  content: "\f4b8";
}

.fa-cpanel:before {
  content: "\f388";
}

.fa-creative-commons:before {
  content: "\f25e";
}

.fa-creative-commons-by:before {
  content: "\f4e7";
}

.fa-creative-commons-nc:before {
  content: "\f4e8";
}

.fa-creative-commons-nc-eu:before {
  content: "\f4e9";
}

.fa-creative-commons-nc-jp:before {
  content: "\f4ea";
}

.fa-creative-commons-nd:before {
  content: "\f4eb";
}

.fa-creative-commons-pd:before {
  content: "\f4ec";
}

.fa-creative-commons-pd-alt:before {
  content: "\f4ed";
}

.fa-creative-commons-remix:before {
  content: "\f4ee";
}

.fa-creative-commons-sa:before {
  content: "\f4ef";
}

.fa-creative-commons-sampling:before {
  content: "\f4f0";
}

.fa-creative-commons-sampling-plus:before {
  content: "\f4f1";
}

.fa-creative-commons-share:before {
  content: "\f4f2";
}

.fa-credit-card:before {
  content: "\f09d";
}

.fa-crop:before {
  content: "\f125";
}

.fa-crop-alt:before {
  content: "\f565";
}

.fa-cross:before {
  content: "\f654";
}

.fa-crosshairs:before {
  content: "\f05b";
}

.fa-crow:before {
  content: "\f520";
}

.fa-crown:before {
  content: "\f521";
}

.fa-css3:before {
  content: "\f13c";
}

.fa-css3-alt:before {
  content: "\f38b";
}

.fa-cube:before {
  content: "\f1b2";
}

.fa-cubes:before {
  content: "\f1b3";
}

.fa-cut:before {
  content: "\f0c4";
}

.fa-cuttlefish:before {
  content: "\f38c";
}

.fa-d-and-d:before {
  content: "\f38d";
}

.fa-dashcube:before {
  content: "\f210";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-deaf:before {
  content: "\f2a4";
}

.fa-delicious:before {
  content: "\f1a5";
}

.fa-deploydog:before {
  content: "\f38e";
}

.fa-deskpro:before {
  content: "\f38f";
}

.fa-desktop:before {
  content: "\f108";
}

.fa-deviantart:before {
  content: "\f1bd";
}

.fa-dharmachakra:before {
  content: "\f655";
}

.fa-diagnoses:before {
  content: "\f470";
}

.fa-dice:before {
  content: "\f522";
}

.fa-dice-five:before {
  content: "\f523";
}

.fa-dice-four:before {
  content: "\f524";
}

.fa-dice-one:before {
  content: "\f525";
}

.fa-dice-six:before {
  content: "\f526";
}

.fa-dice-three:before {
  content: "\f527";
}

.fa-dice-two:before {
  content: "\f528";
}

.fa-digg:before {
  content: "\f1a6";
}

.fa-digital-ocean:before {
  content: "\f391";
}

.fa-digital-tachograph:before {
  content: "\f566";
}

.fa-directions:before {
  content: "\f5eb";
}

.fa-discord:before {
  content: "\f392";
}

.fa-discourse:before {
  content: "\f393";
}

.fa-divide:before {
  content: "\f529";
}

.fa-dizzy:before {
  content: "\f567";
}

.fa-dna:before {
  content: "\f471";
}

.fa-dochub:before {
  content: "\f394";
}

.fa-docker:before {
  content: "\f395";
}

.fa-dollar-sign:before {
  content: "\f155";
}

.fa-dolly:before {
  content: "\f472";
}

.fa-dolly-flatbed:before {
  content: "\f474";
}

.fa-donate:before {
  content: "\f4b9";
}

.fa-door-closed:before {
  content: "\f52a";
}

.fa-door-open:before {
  content: "\f52b";
}

.fa-dot-circle:before {
  content: "\f192";
}

.fa-dove:before {
  content: "\f4ba";
}

.fa-download:before {
  content: "\f019";
}

.fa-draft2digital:before {
  content: "\f396";
}

.fa-drafting-compass:before {
  content: "\f568";
}

.fa-draw-polygon:before {
  content: "\f5ee";
}

.fa-dribbble:before {
  content: "\f17d";
}

.fa-dribbble-square:before {
  content: "\f397";
}

.fa-dropbox:before {
  content: "\f16b";
}

.fa-drum:before {
  content: "\f569";
}

.fa-drum-steelpan:before {
  content: "\f56a";
}

.fa-drupal:before {
  content: "\f1a9";
}

.fa-dumbbell:before {
  content: "\f44b";
}

.fa-dyalog:before {
  content: "\f399";
}

.fa-earlybirds:before {
  content: "\f39a";
}

.fa-ebay:before {
  content: "\f4f4";
}

.fa-edge:before {
  content: "\f282";
}

.fa-edit:before {
  content: "\f044";
}

.fa-eject:before {
  content: "\f052";
}

.fa-elementor:before {
  content: "\f430";
}

.fa-ellipsis-h:before {
  content: "\f141";
}

.fa-ellipsis-v:before {
  content: "\f142";
}

.fa-ello:before {
  content: "\f5f1";
}

.fa-ember:before {
  content: "\f423";
}

.fa-empire:before {
  content: "\f1d1";
}

.fa-envelope:before {
  content: "\f0e0";
}

.fa-envelope-open:before {
  content: "\f2b6";
}

.fa-envelope-open-text:before {
  content: "\f658";
}

.fa-envelope-square:before {
  content: "\f199";
}

.fa-envira:before {
  content: "\f299";
}

.fa-equals:before {
  content: "\f52c";
}

.fa-eraser:before {
  content: "\f12d";
}

.fa-erlang:before {
  content: "\f39d";
}

.fa-ethereum:before {
  content: "\f42e";
}

.fa-etsy:before {
  content: "\f2d7";
}

.fa-euro-sign:before {
  content: "\f153";
}

.fa-exchange-alt:before {
  content: "\f362";
}

.fa-exclamation:before {
  content: "\f12a";
}

.fa-exclamation-circle:before {
  content: "\f06a";
}

.fa-exclamation-triangle:before {
  content: "\f071";
}

.fa-expand:before {
  content: "\f065";
}

.fa-expand-arrows-alt:before {
  content: "\f31e";
}

.fa-expeditedssl:before {
  content: "\f23e";
}

.fa-external-link-alt:before {
  content: "\f35d";
}

.fa-external-link-square-alt:before {
  content: "\f360";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-eye-dropper:before {
  content: "\f1fb";
}

.fa-eye-slash:before {
  content: "\f070";
}

.fa-facebook:before {
  content: "\f09a";
}

.fa-facebook-f:before {
  content: "\f39e";
}

.fa-facebook-messenger:before {
  content: "\f39f";
}

.fa-facebook-square:before {
  content: "\f082";
}

.fa-fast-backward:before {
  content: "\f049";
}

.fa-fast-forward:before {
  content: "\f050";
}

.fa-fax:before {
  content: "\f1ac";
}

.fa-feather:before {
  content: "\f52d";
}

.fa-feather-alt:before {
  content: "\f56b";
}

.fa-female:before {
  content: "\f182";
}

.fa-fighter-jet:before {
  content: "\f0fb";
}

.fa-file:before {
  content: "\f15b";
}

.fa-file-alt:before {
  content: "\f15c";
}

.fa-file-archive:before {
  content: "\f1c6";
}

.fa-file-audio:before {
  content: "\f1c7";
}

.fa-file-code:before {
  content: "\f1c9";
}

.fa-file-contract:before {
  content: "\f56c";
}

.fa-file-download:before {
  content: "\f56d";
}

.fa-file-excel:before {
  content: "\f1c3";
}

.fa-file-export:before {
  content: "\f56e";
}

.fa-file-image:before {
  content: "\f1c5";
}

.fa-file-import:before {
  content: "\f56f";
}

.fa-file-invoice:before {
  content: "\f570";
}

.fa-file-invoice-dollar:before {
  content: "\f571";
}

.fa-file-medical:before {
  content: "\f477";
}

.fa-file-medical-alt:before {
  content: "\f478";
}

.fa-file-pdf:before {
  content: "\f1c1";
}

.fa-file-powerpoint:before {
  content: "\f1c4";
}

.fa-file-prescription:before {
  content: "\f572";
}

.fa-file-signature:before {
  content: "\f573";
}

.fa-file-upload:before {
  content: "\f574";
}

.fa-file-video:before {
  content: "\f1c8";
}

.fa-file-word:before {
  content: "\f1c2";
}

.fa-fill:before {
  content: "\f575";
}

.fa-fill-drip:before {
  content: "\f576";
}

.fa-film:before {
  content: "\f008";
}

.fa-filter:before {
  content: "\f0b0";
}

.fa-fingerprint:before {
  content: "\f577";
}

.fa-fire:before {
  content: "\f06d";
}

.fa-fire-extinguisher:before {
  content: "\f134";
}

.fa-firefox:before {
  content: "\f269";
}

.fa-first-aid:before {
  content: "\f479";
}

.fa-first-order:before {
  content: "\f2b0";
}

.fa-first-order-alt:before {
  content: "\f50a";
}

.fa-firstdraft:before {
  content: "\f3a1";
}

.fa-fish:before {
  content: "\f578";
}

.fa-flag:before {
  content: "\f024";
}

.fa-flag-checkered:before {
  content: "\f11e";
}

.fa-flask:before {
  content: "\f0c3";
}

.fa-flickr:before {
  content: "\f16e";
}

.fa-flipboard:before {
  content: "\f44d";
}

.fa-flushed:before {
  content: "\f579";
}

.fa-fly:before {
  content: "\f417";
}

.fa-folder:before {
  content: "\f07b";
}

.fa-folder-minus:before {
  content: "\f65d";
}

.fa-folder-open:before {
  content: "\f07c";
}

.fa-folder-plus:before {
  content: "\f65e";
}

.fa-font:before {
  content: "\f031";
}

.fa-font-awesome:before {
  content: "\f2b4";
}

.fa-font-awesome-alt:before {
  content: "\f35c";
}

.fa-font-awesome-flag:before {
  content: "\f425";
}

.fa-font-awesome-logo-full:before {
  content: "\f4e6";
}

.fa-fonticons:before {
  content: "\f280";
}

.fa-fonticons-fi:before {
  content: "\f3a2";
}

.fa-football-ball:before {
  content: "\f44e";
}

.fa-fort-awesome:before {
  content: "\f286";
}

.fa-fort-awesome-alt:before {
  content: "\f3a3";
}

.fa-forumbee:before {
  content: "\f211";
}

.fa-forward:before {
  content: "\f04e";
}

.fa-foursquare:before {
  content: "\f180";
}

.fa-free-code-camp:before {
  content: "\f2c5";
}

.fa-freebsd:before {
  content: "\f3a4";
}

.fa-frog:before {
  content: "\f52e";
}

.fa-frown:before {
  content: "\f119";
}

.fa-frown-open:before {
  content: "\f57a";
}

.fa-fulcrum:before {
  content: "\f50b";
}

.fa-funnel-dollar:before {
  content: "\f662";
}

.fa-futbol:before {
  content: "\f1e3";
}

.fa-galactic-republic:before {
  content: "\f50c";
}

.fa-galactic-senate:before {
  content: "\f50d";
}

.fa-gamepad:before {
  content: "\f11b";
}

.fa-gas-pump:before {
  content: "\f52f";
}

.fa-gavel:before {
  content: "\f0e3";
}

.fa-gem:before {
  content: "\f3a5";
}

.fa-genderless:before {
  content: "\f22d";
}

.fa-get-pocket:before {
  content: "\f265";
}

.fa-gg:before {
  content: "\f260";
}

.fa-gg-circle:before {
  content: "\f261";
}

.fa-gift:before {
  content: "\f06b";
}

.fa-git:before {
  content: "\f1d3";
}

.fa-git-square:before {
  content: "\f1d2";
}

.fa-github:before {
  content: "\f09b";
}

.fa-github-alt:before {
  content: "\f113";
}

.fa-github-square:before {
  content: "\f092";
}

.fa-gitkraken:before {
  content: "\f3a6";
}

.fa-gitlab:before {
  content: "\f296";
}

.fa-gitter:before {
  content: "\f426";
}

.fa-glass-martini:before {
  content: "\f000";
}

.fa-glass-martini-alt:before {
  content: "\f57b";
}

.fa-glasses:before {
  content: "\f530";
}

.fa-glide:before {
  content: "\f2a5";
}

.fa-glide-g:before {
  content: "\f2a6";
}

.fa-globe:before {
  content: "\f0ac";
}

.fa-globe-africa:before {
  content: "\f57c";
}

.fa-globe-americas:before {
  content: "\f57d";
}

.fa-globe-asia:before {
  content: "\f57e";
}

.fa-gofore:before {
  content: "\f3a7";
}

.fa-golf-ball:before {
  content: "\f450";
}

.fa-goodreads:before {
  content: "\f3a8";
}

.fa-goodreads-g:before {
  content: "\f3a9";
}

.fa-google:before {
  content: "\f1a0";
}

.fa-google-drive:before {
  content: "\f3aa";
}

.fa-google-play:before {
  content: "\f3ab";
}

.fa-google-plus:before {
  content: "\f2b3";
}

.fa-google-plus-g:before {
  content: "\f0d5";
}

.fa-google-plus-square:before {
  content: "\f0d4";
}

.fa-google-wallet:before {
  content: "\f1ee";
}

.fa-gopuram:before {
  content: "\f664";
}

.fa-graduation-cap:before {
  content: "\f19d";
}

.fa-gratipay:before {
  content: "\f184";
}

.fa-grav:before {
  content: "\f2d6";
}

.fa-greater-than:before {
  content: "\f531";
}

.fa-greater-than-equal:before {
  content: "\f532";
}

.fa-grimace:before {
  content: "\f57f";
}

.fa-grin:before {
  content: "\f580";
}

.fa-grin-alt:before {
  content: "\f581";
}

.fa-grin-beam:before {
  content: "\f582";
}

.fa-grin-beam-sweat:before {
  content: "\f583";
}

.fa-grin-hearts:before {
  content: "\f584";
}

.fa-grin-squint:before {
  content: "\f585";
}

.fa-grin-squint-tears:before {
  content: "\f586";
}

.fa-grin-stars:before {
  content: "\f587";
}

.fa-grin-tears:before {
  content: "\f588";
}

.fa-grin-tongue:before {
  content: "\f589";
}

.fa-grin-tongue-squint:before {
  content: "\f58a";
}

.fa-grin-tongue-wink:before {
  content: "\f58b";
}

.fa-grin-wink:before {
  content: "\f58c";
}

.fa-grip-horizontal:before {
  content: "\f58d";
}

.fa-grip-vertical:before {
  content: "\f58e";
}

.fa-gripfire:before {
  content: "\f3ac";
}

.fa-grunt:before {
  content: "\f3ad";
}

.fa-gulp:before {
  content: "\f3ae";
}

.fa-h-square:before {
  content: "\f0fd";
}

.fa-hacker-news:before {
  content: "\f1d4";
}

.fa-hacker-news-square:before {
  content: "\f3af";
}

.fa-hackerrank:before {
  content: "\f5f7";
}

.fa-hamsa:before {
  content: "\f665";
}

.fa-hand-holding:before {
  content: "\f4bd";
}

.fa-hand-holding-heart:before {
  content: "\f4be";
}

.fa-hand-holding-usd:before {
  content: "\f4c0";
}

.fa-hand-lizard:before {
  content: "\f258";
}

.fa-hand-paper:before {
  content: "\f256";
}

.fa-hand-peace:before {
  content: "\f25b";
}

.fa-hand-point-down:before {
  content: "\f0a7";
}

.fa-hand-point-left:before {
  content: "\f0a5";
}

.fa-hand-point-right:before {
  content: "\f0a4";
}

.fa-hand-point-up:before {
  content: "\f0a6";
}

.fa-hand-pointer:before {
  content: "\f25a";
}

.fa-hand-rock:before {
  content: "\f255";
}

.fa-hand-scissors:before {
  content: "\f257";
}

.fa-hand-spock:before {
  content: "\f259";
}

.fa-hands:before {
  content: "\f4c2";
}

.fa-hands-helping:before {
  content: "\f4c4";
}

.fa-handshake:before {
  content: "\f2b5";
}

.fa-hashtag:before {
  content: "\f292";
}

.fa-haykal:before {
  content: "\f666";
}

.fa-hdd:before {
  content: "\f0a0";
}

.fa-heading:before {
  content: "\f1dc";
}

.fa-headphones:before {
  content: "\f025";
}

.fa-headphones-alt:before {
  content: "\f58f";
}

.fa-headset:before {
  content: "\f590";
}

.fa-heart:before {
  content: "\f004";
}

.fa-heartbeat:before {
  content: "\f21e";
}

.fa-helicopter:before {
  content: "\f533";
}

.fa-highlighter:before {
  content: "\f591";
}

.fa-hips:before {
  content: "\f452";
}

.fa-hire-a-helper:before {
  content: "\f3b0";
}

.fa-history:before {
  content: "\f1da";
}

.fa-hockey-puck:before {
  content: "\f453";
}

.fa-home:before {
  content: "\f015";
}

.fa-hooli:before {
  content: "\f427";
}

.fa-hornbill:before {
  content: "\f592";
}

.fa-hospital:before {
  content: "\f0f8";
}

.fa-hospital-alt:before {
  content: "\f47d";
}

.fa-hospital-symbol:before {
  content: "\f47e";
}

.fa-hot-tub:before {
  content: "\f593";
}

.fa-hotel:before {
  content: "\f594";
}

.fa-hotjar:before {
  content: "\f3b1";
}

.fa-hourglass:before {
  content: "\f254";
}

.fa-hourglass-end:before {
  content: "\f253";
}

.fa-hourglass-half:before {
  content: "\f252";
}

.fa-hourglass-start:before {
  content: "\f251";
}

.fa-houzz:before {
  content: "\f27c";
}

.fa-html5:before {
  content: "\f13b";
}

.fa-hubspot:before {
  content: "\f3b2";
}

.fa-i-cursor:before {
  content: "\f246";
}

.fa-id-badge:before {
  content: "\f2c1";
}

.fa-id-card:before {
  content: "\f2c2";
}

.fa-id-card-alt:before {
  content: "\f47f";
}

.fa-image:before {
  content: "\f03e";
}

.fa-images:before {
  content: "\f302";
}

.fa-imdb:before {
  content: "\f2d8";
}

.fa-inbox:before {
  content: "\f01c";
}

.fa-indent:before {
  content: "\f03c";
}

.fa-industry:before {
  content: "\f275";
}

.fa-infinity:before {
  content: "\f534";
}

.fa-info:before {
  content: "\f129";
}

.fa-info-circle:before {
  content: "\f05a";
}

.fa-instagram:before {
  content: "\f16d";
}

.fa-internet-explorer:before {
  content: "\f26b";
}

.fa-ioxhost:before {
  content: "\f208";
}

.fa-italic:before {
  content: "\f033";
}

.fa-itunes:before {
  content: "\f3b4";
}

.fa-itunes-note:before {
  content: "\f3b5";
}

.fa-java:before {
  content: "\f4e4";
}

.fa-jedi:before {
  content: "\f669";
}

.fa-jedi-order:before {
  content: "\f50e";
}

.fa-jenkins:before {
  content: "\f3b6";
}

.fa-joget:before {
  content: "\f3b7";
}

.fa-joint:before {
  content: "\f595";
}

.fa-joomla:before {
  content: "\f1aa";
}

.fa-journal-whills:before {
  content: "\f66a";
}

.fa-js:before {
  content: "\f3b8";
}

.fa-js-square:before {
  content: "\f3b9";
}

.fa-jsfiddle:before {
  content: "\f1cc";
}

.fa-kaaba:before {
  content: "\f66b";
}

.fa-kaggle:before {
  content: "\f5fa";
}

.fa-key:before {
  content: "\f084";
}

.fa-keybase:before {
  content: "\f4f5";
}

.fa-keyboard:before {
  content: "\f11c";
}

.fa-keycdn:before {
  content: "\f3ba";
}

.fa-khanda:before {
  content: "\f66d";
}

.fa-kickstarter:before {
  content: "\f3bb";
}

.fa-kickstarter-k:before {
  content: "\f3bc";
}

.fa-kiss:before {
  content: "\f596";
}

.fa-kiss-beam:before {
  content: "\f597";
}

.fa-kiss-wink-heart:before {
  content: "\f598";
}

.fa-kiwi-bird:before {
  content: "\f535";
}

.fa-korvue:before {
  content: "\f42f";
}

.fa-landmark:before {
  content: "\f66f";
}

.fa-language:before {
  content: "\f1ab";
}

.fa-laptop:before {
  content: "\f109";
}

.fa-laptop-code:before {
  content: "\f5fc";
}

.fa-laravel:before {
  content: "\f3bd";
}

.fa-lastfm:before {
  content: "\f202";
}

.fa-lastfm-square:before {
  content: "\f203";
}

.fa-laugh:before {
  content: "\f599";
}

.fa-laugh-beam:before {
  content: "\f59a";
}

.fa-laugh-squint:before {
  content: "\f59b";
}

.fa-laugh-wink:before {
  content: "\f59c";
}

.fa-layer-group:before {
  content: "\f5fd";
}

.fa-leaf:before {
  content: "\f06c";
}

.fa-leanpub:before {
  content: "\f212";
}

.fa-lemon:before {
  content: "\f094";
}

.fa-less:before {
  content: "\f41d";
}

.fa-less-than:before {
  content: "\f536";
}

.fa-less-than-equal:before {
  content: "\f537";
}

.fa-level-down-alt:before {
  content: "\f3be";
}

.fa-level-up-alt:before {
  content: "\f3bf";
}

.fa-life-ring:before {
  content: "\f1cd";
}

.fa-lightbulb:before {
  content: "\f0eb";
}

.fa-line:before {
  content: "\f3c0";
}

.fa-link:before {
  content: "\f0c1";
}

.fa-linkedin:before {
  content: "\f08c";
}

.fa-linkedin-in:before {
  content: "\f0e1";
}

.fa-linode:before {
  content: "\f2b8";
}

.fa-linux:before {
  content: "\f17c";
}

.fa-lira-sign:before {
  content: "\f195";
}

.fa-list:before {
  content: "\f03a";
}

.fa-list-alt:before {
  content: "\f022";
}

.fa-list-ol:before {
  content: "\f0cb";
}

.fa-list-ul:before {
  content: "\f0ca";
}

.fa-location-arrow:before {
  content: "\f124";
}

.fa-lock:before {
  content: "\f023";
}

.fa-lock-open:before {
  content: "\f3c1";
}

.fa-long-arrow-alt-down:before {
  content: "\f309";
}

.fa-long-arrow-alt-left:before {
  content: "\f30a";
}

.fa-long-arrow-alt-right:before {
  content: "\f30b";
}

.fa-long-arrow-alt-up:before {
  content: "\f30c";
}

.fa-low-vision:before {
  content: "\f2a8";
}

.fa-luggage-cart:before {
  content: "\f59d";
}

.fa-lyft:before {
  content: "\f3c3";
}

.fa-magento:before {
  content: "\f3c4";
}

.fa-magic:before {
  content: "\f0d0";
}

.fa-magnet:before {
  content: "\f076";
}

.fa-mail-bulk:before {
  content: "\f674";
}

.fa-mailchimp:before {
  content: "\f59e";
}

.fa-male:before {
  content: "\f183";
}

.fa-mandalorian:before {
  content: "\f50f";
}

.fa-map:before {
  content: "\f279";
}

.fa-map-marked:before {
  content: "\f59f";
}

.fa-map-marked-alt:before {
  content: "\f5a0";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-map-marker-alt:before {
  content: "\f3c5";
}

.fa-map-pin:before {
  content: "\f276";
}

.fa-map-signs:before {
  content: "\f277";
}

.fa-markdown:before {
  content: "\f60f";
}

.fa-marker:before {
  content: "\f5a1";
}

.fa-mars:before {
  content: "\f222";
}

.fa-mars-double:before {
  content: "\f227";
}

.fa-mars-stroke:before {
  content: "\f229";
}

.fa-mars-stroke-h:before {
  content: "\f22b";
}

.fa-mars-stroke-v:before {
  content: "\f22a";
}

.fa-mastodon:before {
  content: "\f4f6";
}

.fa-maxcdn:before {
  content: "\f136";
}

.fa-medal:before {
  content: "\f5a2";
}

.fa-medapps:before {
  content: "\f3c6";
}

.fa-medium:before {
  content: "\f23a";
}

.fa-medium-m:before {
  content: "\f3c7";
}

.fa-medkit:before {
  content: "\f0fa";
}

.fa-medrt:before {
  content: "\f3c8";
}

.fa-meetup:before {
  content: "\f2e0";
}

.fa-megaport:before {
  content: "\f5a3";
}

.fa-meh:before {
  content: "\f11a";
}

.fa-meh-blank:before {
  content: "\f5a4";
}

.fa-meh-rolling-eyes:before {
  content: "\f5a5";
}

.fa-memory:before {
  content: "\f538";
}

.fa-menorah:before {
  content: "\f676";
}

.fa-mercury:before {
  content: "\f223";
}

.fa-microchip:before {
  content: "\f2db";
}

.fa-microphone:before {
  content: "\f130";
}

.fa-microphone-alt:before {
  content: "\f3c9";
}

.fa-microphone-alt-slash:before {
  content: "\f539";
}

.fa-microphone-slash:before {
  content: "\f131";
}

.fa-microscope:before {
  content: "\f610";
}

.fa-microsoft:before {
  content: "\f3ca";
}

.fa-minus:before {
  content: "\f068";
}

.fa-minus-circle:before {
  content: "\f056";
}

.fa-minus-square:before {
  content: "\f146";
}

.fa-mix:before {
  content: "\f3cb";
}

.fa-mixcloud:before {
  content: "\f289";
}

.fa-mizuni:before {
  content: "\f3cc";
}

.fa-mobile:before {
  content: "\f10b";
}

.fa-mobile-alt:before {
  content: "\f3cd";
}

.fa-modx:before {
  content: "\f285";
}

.fa-monero:before {
  content: "\f3d0";
}

.fa-money-bill:before {
  content: "\f0d6";
}

.fa-money-bill-alt:before {
  content: "\f3d1";
}

.fa-money-bill-wave:before {
  content: "\f53a";
}

.fa-money-bill-wave-alt:before {
  content: "\f53b";
}

.fa-money-check:before {
  content: "\f53c";
}

.fa-money-check-alt:before {
  content: "\f53d";
}

.fa-monument:before {
  content: "\f5a6";
}

.fa-moon:before {
  content: "\f186";
}

.fa-mortar-pestle:before {
  content: "\f5a7";
}

.fa-mosque:before {
  content: "\f678";
}

.fa-motorcycle:before {
  content: "\f21c";
}

.fa-mouse-pointer:before {
  content: "\f245";
}

.fa-music:before {
  content: "\f001";
}

.fa-napster:before {
  content: "\f3d2";
}

.fa-neos:before {
  content: "\f612";
}

.fa-neuter:before {
  content: "\f22c";
}

.fa-newspaper:before {
  content: "\f1ea";
}

.fa-nimblr:before {
  content: "\f5a8";
}

.fa-nintendo-switch:before {
  content: "\f418";
}

.fa-node:before {
  content: "\f419";
}

.fa-node-js:before {
  content: "\f3d3";
}

.fa-not-equal:before {
  content: "\f53e";
}

.fa-notes-medical:before {
  content: "\f481";
}

.fa-npm:before {
  content: "\f3d4";
}

.fa-ns8:before {
  content: "\f3d5";
}

.fa-nutritionix:before {
  content: "\f3d6";
}

.fa-object-group:before {
  content: "\f247";
}

.fa-object-ungroup:before {
  content: "\f248";
}

.fa-odnoklassniki:before {
  content: "\f263";
}

.fa-odnoklassniki-square:before {
  content: "\f264";
}

.fa-oil-can:before {
  content: "\f613";
}

.fa-old-republic:before {
  content: "\f510";
}

.fa-om:before {
  content: "\f679";
}

.fa-opencart:before {
  content: "\f23d";
}

.fa-openid:before {
  content: "\f19b";
}

.fa-opera:before {
  content: "\f26a";
}

.fa-optin-monster:before {
  content: "\f23c";
}

.fa-osi:before {
  content: "\f41a";
}

.fa-outdent:before {
  content: "\f03b";
}

.fa-page4:before {
  content: "\f3d7";
}

.fa-pagelines:before {
  content: "\f18c";
}

.fa-paint-brush:before {
  content: "\f1fc";
}

.fa-paint-roller:before {
  content: "\f5aa";
}

.fa-palette:before {
  content: "\f53f";
}

.fa-palfed:before {
  content: "\f3d8";
}

.fa-pallet:before {
  content: "\f482";
}

.fa-paper-plane:before {
  content: "\f1d8";
}

.fa-paperclip:before {
  content: "\f0c6";
}

.fa-parachute-box:before {
  content: "\f4cd";
}

.fa-paragraph:before {
  content: "\f1dd";
}

.fa-parking:before {
  content: "\f540";
}

.fa-passport:before {
  content: "\f5ab";
}

.fa-pastafarianism:before {
  content: "\f67b";
}

.fa-paste:before {
  content: "\f0ea";
}

.fa-patreon:before {
  content: "\f3d9";
}

.fa-pause:before {
  content: "\f04c";
}

.fa-pause-circle:before {
  content: "\f28b";
}

.fa-paw:before {
  content: "\f1b0";
}

.fa-paypal:before {
  content: "\f1ed";
}

.fa-peace:before {
  content: "\f67c";
}

.fa-pen:before {
  content: "\f304";
}

.fa-pen-alt:before {
  content: "\f305";
}

.fa-pen-fancy:before {
  content: "\f5ac";
}

.fa-pen-nib:before {
  content: "\f5ad";
}

.fa-pen-square:before {
  content: "\f14b";
}

.fa-pencil-alt:before {
  content: "\f303";
}

.fa-pencil-ruler:before {
  content: "\f5ae";
}

.fa-people-carry:before {
  content: "\f4ce";
}

.fa-percent:before {
  content: "\f295";
}

.fa-percentage:before {
  content: "\f541";
}

.fa-periscope:before {
  content: "\f3da";
}

.fa-phabricator:before {
  content: "\f3db";
}

.fa-phoenix-framework:before {
  content: "\f3dc";
}

.fa-phoenix-squadron:before {
  content: "\f511";
}

.fa-phone:before {
  content: "\f095";
}

.fa-phone-slash:before {
  content: "\f3dd";
}

.fa-phone-square:before {
  content: "\f098";
}

.fa-phone-volume:before {
  content: "\f2a0";
}

.fa-php:before {
  content: "\f457";
}

.fa-pied-piper:before {
  content: "\f2ae";
}

.fa-pied-piper-alt:before {
  content: "\f1a8";
}

.fa-pied-piper-hat:before {
  content: "\f4e5";
}

.fa-pied-piper-pp:before {
  content: "\f1a7";
}

.fa-piggy-bank:before {
  content: "\f4d3";
}

.fa-pills:before {
  content: "\f484";
}

.fa-pinterest:before {
  content: "\f0d2";
}

.fa-pinterest-p:before {
  content: "\f231";
}

.fa-pinterest-square:before {
  content: "\f0d3";
}

.fa-place-of-worship:before {
  content: "\f67f";
}

.fa-plane:before {
  content: "\f072";
}

.fa-plane-arrival:before {
  content: "\f5af";
}

.fa-plane-departure:before {
  content: "\f5b0";
}

.fa-play:before {
  content: "\f04b";
}

.fa-play-circle:before {
  content: "\f144";
}

.fa-playstation:before {
  content: "\f3df";
}

.fa-plug:before {
  content: "\f1e6";
}

.fa-plus:before {
  content: "\f067";
}

.fa-plus-circle:before {
  content: "\f055";
}

.fa-plus-square:before {
  content: "\f0fe";
}

.fa-podcast:before {
  content: "\f2ce";
}

.fa-poll:before {
  content: "\f681";
}

.fa-poll-h:before {
  content: "\f682";
}

.fa-poo:before {
  content: "\f2fe";
}

.fa-poop:before {
  content: "\f619";
}

.fa-portrait:before {
  content: "\f3e0";
}

.fa-pound-sign:before {
  content: "\f154";
}

.fa-power-off:before {
  content: "\f011";
}

.fa-pray:before {
  content: "\f683";
}

.fa-praying-hands:before {
  content: "\f684";
}

.fa-prescription:before {
  content: "\f5b1";
}

.fa-prescription-bottle:before {
  content: "\f485";
}

.fa-prescription-bottle-alt:before {
  content: "\f486";
}

.fa-print:before {
  content: "\f02f";
}

.fa-procedures:before {
  content: "\f487";
}

.fa-product-hunt:before {
  content: "\f288";
}

.fa-project-diagram:before {
  content: "\f542";
}

.fa-pushed:before {
  content: "\f3e1";
}

.fa-puzzle-piece:before {
  content: "\f12e";
}

.fa-python:before {
  content: "\f3e2";
}

.fa-qq:before {
  content: "\f1d6";
}

.fa-qrcode:before {
  content: "\f029";
}

.fa-question:before {
  content: "\f128";
}

.fa-question-circle:before {
  content: "\f059";
}

.fa-quidditch:before {
  content: "\f458";
}

.fa-quinscape:before {
  content: "\f459";
}

.fa-quora:before {
  content: "\f2c4";
}

.fa-quote-left:before {
  content: "\f10d";
}

.fa-quote-right:before {
  content: "\f10e";
}

.fa-quran:before {
  content: "\f687";
}

.fa-r-project:before {
  content: "\f4f7";
}

.fa-random:before {
  content: "\f074";
}

.fa-ravelry:before {
  content: "\f2d9";
}

.fa-react:before {
  content: "\f41b";
}

.fa-readme:before {
  content: "\f4d5";
}

.fa-rebel:before {
  content: "\f1d0";
}

.fa-receipt:before {
  content: "\f543";
}

.fa-recycle:before {
  content: "\f1b8";
}

.fa-red-river:before {
  content: "\f3e3";
}

.fa-reddit:before {
  content: "\f1a1";
}

.fa-reddit-alien:before {
  content: "\f281";
}

.fa-reddit-square:before {
  content: "\f1a2";
}

.fa-redo:before {
  content: "\f01e";
}

.fa-redo-alt:before {
  content: "\f2f9";
}

.fa-registered:before {
  content: "\f25d";
}

.fa-rendact:before {
  content: "\f3e4";
}

.fa-renren:before {
  content: "\f18b";
}

.fa-reply:before {
  content: "\f3e5";
}

.fa-reply-all:before {
  content: "\f122";
}

.fa-replyd:before {
  content: "\f3e6";
}

.fa-researchgate:before {
  content: "\f4f8";
}

.fa-resolving:before {
  content: "\f3e7";
}

.fa-retweet:before {
  content: "\f079";
}

.fa-rev:before {
  content: "\f5b2";
}

.fa-ribbon:before {
  content: "\f4d6";
}

.fa-road:before {
  content: "\f018";
}

.fa-robot:before {
  content: "\f544";
}

.fa-rocket:before {
  content: "\f135";
}

.fa-rocketchat:before {
  content: "\f3e8";
}

.fa-rockrms:before {
  content: "\f3e9";
}

.fa-route:before {
  content: "\f4d7";
}

.fa-rss:before {
  content: "\f09e";
}

.fa-rss-square:before {
  content: "\f143";
}

.fa-ruble-sign:before {
  content: "\f158";
}

.fa-ruler:before {
  content: "\f545";
}

.fa-ruler-combined:before {
  content: "\f546";
}

.fa-ruler-horizontal:before {
  content: "\f547";
}

.fa-ruler-vertical:before {
  content: "\f548";
}

.fa-rupee-sign:before {
  content: "\f156";
}

.fa-sad-cry:before {
  content: "\f5b3";
}

.fa-sad-tear:before {
  content: "\f5b4";
}

.fa-safari:before {
  content: "\f267";
}

.fa-sass:before {
  content: "\f41e";
}

.fa-save:before {
  content: "\f0c7";
}

.fa-schlix:before {
  content: "\f3ea";
}

.fa-school:before {
  content: "\f549";
}

.fa-screwdriver:before {
  content: "\f54a";
}

.fa-scribd:before {
  content: "\f28a";
}

.fa-search:before {
  content: "\f002";
}

.fa-search-dollar:before {
  content: "\f688";
}

.fa-search-location:before {
  content: "\f689";
}

.fa-search-minus:before {
  content: "\f010";
}

.fa-search-plus:before {
  content: "\f00e";
}

.fa-searchengin:before {
  content: "\f3eb";
}

.fa-seedling:before {
  content: "\f4d8";
}

.fa-sellcast:before {
  content: "\f2da";
}

.fa-sellsy:before {
  content: "\f213";
}

.fa-server:before {
  content: "\f233";
}

.fa-servicestack:before {
  content: "\f3ec";
}

.fa-shapes:before {
  content: "\f61f";
}

.fa-share:before {
  content: "\f064";
}

.fa-share-alt:before {
  content: "\f1e0";
}

.fa-share-alt-square:before {
  content: "\f1e1";
}

.fa-share-square:before {
  content: "\f14d";
}

.fa-shekel-sign:before {
  content: "\f20b";
}

.fa-shield-alt:before {
  content: "\f3ed";
}

.fa-ship:before {
  content: "\f21a";
}

.fa-shipping-fast:before {
  content: "\f48b";
}

.fa-shirtsinbulk:before {
  content: "\f214";
}

.fa-shoe-prints:before {
  content: "\f54b";
}

.fa-shopping-bag:before {
  content: "\f290";
}

.fa-shopping-basket:before {
  content: "\f291";
}

.fa-shopping-cart:before {
  content: "\f07a";
}

.fa-shopware:before {
  content: "\f5b5";
}

.fa-shower:before {
  content: "\f2cc";
}

.fa-shuttle-van:before {
  content: "\f5b6";
}

.fa-sign:before {
  content: "\f4d9";
}

.fa-sign-in-alt:before {
  content: "\f2f6";
}

.fa-sign-language:before {
  content: "\f2a7";
}

.fa-sign-out-alt:before {
  content: "\f2f5";
}

.fa-signal:before {
  content: "\f012";
}

.fa-signature:before {
  content: "\f5b7";
}

.fa-simplybuilt:before {
  content: "\f215";
}

.fa-sistrix:before {
  content: "\f3ee";
}

.fa-sitemap:before {
  content: "\f0e8";
}

.fa-sith:before {
  content: "\f512";
}

.fa-skull:before {
  content: "\f54c";
}

.fa-skyatlas:before {
  content: "\f216";
}

.fa-skype:before {
  content: "\f17e";
}

.fa-slack:before {
  content: "\f198";
}

.fa-slack-hash:before {
  content: "\f3ef";
}

.fa-sliders-h:before {
  content: "\f1de";
}

.fa-slideshare:before {
  content: "\f1e7";
}

.fa-smile:before {
  content: "\f118";
}

.fa-smile-beam:before {
  content: "\f5b8";
}

.fa-smile-wink:before {
  content: "\f4da";
}

.fa-smoking:before {
  content: "\f48d";
}

.fa-smoking-ban:before {
  content: "\f54d";
}

.fa-snapchat:before {
  content: "\f2ab";
}

.fa-snapchat-ghost:before {
  content: "\f2ac";
}

.fa-snapchat-square:before {
  content: "\f2ad";
}

.fa-snowflake:before {
  content: "\f2dc";
}

.fa-socks:before {
  content: "\f696";
}

.fa-solar-panel:before {
  content: "\f5ba";
}

.fa-sort:before {
  content: "\f0dc";
}

.fa-sort-alpha-down:before {
  content: "\f15d";
}

.fa-sort-alpha-up:before {
  content: "\f15e";
}

.fa-sort-amount-down:before {
  content: "\f160";
}

.fa-sort-amount-up:before {
  content: "\f161";
}

.fa-sort-down:before {
  content: "\f0dd";
}

.fa-sort-numeric-down:before {
  content: "\f162";
}

.fa-sort-numeric-up:before {
  content: "\f163";
}

.fa-sort-up:before {
  content: "\f0de";
}

.fa-soundcloud:before {
  content: "\f1be";
}

.fa-spa:before {
  content: "\f5bb";
}

.fa-space-shuttle:before {
  content: "\f197";
}

.fa-speakap:before {
  content: "\f3f3";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-splotch:before {
  content: "\f5bc";
}

.fa-spotify:before {
  content: "\f1bc";
}

.fa-spray-can:before {
  content: "\f5bd";
}

.fa-square:before {
  content: "\f0c8";
}

.fa-square-full:before {
  content: "\f45c";
}

.fa-square-root-alt:before {
  content: "\f698";
}

.fa-squarespace:before {
  content: "\f5be";
}

.fa-stack-exchange:before {
  content: "\f18d";
}

.fa-stack-overflow:before {
  content: "\f16c";
}

.fa-stamp:before {
  content: "\f5bf";
}

.fa-star:before {
  content: "\f005";
}

.fa-star-and-crescent:before {
  content: "\f699";
}

.fa-star-half:before {
  content: "\f089";
}

.fa-star-half-alt:before {
  content: "\f5c0";
}

.fa-star-of-david:before {
  content: "\f69a";
}

.fa-star-of-life:before {
  content: "\f621";
}

.fa-staylinked:before {
  content: "\f3f5";
}

.fa-steam:before {
  content: "\f1b6";
}

.fa-steam-square:before {
  content: "\f1b7";
}

.fa-steam-symbol:before {
  content: "\f3f6";
}

.fa-step-backward:before {
  content: "\f048";
}

.fa-step-forward:before {
  content: "\f051";
}

.fa-stethoscope:before {
  content: "\f0f1";
}

.fa-sticker-mule:before {
  content: "\f3f7";
}

.fa-sticky-note:before {
  content: "\f249";
}

.fa-stop:before {
  content: "\f04d";
}

.fa-stop-circle:before {
  content: "\f28d";
}

.fa-stopwatch:before {
  content: "\f2f2";
}

.fa-store:before {
  content: "\f54e";
}

.fa-store-alt:before {
  content: "\f54f";
}

.fa-strava:before {
  content: "\f428";
}

.fa-stream:before {
  content: "\f550";
}

.fa-street-view:before {
  content: "\f21d";
}

.fa-strikethrough:before {
  content: "\f0cc";
}

.fa-stripe:before {
  content: "\f429";
}

.fa-stripe-s:before {
  content: "\f42a";
}

.fa-stroopwafel:before {
  content: "\f551";
}

.fa-studiovinari:before {
  content: "\f3f8";
}

.fa-stumbleupon:before {
  content: "\f1a4";
}

.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

.fa-subscript:before {
  content: "\f12c";
}

.fa-subway:before {
  content: "\f239";
}

.fa-suitcase:before {
  content: "\f0f2";
}

.fa-suitcase-rolling:before {
  content: "\f5c1";
}

.fa-sun:before {
  content: "\f185";
}

.fa-superpowers:before {
  content: "\f2dd";
}

.fa-superscript:before {
  content: "\f12b";
}

.fa-supple:before {
  content: "\f3f9";
}

.fa-surprise:before {
  content: "\f5c2";
}

.fa-swatchbook:before {
  content: "\f5c3";
}

.fa-swimmer:before {
  content: "\f5c4";
}

.fa-swimming-pool:before {
  content: "\f5c5";
}

.fa-synagogue:before {
  content: "\f69b";
}

.fa-sync:before {
  content: "\f021";
}

.fa-sync-alt:before {
  content: "\f2f1";
}

.fa-syringe:before {
  content: "\f48e";
}

.fa-table:before {
  content: "\f0ce";
}

.fa-table-tennis:before {
  content: "\f45d";
}

.fa-tablet:before {
  content: "\f10a";
}

.fa-tablet-alt:before {
  content: "\f3fa";
}

.fa-tablets:before {
  content: "\f490";
}

.fa-tachometer-alt:before {
  content: "\f3fd";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-tape:before {
  content: "\f4db";
}

.fa-tasks:before {
  content: "\f0ae";
}

.fa-taxi:before {
  content: "\f1ba";
}

.fa-teamspeak:before {
  content: "\f4f9";
}

.fa-teeth:before {
  content: "\f62e";
}

.fa-teeth-open:before {
  content: "\f62f";
}

.fa-telegram:before {
  content: "\f2c6";
}

.fa-telegram-plane:before {
  content: "\f3fe";
}

.fa-tencent-weibo:before {
  content: "\f1d5";
}

.fa-terminal:before {
  content: "\f120";
}

.fa-text-height:before {
  content: "\f034";
}

.fa-text-width:before {
  content: "\f035";
}

.fa-th:before {
  content: "\f00a";
}

.fa-th-large:before {
  content: "\f009";
}

.fa-th-list:before {
  content: "\f00b";
}

.fa-the-red-yeti:before {
  content: "\f69d";
}

.fa-theater-masks:before {
  content: "\f630";
}

.fa-themeco:before {
  content: "\f5c6";
}

.fa-themeisle:before {
  content: "\f2b2";
}

.fa-thermometer:before {
  content: "\f491";
}

.fa-thermometer-empty:before {
  content: "\f2cb";
}

.fa-thermometer-full:before {
  content: "\f2c7";
}

.fa-thermometer-half:before {
  content: "\f2c9";
}

.fa-thermometer-quarter:before {
  content: "\f2ca";
}

.fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

.fa-thumbs-down:before {
  content: "\f165";
}

.fa-thumbs-up:before {
  content: "\f164";
}

.fa-thumbtack:before {
  content: "\f08d";
}

.fa-ticket-alt:before {
  content: "\f3ff";
}

.fa-times:before {
  content: "\f00d";
}

.fa-times-circle:before {
  content: "\f057";
}

.fa-tint:before {
  content: "\f043";
}

.fa-tint-slash:before {
  content: "\f5c7";
}

.fa-tired:before {
  content: "\f5c8";
}

.fa-toggle-off:before {
  content: "\f204";
}

.fa-toggle-on:before {
  content: "\f205";
}

.fa-toolbox:before {
  content: "\f552";
}

.fa-tooth:before {
  content: "\f5c9";
}

.fa-torah:before {
  content: "\f6a0";
}

.fa-torii-gate:before {
  content: "\f6a1";
}

.fa-trade-federation:before {
  content: "\f513";
}

.fa-trademark:before {
  content: "\f25c";
}

.fa-traffic-light:before {
  content: "\f637";
}

.fa-train:before {
  content: "\f238";
}

.fa-transgender:before {
  content: "\f224";
}

.fa-transgender-alt:before {
  content: "\f225";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-trash-alt:before {
  content: "\f2ed";
}

.fa-tree:before {
  content: "\f1bb";
}

.fa-trello:before {
  content: "\f181";
}

.fa-tripadvisor:before {
  content: "\f262";
}

.fa-trophy:before {
  content: "\f091";
}

.fa-truck:before {
  content: "\f0d1";
}

.fa-truck-loading:before {
  content: "\f4de";
}

.fa-truck-monster:before {
  content: "\f63b";
}

.fa-truck-moving:before {
  content: "\f4df";
}

.fa-truck-pickup:before {
  content: "\f63c";
}

.fa-tshirt:before {
  content: "\f553";
}

.fa-tty:before {
  content: "\f1e4";
}

.fa-tumblr:before {
  content: "\f173";
}

.fa-tumblr-square:before {
  content: "\f174";
}

.fa-tv:before {
  content: "\f26c";
}

.fa-twitch:before {
  content: "\f1e8";
}

.fa-twitter:before {
  content: "\f099";
}

.fa-twitter-square:before {
  content: "\f081";
}

.fa-typo3:before {
  content: "\f42b";
}

.fa-uber:before {
  content: "\f402";
}

.fa-uikit:before {
  content: "\f403";
}

.fa-umbrella:before {
  content: "\f0e9";
}

.fa-umbrella-beach:before {
  content: "\f5ca";
}

.fa-underline:before {
  content: "\f0cd";
}

.fa-undo:before {
  content: "\f0e2";
}

.fa-undo-alt:before {
  content: "\f2ea";
}

.fa-uniregistry:before {
  content: "\f404";
}

.fa-universal-access:before {
  content: "\f29a";
}

.fa-university:before {
  content: "\f19c";
}

.fa-unlink:before {
  content: "\f127";
}

.fa-unlock:before {
  content: "\f09c";
}

.fa-unlock-alt:before {
  content: "\f13e";
}

.fa-untappd:before {
  content: "\f405";
}

.fa-upload:before {
  content: "\f093";
}

.fa-usb:before {
  content: "\f287";
}

.fa-user:before {
  content: "\f007";
}

.fa-user-alt:before {
  content: "\f406";
}

.fa-user-alt-slash:before {
  content: "\f4fa";
}

.fa-user-astronaut:before {
  content: "\f4fb";
}

.fa-user-check:before {
  content: "\f4fc";
}

.fa-user-circle:before {
  content: "\f2bd";
}

.fa-user-clock:before {
  content: "\f4fd";
}

.fa-user-cog:before {
  content: "\f4fe";
}

.fa-user-edit:before {
  content: "\f4ff";
}

.fa-user-friends:before {
  content: "\f500";
}

.fa-user-graduate:before {
  content: "\f501";
}

.fa-user-lock:before {
  content: "\f502";
}

.fa-user-md:before {
  content: "\f0f0";
}

.fa-user-minus:before {
  content: "\f503";
}

.fa-user-ninja:before {
  content: "\f504";
}

.fa-user-plus:before {
  content: "\f234";
}

.fa-user-secret:before {
  content: "\f21b";
}

.fa-user-shield:before {
  content: "\f505";
}

.fa-user-slash:before {
  content: "\f506";
}

.fa-user-tag:before {
  content: "\f507";
}

.fa-user-tie:before {
  content: "\f508";
}

.fa-user-times:before {
  content: "\f235";
}

.fa-users:before {
  content: "\f0c0";
}

.fa-users-cog:before {
  content: "\f509";
}

.fa-ussunnah:before {
  content: "\f407";
}

.fa-utensil-spoon:before {
  content: "\f2e5";
}

.fa-utensils:before {
  content: "\f2e7";
}

.fa-vaadin:before {
  content: "\f408";
}

.fa-vector-square:before {
  content: "\f5cb";
}

.fa-venus:before {
  content: "\f221";
}

.fa-venus-double:before {
  content: "\f226";
}

.fa-venus-mars:before {
  content: "\f228";
}

.fa-viacoin:before {
  content: "\f237";
}

.fa-viadeo:before {
  content: "\f2a9";
}

.fa-viadeo-square:before {
  content: "\f2aa";
}

.fa-vial:before {
  content: "\f492";
}

.fa-vials:before {
  content: "\f493";
}

.fa-viber:before {
  content: "\f409";
}

.fa-video:before {
  content: "\f03d";
}

.fa-video-slash:before {
  content: "\f4e2";
}

.fa-vihara:before {
  content: "\f6a7";
}

.fa-vimeo:before {
  content: "\f40a";
}

.fa-vimeo-square:before {
  content: "\f194";
}

.fa-vimeo-v:before {
  content: "\f27d";
}

.fa-vine:before {
  content: "\f1ca";
}

.fa-vk:before {
  content: "\f189";
}

.fa-vnv:before {
  content: "\f40b";
}

.fa-volleyball-ball:before {
  content: "\f45f";
}

.fa-volume-down:before {
  content: "\f027";
}

.fa-volume-off:before {
  content: "\f026";
}

.fa-volume-up:before {
  content: "\f028";
}

.fa-vuejs:before {
  content: "\f41f";
}

.fa-walking:before {
  content: "\f554";
}

.fa-wallet:before {
  content: "\f555";
}

.fa-warehouse:before {
  content: "\f494";
}

.fa-weebly:before {
  content: "\f5cc";
}

.fa-weibo:before {
  content: "\f18a";
}

.fa-weight:before {
  content: "\f496";
}

.fa-weight-hanging:before {
  content: "\f5cd";
}

.fa-weixin:before {
  content: "\f1d7";
}

.fa-whatsapp:before {
  content: "\f232";
}

.fa-whatsapp-square:before {
  content: "\f40c";
}

.fa-wheelchair:before {
  content: "\f193";
}

.fa-whmcs:before {
  content: "\f40d";
}

.fa-wifi:before {
  content: "\f1eb";
}

.fa-wikipedia-w:before {
  content: "\f266";
}

.fa-window-close:before {
  content: "\f410";
}

.fa-window-maximize:before {
  content: "\f2d0";
}

.fa-window-minimize:before {
  content: "\f2d1";
}

.fa-window-restore:before {
  content: "\f2d2";
}

.fa-windows:before {
  content: "\f17a";
}

.fa-wine-glass:before {
  content: "\f4e3";
}

.fa-wine-glass-alt:before {
  content: "\f5ce";
}

.fa-wix:before {
  content: "\f5cf";
}

.fa-wolf-pack-battalion:before {
  content: "\f514";
}

.fa-won-sign:before {
  content: "\f159";
}

.fa-wordpress:before {
  content: "\f19a";
}

.fa-wordpress-simple:before {
  content: "\f411";
}

.fa-wpbeginner:before {
  content: "\f297";
}

.fa-wpexplorer:before {
  content: "\f2de";
}

.fa-wpforms:before {
  content: "\f298";
}

.fa-wrench:before {
  content: "\f0ad";
}

.fa-x-ray:before {
  content: "\f497";
}

.fa-xbox:before {
  content: "\f412";
}

.fa-xing:before {
  content: "\f168";
}

.fa-xing-square:before {
  content: "\f169";
}

.fa-y-combinator:before {
  content: "\f23b";
}

.fa-yahoo:before {
  content: "\f19e";
}

.fa-yandex:before {
  content: "\f413";
}

.fa-yandex-international:before {
  content: "\f414";
}

.fa-yelp:before {
  content: "\f1e9";
}

.fa-yen-sign:before {
  content: "\f157";
}

.fa-yin-yang:before {
  content: "\f6ad";
}

.fa-yoast:before {
  content: "\f2b1";
}

.fa-youtube:before {
  content: "\f167";
}

.fa-youtube-square:before {
  content: "\f431";
}

.fa-zhihu:before {
  content: "\f63f";
}

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

/* Apparent font-awesome bug? The circle has some kind of bottom margin */
#kurento .fa-info-circle {
  height: 1em;
}

#kurento :root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #007bff;
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

#kurento *,
#kurento *::before,
#kurento *::after {
  box-sizing: border-box;
}

#kurento html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -ms-overflow-style: scrollbar;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

@-ms-viewport {
  width: device-width;
}

#kurento article,
#kurento aside,
#kurento dialog,
#kurento figcaption,
#kurento figure,
#kurento footer,
#kurento header,
#kurento hgroup,
#kurento main,
#kurento nav,
#kurento section {
  display: block;
}

#kurento body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

#kurento [tabindex="-1"]:focus {
  outline: 0 !important;
}

#kurento hr {
  box-sizing: content-box;
  height: 0;
  overflow: visible;
}

#kurento h1,
#kurento h2,
#kurento h3,
#kurento h4,
#kurento h5,
#kurento h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

#kurento p {
  margin-top: 0;
  margin-bottom: 1rem;
}

#kurento abbr[title],
#kurento abbr[data-original-title] {
  text-decoration: underline;
  text-decoration: underline dotted;
  cursor: help;
  border-bottom: 0;
}

#kurento address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

#kurento ol,
#kurento ul,
#kurento dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

#kurento ol ol,
#kurento ul ul,
#kurento ol ul,
#kurento ul ol {
  margin-bottom: 0;
}

#kurento dt {
  font-weight: 700;
}

#kurento dd {
  margin-bottom: .5rem;
  margin-left: 0;
}

#kurento blockquote {
  margin: 0 0 1rem;
}

#kurento dfn {
  font-style: italic;
}

#kurento b,
#kurento strong {
  font-weight: bolder;
}

#kurento small {
  font-size: 80%;
}

#kurento sub,
#kurento sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

#kurento sub {
  bottom: -.25em;
}

#kurento sup {
  top: -.5em;
}

#kurento a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
  -webkit-text-decoration-skip: objects;
}

#kurento a:hover {
  color: #0056b3;
  text-decoration: underline;
}

#kurento a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}

#kurento a:not([href]):not([tabindex]):hover,
#kurento a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}

#kurento a:not([href]):not([tabindex]):focus {
  outline: 0;
}

#kurento pre,
#kurento code,
#kurento kbd,
#kurento samp {
  font-family: monospace, monospace;
  font-size: 1em;
}

#kurento pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

#kurento figure {
  margin: 0 0 1rem;
}

#kurento img {
  vertical-align: middle;
  border-style: none;
}

#kurento svg:not(:root) {
  overflow: hidden;
}

#kurento table {
  border-collapse: collapse;
}

#kurento caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

#kurento th {
  text-align: inherit;
}

#kurento label {
  display: inline-block;
  margin-bottom: .5rem;
}

#kurento button {
  border-radius: 0;
}

#kurento button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

#kurento input,
#kurento button,
#kurento select,
#kurento optgroup,
#kurento textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

#kurento button,
#kurento input {
  overflow: visible;
}

#kurento button,
#kurento select {
  text-transform: none;
}

#kurento button,
#kurento html [type="button"],
#kurento [type="reset"],
#kurento [type="submit"] {
  -webkit-appearance: button;
}

#kurento button::-moz-focus-inner,
#kurento [type="button"]::-moz-focus-inner,
#kurento [type="reset"]::-moz-focus-inner,
#kurento [type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

#kurento input[type="radio"],
#kurento input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}

#kurento input[type="date"],
#kurento input[type="time"],
#kurento input[type="datetime-local"],
#kurento input[type="month"] {
  -webkit-appearance: listbox;
}

#kurento textarea {
  overflow: auto;
  resize: vertical;
}

#kurento fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

#kurento legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

#kurento progress {
  vertical-align: baseline;
}

#kurento [type="number"]::-webkit-inner-spin-button,
#kurento [type="number"]::-webkit-outer-spin-button {
  height: auto;
}

#kurento [type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

#kurento [type="search"]::-webkit-search-cancel-button,
#kurento [type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

#kurento ::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

#kurento output {
  display: inline-block;
}

#kurento summary {
  display: list-item;
  cursor: pointer;
}

#kurento template {
  display: none;
}

#kurento [hidden] {
  display: none !important;
}

#kurento h1,
#kurento h2,
#kurento h3,
#kurento h4,
#kurento h5,
#kurento h6,
#kurento .h1,
#kurento .h2,
#kurento .h3,
#kurento .h4,
#kurento .h5,
#kurento .h6 {
  margin-bottom: 0.5rem;
  font-family: inherit;
  font-weight: 500;
  line-height: 1.2;
  color: inherit;
}

#kurento h1,
#kurento .h1 {
  font-size: 2.5rem;
}

#kurento h2,
#kurento .h2 {
  font-size: 2rem;
}

#kurento h3,
#kurento .h3 {
  font-size: 1.75rem;
}

#kurento h4,
#kurento .h4 {
  font-size: 1.5rem;
}

#kurento h5,
#kurento .h5 {
  font-size: 1.25rem;
}

#kurento h6,
#kurento .h6 {
  font-size: 1rem;
}

#kurento .lead {
  font-size: 1.25rem;
  font-weight: 300;
}

#kurento .display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

#kurento .display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}

#kurento .display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}

#kurento .display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}

#kurento hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

#kurento small,
#kurento .small {
  font-size: 80%;
  font-weight: 400;
}

#kurento mark,
#kurento .mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

#kurento .list-unstyled {
  padding-left: 0;
  list-style: none;
}

#kurento .list-inline {
  padding-left: 0;
  list-style: none;
}

#kurento .list-inline-item {
  display: inline-block;
}

#kurento .list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

#kurento .initialism {
  font-size: 90%;
  text-transform: uppercase;
}

#kurento .blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

#kurento .blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d;
}

#kurento .blockquote-footer::before {
  content: "\2014 \00A0";
}

#kurento .img-fluid {
  max-width: 100%;
  height: auto;
}

#kurento .img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

#kurento .figure {
  display: inline-block;
}

#kurento .figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

#kurento .figure-caption {
  font-size: 90%;
  color: #6c757d;
}

#kurento .container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  #kurento .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  #kurento .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  #kurento .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  #kurento .container {
    max-width: 1140px;
  }
}

#kurento .container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

#kurento .row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

#kurento .no-gutters {
  margin-right: 0;
  margin-left: 0;
}

#kurento .no-gutters>.col,
#kurento .no-gutters>[class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

#kurento .col-1,
#kurento .col-2,
#kurento .col-3,
#kurento .col-4,
#kurento .col-5,
#kurento .col-6,
#kurento .col-7,
#kurento .col-8,
#kurento .col-9,
#kurento .col-10,
#kurento .col-11,
#kurento .col-12,
#kurento .col,
#kurento .col-auto,
#kurento .col-sm-1,
#kurento .col-sm-2,
#kurento .col-sm-3,
#kurento .col-sm-4,
#kurento .col-sm-5,
#kurento .col-sm-6,
#kurento .col-sm-7,
#kurento .col-sm-8,
#kurento .col-sm-9,
#kurento .col-sm-10,
#kurento .col-sm-11,
#kurento .col-sm-12,
#kurento .col-sm,
#kurento .col-sm-auto,
#kurento .col-md-1,
#kurento .col-md-2,
#kurento .col-md-3,
#kurento .col-md-4,
#kurento .col-md-5,
#kurento .col-md-6,
#kurento .col-md-7,
#kurento .col-md-8,
#kurento .col-md-9,
#kurento .col-md-10,
#kurento .col-md-11,
#kurento .col-md-12,
#kurento .col-md,
#kurento .col-md-auto,
#kurento .col-lg-1,
#kurento .col-lg-2,
#kurento .col-lg-3,
#kurento .col-lg-4,
#kurento .col-lg-5,
#kurento .col-lg-6,
#kurento .col-lg-7,
#kurento .col-lg-8,
#kurento .col-lg-9,
#kurento .col-lg-10,
#kurento .col-lg-11,
#kurento .col-lg-12,
#kurento .col-lg,
#kurento .col-lg-auto,
#kurento .col-xl-1,
#kurento .col-xl-2,
#kurento .col-xl-3,
#kurento .col-xl-4,
#kurento .col-xl-5,
#kurento .col-xl-6,
#kurento .col-xl-7,
#kurento .col-xl-8,
#kurento .col-xl-9,
#kurento .col-xl-10,
#kurento .col-xl-11,
#kurento .col-xl-12,
#kurento .col-xl,
#kurento .col-xl-auto {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

#kurento .col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
}

#kurento .col-auto {
  flex: 0 0 auto;
  width: auto;
  max-width: none;
  display: none;
}

#kurento .col-1 {
  flex: 0 0 8.33333%;
  max-width: 8.33333%;
}

#kurento .col-2 {
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}

#kurento .col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

#kurento .col-4 {
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}

#kurento .col-5 {
  flex: 0 0 41.66667%;
  max-width: 41.66667%;
}

#kurento .col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

#kurento .col-7 {
  flex: 0 0 58.33333%;
  max-width: 58.33333%;
}

#kurento .col-8 {
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
}

#kurento .col-9 {
  flex: 0 0 75%;
  max-width: 75%;
}

#kurento .col-10 {
  flex: 0 0 83.33333%;
  max-width: 83.33333%;
}

#kurento .col-11 {
  flex: 0 0 91.66667%;
  max-width: 91.66667%;
}

#kurento .col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

#kurento .order-first {
  order: -1;
}

#kurento .order-last {
  order: 13;
}

#kurento .order-0 {
  order: 0;
}

#kurento .order-1 {
  order: 1;
}

#kurento .order-2 {
  order: 2;
}

#kurento .order-3 {
  order: 3;
}

#kurento .order-4 {
  order: 4;
}

#kurento .order-5 {
  order: 5;
}

#kurento .order-6 {
  order: 6;
}

#kurento .order-7 {
  order: 7;
}

#kurento .order-8 {
  order: 8;
}

#kurento .order-9 {
  order: 9;
}

#kurento .order-10 {
  order: 10;
}

#kurento .order-11 {
  order: 11;
}

#kurento .order-12 {
  order: 12;
}

#kurento .offset-1 {
  margin-left: 8.33333%;
}

#kurento .offset-2 {
  margin-left: 16.66667%;
}

#kurento .offset-3 {
  margin-left: 25%;
}

#kurento .offset-4 {
  margin-left: 33.33333%;
}

#kurento .offset-5 {
  margin-left: 41.66667%;
}

#kurento .offset-6 {
  margin-left: 50%;
}

#kurento .offset-7 {
  margin-left: 58.33333%;
}

#kurento .offset-8 {
  margin-left: 66.66667%;
}

#kurento .offset-9 {
  margin-left: 75%;
}

#kurento .offset-10 {
  margin-left: 83.33333%;
}

#kurento .offset-11 {
  margin-left: 91.66667%;
}

@media (min-width: 576px) {
  #kurento .col-sm {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  #kurento .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  #kurento .col-sm-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }

  #kurento .col-sm-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }

  #kurento .col-sm-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  #kurento .col-sm-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  #kurento .col-sm-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }

  #kurento .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  #kurento .col-sm-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }

  #kurento .col-sm-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }

  #kurento .col-sm-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  #kurento .col-sm-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }

  #kurento .col-sm-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }

  #kurento .col-sm-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  #kurento .order-sm-first {
    order: -1;
  }

  #kurento .order-sm-last {
    order: 13;
  }

  #kurento .order-sm-0 {
    order: 0;
  }

  #kurento .order-sm-1 {
    order: 1;
  }

  #kurento .order-sm-2 {
    order: 2;
  }

  #kurento .order-sm-3 {
    order: 3;
  }

  #kurento .order-sm-4 {
    order: 4;
  }

  #kurento .order-sm-5 {
    order: 5;
  }

  #kurento .order-sm-6 {
    order: 6;
  }

  #kurento .order-sm-7 {
    order: 7;
  }

  #kurento .order-sm-8 {
    order: 8;
  }

  #kurento .order-sm-9 {
    order: 9;
  }

  #kurento .order-sm-10 {
    order: 10;
  }

  #kurento .order-sm-11 {
    order: 11;
  }

  #kurento .order-sm-12 {
    order: 12;
  }

  #kurento .offset-sm-0 {
    margin-left: 0;
  }

  #kurento .offset-sm-1 {
    margin-left: 8.33333%;
  }

  #kurento .offset-sm-2 {
    margin-left: 16.66667%;
  }

  #kurento .offset-sm-3 {
    margin-left: 25%;
  }

  #kurento .offset-sm-4 {
    margin-left: 33.33333%;
  }

  #kurento .offset-sm-5 {
    margin-left: 41.66667%;
  }

  #kurento .offset-sm-6 {
    margin-left: 50%;
  }

  #kurento .offset-sm-7 {
    margin-left: 58.33333%;
  }

  #kurento .offset-sm-8 {
    margin-left: 66.66667%;
  }

  #kurento .offset-sm-9 {
    margin-left: 75%;
  }

  #kurento .offset-sm-10 {
    margin-left: 83.33333%;
  }

  #kurento .offset-sm-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 768px) {
  #kurento .col-md {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  #kurento .col-md-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  #kurento .col-md-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }

  #kurento .col-md-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }

  #kurento .col-md-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  #kurento .col-md-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  #kurento .col-md-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }

  #kurento .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  #kurento .col-md-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }

  #kurento .col-md-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }

  #kurento .col-md-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  #kurento .col-md-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }

  #kurento .col-md-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }

  #kurento .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  #kurento .order-md-first {
    order: -1;
  }

  #kurento .order-md-last {
    order: 13;
  }

  #kurento .order-md-0 {
    order: 0;
  }

  #kurento .order-md-1 {
    order: 1;
  }

  #kurento .order-md-2 {
    order: 2;
  }

  #kurento .order-md-3 {
    order: 3;
  }

  #kurento .order-md-4 {
    order: 4;
  }

  #kurento .order-md-5 {
    order: 5;
  }

  #kurento .order-md-6 {
    order: 6;
  }

  #kurento .order-md-7 {
    order: 7;
  }

  #kurento .order-md-8 {
    order: 8;
  }

  #kurento .order-md-9 {
    order: 9;
  }

  #kurento .order-md-10 {
    order: 10;
  }

  #kurento .order-md-11 {
    order: 11;
  }

  #kurento .order-md-12 {
    order: 12;
  }

  #kurento .offset-md-0 {
    margin-left: 0;
  }

  #kurento .offset-md-1 {
    margin-left: 8.33333%;
  }

  #kurento .offset-md-2 {
    margin-left: 16.66667%;
  }

  #kurento .offset-md-3 {
    margin-left: 25%;
  }

  #kurento .offset-md-4 {
    margin-left: 33.33333%;
  }

  #kurento .offset-md-5 {
    margin-left: 41.66667%;
  }

  #kurento .offset-md-6 {
    margin-left: 50%;
  }

  #kurento .offset-md-7 {
    margin-left: 58.33333%;
  }

  #kurento .offset-md-8 {
    margin-left: 66.66667%;
  }

  #kurento .offset-md-9 {
    margin-left: 75%;
  }

  #kurento .offset-md-10 {
    margin-left: 83.33333%;
  }

  #kurento .offset-md-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 992px) {
  #kurento .col-lg {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  #kurento .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  #kurento .col-lg-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }

  #kurento .col-lg-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }

  #kurento .col-lg-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  #kurento .col-lg-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  #kurento .col-lg-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }

  #kurento .col-lg-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  #kurento .col-lg-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }

  #kurento .col-lg-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }

  #kurento .col-lg-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  #kurento .col-lg-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }

  #kurento .col-lg-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }

  #kurento .col-lg-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  #kurento .order-lg-first {
    order: -1;
  }

  #kurento .order-lg-last {
    order: 13;
  }

  #kurento .order-lg-0 {
    order: 0;
  }

  #kurento .order-lg-1 {
    order: 1;
  }

  #kurento .order-lg-2 {
    order: 2;
  }

  #kurento .order-lg-3 {
    order: 3;
  }

  #kurento .order-lg-4 {
    order: 4;
  }

  #kurento .order-lg-5 {
    order: 5;
  }

  #kurento .order-lg-6 {
    order: 6;
  }

  #kurento .order-lg-7 {
    order: 7;
  }

  #kurento .order-lg-8 {
    order: 8;
  }

  #kurento .order-lg-9 {
    order: 9;
  }

  #kurento .order-lg-10 {
    order: 10;
  }

  #kurento .order-lg-11 {
    order: 11;
  }

  #kurento .order-lg-12 {
    order: 12;
  }

  #kurento .offset-lg-0 {
    margin-left: 0;
  }

  #kurento .offset-lg-1 {
    margin-left: 8.33333%;
  }

  #kurento .offset-lg-2 {
    margin-left: 16.66667%;
  }

  #kurento .offset-lg-3 {
    margin-left: 25%;
  }

  #kurento .offset-lg-4 {
    margin-left: 33.33333%;
  }

  #kurento .offset-lg-5 {
    margin-left: 41.66667%;
  }

  #kurento .offset-lg-6 {
    margin-left: 50%;
  }

  #kurento .offset-lg-7 {
    margin-left: 58.33333%;
  }

  #kurento .offset-lg-8 {
    margin-left: 66.66667%;
  }

  #kurento .offset-lg-9 {
    margin-left: 75%;
  }

  #kurento .offset-lg-10 {
    margin-left: 83.33333%;
  }

  #kurento .offset-lg-11 {
    margin-left: 91.66667%;
  }
}

@media (min-width: 1200px) {
  #kurento .col-xl {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%;
  }

  #kurento .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: none;
  }

  #kurento .col-xl-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%;
  }

  #kurento .col-xl-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }

  #kurento .col-xl-3 {
    flex: 0 0 25%;
    max-width: 25%;
  }

  #kurento .col-xl-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }

  #kurento .col-xl-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%;
  }

  #kurento .col-xl-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  #kurento .col-xl-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }

  #kurento .col-xl-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }

  #kurento .col-xl-9 {
    flex: 0 0 75%;
    max-width: 75%;
  }

  #kurento .col-xl-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }

  #kurento .col-xl-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%;
  }

  #kurento .col-xl-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  #kurento .order-xl-first {
    order: -1;
  }

  #kurento .order-xl-last {
    order: 13;
  }

  #kurento .order-xl-0 {
    order: 0;
  }

  #kurento .order-xl-1 {
    order: 1;
  }

  #kurento .order-xl-2 {
    order: 2;
  }

  #kurento .order-xl-3 {
    order: 3;
  }

  #kurento .order-xl-4 {
    order: 4;
  }

  #kurento .order-xl-5 {
    order: 5;
  }

  #kurento .order-xl-6 {
    order: 6;
  }

  #kurento .order-xl-7 {
    order: 7;
  }

  #kurento .order-xl-8 {
    order: 8;
  }

  #kurento .order-xl-9 {
    order: 9;
  }

  #kurento .order-xl-10 {
    order: 10;
  }

  #kurento .order-xl-11 {
    order: 11;
  }

  #kurento .order-xl-12 {
    order: 12;
  }

  #kurento .offset-xl-0 {
    margin-left: 0;
  }

  #kurento .offset-xl-1 {
    margin-left: 8.33333%;
  }

  #kurento .offset-xl-2 {
    margin-left: 16.66667%;
  }

  #kurento .offset-xl-3 {
    margin-left: 25%;
  }

  #kurento .offset-xl-4 {
    margin-left: 33.33333%;
  }

  #kurento .offset-xl-5 {
    margin-left: 41.66667%;
  }

  #kurento .offset-xl-6 {
    margin-left: 50%;
  }

  #kurento .offset-xl-7 {
    margin-left: 58.33333%;
  }

  #kurento .offset-xl-8 {
    margin-left: 66.66667%;
  }

  #kurento .offset-xl-9 {
    margin-left: 75%;
  }

  #kurento .offset-xl-10 {
    margin-left: 83.33333%;
  }

  #kurento .offset-xl-11 {
    margin-left: 91.66667%;
  }
}

#kurento .form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#kurento .form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

#kurento .form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#kurento .form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}

#kurento .form-control:disabled,
#kurento .form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

#kurento select.form-control:not([size]):not([multiple]) {
  height: calc(2.25rem + 2px);
}

#kurento select.form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

#kurento .form-control-file,
#kurento .form-control-range {
  display: block;
  width: 100%;
}

#kurento .col-form-label {
  padding-top: 0;
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

#kurento .col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}

#kurento .col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5;
}

#kurento .form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  line-height: 1.5;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}

#kurento .form-control-plaintext.form-control-sm,
#kurento .input-group-sm>.form-control-plaintext.form-control,
#kurento .input-group-sm>.input-group-prepend>.form-control-plaintext.input-group-text,
#kurento .input-group-sm>.input-group-append>.form-control-plaintext.input-group-text,
#kurento .input-group-sm>.input-group-prepend>.form-control-plaintext.btn,
#kurento .input-group-sm>.input-group-append>.form-control-plaintext.btn,
#kurento .form-control-plaintext.form-control-lg,
#kurento .input-group-lg>.form-control-plaintext.form-control,
#kurento .input-group-lg>.input-group-prepend>.form-control-plaintext.input-group-text,
#kurento .input-group-lg>.input-group-append>.form-control-plaintext.input-group-text,
#kurento .input-group-lg>.input-group-prepend>.form-control-plaintext.btn,
#kurento .input-group-lg>.input-group-append>.form-control-plaintext.btn {
  padding-right: 0;
  padding-left: 0;
}

#kurento .form-control-sm,
#kurento .input-group-sm>.form-control,
#kurento .input-group-sm>.input-group-prepend>.input-group-text,
#kurento .input-group-sm>.input-group-append>.input-group-text,
#kurento .input-group-sm>.input-group-prepend>.btn,
#kurento .input-group-sm>.input-group-append>.btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

#kurento select.form-control-sm:not([size]):not([multiple]),
#kurento .input-group-sm>select.form-control:not([size]):not([multiple]),
#kurento .input-group-sm>.input-group-prepend>select.input-group-text:not([size]):not([multiple]),
#kurento .input-group-sm>.input-group-append>select.input-group-text:not([size]):not([multiple]),
#kurento .input-group-sm>.input-group-prepend>select.btn:not([size]):not([multiple]),
#kurento .input-group-sm>.input-group-append>select.btn:not([size]):not([multiple]) {
  height: calc(1.8125rem + 2px);
}

#kurento .form-control-lg,
#kurento .input-group-lg>.form-control,
#kurento .input-group-lg>.input-group-prepend>.input-group-text,
#kurento .input-group-lg>.input-group-append>.input-group-text,
#kurento .input-group-lg>.input-group-prepend>.btn,
#kurento .input-group-lg>.input-group-append>.btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

#kurento select.form-control-lg:not([size]):not([multiple]),
#kurento .input-group-lg>select.form-control:not([size]):not([multiple]),
#kurento .input-group-lg>.input-group-prepend>select.input-group-text:not([size]):not([multiple]),
#kurento .input-group-lg>.input-group-append>select.input-group-text:not([size]):not([multiple]),
#kurento .input-group-lg>.input-group-prepend>select.btn:not([size]):not([multiple]),
#kurento .input-group-lg>.input-group-append>select.btn:not([size]):not([multiple]) {
  height: calc(2.875rem + 2px);
}

#kurento .form-group {
  margin-bottom: 1rem;
}

#kurento .form-text {
  display: block;
  margin-top: 0.25rem;
}

#kurento .form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}

#kurento .form-row>.col,
#kurento .form-row>[class*="col-"] {
  padding-right: 5px;
  padding-left: 5px;
}

#kurento .form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

#kurento .form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}

#kurento .form-check-input:disabled~.form-check-label {
  color: #6c757d;
}

#kurento .form-check-label {
  margin-bottom: 0;
}

#kurento .form-check-inline {
  display: inline-flex;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}

#kurento .form-check-inline .form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

#kurento .valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #28a745;
}

#kurento .valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: .5rem;
  margin-top: .1rem;
  font-size: .875rem;
  line-height: 1;
  color: #fff;
  background-color: rgba(40, 167, 69, 0.8);
  border-radius: .2rem;
}

.was-validated #kurento .form-control:valid,
#kurento .form-control.is-valid,
.was-validated #kurento .custom-select:valid,
#kurento .custom-select.is-valid {
  border-color: #28a745;
}

.was-validated #kurento .form-control:valid:focus,
#kurento .form-control.is-valid:focus,
.was-validated #kurento .custom-select:valid:focus,
#kurento .custom-select.is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated #kurento .form-control:valid~.valid-feedback,
.was-validated #kurento .form-control:valid~.valid-tooltip,
#kurento .form-control.is-valid~.valid-feedback,
#kurento .form-control.is-valid~.valid-tooltip,
.was-validated #kurento .custom-select:valid~.valid-feedback,
.was-validated #kurento .custom-select:valid~.valid-tooltip,
#kurento .custom-select.is-valid~.valid-feedback,
#kurento .custom-select.is-valid~.valid-tooltip {
  display: block;
}

.was-validated #kurento .form-check-input:valid~.form-check-label,
#kurento .form-check-input.is-valid~.form-check-label {
  color: #28a745;
}

.was-validated #kurento .form-check-input:valid~.valid-feedback,
.was-validated #kurento .form-check-input:valid~.valid-tooltip,
#kurento .form-check-input.is-valid~.valid-feedback,
#kurento .form-check-input.is-valid~.valid-tooltip {
  display: block;
}

.was-validated #kurento .custom-control-input:valid~.custom-control-label,
#kurento .custom-control-input.is-valid~.custom-control-label {
  color: #28a745;
}

.was-validated #kurento .custom-control-input:valid~.custom-control-label::before,
#kurento .custom-control-input.is-valid~.custom-control-label::before {
  background-color: #71dd8a;
}

.was-validated #kurento .custom-control-input:valid~.valid-feedback,
.was-validated #kurento .custom-control-input:valid~.valid-tooltip,
#kurento .custom-control-input.is-valid~.valid-feedback,
#kurento .custom-control-input.is-valid~.valid-tooltip {
  display: block;
}

.was-validated #kurento .custom-control-input:valid:checked~.custom-control-label::before,
#kurento .custom-control-input.is-valid:checked~.custom-control-label::before {
  background-color: #34ce57;
}

.was-validated #kurento .custom-control-input:valid:focus~.custom-control-label::before,
#kurento .custom-control-input.is-valid:focus~.custom-control-label::before {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.was-validated #kurento .custom-file-input:valid~.custom-file-label,
#kurento .custom-file-input.is-valid~.custom-file-label {
  border-color: #28a745;
}

.was-validated #kurento .custom-file-input:valid~.custom-file-label::before,
#kurento .custom-file-input.is-valid~.custom-file-label::before {
  border-color: inherit;
}

.was-validated #kurento .custom-file-input:valid~.valid-feedback,
.was-validated #kurento .custom-file-input:valid~.valid-tooltip,
#kurento .custom-file-input.is-valid~.valid-feedback,
#kurento .custom-file-input.is-valid~.valid-tooltip {
  display: block;
}

.was-validated #kurento .custom-file-input:valid:focus~.custom-file-label,
#kurento .custom-file-input.is-valid:focus~.custom-file-label {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

#kurento .invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

#kurento .invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: .5rem;
  margin-top: .1rem;
  font-size: .875rem;
  line-height: 1;
  color: #fff;
  background-color: rgba(220, 53, 69, 0.8);
  border-radius: .2rem;
}

.was-validated #kurento .form-control:invalid,
#kurento .form-control.is-invalid,
.was-validated #kurento .custom-select:invalid,
#kurento .custom-select.is-invalid {
  border-color: #dc3545;
}

.was-validated #kurento .form-control:invalid:focus,
#kurento .form-control.is-invalid:focus,
.was-validated #kurento .custom-select:invalid:focus,
#kurento .custom-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.was-validated #kurento .form-control:invalid~.invalid-feedback,
.was-validated #kurento .form-control:invalid~.invalid-tooltip,
#kurento .form-control.is-invalid~.invalid-feedback,
#kurento .form-control.is-invalid~.invalid-tooltip,
.was-validated #kurento .custom-select:invalid~.invalid-feedback,
.was-validated #kurento .custom-select:invalid~.invalid-tooltip,
#kurento .custom-select.is-invalid~.invalid-feedback,
#kurento .custom-select.is-invalid~.invalid-tooltip {
  display: block;
}

.was-validated #kurento .form-check-input:invalid~.form-check-label,
#kurento .form-check-input.is-invalid~.form-check-label {
  color: #dc3545;
}

.was-validated #kurento .form-check-input:invalid~.invalid-feedback,
.was-validated #kurento .form-check-input:invalid~.invalid-tooltip,
#kurento .form-check-input.is-invalid~.invalid-feedback,
#kurento .form-check-input.is-invalid~.invalid-tooltip {
  display: block;
}

.was-validated #kurento .custom-control-input:invalid~.custom-control-label,
#kurento .custom-control-input.is-invalid~.custom-control-label {
  color: #dc3545;
}

.was-validated #kurento .custom-control-input:invalid~.custom-control-label::before,
#kurento .custom-control-input.is-invalid~.custom-control-label::before {
  background-color: #efa2a9;
}

.was-validated #kurento .custom-control-input:invalid~.invalid-feedback,
.was-validated #kurento .custom-control-input:invalid~.invalid-tooltip,
#kurento .custom-control-input.is-invalid~.invalid-feedback,
#kurento .custom-control-input.is-invalid~.invalid-tooltip {
  display: block;
}

.was-validated #kurento .custom-control-input:invalid:checked~.custom-control-label::before,
#kurento .custom-control-input.is-invalid:checked~.custom-control-label::before {
  background-color: #e4606d;
}

.was-validated #kurento .custom-control-input:invalid:focus~.custom-control-label::before,
#kurento .custom-control-input.is-invalid:focus~.custom-control-label::before {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.was-validated #kurento .custom-file-input:invalid~.custom-file-label,
#kurento .custom-file-input.is-invalid~.custom-file-label {
  border-color: #dc3545;
}

.was-validated #kurento .custom-file-input:invalid~.custom-file-label::before,
#kurento .custom-file-input.is-invalid~.custom-file-label::before {
  border-color: inherit;
}

.was-validated #kurento .custom-file-input:invalid~.invalid-feedback,
.was-validated #kurento .custom-file-input:invalid~.invalid-tooltip,
#kurento .custom-file-input.is-invalid~.invalid-feedback,
#kurento .custom-file-input.is-invalid~.invalid-tooltip {
  display: block;
}

.was-validated #kurento .custom-file-input:invalid:focus~.custom-file-label,
#kurento .custom-file-input.is-invalid:focus~.custom-file-label {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

#kurento .form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

#kurento .form-inline .form-check {
  width: 100%;
}

@media (min-width: 576px) {
  #kurento .form-inline label {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
  }

  #kurento .form-inline .form-group {
    display: flex;
    flex: 0 0 auto;
    flex-flow: row wrap;
    align-items: center;
    margin-bottom: 0;
  }

  #kurento .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }

  #kurento .form-inline .form-control-plaintext {
    display: inline-block;
  }

  #kurento .form-inline .input-group {
    width: auto;
  }

  #kurento .form-inline .form-check {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }

  #kurento .form-inline .form-check-input {
    position: relative;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }

  #kurento .form-inline .custom-control {
    align-items: center;
    justify-content: center;
  }

  #kurento .form-inline .custom-control-label {
    margin-bottom: 0;
  }
}

#kurento .btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#kurento .btn:hover,
#kurento .btn:focus {
  text-decoration: none;
}

#kurento .btn:focus,
#kurento .btn.focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#kurento .btn.disabled,
#kurento .btn:disabled {
  opacity: 0.65;
}

#kurento .btn:not(:disabled):not(.disabled) {
  cursor: pointer;
}

#kurento .btn:not(:disabled):not(.disabled):active,
#kurento .btn:not(:disabled):not(.disabled).active {
  background-image: none;
}

#kurento a.btn.disabled,
#kurento fieldset:disabled a.btn {
  pointer-events: none;
}

#kurento .btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

#kurento .btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

#kurento .btn-primary:focus,
#kurento .btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

#kurento .btn-primary.disabled,
#kurento .btn-primary:disabled {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

#kurento .btn-primary:not(:disabled):not(.disabled):active,
#kurento .btn-primary:not(:disabled):not(.disabled).active,
.show>#kurento .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}

#kurento .btn-primary:not(:disabled):not(.disabled):active:focus,
#kurento .btn-primary:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

#kurento .btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

#kurento .btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

#kurento .btn-secondary:focus,
#kurento .btn-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

#kurento .btn-secondary.disabled,
#kurento .btn-secondary:disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

#kurento .btn-secondary:not(:disabled):not(.disabled):active,
#kurento .btn-secondary:not(:disabled):not(.disabled).active,
.show>#kurento .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}

#kurento .btn-secondary:not(:disabled):not(.disabled):active:focus,
#kurento .btn-secondary:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

#kurento .btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

#kurento .btn-success:hover {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
}

#kurento .btn-success:focus,
#kurento .btn-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

#kurento .btn-success.disabled,
#kurento .btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

#kurento .btn-success:not(:disabled):not(.disabled):active,
#kurento .btn-success:not(:disabled):not(.disabled).active,
.show>#kurento .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}

#kurento .btn-success:not(:disabled):not(.disabled):active:focus,
#kurento .btn-success:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

#kurento .btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

#kurento .btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}

#kurento .btn-info:focus,
#kurento .btn-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

#kurento .btn-info.disabled,
#kurento .btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

#kurento .btn-info:not(:disabled):not(.disabled):active,
#kurento .btn-info:not(:disabled):not(.disabled).active,
.show>#kurento .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}

#kurento .btn-info:not(:disabled):not(.disabled):active:focus,
#kurento .btn-info:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

#kurento .btn-warning {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

#kurento .btn-warning:hover {
  color: #212529;
  background-color: #e0a800;
  border-color: #d39e00;
}

#kurento .btn-warning:focus,
#kurento .btn-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

#kurento .btn-warning.disabled,
#kurento .btn-warning:disabled {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

#kurento .btn-warning:not(:disabled):not(.disabled):active,
#kurento .btn-warning:not(:disabled):not(.disabled).active,
.show>#kurento .btn-warning.dropdown-toggle {
  color: #212529;
  background-color: #d39e00;
  border-color: #c69500;
}

#kurento .btn-warning:not(:disabled):not(.disabled):active:focus,
#kurento .btn-warning:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

#kurento .btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

#kurento .btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

#kurento .btn-danger:focus,
#kurento .btn-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

#kurento .btn-danger.disabled,
#kurento .btn-danger:disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

#kurento .btn-danger:not(:disabled):not(.disabled):active,
#kurento .btn-danger:not(:disabled):not(.disabled).active,
.show>#kurento .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #bd2130;
  border-color: #b21f2d;
}

#kurento .btn-danger:not(:disabled):not(.disabled):active:focus,
#kurento .btn-danger:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

#kurento .btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

#kurento .btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

#kurento .btn-light:focus,
#kurento .btn-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

#kurento .btn-light.disabled,
#kurento .btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

#kurento .btn-light:not(:disabled):not(.disabled):active,
#kurento .btn-light:not(:disabled):not(.disabled).active,
.show>#kurento .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}

#kurento .btn-light:not(:disabled):not(.disabled):active:focus,
#kurento .btn-light:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

#kurento .btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

#kurento .btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}

#kurento .btn-dark:focus,
#kurento .btn-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

#kurento .btn-dark.disabled,
#kurento .btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

#kurento .btn-dark:not(:disabled):not(.disabled):active,
#kurento .btn-dark:not(:disabled):not(.disabled).active,
.show>#kurento .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}

#kurento .btn-dark:not(:disabled):not(.disabled):active:focus,
#kurento .btn-dark:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

#kurento .btn-outline-primary {
  color: #007bff;
  background-color: transparent;
  background-image: none;
  border-color: #007bff;
}

#kurento .btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

#kurento .btn-outline-primary:focus,
#kurento .btn-outline-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

#kurento .btn-outline-primary.disabled,
#kurento .btn-outline-primary:disabled {
  color: #007bff;
  background-color: transparent;
}

#kurento .btn-outline-primary:not(:disabled):not(.disabled):active,
#kurento .btn-outline-primary:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

#kurento .btn-outline-primary:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

#kurento .btn-outline-secondary {
  color: #6c757d;
  background-color: transparent;
  background-image: none;
  border-color: #6c757d;
}

#kurento .btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

#kurento .btn-outline-secondary:focus,
#kurento .btn-outline-secondary.focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

#kurento .btn-outline-secondary.disabled,
#kurento .btn-outline-secondary:disabled {
  color: #6c757d;
  background-color: transparent;
}

#kurento .btn-outline-secondary:not(:disabled):not(.disabled):active,
#kurento .btn-outline-secondary:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

#kurento .btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

#kurento .btn-outline-success {
  color: #28a745;
  background-color: transparent;
  background-image: none;
  border-color: #28a745;
}

#kurento .btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

#kurento .btn-outline-success:focus,
#kurento .btn-outline-success.focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

#kurento .btn-outline-success.disabled,
#kurento .btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}

#kurento .btn-outline-success:not(:disabled):not(.disabled):active,
#kurento .btn-outline-success:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

#kurento .btn-outline-success:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-success:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

#kurento .btn-outline-info {
  color: #17a2b8;
  background-color: transparent;
  background-image: none;
  border-color: #17a2b8;
}

#kurento .btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

#kurento .btn-outline-info:focus,
#kurento .btn-outline-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

#kurento .btn-outline-info.disabled,
#kurento .btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}

#kurento .btn-outline-info:not(:disabled):not(.disabled):active,
#kurento .btn-outline-info:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

#kurento .btn-outline-info:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-info:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

#kurento .btn-outline-warning {
  color: #ffc107;
  background-color: transparent;
  background-image: none;
  border-color: #ffc107;
}

#kurento .btn-outline-warning:hover {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

#kurento .btn-outline-warning:focus,
#kurento .btn-outline-warning.focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

#kurento .btn-outline-warning.disabled,
#kurento .btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}

#kurento .btn-outline-warning:not(:disabled):not(.disabled):active,
#kurento .btn-outline-warning:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

#kurento .btn-outline-warning:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

#kurento .btn-outline-danger {
  color: #dc3545;
  background-color: transparent;
  background-image: none;
  border-color: #dc3545;
}

#kurento .btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

#kurento .btn-outline-danger:focus,
#kurento .btn-outline-danger.focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

#kurento .btn-outline-danger.disabled,
#kurento .btn-outline-danger:disabled {
  color: #dc3545;
  background-color: transparent;
}

#kurento .btn-outline-danger:not(:disabled):not(.disabled):active,
#kurento .btn-outline-danger:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

#kurento .btn-outline-danger:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

#kurento .btn-outline-light {
  color: #f8f9fa;
  background-color: transparent;
  background-image: none;
  border-color: #f8f9fa;
}

#kurento .btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

#kurento .btn-outline-light:focus,
#kurento .btn-outline-light.focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

#kurento .btn-outline-light.disabled,
#kurento .btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}

#kurento .btn-outline-light:not(:disabled):not(.disabled):active,
#kurento .btn-outline-light:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

#kurento .btn-outline-light:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

#kurento .btn-outline-dark {
  color: #343a40;
  background-color: transparent;
  background-image: none;
  border-color: #343a40;
}

#kurento .btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

#kurento .btn-outline-dark:focus,
#kurento .btn-outline-dark.focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

#kurento .btn-outline-dark.disabled,
#kurento .btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}

#kurento .btn-outline-dark:not(:disabled):not(.disabled):active,
#kurento .btn-outline-dark:not(:disabled):not(.disabled).active,
.show>#kurento .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

#kurento .btn-outline-dark:not(:disabled):not(.disabled):active:focus,
#kurento .btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.show>#kurento .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

#kurento .btn-link {
  font-weight: 400;
  color: #007bff;
  background-color: transparent;
}

#kurento .btn-link:hover {
  color: #0056b3;
  text-decoration: underline;
  background-color: transparent;
  border-color: transparent;
}

#kurento .btn-link:focus,
#kurento .btn-link.focus {
  text-decoration: underline;
  border-color: transparent;
  box-shadow: none;
}

#kurento .btn-link:disabled,
#kurento .btn-link.disabled {
  color: #6c757d;
}

#kurento .btn-lg,
#kurento .btn-group-lg>.btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

#kurento .btn-sm,
#kurento .btn-group-sm>.btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

#kurento .btn-block {
  display: block;
  width: 100%;
}

#kurento .btn-block+.btn-block {
  margin-top: 0.5rem;
}

#kurento input[type="submit"].btn-block,
#kurento input[type="reset"].btn-block,
#kurento input[type="button"].btn-block {
  width: 100%;
}

#kurento .fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}

#kurento .fade.show {
  opacity: 1;
}

#kurento .collapse {
  display: none;
}

#kurento .collapse.show {
  display: block;
}

#kurento tr.collapse.show {
  display: table-row;
}

#kurento tbody.collapse.show {
  display: table-row-group;
}

#kurento .collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

#kurento .dropup,
#kurento .dropdown {
  position: relative;
}

#kurento .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

#kurento .dropdown-toggle:empty::after {
  margin-left: 0;
}

#kurento .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

#kurento .dropup .dropdown-menu {
  margin-top: 0;
  margin-bottom: 0.125rem;
}

#kurento .dropup .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

#kurento .dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

#kurento .dropright .dropdown-menu {
  margin-top: 0;
  margin-left: 0.125rem;
}

#kurento .dropright .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}

#kurento .dropright .dropdown-toggle:empty::after {
  margin-left: 0;
}

#kurento .dropright .dropdown-toggle::after {
  vertical-align: 0;
}

#kurento .dropleft .dropdown-menu {
  margin-top: 0;
  margin-right: 0.125rem;
}

#kurento .dropleft .dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}

#kurento .dropleft .dropdown-toggle::after {
  display: none;
}

#kurento .dropleft .dropdown-toggle::before {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}

#kurento .dropleft .dropdown-toggle:empty::after {
  margin-left: 0;
}

#kurento .dropleft .dropdown-toggle::before {
  vertical-align: 0;
}

#kurento .dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}

#kurento .dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

#kurento .dropdown-item:hover,
#kurento .dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}

#kurento .dropdown-item.active,
#kurento .dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #007bff;
}

#kurento .dropdown-item.disabled,
#kurento .dropdown-item:disabled {
  color: #6c757d;
  background-color: transparent;
}

#kurento .dropdown-menu.show {
  display: block;
}

#kurento .dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

#kurento .btn-group,
#kurento .btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
  padding-top: .8em;
}

#kurento .btn-group>.btn,
#kurento .btn-group-vertical>.btn {
  position: relative;
  flex: 0 1 auto;
}

#kurento .btn-group>.btn:hover,
#kurento .btn-group-vertical>.btn:hover {
  z-index: 1;
}

#kurento .btn-group>.btn:focus,
#kurento .btn-group>.btn:active,
#kurento .btn-group>.btn.active,
#kurento .btn-group-vertical>.btn:focus,
#kurento .btn-group-vertical>.btn:active,
#kurento .btn-group-vertical>.btn.active {
  z-index: 1;
}

#kurento .btn-group .btn+.btn,
#kurento .btn-group .btn+.btn-group,
#kurento .btn-group .btn-group+.btn,
#kurento .btn-group .btn-group+.btn-group,
#kurento .btn-group-vertical .btn+.btn,
#kurento .btn-group-vertical .btn+.btn-group,
#kurento .btn-group-vertical .btn-group+.btn,
#kurento .btn-group-vertical .btn-group+.btn-group {
  margin-left: -1px;
}

#kurento .btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

#kurento .btn-toolbar .input-group {
  width: auto;
}

#kurento .btn-group>.btn:first-child {
  margin-left: 0;
}

#kurento .btn-group>.btn:not(:last-child):not(.dropdown-toggle),
#kurento .btn-group>.btn-group:not(:last-child)>.btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#kurento .btn-group>.btn:not(:first-child),
#kurento .btn-group>.btn-group:not(:first-child)>.btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#kurento .dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}

#kurento .dropdown-toggle-split::after {
  margin-left: 0;
}

#kurento .btn-sm+.dropdown-toggle-split,
#kurento .btn-group-sm>.btn+.dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

#kurento .btn-lg+.dropdown-toggle-split,
#kurento .btn-group-lg>.btn+.dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

#kurento .btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

#kurento .btn-group-vertical .btn,
#kurento .btn-group-vertical .btn-group {
  width: 100%;
}

#kurento .btn-group-vertical>.btn+.btn,
#kurento .btn-group-vertical>.btn+.btn-group,
#kurento .btn-group-vertical>.btn-group+.btn,
#kurento .btn-group-vertical>.btn-group+.btn-group {
  margin-top: -1px;
  margin-left: 0;
}

#kurento .btn-group-vertical>.btn:not(:last-child):not(.dropdown-toggle),
#kurento .btn-group-vertical>.btn-group:not(:last-child)>.btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

#kurento .btn-group-vertical>.btn:not(:first-child),
#kurento .btn-group-vertical>.btn-group:not(:first-child)>.btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

#kurento .btn-group-toggle>.btn,
#kurento .btn-group-toggle>.btn-group>.btn {
  margin-bottom: 0;
}

#kurento .btn-group-toggle>.btn input[type="radio"],
#kurento .btn-group-toggle>.btn input[type="checkbox"],
#kurento .btn-group-toggle>.btn-group>.btn input[type="radio"],
#kurento .btn-group-toggle>.btn-group>.btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

#kurento .input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

#kurento .input-group>.form-control,
#kurento .input-group>.custom-select,
#kurento .input-group>.custom-file {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}

#kurento .input-group>.form-control:focus,
#kurento .input-group>.custom-select:focus,
#kurento .input-group>.custom-file:focus {
  z-index: 3;
}

#kurento .input-group>.form-control+.form-control,
#kurento .input-group>.form-control+.custom-select,
#kurento .input-group>.form-control+.custom-file,
#kurento .input-group>.custom-select+.form-control,
#kurento .input-group>.custom-select+.custom-select,
#kurento .input-group>.custom-select+.custom-file,
#kurento .input-group>.custom-file+.form-control,
#kurento .input-group>.custom-file+.custom-select,
#kurento .input-group>.custom-file+.custom-file {
  margin-left: -1px;
}

#kurento .input-group>.form-control:not(:last-child),
#kurento .input-group>.custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#kurento .input-group>.form-control:not(:first-child),
#kurento .input-group>.custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#kurento .input-group>.custom-file {
  display: flex;
  align-items: center;
}

#kurento .input-group>.custom-file:not(:last-child) .custom-file-label,
#kurento .input-group>.custom-file:not(:last-child) .custom-file-label::before {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#kurento .input-group>.custom-file:not(:first-child) .custom-file-label,
#kurento .input-group>.custom-file:not(:first-child) .custom-file-label::before {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#kurento .input-group-prepend,
#kurento .input-group-append {
  display: flex;
}

#kurento .input-group-prepend .btn,
#kurento .input-group-append .btn {
  position: relative;
  z-index: 2;
}

#kurento .input-group-prepend .btn+.btn,
#kurento .input-group-prepend .btn+.input-group-text,
#kurento .input-group-prepend .input-group-text+.input-group-text,
#kurento .input-group-prepend .input-group-text+.btn,
#kurento .input-group-append .btn+.btn,
#kurento .input-group-append .btn+.input-group-text,
#kurento .input-group-append .input-group-text+.input-group-text,
#kurento .input-group-append .input-group-text+.btn {
  margin-left: -1px;
}

#kurento .input-group-prepend {
  margin-right: -1px;
}

#kurento .input-group-append {
  margin-left: -1px;
}

#kurento .input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

#kurento .input-group-text input[type="radio"],
#kurento .input-group-text input[type="checkbox"] {
  margin-top: 0;
}

#kurento .input-group>.input-group-prepend>.btn,
#kurento .input-group>.input-group-prepend>.input-group-text,
#kurento .input-group>.input-group-append:not(:last-child)>.btn,
#kurento .input-group>.input-group-append:not(:last-child)>.input-group-text,
#kurento .input-group>.input-group-append:last-child>.btn:not(:last-child):not(.dropdown-toggle),
#kurento .input-group>.input-group-append:last-child>.input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#kurento .input-group>.input-group-append>.btn,
#kurento .input-group>.input-group-append>.input-group-text,
#kurento .input-group>.input-group-prepend:not(:first-child)>.btn,
#kurento .input-group>.input-group-prepend:not(:first-child)>.input-group-text,
#kurento .input-group>.input-group-prepend:first-child>.btn:not(:first-child),
#kurento .input-group>.input-group-prepend:first-child>.input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

#kurento .custom-control {
  position: relative;
  display: block;
  min-height: 2rem;
  padding-left: 1.5rem;
}

#kurento .custom-control-inline {
  display: inline-flex;
  margin-right: 1rem;
}

#kurento .custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

#kurento .custom-control-input:checked~.custom-control-label::before {
  color: #fff;
  background-color: #007bff;
}

#kurento .custom-control-input:focus~.custom-control-label::before {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#kurento .custom-control-input:active~.custom-control-label::before {
  color: #fff;
  background-color: #b3d7ff;
}

#kurento .custom-control-input:disabled~.custom-control-label {
  color: #6c757d;
}

#kurento .custom-control-input:disabled~.custom-control-label::before {
  background-color: #e9ecef;
}

#kurento .custom-control-label {
  margin-bottom: 0;
}

#kurento .custom-control-label::before {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  user-select: none;
  background-color: #dee2e6;
}

#kurento .custom-control-label::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 50% 50%;
}

#kurento .custom-checkbox .custom-control-label::before {
  border-radius: 0.25rem;
}

#kurento .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
  background-color: #007bff;
}

#kurento .custom-checkbox .custom-control-input:checked~.custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3E%3C/svg%3E");
}

#kurento .custom-checkbox .custom-control-input:indeterminate~.custom-control-label::before {
  background-color: #007bff;
}

#kurento .custom-checkbox .custom-control-input:indeterminate~.custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3E%3Cpath stroke='%23fff' d='M0 2h4'/%3E%3C/svg%3E");
}

#kurento .custom-checkbox .custom-control-input:disabled:checked~.custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

#kurento .custom-checkbox .custom-control-input:disabled:indeterminate~.custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

#kurento .custom-radio .custom-control-label::before {
  border-radius: 50%;
}

#kurento .custom-radio .custom-control-input:checked~.custom-control-label::before {
  background-color: #007bff;
}

#kurento .custom-radio .custom-control-input:checked~.custom-control-label::after {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3E%3Ccircle r='3' fill='%23fff'/%3E%3C/svg%3E");
}

#kurento .custom-radio .custom-control-input:disabled:checked~.custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

#kurento .custom-select {
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3E%3C/svg%3E") no-repeat right 0.75rem center;
  background-size: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  appearance: none;
}

#kurento .custom-select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075), 0 0 5px rgba(128, 189, 255, 0.5);
}

#kurento .custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

#kurento .custom-select[multiple],
#kurento .custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}

#kurento .custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}

#kurento .custom-select::-ms-expand {
  opacity: 0;
}

#kurento .custom-select-sm {
  height: calc(1.8125rem + 2px);
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 75%;
}

#kurento .custom-select-lg {
  height: calc(2.875rem + 2px);
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 125%;
}

#kurento .custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin-bottom: 0;
}

#kurento .custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(2.25rem + 2px);
  margin: 0;
  opacity: 0;
}

#kurento .custom-file-input:focus~.custom-file-control {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#kurento .custom-file-input:focus~.custom-file-control::before {
  border-color: #80bdff;
}

#kurento .custom-file-input:lang(en)~.custom-file-label::after {
  content: "Browse";
}

#kurento .custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(2.25rem + 2px);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

#kurento .custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(calc(2.25rem + 2px) - 1px * 2);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  content: "Browse";
  background-color: #e9ecef;
  border-left: 1px solid #ced4da;
  border-radius: 0 0.25rem 0.25rem 0;
}

#kurento .nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

#kurento .nav-link {
  display: block;
  padding: 0.5rem 1rem;
}

#kurento .nav-link:hover,
#kurento .nav-link:focus {
  text-decoration: none;
}

#kurento .nav-link.disabled {
  color: #6c757d;
}

#kurento .nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

#kurento .nav-tabs .nav-item {
  margin-bottom: -1px;
}

#kurento .nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

#kurento .nav-tabs .nav-link:hover,
#kurento .nav-tabs .nav-link:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
}

#kurento .nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

#kurento .nav-tabs .nav-link.active,
#kurento .nav-tabs .nav-item.show .nav-link {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

#kurento .nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

#kurento .nav-pills .nav-link {
  border-radius: 0.25rem;
}

#kurento .nav-pills .nav-link.active,
#kurento .nav-pills .show>.nav-link {
  color: #fff;
  background-color: #007bff;
}

#kurento .nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

#kurento .nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

#kurento .tab-content>.tab-pane {
  display: none;
}

#kurento .tab-content>.active {
  display: block;
}

#kurento .navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

#kurento .navbar>.container,
#kurento .navbar>.container-fluid {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}

#kurento .navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}

#kurento .navbar-brand:hover,
#kurento .navbar-brand:focus {
  text-decoration: none;
}

#kurento .navbar-nav {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

#kurento .navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}

#kurento .navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

#kurento .navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

#kurento .navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

#kurento .navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

#kurento .navbar-toggler:hover,
#kurento .navbar-toggler:focus {
  text-decoration: none;
}

#kurento .navbar-toggler:not(:disabled):not(.disabled) {
  cursor: pointer;
}

#kurento .navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat center center;
  background-size: 100% 100%;
}

@media (max-width: 575.98px) {

  #kurento .navbar-expand-sm>.container,
  #kurento .navbar-expand-sm>.container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 576px) {
  #kurento .navbar-expand-sm {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }

  #kurento .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }

  #kurento .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }

  #kurento .navbar-expand-sm .navbar-nav .dropdown-menu-right {
    right: 0;
    left: auto;
  }

  #kurento .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  #kurento .navbar-expand-sm>.container,
  #kurento .navbar-expand-sm>.container-fluid {
    flex-wrap: nowrap;
  }

  #kurento .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }

  #kurento .navbar-expand-sm .navbar-toggler {
    display: none;
  }

  #kurento .navbar-expand-sm .dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
  }
}

@media (max-width: 767.98px) {

  #kurento .navbar-expand-md>.container,
  #kurento .navbar-expand-md>.container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  #kurento .navbar-expand-md {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }

  #kurento .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }

  #kurento .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }

  #kurento .navbar-expand-md .navbar-nav .dropdown-menu-right {
    right: 0;
    left: auto;
  }

  #kurento .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  #kurento .navbar-expand-md>.container,
  #kurento .navbar-expand-md>.container-fluid {
    flex-wrap: nowrap;
  }

  #kurento .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }

  #kurento .navbar-expand-md .navbar-toggler {
    display: none;
  }

  #kurento .navbar-expand-md .dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
  }
}

@media (max-width: 991.98px) {

  #kurento .navbar-expand-lg>.container,
  #kurento .navbar-expand-lg>.container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  #kurento .navbar-expand-lg {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }

  #kurento .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }

  #kurento .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }

  #kurento .navbar-expand-lg .navbar-nav .dropdown-menu-right {
    right: 0;
    left: auto;
  }

  #kurento .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  #kurento .navbar-expand-lg>.container,
  #kurento .navbar-expand-lg>.container-fluid {
    flex-wrap: nowrap;
  }

  #kurento .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }

  #kurento .navbar-expand-lg .navbar-toggler {
    display: none;
  }

  #kurento .navbar-expand-lg .dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
  }
}

@media (max-width: 1199.98px) {

  #kurento .navbar-expand-xl>.container,
  #kurento .navbar-expand-xl>.container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 1200px) {
  #kurento .navbar-expand-xl {
    flex-flow: row nowrap;
    justify-content: flex-start;
  }

  #kurento .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }

  #kurento .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }

  #kurento .navbar-expand-xl .navbar-nav .dropdown-menu-right {
    right: 0;
    left: auto;
  }

  #kurento .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  #kurento .navbar-expand-xl>.container,
  #kurento .navbar-expand-xl>.container-fluid {
    flex-wrap: nowrap;
  }

  #kurento .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }

  #kurento .navbar-expand-xl .navbar-toggler {
    display: none;
  }

  #kurento .navbar-expand-xl .dropup .dropdown-menu {
    top: auto;
    bottom: 100%;
  }
}

#kurento .navbar-expand {
  flex-flow: row nowrap;
  justify-content: flex-start;
}

#kurento .navbar-expand>.container,
#kurento .navbar-expand>.container-fluid {
  padding-right: 0;
  padding-left: 0;
}

#kurento .navbar-expand .navbar-nav {
  flex-direction: row;
}

#kurento .navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}

#kurento .navbar-expand .navbar-nav .dropdown-menu-right {
  right: 0;
  left: auto;
}

#kurento .navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

#kurento .navbar-expand>.container,
#kurento .navbar-expand>.container-fluid {
  flex-wrap: nowrap;
}

#kurento .navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}

#kurento .navbar-expand .navbar-toggler {
  display: none;
}

#kurento .navbar-expand .dropup .dropdown-menu {
  top: auto;
  bottom: 100%;
}

#kurento .navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}

#kurento .navbar-light .navbar-brand:hover,
#kurento .navbar-light .navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}

#kurento .navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}

#kurento .navbar-light .navbar-nav .nav-link:hover,
#kurento .navbar-light .navbar-nav .nav-link:focus {
  color: rgba(0, 0, 0, 0.7);
}

#kurento .navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}

#kurento .navbar-light .navbar-nav .show>.nav-link,
#kurento .navbar-light .navbar-nav .active>.nav-link,
#kurento .navbar-light .navbar-nav .nav-link.show,
#kurento .navbar-light .navbar-nav .nav-link.active {
  color: rgba(0, 0, 0, 0.9);
}

#kurento .navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}

#kurento .navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

#kurento .navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}

#kurento .navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}

#kurento .navbar-light .navbar-text a:hover,
#kurento .navbar-light .navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}

#kurento .navbar-dark .navbar-brand {
  color: #fff;
}

#kurento .navbar-dark .navbar-brand:hover,
#kurento .navbar-dark .navbar-brand:focus {
  color: #fff;
}

#kurento .navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}

#kurento .navbar-dark .navbar-nav .nav-link:hover,
#kurento .navbar-dark .navbar-nav .nav-link:focus {
  color: rgba(255, 255, 255, 0.75);
}

#kurento .navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}

#kurento .navbar-dark .navbar-nav .show>.nav-link,
#kurento .navbar-dark .navbar-nav .active>.nav-link,
#kurento .navbar-dark .navbar-nav .nav-link.show,
#kurento .navbar-dark .navbar-nav .nav-link.active {
  color: #fff;
}

#kurento .navbar-dark .navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

#kurento .navbar-dark .navbar-toggler-icon {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E");
}

#kurento .navbar-dark .navbar-text {
  color: rgba(255, 255, 255, 0.5);
}

#kurento .navbar-dark .navbar-text a {
  color: #fff;
}

#kurento .navbar-dark .navbar-text a:hover,
#kurento .navbar-dark .navbar-text a:focus {
  color: #fff;
}

#kurento .card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

#kurento .card>hr {
  margin-right: 0;
  margin-left: 0;
}

#kurento .card>.list-group:first-child .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

#kurento .card>.list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

#kurento .card-body {
  flex: 1 1 auto;
  padding: 1.25rem;
}

#kurento .card-title {
  margin-bottom: 0.75rem;
}

#kurento .card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}

#kurento .card-text:last-child {
  margin-bottom: 0;
}

#kurento .card-link:hover {
  text-decoration: none;
}

#kurento .card-link+.card-link {
  margin-left: 1.25rem;
}

#kurento .card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

#kurento .card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

#kurento .card-header+.list-group .list-group-item:first-child {
  border-top: 0;
}

#kurento .card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

#kurento .card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

#kurento .card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}

#kurento .card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

#kurento .card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}

#kurento .card-img {
  width: 100%;
  border-radius: calc(0.25rem - 1px);
}

#kurento .card-img-top {
  width: 100%;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

#kurento .card-img-bottom {
  width: 100%;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

#kurento .card-deck {
  display: flex;
  flex-direction: column;
}

#kurento .card-deck .card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  #kurento .card-deck {
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }

  #kurento .card-deck .card {
    display: flex;
    flex: 1 0 0%;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

#kurento .card-group {
  display: flex;
  flex-direction: column;
}

#kurento .card-group>.card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  #kurento .card-group {
    flex-flow: row wrap;
  }

  #kurento .card-group>.card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }

  #kurento .card-group>.card+.card {
    margin-left: 0;
    border-left: 0;
  }

  #kurento .card-group>.card:first-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  #kurento .card-group>.card:first-child .card-img-top,
  #kurento .card-group>.card:first-child .card-header {
    border-top-right-radius: 0;
  }

  #kurento .card-group>.card:first-child .card-img-bottom,
  #kurento .card-group>.card:first-child .card-footer {
    border-bottom-right-radius: 0;
  }

  #kurento .card-group>.card:last-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  #kurento .card-group>.card:last-child .card-img-top,
  #kurento .card-group>.card:last-child .card-header {
    border-top-left-radius: 0;
  }

  #kurento .card-group>.card:last-child .card-img-bottom,
  #kurento .card-group>.card:last-child .card-footer {
    border-bottom-left-radius: 0;
  }

  #kurento .card-group>.card:only-child {
    border-radius: 0.25rem;
  }

  #kurento .card-group>.card:only-child .card-img-top,
  #kurento .card-group>.card:only-child .card-header {
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
  }

  #kurento .card-group>.card:only-child .card-img-bottom,
  #kurento .card-group>.card:only-child .card-footer {
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }

  #kurento .card-group>.card:not(:first-child):not(:last-child):not(:only-child) {
    border-radius: 0;
  }

  #kurento .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-top,
  #kurento .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-img-bottom,
  #kurento .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-header,
  #kurento .card-group>.card:not(:first-child):not(:last-child):not(:only-child) .card-footer {
    border-radius: 0;
  }
}

#kurento .card-columns .card {
  margin-bottom: 0.75rem;
}

@media (min-width: 576px) {
  #kurento .card-columns {
    column-count: 3;
    column-gap: 1.25rem;
  }

  #kurento .card-columns .card {
    display: inline-block;
    width: 100%;
  }
}

#kurento .breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

#kurento .breadcrumb-item+.breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  color: #6c757d;
  content: "/";
}

#kurento .breadcrumb-item+.breadcrumb-item:hover::before {
  text-decoration: underline;
}

#kurento .breadcrumb-item+.breadcrumb-item:hover::before {
  text-decoration: none;
}

#kurento .breadcrumb-item.active {
  color: #6c757d;
}

#kurento .badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

#kurento .badge:empty {
  display: none;
}

#kurento .btn .badge {
  position: relative;
  top: -1px;
}

#kurento .badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

#kurento .badge-primary {
  color: #fff;
  background-color: #007bff;
}

#kurento .badge-primary[href]:hover,
#kurento .badge-primary[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #0062cc;
}

#kurento .badge-secondary {
  color: #fff;
  background-color: #6c757d;
}

#kurento .badge-secondary[href]:hover,
#kurento .badge-secondary[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #545b62;
}

#kurento .badge-success {
  color: #fff;
  background-color: #28a745;
}

#kurento .badge-success[href]:hover,
#kurento .badge-success[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #1e7e34;
}

#kurento .badge-info {
  color: #fff;
  background-color: #17a2b8;
}

#kurento .badge-info[href]:hover,
#kurento .badge-info[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #117a8b;
}

#kurento .badge-warning {
  color: #212529;
  background-color: #ffc107;
}

#kurento .badge-warning[href]:hover,
#kurento .badge-warning[href]:focus {
  color: #212529;
  text-decoration: none;
  background-color: #d39e00;
}

#kurento .badge-danger {
  color: #fff;
  background-color: #dc3545;
}

#kurento .badge-danger[href]:hover,
#kurento .badge-danger[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #bd2130;
}

#kurento .badge-light {
  color: #212529;
  background-color: #f8f9fa;
}

#kurento .badge-light[href]:hover,
#kurento .badge-light[href]:focus {
  color: #212529;
  text-decoration: none;
  background-color: #dae0e5;
}

#kurento .badge-dark {
  color: #fff;
  background-color: #343a40;
}

#kurento .badge-dark[href]:hover,
#kurento .badge-dark[href]:focus {
  color: #fff;
  text-decoration: none;
  background-color: #1d2124;
}

#kurento .alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

#kurento .alert-heading {
  color: inherit;
}

#kurento .alert-link {
  font-weight: 700;
}

#kurento .alert-dismissible {
  padding-right: 4rem;
}

#kurento .alert-dismissible .close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
}

#kurento .alert-primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}

#kurento .alert-primary hr {
  border-top-color: #9fcdff;
}

#kurento .alert-primary .alert-link {
  color: #002752;
}

#kurento .alert-secondary {
  color: #383d41;
  background-color: #e2e3e5;
  border-color: #d6d8db;
}

#kurento .alert-secondary hr {
  border-top-color: #c8cbcf;
}

#kurento .alert-secondary .alert-link {
  color: #202326;
}

#kurento .alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

#kurento .alert-success hr {
  border-top-color: #b1dfbb;
}

#kurento .alert-success .alert-link {
  color: #0b2e13;
}

#kurento .alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

#kurento .alert-info hr {
  border-top-color: #abdde5;
}

#kurento .alert-info .alert-link {
  color: #062c33;
}

#kurento .alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

#kurento .alert-warning hr {
  border-top-color: #ffe8a1;
}

#kurento .alert-warning .alert-link {
  color: #533f03;
}

#kurento .alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

#kurento .alert-danger hr {
  border-top-color: #f1b0b7;
}

#kurento .alert-danger .alert-link {
  color: #491217;
}

#kurento .alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}

#kurento .alert-light hr {
  border-top-color: #ececf6;
}

#kurento .alert-light .alert-link {
  color: #686868;
}

#kurento .alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}

#kurento .alert-dark hr {
  border-top-color: #b9bbbe;
}

#kurento .alert-dark .alert-link {
  color: #040505;
}

#kurento .media {
  display: flex;
  align-items: flex-start;
}

#kurento .media-body {
  flex: 1;
}

#kurento .list-group {
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
}

#kurento .list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}

#kurento .list-group-item-action:hover,
#kurento .list-group-item-action:focus {
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}

#kurento .list-group-item-action:active {
  color: #212529;
  background-color: #e9ecef;
}

#kurento .list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

#kurento .list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

#kurento .list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

#kurento .list-group-item:hover,
#kurento .list-group-item:focus {
  z-index: 1;
  text-decoration: none;
}

#kurento .list-group-item.disabled,
#kurento .list-group-item:disabled {
  color: #6c757d;
  background-color: #fff;
}

#kurento .list-group-item.active {
  z-index: 2;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

#kurento .list-group-flush .list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

#kurento .list-group-flush:first-child .list-group-item:first-child {
  border-top: 0;
}

#kurento .list-group-flush:last-child .list-group-item:last-child {
  border-bottom: 0;
}

#kurento .list-group-item-primary {
  color: #004085;
  background-color: #b8daff;
}

#kurento .list-group-item-primary.list-group-item-action:hover,
#kurento .list-group-item-primary.list-group-item-action:focus {
  color: #004085;
  background-color: #9fcdff;
}

#kurento .list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: #004085;
  border-color: #004085;
}

#kurento .list-group-item-secondary {
  color: #383d41;
  background-color: #d6d8db;
}

#kurento .list-group-item-secondary.list-group-item-action:hover,
#kurento .list-group-item-secondary.list-group-item-action:focus {
  color: #383d41;
  background-color: #c8cbcf;
}

#kurento .list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: #383d41;
  border-color: #383d41;
}

#kurento .list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}

#kurento .list-group-item-success.list-group-item-action:hover,
#kurento .list-group-item-success.list-group-item-action:focus {
  color: #155724;
  background-color: #b1dfbb;
}

#kurento .list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}

#kurento .list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}

#kurento .list-group-item-info.list-group-item-action:hover,
#kurento .list-group-item-info.list-group-item-action:focus {
  color: #0c5460;
  background-color: #abdde5;
}

#kurento .list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}

#kurento .list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}

#kurento .list-group-item-warning.list-group-item-action:hover,
#kurento .list-group-item-warning.list-group-item-action:focus {
  color: #856404;
  background-color: #ffe8a1;
}

#kurento .list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}

#kurento .list-group-item-danger {
  color: #721c24;
  background-color: #f5c6cb;
}

#kurento .list-group-item-danger.list-group-item-action:hover,
#kurento .list-group-item-danger.list-group-item-action:focus {
  color: #721c24;
  background-color: #f1b0b7;
}

#kurento .list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: #721c24;
  border-color: #721c24;
}

#kurento .list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}

#kurento .list-group-item-light.list-group-item-action:hover,
#kurento .list-group-item-light.list-group-item-action:focus {
  color: #818182;
  background-color: #ececf6;
}

#kurento .list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}

#kurento .list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}

#kurento .list-group-item-dark.list-group-item-action:hover,
#kurento .list-group-item-dark.list-group-item-action:focus {
  color: #1b1e21;
  background-color: #b9bbbe;
}

#kurento .list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}

#kurento .close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .5;
}

#kurento .close:hover,
#kurento .close:focus {
  color: #000;
  text-decoration: none;
  opacity: .75;
}

#kurento .close:not(:disabled):not(.disabled) {
  cursor: pointer;
}

#kurento button.close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
}

#kurento .modal-open {
  overflow: hidden;
}

#kurento .modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  display: none;
  overflow: hidden;
  outline: 0;
}

.modal-open #kurento .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

#kurento .modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
  overflow: inherit;
}

.modal.fade #kurento .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -25%);
}

.modal.show #kurento .modal-dialog {
  transform: translate(0, 0);
}

#kurento .modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - (0.5rem * 2));
}

#kurento .modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

#kurento .modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000;
}

#kurento .modal-backdrop.fade {
  opacity: 0;
}

#kurento .modal-backdrop.show {
  opacity: 0.5;
}

#kurento .modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

#kurento .modal-header .close {
  padding: 1rem;
  margin: -1rem -1rem -1rem auto;
}

#kurento .modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

#kurento .modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

#kurento .modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #e9ecef;
}

#kurento .modal-footer> :not(:first-child) {
  margin-left: .25rem;
}

#kurento .modal-footer> :not(:last-child) {
  margin-right: .25rem;
}

#kurento .modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  #kurento .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }

  #kurento .modal-dialog-centered {
    min-height: calc(100% - (1.75rem * 2));
  }

  #kurento .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  #kurento .modal-lg {
    max-width: 800px;
  }
}

#kurento .tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

#kurento .tooltip.show {
  opacity: 0.9;
}

#kurento .tooltip .arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}

#kurento .tooltip .arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

#kurento .bs-tooltip-top,
#kurento .bs-tooltip-auto[x-placement^="top"] {
  padding: 0.4rem 0;
}

#kurento .bs-tooltip-top .arrow,
#kurento .bs-tooltip-auto[x-placement^="top"] .arrow {
  bottom: 0;
}

#kurento .bs-tooltip-top .arrow::before,
#kurento .bs-tooltip-auto[x-placement^="top"] .arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

#kurento .bs-tooltip-right,
#kurento .bs-tooltip-auto[x-placement^="right"] {
  padding: 0 0.4rem;
}

#kurento .bs-tooltip-right .arrow,
#kurento .bs-tooltip-auto[x-placement^="right"] .arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}

#kurento .bs-tooltip-right .arrow::before,
#kurento .bs-tooltip-auto[x-placement^="right"] .arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}

#kurento .bs-tooltip-bottom,
#kurento .bs-tooltip-auto[x-placement^="bottom"] {
  padding: 0.4rem 0;
}

#kurento .bs-tooltip-bottom .arrow,
#kurento .bs-tooltip-auto[x-placement^="bottom"] .arrow {
  top: 0;
}

#kurento .bs-tooltip-bottom .arrow::before,
#kurento .bs-tooltip-auto[x-placement^="bottom"] .arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

#kurento .bs-tooltip-left,
#kurento .bs-tooltip-auto[x-placement^="left"] {
  padding: 0 0.4rem;
}

#kurento .bs-tooltip-left .arrow,
#kurento .bs-tooltip-auto[x-placement^="left"] .arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}

#kurento .bs-tooltip-left .arrow::before,
#kurento .bs-tooltip-auto[x-placement^="left"] .arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}

#kurento .tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}

#kurento .popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}

#kurento .popover .arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}

#kurento .popover .arrow::before,
#kurento .popover .arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

#kurento .bs-popover-top,
#kurento .bs-popover-auto[x-placement^="top"] {
  margin-bottom: 0.5rem;
}

#kurento .bs-popover-top .arrow,
#kurento .bs-popover-auto[x-placement^="top"] .arrow {
  bottom: calc((0.5rem + 1px) * -1);
}

#kurento .bs-popover-top .arrow::before,
#kurento .bs-popover-auto[x-placement^="top"] .arrow::before,
#kurento .bs-popover-top .arrow::after,
#kurento .bs-popover-auto[x-placement^="top"] .arrow::after {
  border-width: 0.5rem 0.5rem 0;
}

#kurento .bs-popover-top .arrow::before,
#kurento .bs-popover-auto[x-placement^="top"] .arrow::before {
  bottom: 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

#kurento .bs-popover-top .arrow::after,
#kurento .bs-popover-auto[x-placement^="top"] .arrow::after {
  bottom: 1px;
  border-top-color: #fff;
}

#kurento .bs-popover-right,
#kurento .bs-popover-auto[x-placement^="right"] {
  margin-left: 0.5rem;
}

#kurento .bs-popover-right .arrow,
#kurento .bs-popover-auto[x-placement^="right"] .arrow {
  left: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

#kurento .bs-popover-right .arrow::before,
#kurento .bs-popover-auto[x-placement^="right"] .arrow::before,
#kurento .bs-popover-right .arrow::after,
#kurento .bs-popover-auto[x-placement^="right"] .arrow::after {
  border-width: 0.5rem 0.5rem 0.5rem 0;
}

#kurento .bs-popover-right .arrow::before,
#kurento .bs-popover-auto[x-placement^="right"] .arrow::before {
  left: 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

#kurento .bs-popover-right .arrow::after,
#kurento .bs-popover-auto[x-placement^="right"] .arrow::after {
  left: 1px;
  border-right-color: #fff;
}

#kurento .bs-popover-bottom,
#kurento .bs-popover-auto[x-placement^="bottom"] {
  margin-top: 0.5rem;
}

#kurento .bs-popover-bottom .arrow,
#kurento .bs-popover-auto[x-placement^="bottom"] .arrow {
  top: calc((0.5rem + 1px) * -1);
}

#kurento .bs-popover-bottom .arrow::before,
#kurento .bs-popover-auto[x-placement^="bottom"] .arrow::before,
#kurento .bs-popover-bottom .arrow::after,
#kurento .bs-popover-auto[x-placement^="bottom"] .arrow::after {
  border-width: 0 0.5rem 0.5rem 0.5rem;
}

#kurento .bs-popover-bottom .arrow::before,
#kurento .bs-popover-auto[x-placement^="bottom"] .arrow::before {
  top: 0;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

#kurento .bs-popover-bottom .arrow::after,
#kurento .bs-popover-auto[x-placement^="bottom"] .arrow::after {
  top: 1px;
  border-bottom-color: #fff;
}

#kurento .bs-popover-bottom .popover-header::before,
#kurento .bs-popover-auto[x-placement^="bottom"] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}

#kurento .bs-popover-left,
#kurento .bs-popover-auto[x-placement^="left"] {
  margin-right: 0.5rem;
}

#kurento .bs-popover-left .arrow,
#kurento .bs-popover-auto[x-placement^="left"] .arrow {
  right: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

#kurento .bs-popover-left .arrow::before,
#kurento .bs-popover-auto[x-placement^="left"] .arrow::before,
#kurento .bs-popover-left .arrow::after,
#kurento .bs-popover-auto[x-placement^="left"] .arrow::after {
  border-width: 0.5rem 0 0.5rem 0.5rem;
}

#kurento .bs-popover-left .arrow::before,
#kurento .bs-popover-auto[x-placement^="left"] .arrow::before {
  right: 0;
  border-left-color: rgba(0, 0, 0, 0.25);
}

#kurento .bs-popover-left .arrow::after,
#kurento .bs-popover-auto[x-placement^="left"] .arrow::after {
  right: 1px;
  border-left-color: #fff;
}

#kurento .popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: inherit;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

#kurento .popover-header:empty {
  display: none;
}

#kurento .popover-body {
  padding: 0.5rem 0.75rem;
  color: #212529;
}

#kurento .align-baseline {
  vertical-align: baseline !important;
}

#kurento .align-top {
  vertical-align: top !important;
}

#kurento .align-middle {
  vertical-align: middle !important;
}

#kurento .align-bottom {
  vertical-align: bottom !important;
}

#kurento .align-text-bottom {
  vertical-align: text-bottom !important;
}

#kurento .align-text-top {
  vertical-align: text-top !important;
}

#kurento .bg-primary {
  background-color: #007bff !important;
}

#kurento a.bg-primary:hover,
#kurento a.bg-primary:focus,
#kurento button.bg-primary:hover,
#kurento button.bg-primary:focus {
  background-color: #0062cc !important;
}

#kurento .bg-secondary {
  background-color: #6c757d !important;
}

#kurento a.bg-secondary:hover,
#kurento a.bg-secondary:focus,
#kurento button.bg-secondary:hover,
#kurento button.bg-secondary:focus {
  background-color: #545b62 !important;
}

#kurento .bg-success {
  background-color: #28a745 !important;
}

#kurento a.bg-success:hover,
#kurento a.bg-success:focus,
#kurento button.bg-success:hover,
#kurento button.bg-success:focus {
  background-color: #1e7e34 !important;
}

#kurento .bg-info {
  background-color: #17a2b8 !important;
}

#kurento a.bg-info:hover,
#kurento a.bg-info:focus,
#kurento button.bg-info:hover,
#kurento button.bg-info:focus {
  background-color: #117a8b !important;
}

#kurento .bg-warning {
  background-color: #ffc107 !important;
}

#kurento a.bg-warning:hover,
#kurento a.bg-warning:focus,
#kurento button.bg-warning:hover,
#kurento button.bg-warning:focus {
  background-color: #d39e00 !important;
}

#kurento .bg-danger {
  background-color: #dc3545 !important;
}

#kurento a.bg-danger:hover,
#kurento a.bg-danger:focus,
#kurento button.bg-danger:hover,
#kurento button.bg-danger:focus {
  background-color: #bd2130 !important;
}

#kurento .bg-light {
  background-color: #f8f9fa !important;
}

#kurento a.bg-light:hover,
#kurento a.bg-light:focus,
#kurento button.bg-light:hover,
#kurento button.bg-light:focus {
  background-color: #dae0e5 !important;
}

#kurento .bg-dark {
  background-color: #343a40 !important;
}

#kurento a.bg-dark:hover,
#kurento a.bg-dark:focus,
#kurento button.bg-dark:hover,
#kurento button.bg-dark:focus {
  background-color: #1d2124 !important;
}

#kurento .bg-white {
  background-color: #fff !important;
}

#kurento .bg-transparent {
  background-color: transparent !important;
}

#kurento .border {
  border: 1px solid #dee2e6 !important;
}

#kurento .border-top {
  border-top: 1px solid #dee2e6 !important;
}

#kurento .border-right {
  border-right: 1px solid #dee2e6 !important;
}

#kurento .border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

#kurento .border-left {
  border-left: 1px solid #dee2e6 !important;
}

#kurento .border-0 {
  border: 0 !important;
}

#kurento .border-top-0 {
  border-top: 0 !important;
}

#kurento .border-right-0 {
  border-right: 0 !important;
}

#kurento .border-bottom-0 {
  border-bottom: 0 !important;
}

#kurento .border-left-0 {
  border-left: 0 !important;
}

#kurento .border-primary {
  border-color: #007bff !important;
}

#kurento .border-secondary {
  border-color: #6c757d !important;
}

#kurento .border-success {
  border-color: #28a745 !important;
}

#kurento .border-info {
  border-color: #17a2b8 !important;
}

#kurento .border-warning {
  border-color: #ffc107 !important;
}

#kurento .border-danger {
  border-color: #dc3545 !important;
}

#kurento .border-light {
  border-color: #f8f9fa !important;
}

#kurento .border-dark {
  border-color: #343a40 !important;
}

#kurento .border-white {
  border-color: #fff !important;
}

#kurento .rounded {
  border-radius: 0.25rem !important;
}

#kurento .rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

#kurento .rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

#kurento .rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

#kurento .rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

#kurento .rounded-circle {
  border-radius: 50% !important;
}

#kurento .rounded-0 {
  border-radius: 0 !important;
}

#kurento .clearfix::after {
  display: block;
  clear: both;
  content: "";
}

#kurento .d-none {
  display: none !important;
}

#kurento .d-inline {
  display: inline !important;
}

#kurento .d-inline-block {
  display: inline-block !important;
}

#kurento .d-block {
  display: block !important;
}

#kurento .d-table {
  display: table !important;
}

#kurento .d-table-row {
  display: table-row !important;
}

#kurento .d-table-cell {
  display: table-cell !important;
}

#kurento .d-flex {
  display: flex !important;
}

#kurento .d-inline-flex {
  display: inline-flex !important;
}

@media (min-width: 576px) {
  #kurento .d-sm-none {
    display: none !important;
  }

  #kurento .d-sm-inline {
    display: inline !important;
  }

  #kurento .d-sm-inline-block {
    display: inline-block !important;
  }

  #kurento .d-sm-block {
    display: block !important;
  }

  #kurento .d-sm-table {
    display: table !important;
  }

  #kurento .d-sm-table-row {
    display: table-row !important;
  }

  #kurento .d-sm-table-cell {
    display: table-cell !important;
  }

  #kurento .d-sm-flex {
    display: flex !important;
  }

  #kurento .d-sm-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 768px) {
  #kurento .d-md-none {
    display: none !important;
  }

  #kurento .d-md-inline {
    display: inline !important;
  }

  #kurento .d-md-inline-block {
    display: inline-block !important;
  }

  #kurento .d-md-block {
    display: block !important;
  }

  #kurento .d-md-table {
    display: table !important;
  }

  #kurento .d-md-table-row {
    display: table-row !important;
  }

  #kurento .d-md-table-cell {
    display: table-cell !important;
  }

  #kurento .d-md-flex {
    display: flex !important;
  }

  #kurento .d-md-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 992px) {
  #kurento .d-lg-none {
    display: none !important;
  }

  #kurento .d-lg-inline {
    display: inline !important;
  }

  #kurento .d-lg-inline-block {
    display: inline-block !important;
  }

  #kurento .d-lg-block {
    display: block !important;
  }

  #kurento .d-lg-table {
    display: table !important;
  }

  #kurento .d-lg-table-row {
    display: table-row !important;
  }

  #kurento .d-lg-table-cell {
    display: table-cell !important;
  }

  #kurento .d-lg-flex {
    display: flex !important;
  }

  #kurento .d-lg-inline-flex {
    display: inline-flex !important;
  }
}

@media (min-width: 1200px) {
  #kurento .d-xl-none {
    display: none !important;
  }

  #kurento .d-xl-inline {
    display: inline !important;
  }

  #kurento .d-xl-inline-block {
    display: inline-block !important;
  }

  #kurento .d-xl-block {
    display: block !important;
  }

  #kurento .d-xl-table {
    display: table !important;
  }

  #kurento .d-xl-table-row {
    display: table-row !important;
  }

  #kurento .d-xl-table-cell {
    display: table-cell !important;
  }

  #kurento .d-xl-flex {
    display: flex !important;
  }

  #kurento .d-xl-inline-flex {
    display: inline-flex !important;
  }
}

@media print {
  #kurento .d-print-none {
    display: none !important;
  }

  #kurento .d-print-inline {
    display: inline !important;
  }

  #kurento .d-print-inline-block {
    display: inline-block !important;
  }

  #kurento .d-print-block {
    display: block !important;
  }

  #kurento .d-print-table {
    display: table !important;
  }

  #kurento .d-print-table-row {
    display: table-row !important;
  }

  #kurento .d-print-table-cell {
    display: table-cell !important;
  }

  #kurento .d-print-flex {
    display: flex !important;
  }

  #kurento .d-print-inline-flex {
    display: inline-flex !important;
  }
}

#kurento .embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

#kurento .embed-responsive::before {
  display: block;
  content: "";
}

#kurento .embed-responsive .embed-responsive-item,
#kurento .embed-responsive iframe,
#kurento .embed-responsive embed,
#kurento .embed-responsive object,
#kurento .embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

#kurento .embed-responsive-21by9::before {
  padding-top: 42.85714%;
}

#kurento .embed-responsive-16by9::before {
  padding-top: 56.25%;
}

#kurento .embed-responsive-4by3::before {
  padding-top: 75%;
}

#kurento .embed-responsive-1by1::before {
  padding-top: 100%;
}

#kurento .flex-row {
  flex-direction: row !important;
}

#kurento .flex-column {
  flex-direction: column !important;
}

#kurento .flex-row-reverse {
  flex-direction: row-reverse !important;
}

#kurento .flex-column-reverse {
  flex-direction: column-reverse !important;
}

#kurento .flex-wrap {
  flex-wrap: wrap !important;
}

#kurento .flex-nowrap {
  flex-wrap: nowrap !important;
}

#kurento .flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

#kurento .justify-content-start {
  justify-content: flex-start !important;
}

#kurento .justify-content-end {
  justify-content: flex-end !important;
}

#kurento .justify-content-center {
  justify-content: center !important;
}

#kurento .justify-content-between {
  justify-content: space-between !important;
}

#kurento .justify-content-around {
  justify-content: space-around !important;
}

#kurento .align-items-start {
  align-items: flex-start !important;
}

#kurento .align-items-end {
  align-items: flex-end !important;
}

#kurento .align-items-center {
  align-items: center !important;
}

#kurento .align-items-baseline {
  align-items: baseline !important;
}

#kurento .align-items-stretch {
  align-items: stretch !important;
}

#kurento .align-content-start {
  align-content: flex-start !important;
}

#kurento .align-content-end {
  align-content: flex-end !important;
}

#kurento .align-content-center {
  align-content: center !important;
}

#kurento .align-content-between {
  align-content: space-between !important;
}

#kurento .align-content-around {
  align-content: space-around !important;
}

#kurento .align-content-stretch {
  align-content: stretch !important;
}

#kurento .align-self-auto {
  align-self: auto !important;
}

#kurento .align-self-start {
  align-self: flex-start !important;
}

#kurento .align-self-end {
  align-self: flex-end !important;
}

#kurento .align-self-center {
  align-self: center !important;
}

#kurento .align-self-baseline {
  align-self: baseline !important;
}

#kurento .align-self-stretch {
  align-self: stretch !important;
}

@media (min-width: 576px) {
  #kurento .flex-sm-row {
    flex-direction: row !important;
  }

  #kurento .flex-sm-column {
    flex-direction: column !important;
  }

  #kurento .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  #kurento .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }

  #kurento .flex-sm-wrap {
    flex-wrap: wrap !important;
  }

  #kurento .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }

  #kurento .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  #kurento .justify-content-sm-start {
    justify-content: flex-start !important;
  }

  #kurento .justify-content-sm-end {
    justify-content: flex-end !important;
  }

  #kurento .justify-content-sm-center {
    justify-content: center !important;
  }

  #kurento .justify-content-sm-between {
    justify-content: space-between !important;
  }

  #kurento .justify-content-sm-around {
    justify-content: space-around !important;
  }

  #kurento .align-items-sm-start {
    align-items: flex-start !important;
  }

  #kurento .align-items-sm-end {
    align-items: flex-end !important;
  }

  #kurento .align-items-sm-center {
    align-items: center !important;
  }

  #kurento .align-items-sm-baseline {
    align-items: baseline !important;
  }

  #kurento .align-items-sm-stretch {
    align-items: stretch !important;
  }

  #kurento .align-content-sm-start {
    align-content: flex-start !important;
  }

  #kurento .align-content-sm-end {
    align-content: flex-end !important;
  }

  #kurento .align-content-sm-center {
    align-content: center !important;
  }

  #kurento .align-content-sm-between {
    align-content: space-between !important;
  }

  #kurento .align-content-sm-around {
    align-content: space-around !important;
  }

  #kurento .align-content-sm-stretch {
    align-content: stretch !important;
  }

  #kurento .align-self-sm-auto {
    align-self: auto !important;
  }

  #kurento .align-self-sm-start {
    align-self: flex-start !important;
  }

  #kurento .align-self-sm-end {
    align-self: flex-end !important;
  }

  #kurento .align-self-sm-center {
    align-self: center !important;
  }

  #kurento .align-self-sm-baseline {
    align-self: baseline !important;
  }

  #kurento .align-self-sm-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 768px) {
  #kurento .flex-md-row {
    flex-direction: row !important;
  }

  #kurento .flex-md-column {
    flex-direction: column !important;
  }

  #kurento .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }

  #kurento .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }

  #kurento .flex-md-wrap {
    flex-wrap: wrap !important;
  }

  #kurento .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }

  #kurento .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  #kurento .justify-content-md-start {
    justify-content: flex-start !important;
  }

  #kurento .justify-content-md-end {
    justify-content: flex-end !important;
  }

  #kurento .justify-content-md-center {
    justify-content: center !important;
  }

  #kurento .justify-content-md-between {
    justify-content: space-between !important;
  }

  #kurento .justify-content-md-around {
    justify-content: space-around !important;
  }

  #kurento .align-items-md-start {
    align-items: flex-start !important;
  }

  #kurento .align-items-md-end {
    align-items: flex-end !important;
  }

  #kurento .align-items-md-center {
    align-items: center !important;
  }

  #kurento .align-items-md-baseline {
    align-items: baseline !important;
  }

  #kurento .align-items-md-stretch {
    align-items: stretch !important;
  }

  #kurento .align-content-md-start {
    align-content: flex-start !important;
  }

  #kurento .align-content-md-end {
    align-content: flex-end !important;
  }

  #kurento .align-content-md-center {
    align-content: center !important;
  }

  #kurento .align-content-md-between {
    align-content: space-between !important;
  }

  #kurento .align-content-md-around {
    align-content: space-around !important;
  }

  #kurento .align-content-md-stretch {
    align-content: stretch !important;
  }

  #kurento .align-self-md-auto {
    align-self: auto !important;
  }

  #kurento .align-self-md-start {
    align-self: flex-start !important;
  }

  #kurento .align-self-md-end {
    align-self: flex-end !important;
  }

  #kurento .align-self-md-center {
    align-self: center !important;
  }

  #kurento .align-self-md-baseline {
    align-self: baseline !important;
  }

  #kurento .align-self-md-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 992px) {
  #kurento .flex-lg-row {
    flex-direction: row !important;
  }

  #kurento .flex-lg-column {
    flex-direction: column !important;
  }

  #kurento .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }

  #kurento .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }

  #kurento .flex-lg-wrap {
    flex-wrap: wrap !important;
  }

  #kurento .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }

  #kurento .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  #kurento .justify-content-lg-start {
    justify-content: flex-start !important;
  }

  #kurento .justify-content-lg-end {
    justify-content: flex-end !important;
  }

  #kurento .justify-content-lg-center {
    justify-content: center !important;
  }

  #kurento .justify-content-lg-between {
    justify-content: space-between !important;
  }

  #kurento .justify-content-lg-around {
    justify-content: space-around !important;
  }

  #kurento .align-items-lg-start {
    align-items: flex-start !important;
  }

  #kurento .align-items-lg-end {
    align-items: flex-end !important;
  }

  #kurento .align-items-lg-center {
    align-items: center !important;
  }

  #kurento .align-items-lg-baseline {
    align-items: baseline !important;
  }

  #kurento .align-items-lg-stretch {
    align-items: stretch !important;
  }

  #kurento .align-content-lg-start {
    align-content: flex-start !important;
  }

  #kurento .align-content-lg-end {
    align-content: flex-end !important;
  }

  #kurento .align-content-lg-center {
    align-content: center !important;
  }

  #kurento .align-content-lg-between {
    align-content: space-between !important;
  }

  #kurento .align-content-lg-around {
    align-content: space-around !important;
  }

  #kurento .align-content-lg-stretch {
    align-content: stretch !important;
  }

  #kurento .align-self-lg-auto {
    align-self: auto !important;
  }

  #kurento .align-self-lg-start {
    align-self: flex-start !important;
  }

  #kurento .align-self-lg-end {
    align-self: flex-end !important;
  }

  #kurento .align-self-lg-center {
    align-self: center !important;
  }

  #kurento .align-self-lg-baseline {
    align-self: baseline !important;
  }

  #kurento .align-self-lg-stretch {
    align-self: stretch !important;
  }
}

@media (min-width: 1200px) {
  #kurento .flex-xl-row {
    flex-direction: row !important;
  }

  #kurento .flex-xl-column {
    flex-direction: column !important;
  }

  #kurento .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }

  #kurento .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }

  #kurento .flex-xl-wrap {
    flex-wrap: wrap !important;
  }

  #kurento .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }

  #kurento .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  #kurento .justify-content-xl-start {
    justify-content: flex-start !important;
  }

  #kurento .justify-content-xl-end {
    justify-content: flex-end !important;
  }

  #kurento .justify-content-xl-center {
    justify-content: center !important;
  }

  #kurento .justify-content-xl-between {
    justify-content: space-between !important;
  }

  #kurento .justify-content-xl-around {
    justify-content: space-around !important;
  }

  #kurento .align-items-xl-start {
    align-items: flex-start !important;
  }

  #kurento .align-items-xl-end {
    align-items: flex-end !important;
  }

  #kurento .align-items-xl-center {
    align-items: center !important;
  }

  #kurento .align-items-xl-baseline {
    align-items: baseline !important;
  }

  #kurento .align-items-xl-stretch {
    align-items: stretch !important;
  }

  #kurento .align-content-xl-start {
    align-content: flex-start !important;
  }

  #kurento .align-content-xl-end {
    align-content: flex-end !important;
  }

  #kurento .align-content-xl-center {
    align-content: center !important;
  }

  #kurento .align-content-xl-between {
    align-content: space-between !important;
  }

  #kurento .align-content-xl-around {
    align-content: space-around !important;
  }

  #kurento .align-content-xl-stretch {
    align-content: stretch !important;
  }

  #kurento .align-self-xl-auto {
    align-self: auto !important;
  }

  #kurento .align-self-xl-start {
    align-self: flex-start !important;
  }

  #kurento .align-self-xl-end {
    align-self: flex-end !important;
  }

  #kurento .align-self-xl-center {
    align-self: center !important;
  }

  #kurento .align-self-xl-baseline {
    align-self: baseline !important;
  }

  #kurento .align-self-xl-stretch {
    align-self: stretch !important;
  }
}

#kurento .float-left {
  float: left !important;
}

#kurento .float-right {
  float: right !important;
}

#kurento .float-none {
  float: none !important;
}

@media (min-width: 576px) {
  #kurento .float-sm-left {
    float: left !important;
  }

  #kurento .float-sm-right {
    float: right !important;
  }

  #kurento .float-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  #kurento .float-md-left {
    float: left !important;
  }

  #kurento .float-md-right {
    float: right !important;
  }

  #kurento .float-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  #kurento .float-lg-left {
    float: left !important;
  }

  #kurento .float-lg-right {
    float: right !important;
  }

  #kurento .float-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  #kurento .float-xl-left {
    float: left !important;
  }

  #kurento .float-xl-right {
    float: right !important;
  }

  #kurento .float-xl-none {
    float: none !important;
  }
}

#kurento .position-static {
  position: static !important;
}

#kurento .position-relative {
  position: relative !important;
}

#kurento .position-absolute {
  position: absolute !important;
}

#kurento .position-fixed {
  position: fixed !important;
}

#kurento .position-sticky {
  position: sticky !important;
}

#kurento .fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

#kurento .fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports (position: sticky) {
  #kurento .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

#kurento .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  clip-path: inset(50%);
  border: 0;
}

#kurento .sr-only-focusable:active,
#kurento .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
  clip-path: none;
}

#kurento .w-25 {
  width: 25% !important;
}

#kurento .w-50 {
  width: 50% !important;
}

#kurento .w-75 {
  width: 75% !important;
}

#kurento .w-100 {
  width: 100% !important;
}

#kurento .h-25 {
  height: 25% !important;
}

#kurento .h-50 {
  height: 50% !important;
}

#kurento .h-75 {
  height: 75% !important;
}

#kurento .h-100 {
  height: 100% !important;
}

#kurento .mw-100 {
  max-width: 100% !important;
}

#kurento .mh-100 {
  max-height: 100% !important;
}

#kurento .m-0 {
  margin: 0 !important;
}

#kurento .mt-0,
#kurento .my-0 {
  margin-top: 0 !important;
}

#kurento .mr-0,
#kurento .mx-0 {
  margin-right: 0 !important;
}

#kurento .mb-0,
#kurento .my-0 {
  margin-bottom: 0 !important;
}

#kurento .ml-0,
#kurento .mx-0 {
  margin-left: 0 !important;
}

#kurento .m-1 {
  margin: 0.25rem !important;
}

#kurento .mt-1,
#kurento .my-1 {
  margin-top: 0.25rem !important;
}

#kurento .mr-1,
#kurento .mx-1 {
  margin-right: 0.25rem !important;
}

#kurento .mb-1,
#kurento .my-1 {
  margin-bottom: 0.25rem !important;
}

#kurento .ml-1,
#kurento .mx-1 {
  margin-left: 0.25rem !important;
}

#kurento .m-2 {
  margin: 0.5rem !important;
}

#kurento .mt-2,
#kurento .my-2 {
  margin-top: 0.5rem !important;
}

#kurento .mr-2,
#kurento .mx-2 {
  margin-right: 0.5rem !important;
}

#kurento .mb-2,
#kurento .my-2 {
  margin-bottom: 0.5rem !important;
}

#kurento .ml-2,
#kurento .mx-2 {
  margin-left: 0.5rem !important;
}

#kurento .m-3 {
  margin: 1rem !important;
}

#kurento .mt-3,
#kurento .my-3 {
  margin-top: 1rem !important;
}

#kurento .mr-3,
#kurento .mx-3 {
  margin-right: 1rem !important;
}

#kurento .mb-3,
#kurento .my-3 {
  margin-bottom: 1rem !important;
}

#kurento .ml-3,
#kurento .mx-3 {
  margin-left: 1rem !important;
}

#kurento .m-4 {
  margin: 1.5rem !important;
}

#kurento .mt-4,
#kurento .my-4 {
  margin-top: 1.5rem !important;
}

#kurento .mr-4,
#kurento .mx-4 {
  margin-right: 1.5rem !important;
}

#kurento .mb-4,
#kurento .my-4 {
  margin-bottom: 1.5rem !important;
}

#kurento .ml-4,
#kurento .mx-4 {
  margin-left: 1.5rem !important;
}

#kurento .m-5 {
  margin: 3rem !important;
}

#kurento .mt-5,
#kurento .my-5 {
  margin-top: 3rem !important;
}

#kurento .mr-5,
#kurento .mx-5 {
  margin-right: 3rem !important;
}

#kurento .mb-5,
#kurento .my-5 {
  margin-bottom: 3rem !important;
}

#kurento .ml-5,
#kurento .mx-5 {
  margin-left: 3rem !important;
}

#kurento .p-0 {
  padding: 0 !important;
}

#kurento .pt-0,
#kurento .py-0 {
  padding-top: 0 !important;
}

#kurento .pr-0,
#kurento .px-0 {
  padding-right: 0 !important;
}

#kurento .pb-0,
#kurento .py-0 {
  padding-bottom: 0 !important;
}

#kurento .pl-0,
#kurento .px-0 {
  padding-left: 0 !important;
}

#kurento .p-1 {
  padding: 0.25rem !important;
}

#kurento .pt-1,
#kurento .py-1 {
  padding-top: 0.25rem !important;
}

#kurento .pr-1,
#kurento .px-1 {
  padding-right: 0.25rem !important;
}

#kurento .pb-1,
#kurento .py-1 {
  padding-bottom: 0.25rem !important;
}

#kurento .pl-1,
#kurento .px-1 {
  padding-left: 0.25rem !important;
}

#kurento .p-2 {
  padding: 0.5rem !important;
}

#kurento .pt-2,
#kurento .py-2 {
  padding-top: 0.5rem !important;
}

#kurento .pr-2,
#kurento .px-2 {
  padding-right: 0.5rem !important;
}

#kurento .pb-2,
#kurento .py-2 {
  padding-bottom: 0.5rem !important;
}

#kurento .pl-2,
#kurento .px-2 {
  padding-left: 0.5rem !important;
}

#kurento .p-3 {
  padding: 1rem !important;
}

#kurento .pt-3,
#kurento .py-3 {
  padding-top: 1rem !important;
}

#kurento .pr-3,
#kurento .px-3 {
  padding-right: 1rem !important;
}

#kurento .pb-3,
#kurento .py-3 {
  padding-bottom: 1rem !important;
}

#kurento .pl-3,
#kurento .px-3 {
  padding-left: 1rem !important;
}

#kurento .p-4 {
  padding: 1.5rem !important;
}

#kurento .pt-4,
#kurento .py-4 {
  padding-top: 1.5rem !important;
}

#kurento .pr-4,
#kurento .px-4 {
  padding-right: 1.5rem !important;
}

#kurento .pb-4,
#kurento .py-4 {
  padding-bottom: 1.5rem !important;
}

#kurento .pl-4,
#kurento .px-4 {
  padding-left: 1.5rem !important;
}

#kurento .p-5 {
  padding: 3rem !important;
}

#kurento .pt-5,
#kurento .py-5 {
  padding-top: 3rem !important;
}

#kurento .pr-5,
#kurento .px-5 {
  padding-right: 3rem !important;
}

#kurento .pb-5,
#kurento .py-5 {
  padding-bottom: 3rem !important;
}

#kurento .pl-5,
#kurento .px-5 {
  padding-left: 3rem !important;
}

#kurento .m-auto {
  margin: auto !important;
}

#kurento .mt-auto,
#kurento .my-auto {
  margin-top: auto !important;
}

#kurento .mr-auto,
#kurento .mx-auto {
  margin-right: auto !important;
}

#kurento .mb-auto,
#kurento .my-auto {
  margin-bottom: auto !important;
}

#kurento .ml-auto,
#kurento .mx-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  #kurento .m-sm-0 {
    margin: 0 !important;
  }

  #kurento .mt-sm-0,
  #kurento .my-sm-0 {
    margin-top: 0 !important;
  }

  #kurento .mr-sm-0,
  #kurento .mx-sm-0 {
    margin-right: 0 !important;
  }

  #kurento .mb-sm-0,
  #kurento .my-sm-0 {
    margin-bottom: 0 !important;
  }

  #kurento .ml-sm-0,
  #kurento .mx-sm-0 {
    margin-left: 0 !important;
  }

  #kurento .m-sm-1 {
    margin: 0.25rem !important;
  }

  #kurento .mt-sm-1,
  #kurento .my-sm-1 {
    margin-top: 0.25rem !important;
  }

  #kurento .mr-sm-1,
  #kurento .mx-sm-1 {
    margin-right: 0.25rem !important;
  }

  #kurento .mb-sm-1,
  #kurento .my-sm-1 {
    margin-bottom: 0.25rem !important;
  }

  #kurento .ml-sm-1,
  #kurento .mx-sm-1 {
    margin-left: 0.25rem !important;
  }

  #kurento .m-sm-2 {
    margin: 0.5rem !important;
  }

  #kurento .mt-sm-2,
  #kurento .my-sm-2 {
    margin-top: 0.5rem !important;
  }

  #kurento .mr-sm-2,
  #kurento .mx-sm-2 {
    margin-right: 0.5rem !important;
  }

  #kurento .mb-sm-2,
  #kurento .my-sm-2 {
    margin-bottom: 0.5rem !important;
  }

  #kurento .ml-sm-2,
  #kurento .mx-sm-2 {
    margin-left: 0.5rem !important;
  }

  #kurento .m-sm-3 {
    margin: 1rem !important;
  }

  #kurento .mt-sm-3,
  #kurento .my-sm-3 {
    margin-top: 1rem !important;
  }

  #kurento .mr-sm-3,
  #kurento .mx-sm-3 {
    margin-right: 1rem !important;
  }

  #kurento .mb-sm-3,
  #kurento .my-sm-3 {
    margin-bottom: 1rem !important;
  }

  #kurento .ml-sm-3,
  #kurento .mx-sm-3 {
    margin-left: 1rem !important;
  }

  #kurento .m-sm-4 {
    margin: 1.5rem !important;
  }

  #kurento .mt-sm-4,
  #kurento .my-sm-4 {
    margin-top: 1.5rem !important;
  }

  #kurento .mr-sm-4,
  #kurento .mx-sm-4 {
    margin-right: 1.5rem !important;
  }

  #kurento .mb-sm-4,
  #kurento .my-sm-4 {
    margin-bottom: 1.5rem !important;
  }

  #kurento .ml-sm-4,
  #kurento .mx-sm-4 {
    margin-left: 1.5rem !important;
  }

  #kurento .m-sm-5 {
    margin: 3rem !important;
  }

  #kurento .mt-sm-5,
  #kurento .my-sm-5 {
    margin-top: 3rem !important;
  }

  #kurento .mr-sm-5,
  #kurento .mx-sm-5 {
    margin-right: 3rem !important;
  }

  #kurento .mb-sm-5,
  #kurento .my-sm-5 {
    margin-bottom: 3rem !important;
  }

  #kurento .ml-sm-5,
  #kurento .mx-sm-5 {
    margin-left: 3rem !important;
  }

  #kurento .p-sm-0 {
    padding: 0 !important;
  }

  #kurento .pt-sm-0,
  #kurento .py-sm-0 {
    padding-top: 0 !important;
  }

  #kurento .pr-sm-0,
  #kurento .px-sm-0 {
    padding-right: 0 !important;
  }

  #kurento .pb-sm-0,
  #kurento .py-sm-0 {
    padding-bottom: 0 !important;
  }

  #kurento .pl-sm-0,
  #kurento .px-sm-0 {
    padding-left: 0 !important;
  }

  #kurento .p-sm-1 {
    padding: 0.25rem !important;
  }

  #kurento .pt-sm-1,
  #kurento .py-sm-1 {
    padding-top: 0.25rem !important;
  }

  #kurento .pr-sm-1,
  #kurento .px-sm-1 {
    padding-right: 0.25rem !important;
  }

  #kurento .pb-sm-1,
  #kurento .py-sm-1 {
    padding-bottom: 0.25rem !important;
  }

  #kurento .pl-sm-1,
  #kurento .px-sm-1 {
    padding-left: 0.25rem !important;
  }

  #kurento .p-sm-2 {
    padding: 0.5rem !important;
  }

  #kurento .pt-sm-2,
  #kurento .py-sm-2 {
    padding-top: 0.5rem !important;
  }

  #kurento .pr-sm-2,
  #kurento .px-sm-2 {
    padding-right: 0.5rem !important;
  }

  #kurento .pb-sm-2,
  #kurento .py-sm-2 {
    padding-bottom: 0.5rem !important;
  }

  #kurento .pl-sm-2,
  #kurento .px-sm-2 {
    padding-left: 0.5rem !important;
  }

  #kurento .p-sm-3 {
    padding: 1rem !important;
  }

  #kurento .pt-sm-3,
  #kurento .py-sm-3 {
    padding-top: 1rem !important;
  }

  #kurento .pr-sm-3,
  #kurento .px-sm-3 {
    padding-right: 1rem !important;
  }

  #kurento .pb-sm-3,
  #kurento .py-sm-3 {
    padding-bottom: 1rem !important;
  }

  #kurento .pl-sm-3,
  #kurento .px-sm-3 {
    padding-left: 1rem !important;
  }

  #kurento .p-sm-4 {
    padding: 1.5rem !important;
  }

  #kurento .pt-sm-4,
  #kurento .py-sm-4 {
    padding-top: 1.5rem !important;
  }

  #kurento .pr-sm-4,
  #kurento .px-sm-4 {
    padding-right: 1.5rem !important;
  }

  #kurento .pb-sm-4,
  #kurento .py-sm-4 {
    padding-bottom: 1.5rem !important;
  }

  #kurento .pl-sm-4,
  #kurento .px-sm-4 {
    padding-left: 1.5rem !important;
  }

  #kurento .p-sm-5 {
    padding: 3rem !important;
  }

  #kurento .pt-sm-5,
  #kurento .py-sm-5 {
    padding-top: 3rem !important;
  }

  #kurento .pr-sm-5,
  #kurento .px-sm-5 {
    padding-right: 3rem !important;
  }

  #kurento .pb-sm-5,
  #kurento .py-sm-5 {
    padding-bottom: 3rem !important;
  }

  #kurento .pl-sm-5,
  #kurento .px-sm-5 {
    padding-left: 3rem !important;
  }

  #kurento .m-sm-auto {
    margin: auto !important;
  }

  #kurento .mt-sm-auto,
  #kurento .my-sm-auto {
    margin-top: auto !important;
  }

  #kurento .mr-sm-auto,
  #kurento .mx-sm-auto {
    margin-right: auto !important;
  }

  #kurento .mb-sm-auto,
  #kurento .my-sm-auto {
    margin-bottom: auto !important;
  }

  #kurento .ml-sm-auto,
  #kurento .mx-sm-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 768px) {
  #kurento .m-md-0 {
    margin: 0 !important;
  }

  #kurento .mt-md-0,
  #kurento .my-md-0 {
    margin-top: 0 !important;
  }

  #kurento .mr-md-0,
  #kurento .mx-md-0 {
    margin-right: 0 !important;
  }

  #kurento .mb-md-0,
  #kurento .my-md-0 {
    margin-bottom: 0 !important;
  }

  #kurento .ml-md-0,
  #kurento .mx-md-0 {
    margin-left: 0 !important;
  }

  #kurento .m-md-1 {
    margin: 0.25rem !important;
  }

  #kurento .mt-md-1,
  #kurento .my-md-1 {
    margin-top: 0.25rem !important;
  }

  #kurento .mr-md-1,
  #kurento .mx-md-1 {
    margin-right: 0.25rem !important;
  }

  #kurento .mb-md-1,
  #kurento .my-md-1 {
    margin-bottom: 0.25rem !important;
  }

  #kurento .ml-md-1,
  #kurento .mx-md-1 {
    margin-left: 0.25rem !important;
  }

  #kurento .m-md-2 {
    margin: 0.5rem !important;
  }

  #kurento .mt-md-2,
  #kurento .my-md-2 {
    margin-top: 0.5rem !important;
  }

  #kurento .mr-md-2,
  #kurento .mx-md-2 {
    margin-right: 0.5rem !important;
  }

  #kurento .mb-md-2,
  #kurento .my-md-2 {
    margin-bottom: 0.5rem !important;
  }

  #kurento .ml-md-2,
  #kurento .mx-md-2 {
    margin-left: 0.5rem !important;
  }

  #kurento .m-md-3 {
    margin: 1rem !important;
  }

  #kurento .mt-md-3,
  #kurento .my-md-3 {
    margin-top: 1rem !important;
  }

  #kurento .mr-md-3,
  #kurento .mx-md-3 {
    margin-right: 1rem !important;
  }

  #kurento .mb-md-3,
  #kurento .my-md-3 {
    margin-bottom: 1rem !important;
  }

  #kurento .ml-md-3,
  #kurento .mx-md-3 {
    margin-left: 1rem !important;
  }

  #kurento .m-md-4 {
    margin: 1.5rem !important;
  }

  #kurento .mt-md-4,
  #kurento .my-md-4 {
    margin-top: 1.5rem !important;
  }

  #kurento .mr-md-4,
  #kurento .mx-md-4 {
    margin-right: 1.5rem !important;
  }

  #kurento .mb-md-4,
  #kurento .my-md-4 {
    margin-bottom: 1.5rem !important;
  }

  #kurento .ml-md-4,
  #kurento .mx-md-4 {
    margin-left: 1.5rem !important;
  }

  #kurento .m-md-5 {
    margin: 3rem !important;
  }

  #kurento .mt-md-5,
  #kurento .my-md-5 {
    margin-top: 3rem !important;
  }

  #kurento .mr-md-5,
  #kurento .mx-md-5 {
    margin-right: 3rem !important;
  }

  #kurento .mb-md-5,
  #kurento .my-md-5 {
    margin-bottom: 3rem !important;
  }

  #kurento .ml-md-5,
  #kurento .mx-md-5 {
    margin-left: 3rem !important;
  }

  #kurento .p-md-0 {
    padding: 0 !important;
  }

  #kurento .pt-md-0,
  #kurento .py-md-0 {
    padding-top: 0 !important;
  }

  #kurento .pr-md-0,
  #kurento .px-md-0 {
    padding-right: 0 !important;
  }

  #kurento .pb-md-0,
  #kurento .py-md-0 {
    padding-bottom: 0 !important;
  }

  #kurento .pl-md-0,
  #kurento .px-md-0 {
    padding-left: 0 !important;
  }

  #kurento .p-md-1 {
    padding: 0.25rem !important;
  }

  #kurento .pt-md-1,
  #kurento .py-md-1 {
    padding-top: 0.25rem !important;
  }

  #kurento .pr-md-1,
  #kurento .px-md-1 {
    padding-right: 0.25rem !important;
  }

  #kurento .pb-md-1,
  #kurento .py-md-1 {
    padding-bottom: 0.25rem !important;
  }

  #kurento .pl-md-1,
  #kurento .px-md-1 {
    padding-left: 0.25rem !important;
  }

  #kurento .p-md-2 {
    padding: 0.5rem !important;
  }

  #kurento .pt-md-2,
  #kurento .py-md-2 {
    padding-top: 0.5rem !important;
  }

  #kurento .pr-md-2,
  #kurento .px-md-2 {
    padding-right: 0.5rem !important;
  }

  #kurento .pb-md-2,
  #kurento .py-md-2 {
    padding-bottom: 0.5rem !important;
  }

  #kurento .pl-md-2,
  #kurento .px-md-2 {
    padding-left: 0.5rem !important;
  }

  #kurento .p-md-3 {
    padding: 1rem !important;
  }

  #kurento .pt-md-3,
  #kurento .py-md-3 {
    padding-top: 1rem !important;
  }

  #kurento .pr-md-3,
  #kurento .px-md-3 {
    padding-right: 1rem !important;
  }

  #kurento .pb-md-3,
  #kurento .py-md-3 {
    padding-bottom: 1rem !important;
  }

  #kurento .pl-md-3,
  #kurento .px-md-3 {
    padding-left: 1rem !important;
  }

  #kurento .p-md-4 {
    padding: 1.5rem !important;
  }

  #kurento .pt-md-4,
  #kurento .py-md-4 {
    padding-top: 1.5rem !important;
  }

  #kurento .pr-md-4,
  #kurento .px-md-4 {
    padding-right: 1.5rem !important;
  }

  #kurento .pb-md-4,
  #kurento .py-md-4 {
    padding-bottom: 1.5rem !important;
  }

  #kurento .pl-md-4,
  #kurento .px-md-4 {
    padding-left: 1.5rem !important;
  }

  #kurento .p-md-5 {
    padding: 3rem !important;
  }

  #kurento .pt-md-5,
  #kurento .py-md-5 {
    padding-top: 3rem !important;
  }

  #kurento .pr-md-5,
  #kurento .px-md-5 {
    padding-right: 3rem !important;
  }

  #kurento .pb-md-5,
  #kurento .py-md-5 {
    padding-bottom: 3rem !important;
  }

  #kurento .pl-md-5,
  #kurento .px-md-5 {
    padding-left: 3rem !important;
  }

  #kurento .m-md-auto {
    margin: auto !important;
  }

  #kurento .mt-md-auto,
  #kurento .my-md-auto {
    margin-top: auto !important;
  }

  #kurento .mr-md-auto,
  #kurento .mx-md-auto {
    margin-right: auto !important;
  }

  #kurento .mb-md-auto,
  #kurento .my-md-auto {
    margin-bottom: auto !important;
  }

  #kurento .ml-md-auto,
  #kurento .mx-md-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 992px) {
  #kurento .m-lg-0 {
    margin: 0 !important;
  }

  #kurento .mt-lg-0,
  #kurento .my-lg-0 {
    margin-top: 0 !important;
  }

  #kurento .mr-lg-0,
  #kurento .mx-lg-0 {
    margin-right: 0 !important;
  }

  #kurento .mb-lg-0,
  #kurento .my-lg-0 {
    margin-bottom: 0 !important;
  }

  #kurento .ml-lg-0,
  #kurento .mx-lg-0 {
    margin-left: 0 !important;
  }

  #kurento .m-lg-1 {
    margin: 0.25rem !important;
  }

  #kurento .mt-lg-1,
  #kurento .my-lg-1 {
    margin-top: 0.25rem !important;
  }

  #kurento .mr-lg-1,
  #kurento .mx-lg-1 {
    margin-right: 0.25rem !important;
  }

  #kurento .mb-lg-1,
  #kurento .my-lg-1 {
    margin-bottom: 0.25rem !important;
  }

  #kurento .ml-lg-1,
  #kurento .mx-lg-1 {
    margin-left: 0.25rem !important;
  }

  #kurento .m-lg-2 {
    margin: 0.5rem !important;
  }

  #kurento .mt-lg-2,
  #kurento .my-lg-2 {
    margin-top: 0.5rem !important;
  }

  #kurento .mr-lg-2,
  #kurento .mx-lg-2 {
    margin-right: 0.5rem !important;
  }

  #kurento .mb-lg-2,
  #kurento .my-lg-2 {
    margin-bottom: 0.5rem !important;
  }

  #kurento .ml-lg-2,
  #kurento .mx-lg-2 {
    margin-left: 0.5rem !important;
  }

  #kurento .m-lg-3 {
    margin: 1rem !important;
  }

  #kurento .mt-lg-3,
  #kurento .my-lg-3 {
    margin-top: 1rem !important;
  }

  #kurento .mr-lg-3,
  #kurento .mx-lg-3 {
    margin-right: 1rem !important;
  }

  #kurento .mb-lg-3,
  #kurento .my-lg-3 {
    margin-bottom: 1rem !important;
  }

  #kurento .ml-lg-3,
  #kurento .mx-lg-3 {
    margin-left: 1rem !important;
  }

  #kurento .m-lg-4 {
    margin: 1.5rem !important;
  }

  #kurento .mt-lg-4,
  #kurento .my-lg-4 {
    margin-top: 1.5rem !important;
  }

  #kurento .mr-lg-4,
  #kurento .mx-lg-4 {
    margin-right: 1.5rem !important;
  }

  #kurento .mb-lg-4,
  #kurento .my-lg-4 {
    margin-bottom: 1.5rem !important;
  }

  #kurento .ml-lg-4,
  #kurento .mx-lg-4 {
    margin-left: 1.5rem !important;
  }

  #kurento .m-lg-5 {
    margin: 3rem !important;
  }

  #kurento .mt-lg-5,
  #kurento .my-lg-5 {
    margin-top: 3rem !important;
  }

  #kurento .mr-lg-5,
  #kurento .mx-lg-5 {
    margin-right: 3rem !important;
  }

  #kurento .mb-lg-5,
  #kurento .my-lg-5 {
    margin-bottom: 3rem !important;
  }

  #kurento .ml-lg-5,
  #kurento .mx-lg-5 {
    margin-left: 3rem !important;
  }

  #kurento .p-lg-0 {
    padding: 0 !important;
  }

  #kurento .pt-lg-0,
  #kurento .py-lg-0 {
    padding-top: 0 !important;
  }

  #kurento .pr-lg-0,
  #kurento .px-lg-0 {
    padding-right: 0 !important;
  }

  #kurento .pb-lg-0,
  #kurento .py-lg-0 {
    padding-bottom: 0 !important;
  }

  #kurento .pl-lg-0,
  #kurento .px-lg-0 {
    padding-left: 0 !important;
  }

  #kurento .p-lg-1 {
    padding: 0.25rem !important;
  }

  #kurento .pt-lg-1,
  #kurento .py-lg-1 {
    padding-top: 0.25rem !important;
  }

  #kurento .pr-lg-1,
  #kurento .px-lg-1 {
    padding-right: 0.25rem !important;
  }

  #kurento .pb-lg-1,
  #kurento .py-lg-1 {
    padding-bottom: 0.25rem !important;
  }

  #kurento .pl-lg-1,
  #kurento .px-lg-1 {
    padding-left: 0.25rem !important;
  }

  #kurento .p-lg-2 {
    padding: 0.5rem !important;
  }

  #kurento .pt-lg-2,
  #kurento .py-lg-2 {
    padding-top: 0.5rem !important;
  }

  #kurento .pr-lg-2,
  #kurento .px-lg-2 {
    padding-right: 0.5rem !important;
  }

  #kurento .pb-lg-2,
  #kurento .py-lg-2 {
    padding-bottom: 0.5rem !important;
  }

  #kurento .pl-lg-2,
  #kurento .px-lg-2 {
    padding-left: 0.5rem !important;
  }

  #kurento .p-lg-3 {
    padding: 1rem !important;
  }

  #kurento .pt-lg-3,
  #kurento .py-lg-3 {
    padding-top: 1rem !important;
  }

  #kurento .pr-lg-3,
  #kurento .px-lg-3 {
    padding-right: 1rem !important;
  }

  #kurento .pb-lg-3,
  #kurento .py-lg-3 {
    padding-bottom: 1rem !important;
  }

  #kurento .pl-lg-3,
  #kurento .px-lg-3 {
    padding-left: 1rem !important;
  }

  #kurento .p-lg-4 {
    padding: 1.5rem !important;
  }

  #kurento .pt-lg-4,
  #kurento .py-lg-4 {
    padding-top: 1.5rem !important;
  }

  #kurento .pr-lg-4,
  #kurento .px-lg-4 {
    padding-right: 1.5rem !important;
  }

  #kurento .pb-lg-4,
  #kurento .py-lg-4 {
    padding-bottom: 1.5rem !important;
  }

  #kurento .pl-lg-4,
  #kurento .px-lg-4 {
    padding-left: 1.5rem !important;
  }

  #kurento .p-lg-5 {
    padding: 3rem !important;
  }

  #kurento .pt-lg-5,
  #kurento .py-lg-5 {
    padding-top: 3rem !important;
  }

  #kurento .pr-lg-5,
  #kurento .px-lg-5 {
    padding-right: 3rem !important;
  }

  #kurento .pb-lg-5,
  #kurento .py-lg-5 {
    padding-bottom: 3rem !important;
  }

  #kurento .pl-lg-5,
  #kurento .px-lg-5 {
    padding-left: 3rem !important;
  }

  #kurento .m-lg-auto {
    margin: auto !important;
  }

  #kurento .mt-lg-auto,
  #kurento .my-lg-auto {
    margin-top: auto !important;
  }

  #kurento .mr-lg-auto,
  #kurento .mx-lg-auto {
    margin-right: auto !important;
  }

  #kurento .mb-lg-auto,
  #kurento .my-lg-auto {
    margin-bottom: auto !important;
  }

  #kurento .ml-lg-auto,
  #kurento .mx-lg-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 1200px) {
  #kurento .m-xl-0 {
    margin: 0 !important;
  }

  #kurento .mt-xl-0,
  #kurento .my-xl-0 {
    margin-top: 0 !important;
  }

  #kurento .mr-xl-0,
  #kurento .mx-xl-0 {
    margin-right: 0 !important;
  }

  #kurento .mb-xl-0,
  #kurento .my-xl-0 {
    margin-bottom: 0 !important;
  }

  #kurento .ml-xl-0,
  #kurento .mx-xl-0 {
    margin-left: 0 !important;
  }

  #kurento .m-xl-1 {
    margin: 0.25rem !important;
  }

  #kurento .mt-xl-1,
  #kurento .my-xl-1 {
    margin-top: 0.25rem !important;
  }

  #kurento .mr-xl-1,
  #kurento .mx-xl-1 {
    margin-right: 0.25rem !important;
  }

  #kurento .mb-xl-1,
  #kurento .my-xl-1 {
    margin-bottom: 0.25rem !important;
  }

  #kurento .ml-xl-1,
  #kurento .mx-xl-1 {
    margin-left: 0.25rem !important;
  }

  #kurento .m-xl-2 {
    margin: 0.5rem !important;
  }

  #kurento .mt-xl-2,
  #kurento .my-xl-2 {
    margin-top: 0.5rem !important;
  }

  #kurento .mr-xl-2,
  #kurento .mx-xl-2 {
    margin-right: 0.5rem !important;
  }

  #kurento .mb-xl-2,
  #kurento .my-xl-2 {
    margin-bottom: 0.5rem !important;
  }

  #kurento .ml-xl-2,
  #kurento .mx-xl-2 {
    margin-left: 0.5rem !important;
  }

  #kurento .m-xl-3 {
    margin: 1rem !important;
  }

  #kurento .mt-xl-3,
  #kurento .my-xl-3 {
    margin-top: 1rem !important;
  }

  #kurento .mr-xl-3,
  #kurento .mx-xl-3 {
    margin-right: 1rem !important;
  }

  #kurento .mb-xl-3,
  #kurento .my-xl-3 {
    margin-bottom: 1rem !important;
  }

  #kurento .ml-xl-3,
  #kurento .mx-xl-3 {
    margin-left: 1rem !important;
  }

  #kurento .m-xl-4 {
    margin: 1.5rem !important;
  }

  #kurento .mt-xl-4,
  #kurento .my-xl-4 {
    margin-top: 1.5rem !important;
  }

  #kurento .mr-xl-4,
  #kurento .mx-xl-4 {
    margin-right: 1.5rem !important;
  }

  #kurento .mb-xl-4,
  #kurento .my-xl-4 {
    margin-bottom: 1.5rem !important;
  }

  #kurento .ml-xl-4,
  #kurento .mx-xl-4 {
    margin-left: 1.5rem !important;
  }

  #kurento .m-xl-5 {
    margin: 3rem !important;
  }

  #kurento .mt-xl-5,
  #kurento .my-xl-5 {
    margin-top: 3rem !important;
  }

  #kurento .mr-xl-5,
  #kurento .mx-xl-5 {
    margin-right: 3rem !important;
  }

  #kurento .mb-xl-5,
  #kurento .my-xl-5 {
    margin-bottom: 3rem !important;
  }

  #kurento .ml-xl-5,
  #kurento .mx-xl-5 {
    margin-left: 3rem !important;
  }

  #kurento .p-xl-0 {
    padding: 0 !important;
  }

  #kurento .pt-xl-0,
  #kurento .py-xl-0 {
    padding-top: 0 !important;
  }

  #kurento .pr-xl-0,
  #kurento .px-xl-0 {
    padding-right: 0 !important;
  }

  #kurento .pb-xl-0,
  #kurento .py-xl-0 {
    padding-bottom: 0 !important;
  }

  #kurento .pl-xl-0,
  #kurento .px-xl-0 {
    padding-left: 0 !important;
  }

  #kurento .p-xl-1 {
    padding: 0.25rem !important;
  }

  #kurento .pt-xl-1,
  #kurento .py-xl-1 {
    padding-top: 0.25rem !important;
  }

  #kurento .pr-xl-1,
  #kurento .px-xl-1 {
    padding-right: 0.25rem !important;
  }

  #kurento .pb-xl-1,
  #kurento .py-xl-1 {
    padding-bottom: 0.25rem !important;
  }

  #kurento .pl-xl-1,
  #kurento .px-xl-1 {
    padding-left: 0.25rem !important;
  }

  #kurento .p-xl-2 {
    padding: 0.5rem !important;
  }

  #kurento .pt-xl-2,
  #kurento .py-xl-2 {
    padding-top: 0.5rem !important;
  }

  #kurento .pr-xl-2,
  #kurento .px-xl-2 {
    padding-right: 0.5rem !important;
  }

  #kurento .pb-xl-2,
  #kurento .py-xl-2 {
    padding-bottom: 0.5rem !important;
  }

  #kurento .pl-xl-2,
  #kurento .px-xl-2 {
    padding-left: 0.5rem !important;
  }

  #kurento .p-xl-3 {
    padding: 1rem !important;
  }

  #kurento .pt-xl-3,
  #kurento .py-xl-3 {
    padding-top: 1rem !important;
  }

  #kurento .pr-xl-3,
  #kurento .px-xl-3 {
    padding-right: 1rem !important;
  }

  #kurento .pb-xl-3,
  #kurento .py-xl-3 {
    padding-bottom: 1rem !important;
  }

  #kurento .pl-xl-3,
  #kurento .px-xl-3 {
    padding-left: 1rem !important;
  }

  #kurento .p-xl-4 {
    padding: 1.5rem !important;
  }

  #kurento .pt-xl-4,
  #kurento .py-xl-4 {
    padding-top: 1.5rem !important;
  }

  #kurento .pr-xl-4,
  #kurento .px-xl-4 {
    padding-right: 1.5rem !important;
  }

  #kurento .pb-xl-4,
  #kurento .py-xl-4 {
    padding-bottom: 1.5rem !important;
  }

  #kurento .pl-xl-4,
  #kurento .px-xl-4 {
    padding-left: 1.5rem !important;
  }

  #kurento .p-xl-5 {
    padding: 3rem !important;
  }

  #kurento .pt-xl-5,
  #kurento .py-xl-5 {
    padding-top: 3rem !important;
  }

  #kurento .pr-xl-5,
  #kurento .px-xl-5 {
    padding-right: 3rem !important;
  }

  #kurento .pb-xl-5,
  #kurento .py-xl-5 {
    padding-bottom: 3rem !important;
  }

  #kurento .pl-xl-5,
  #kurento .px-xl-5 {
    padding-left: 3rem !important;
  }

  #kurento .m-xl-auto {
    margin: auto !important;
  }

  #kurento .mt-xl-auto,
  #kurento .my-xl-auto {
    margin-top: auto !important;
  }

  #kurento .mr-xl-auto,
  #kurento .mx-xl-auto {
    margin-right: auto !important;
  }

  #kurento .mb-xl-auto,
  #kurento .my-xl-auto {
    margin-bottom: auto !important;
  }

  #kurento .ml-xl-auto,
  #kurento .mx-xl-auto {
    margin-left: auto !important;
  }
}

#kurento .text-justify {
  text-align: justify !important;
}

#kurento .text-nowrap {
  white-space: nowrap !important;
}

#kurento .text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#kurento .text-left {
  text-align: left !important;
}

#kurento .text-right {
  text-align: right !important;
}

#kurento .text-center {
  text-align: center !important;
}

@media (min-width: 576px) {
  #kurento .text-sm-left {
    text-align: left !important;
  }

  #kurento .text-sm-right {
    text-align: right !important;
  }

  #kurento .text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  #kurento .text-md-left {
    text-align: left !important;
  }

  #kurento .text-md-right {
    text-align: right !important;
  }

  #kurento .text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  #kurento .text-lg-left {
    text-align: left !important;
  }

  #kurento .text-lg-right {
    text-align: right !important;
  }

  #kurento .text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  #kurento .text-xl-left {
    text-align: left !important;
  }

  #kurento .text-xl-right {
    text-align: right !important;
  }

  #kurento .text-xl-center {
    text-align: center !important;
  }
}

#kurento .text-lowercase {
  text-transform: lowercase !important;
}

#kurento .text-uppercase {
  text-transform: uppercase !important;
}

#kurento .text-capitalize {
  text-transform: capitalize !important;
}

#kurento .font-weight-light {
  font-weight: 300 !important;
}

#kurento .font-weight-normal {
  font-weight: 400 !important;
}

#kurento .font-weight-bold {
  font-weight: 700 !important;
}

#kurento .font-italic {
  font-style: italic !important;
}

#kurento .text-white {
  color: #fff !important;
}

#kurento .text-primary {
  color: #007bff !important;
}

#kurento a.text-primary:hover,
#kurento a.text-primary:focus {
  color: #0062cc !important;
}

#kurento .text-secondary {
  color: #6c757d !important;
}

#kurento a.text-secondary:hover,
#kurento a.text-secondary:focus {
  color: #545b62 !important;
}

#kurento .text-success {
  color: #28a745 !important;
}

#kurento a.text-success:hover,
#kurento a.text-success:focus {
  color: #1e7e34 !important;
}

#kurento .text-info {
  color: #17a2b8 !important;
}

#kurento a.text-info:hover,
#kurento a.text-info:focus {
  color: #117a8b !important;
}

#kurento .text-warning {
  color: #ffc107 !important;
}

#kurento a.text-warning:hover,
#kurento a.text-warning:focus {
  color: #d39e00 !important;
}

#kurento .text-danger {
  color: #dc3545 !important;
}

#kurento a.text-danger:hover,
#kurento a.text-danger:focus {
  color: #bd2130 !important;
}

#kurento .text-light {
  color: #f8f9fa !important;
}

#kurento a.text-light:hover,
#kurento a.text-light:focus {
  color: #dae0e5 !important;
}

#kurento .text-dark {
  color: #343a40 !important;
}

#kurento a.text-dark:hover,
#kurento a.text-dark:focus {
  color: #1d2124 !important;
}

#kurento .text-muted {
  color: #6c757d !important;
}

#kurento .text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

#kurento .visible {
  visibility: visible !important;
}

#kurento .invisible {
  visibility: hidden !important;
}

#kurento,
#kurento-bg,
.kurento-fullscreen {
  --subdued-color: #A8ABA1;
  --subdued-color-lighten-25-percent: #e6e7e4;
  --chat-status-online: #3AA569;
  --chat-status-busy: #E77051;
  --chat-status-away: #E7A151;
  --brand-heading-color: #387592;
  --completion-light-color: #FFB9A7;
  --completion-normal-color: #E77051;
  --completion-dark-color: #D24E2B;
  --link-color: #578EA9;
  --link-color-darken-20-percent: #345566;
  --link-color-lighten-10-percent: #79a5ba;
  --dark-link-color: #206485;
  --global-background-color: #397491;
  --inverse-link-color: white;
  --text-shadow-color: #FAFAFA;
  --text-color: #222124;
  --text-color-lighten-15-percent: #8c8c8c;
  --message-text-color: #555;
  --message-receipt-color: #3AA569;
  --save-button-color: #3AA569;
  --message-avatar-width: 36px;
  --message-avatar-height: 36px;
  --chat-textarea-color: #666;
  --chat-textarea-background-color: white;
  --chat-textarea-height: 60px;
  --send-button-height: 27px;
  --send-button-margin: 3px;
  --controlbox-heading-top-margin: 0.75em;
  --inline-action-margin: 0.75em;
  --roster-height: 194px;
  --flyout-padding: 1.2em;
  /* --chat-head-color: #3AA569;
  --chat-head-color-dark: #1E9652;
  --chat-head-color-darker: #0E763B;
  --chat-head-color-lighten-50-percent: #e7f7ee;*/
  --chat-head-text-color: white;
  --chat-correcting-color: var(--chat-head-color-lighten-50-percent);
  --chat-topic-display: block;
  --chat-info-display: block;
  --highlight-color: #DCF9F6;
  --primary-color: #578EA9;
  --primary-color-dark: #397491;
  --secondary-color: #818479;
  --secondary-color-dark: #585B51;
  --warning-color: #E7A151;
  --warning-color-dark: #D2842B;
  --danger-color: #D24E2B;
  --danger-color-dark: #A93415;
  --light-background-color: #FCFDFD;
  --error-color: #D24E2B;
  --info-color: #1E9652;
  --button-border-radius: 5px;
  --chatbox-border-radius: 4px;
  --controlbox-width: 12.5vw;
  --controlbox-head-color: #578EA9;
  --controlbox-head-color-lighten-45-percent: #eff4f7;
  --controlbox-pane-background-color: white;
  --controlbox-heading-color: inherit;
  --controlbox-heading-font-weight: bold;
  --chat-gutter: 0.5em;
  --minimized-chats-width: 130px;
  /*--mobile-chat-width: 100%;
  --mobile-chat-height: 400px;*/
  --mobile-chat-width: 20%;
  --mobile-chat-height: 40vh;
  --normal-font: "Helvetica", "Arial", sans-serif;
  --heading-font: 'Muli', normal;
  --branding-font: 'Baumans', cursive;
  --heading-display: block;
  --heading-color: white;
  --chatroom-head-color: #4296de;
  --chatroom-head-color-dark: #3f8dd0;
  --chatroom-head-color-lighten-25-percent: #f6ccc1;
  --chatroom-head-button-color: var(--chatroom-head-color);
  --chatroom-head-title-font-weight: normal;
  --chatroom-head-title-padding-right: 0px;
  --chatroom-head-description-color: var(--chatroom-head-color-lighten-25-percent);
  --chatroom-head-description-link-color: white;
  --chatroom-head-description-display: block;
  --chatroom-head-description-border-left: 0px;
  --chatroom-head-description-padding-left: 0px;
  --chatroom-head-border-bottom: 0px;
  --chatroom-width: 400px;
  --chatroom-correcting-color: #fadfd7;
  --chatroom-badge-color: var(--chatroom-head-color);
  --chatroom-badge-hover-color: var(--chatroom-head-color-dark);
  --headline-head-color: #E7A151;
  --headline-message-color: #D2842B;
  --chatbox-button-size: 14px;
  --fullpage-chatbox-button-size: 16px;
  --font-size-tiny: 10px;
  --font-size-small: 12px;
  --font-size: 14px;
  --font-size-large: 16px;
  --font-size-huge: 20px;
  --message-font-size: var(--font-size);
  --separator-text-color: #555555b0;
  --chat-separator-border-bottom: 2px solid var(--chat-head-color);
  --chatroom-separator-border-bottom: 2px solid var(--chatroom-head-color);
  --message-input-border-top: 4px solid var(--chatroom-head-color);
  --message-input-color: var(--chatroom-head-color);
  --line-height-small: 14px;
  --line-height: 16px;
  --line-height-large: 20px;
  --line-height-huge: 27px;
  --occupants-padding: 1em;
  --occupants-background-color: white;
  --occupants-max-width: inherit;
  --occupants-border-left: 1px solid var(--text-color);
  --occupants-border-bottom: 1px solid lightgrey;
  --occupants-features-display: block;
  --embedded-emoji-picker-height: 200px;
  --avatar-border-radius: 10%;
  --avatar-border: 1px solid lightgrey;
  --avatar-background-color: white;
  --fullpage-chat-head-height: 62px;
  --fullpage-chat-height: 100vh;
  --fullpage-chat-width: 100%;
  --fullpage-emoji-picker-height: 200px;
  --fullpage-max-chat-textarea-height: 15em;
  --overlayed-chat-head-height: 55px;
  --overlayed-chat-height: 50vh;
  --overlayed-chat-width: 15vw;
  --overlayed-chatbox-hover-height: 1em;
  --overlayed-emoji-picker-height: 100px;
  --overlayed-max-chat-textarea-height: 200px;
  --overlayed-badge-color: #818479;
  --list-toggle-color: #222124;
  --list-toggle-hover-color: #585B51;
  --list-toggle-font-weight: normal;
  --list-item-action-color: #e3eef3;
  --list-item-link-color: inherit;
  --list-item-link-hover-color: var(--dark-link-color);
  --list-item-open-color: var(--controlbox-head-color);
  --list-item-open-hover-color: var(--controlbox-head-color);
  --list-circle-color: #89d6ab;
  --list-minus-circle-color: #f0a794;
  --list-dot-circle-color: #f6dec1;
}

#kurento.theme-concord {
  --avatar-border-radius: 100%;
  --avatar-border: 0px;
  --avatar-background-color: none;
  --controlbox-pane-background-color: #333;
  --controlbox-heading-color: #777;
  --controlbox-heading-font-weight: bold;
  --chat-topic-display: none;
  --chat-info-display: none;
  --chat-textarea-background-color: #F6F6F6;
  --chat-correcting-color: #FFFFC0;
  --chat-head-text-color: #999;
  --chatbox-border-radius: 0px;
  --heading-display: inline;
  --heading-color: #4F545C;
  --chatroom-head-color: white;
  --chatroom-head-color-lighten-25-percent: blue;
  --chatroom-head-button-color: #999;
  --chatroom-head-title-font-weight: bold;
  --chatroom-head-title-padding-right: 12px;
  --chatroom-head-description-color: black;
  --chatroom-head-description-link-color: #00b3f4;
  --chatroom-head-description-display: inline;
  --chatroom-head-description-border-left: 1px solid #DDD;
  --chatroom-head-description-padding-left: 12px;
  --chatroom-head-border-bottom: 1px solid #EEE;
  --chatroom-correcting-color: #FFFFC0;
  --chatroom-badge-color: #E77051;
  --chatroom-badge-hover-color: #D24E2B;
  --occupants-background-color: #F3F3F3;
  /* TODO: find a way to allow that and reflow the chat-area properly.
     * --occupants-max-width: 240px; */
  --occupants-border-left: 0px;
  --occupants-border-bottom: 0px;
  --occupants-features-display: none;
  --separator-text-color: #AAA;
  --chat-separator-border-bottom: 1px solid #AAA;
  --chatroom-separator-border-bottom: 1px solid #AAA;
  --message-input-border-top: 1px solid #CCC;
  --message-input-color: #CCC;
  --fullpage-chat-head-height: 40px;
  --fullpage-chatbox-button-size: 24px;
  --list-toggle-font-weight: bold;
  --list-item-link-color: #F1F1F1;
  --list-item-link-hover-color: #DDD;
  --list-item-open-color: #444;
  --list-item-open-hover-color: #444;
}

body.kurento-fullscreen {
  margin: 0;
  background-color: var(--global-background-color);
}

#kurento-bg .kurento-brand {
  display: flex;
  justify-content: space-between;
  margin-top: 15vh;
  -webkit-animation-name: fadein;
  -moz-animation-name: fadein;
  animation-name: fadein;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 5s;
  -moz-animation-duration: 5s;
  animation-duration: 5s;
  -webkit-animation-timing-function: ease;
  -moz-animation-timing-function: ease;
  animation-timing-function: ease;
}

#kurento-bg .kurento-brand__padding {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  padding: 0;
}

@media (min-width: 768px) {
  #kurento-bg .kurento-brand__padding {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
}

@media (min-width: 992px) {
  #kurento-bg .kurento-brand__padding {
    flex: 0 0 25%;
    max-width: 25%;
  }
}

@media (min-width: 1200px) {
  #kurento-bg .kurento-brand__padding {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
}

#kurento-bg .kurento-brand__heading {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  padding: 0;
  display: flex;
  justify-content: center;
  margin: auto;
}

@media (min-width: 768px) {
  #kurento-bg .kurento-brand__heading {
    font-size: 4em;
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
}

@media (min-width: 992px) {
  #kurento-bg .kurento-brand__heading {
    font-size: 5em;
    flex: 0 0 75%;
    max-width: 75%;
  }
}

@media (min-width: 1200px) {
  #kurento-bg .kurento-brand__heading {
    font-size: 6em;
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
}

#kurento-bg .kurento-brand__heading svg {
  margin-top: 0.3em;
}

#kurento-bg .kurento-brand__text {
  color: #ffffff;
  font-family: var(--branding-font);
  font-weight: normal;
  text-align: center;
  font-size: 140%;
  margin-left: 0.2em;
}

#kurento-bg .kurento-brand__text .byline {
  margin: 0;
  font-family: var(--heading-font);
  font-size: 0.3em;
  opacity: 0.55;
  margin-bottom: 2em;
  margin-left: -2.7em;
  word-spacing: 5px;
}

#kurento-bg .subdued,
#kurento .subdued {
  opacity: 0.35;
}

#kurento {
  bottom: 0;
  height: 100%;
  position: fixed;
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
  color: var(--text-color);
  font-family: "Helvetica", "Arial", sans-serif;
  font-size: var(--font-size);
  direction: ltr;
  z-index: 22;
}

#kurento textarea:disabled {
  background-color: #EEE !important;
}

#kurento .nopadding {
  padding: 0 !important;
}

#kurento.kurento-overlayed>.row {
  flex-direction: row-reverse;
}

#kurento.kurento-fullscreen .kurento-chatboxes,
#kurento.kurento-mobile .kurento-chatboxes {
  width: 100vw;
  left: -15px;
}

#kurento.kurento-overlayed {
  height: 3em;
}

#kurento .brand-heading-container {
  text-align: center;
}

#kurento .brand-heading {
  display: inline-flex;
  flex-direction: row;
  align-items: flex-start;
  font-family: var(--branding-font);
  color: var(--link-color);
  margin-bottom: 1em;
}

#kurento .brand-heading .brand-name {
  color: var(--link-color);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -0.5em;
}

#kurento .brand-heading .brand-name__text {
  font-size: 120%;
  vertical-align: text-bottom;
}

#kurento .brand-heading .kurento-svg-logo {
  color: var(--link-color);
  height: 1.5em;
  margin-right: 0.25em;
  margin-bottom: -0.25em;
}

#kurento .brand-heading .kurento-svg-logo .cls-1 {
  isolation: isolate;
}

#kurento .brand-heading .kurento-svg-logo .cls-2 {
  opacity: 0.5;
  mix-blend-mode: multiply;
}

#kurento .brand-heading .kurento-svg-logo .cls-3 {
  fill: var(--link-color);
}

#kurento .brand-heading .kurento-svg-logo .cls-4 {
  fill: var(--link-color);
}

#kurento .brand-heading--inverse .kurento-svg-logo {
  margin-bottom: 0em;
  margin-top: -0.2em;
}

#kurento .brand-heading--inverse .byline {
  margin: 0;
  font-family: var(--heading-font);
  font-size: 0.25em;
  opacity: 0.55;
  margin-left: -7em;
  word-spacing: 5px;
}

#kurento .popover {
  position: fixed;
}

#kurento .kurento-chatboxes {
  z-index: 5;
  position: fixed;
  bottom: 2.8em;
  right: -0.5em;
}

#kurento ::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: var(--subdued-color);
}

#kurento ::-moz-placeholder {
  /* Firefox 19+ */
  color: var(--subdued-color);
}

#kurento :-ms-input-placeholder {
  /* IE 10+ */
  color: var(--subdued-color);
}

#kurento :-moz-placeholder {
  /* Firefox 18- */
  color: var(--subdued-color);
}

#kurento ::placeholder {
  color: var(--subdued-color);
}

#kurento ::selection {
  background-color: var(--highlight-color);
}

#kurento ::-moz-selection {
  background-color: var(--highlight-color);
}

@media screen and (max-width: 480px) {
  #kurento {
    margin: 0;
    bottom: 5px;
  }
}

@media screen and (max-height: 450px) {
  #kurento {
    margin: 0;
    bottom: 5px;
  }
}

#kurento ul li {
  height: auto;
}

#kurento div,
#kurento span,
#kurento h1,
#kurento h2,
#kurento h3,
#kurento h4,
#kurento h5,
#kurento h6,
#kurento p,
#kurento blockquote,
#kurento pre,
#kurento a,
#kurento em,
#kurento img,
#kurento strong,
#kurento dl,
#kurento dt,
#kurento dd,
#kurento ol,
#kurento ul,
#kurento li,
#kurento fieldset,
#kurento form,
#kurento legend,
#kurento table,
#kurento caption,
#kurento tbody,
#kurento tfoot,
#kurento thead,
#kurento tr,
#kurento th,
#kurento td,
#kurento article,
#kurento aside,
#kurento details,
#kurento embed,
#kurento figure,
#kurento figcaption,
#kurento footer,
#kurento header,
#kurento hgroup,
#kurento menu,
#kurento nav,
#kurento output,
#kurento ruby,
#kurento section,
#kurento summary,
#kurento time,
#kurento mark,
#kurento audio,
#kurento video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
}

#kurento textarea,
#kurento input[type=submit],
#kurento input[type=button],
#kurento input[type=text],
#kurento input[type=password],
#kurento button {
  font-size: var(--font-size);
  padding: 0.25em;
  min-height: 0;
}

#kurento strong {
  font-weight: 700;
}

#kurento em {
  font-style: italic;
}

#kurento ol,
#kurento ul {
  list-style: none;
}

#kurento li {
  height: 10px;
}

#kurento ul,
#kurento ol,
#kurento dl {
  font: inherit;
  margin: 0;
}

#kurento a,
#kurento a:visited,
#kurento a:not([href]):not([tabindex]) {
  text-decoration: none;
  color: var(--link-color);
  text-shadow: none;
}

#kurento a:hover,
#kurento a:visited:hover,
#kurento a:not([href]):not([tabindex]):hover {
  color: var(--link-color-darken-20-percent);
  text-decoration: none;
  text-shadow: none;
}

#kurento a.fa,
#kurento a.far,
#kurento a.fas,
#kurento a:visited.fa,
#kurento a:visited.far,
#kurento a:visited.fas,
#kurento a:not([href]):not([tabindex]).fa,
#kurento a:not([href]):not([tabindex]).far,
#kurento a:not([href]):not([tabindex]).fas {
  color: var(--subdued-color);
}

#kurento a.fa:hover,
#kurento a.far:hover,
#kurento a.fas:hover,
#kurento a:visited.fa:hover,
#kurento a:visited.far:hover,
#kurento a:visited.fas:hover,
#kurento a:not([href]):not([tabindex]).fa:hover,
#kurento a:not([href]):not([tabindex]).far:hover,
#kurento a:not([href]):not([tabindex]).fas:hover {
  color: var(--gray-color);
}

#kurento svg {
  border-radius: var(--chatbox-border-radius);
}

#kurento .fa,
#kurento .far,
#kurento .fas {
  color: var(--subdued-color);
}

#kurento .fa:hover,
#kurento .far:hover,
#kurento .fas:hover {
  color: var(--gray-color);
}

#kurento .modal {
  background-color: rgba(0, 0, 0, 0.4);
}

#kurento .modal .modal-body p {
  padding: 0.25rem 0;
}

#kurento .modal .modal-footer {
  justify-content: flex-start;
}

#kurento .selected {
  color: var(--link-color) !important;
}

#kurento .circle {
  border-radius: 50%;
}

#kurento .badge {
  line-height: 1;
  font-weight: normal;
  font-size: 90%;
}

#kurento .btn {
  font-weight: normal;
  color: #fff;
}

#kurento .btn .fa,
#kurento .btn .far,
#kurento .btn .fas {
  color: #fff;
  margin-right: 0.5em;
}

#kurento .no-text-select {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

@keyframes colorchange-chatmessage {
  0% {
    background-color: #8dd8ae;
  }

  25% {
    background-color: rgba(141, 216, 174, 0.75);
  }

  50% {
    background-color: rgba(141, 216, 174, 0.5);
  }

  75% {
    background-color: rgba(141, 216, 174, 0.25);
  }

  100% {
    background-color: transparent;
  }
}

@-webkit-keyframes colorchange-chatmessage {
  0% {
    background-color: #8dd8ae;
  }

  25% {
    background-color: rgba(141, 216, 174, 0.75);
  }

  50% {
    background-color: rgba(141, 216, 174, 0.5);
  }

  75% {
    background-color: rgba(141, 216, 174, 0.25);
  }

  100% {
    background-color: transparent;
  }
}

@keyframes colorchange-chatmessage-muc {
  0% {
    background-color: #ffb5a2;
  }

  25% {
    background-color: rgba(255, 181, 162, 0.75);
  }

  50% {
    background-color: rgba(255, 181, 162, 0.5);
  }

  75% {
    background-color: rgba(255, 181, 162, 0.25);
  }

  100% {
    background-color: transparent;
  }
}

@-webkit-keyframes colorchange-chatmessage-muc {
  0% {
    background-color: #ffb5a2;
  }

  25% {
    background-color: rgba(255, 181, 162, 0.75);
  }

  50% {
    background-color: rgba(255, 181, 162, 0.5);
  }

  75% {
    background-color: rgba(255, 181, 162, 0.25);
  }

  100% {
    background-color: transparent;
  }
}

@keyframes fadein {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadein {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeOut {
  0% {
    opacity: 1;
    visibility: visible;
  }

  100% {
    opacity: 0;
    visibility: hidden;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    visibility: visible;
  }

  100% {
    opacity: 0;
    visibility: hidden;
  }
}

#kurento .fade-in {
  opacity: 0;
  /* make things invisible upon start */
  -webkit-animation-name: fadein;
  -moz-animation-name: fadein;
  animation-name: fadein;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.75s;
  -moz-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-timing-function: ease;
  -moz-animation-timing-function: ease;
  animation-timing-function: ease;
}

#kurento .visible {
  opacity: 0;
  /* make things invisible upon start */
  -webkit-animation-name: fadein;
  -moz-animation-name: fadein;
  animation-name: fadein;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 500ms;
  -moz-animation-duration: 500ms;
  animation-duration: 500ms;
  -webkit-animation-timing-function: ease;
  -moz-animation-timing-function: ease;
  animation-timing-function: ease;
}

#kurento .hidden {
  opacity: 0 !important;
  visibility: hidden !important;
}

#kurento .fade-out {
  animation-duration: 1s;
  animation-fill-mode: forwards;
  animation-name: fadeOut;
  animation-timing-function: ease-in-out;
}

#kurento .collapsed {
  height: 0 !important;
  overflow: hidden !important;
  padding: 0 !important;
}

#kurento .locked {
  padding-right: 22px;
}

@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@-moz-keyframes spin {
  from {
    -moz-transform: rotate(0deg);
  }

  to {
    -moz-transform: rotate(359deg);
  }
}

@keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
    -moz-transform: rotate(359deg);
    -ms-transform: rotate(359deg);
    -o-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

#kurento .spinner {
  -webkit-animation: spin 2s infinite, linear;
  -moz-animation: spin 2s infinite, linear;
  animation: spin 2s infinite, linear;
  width: 1em;
  display: block;
  text-align: center;
  margin: 2em;
  font-size: 24px;
}

#kurento .left {
  float: left;
}

#kurento .right {
  float: right;
}

#kurento .centered {
  text-align: center;
  display: block;
  margin: auto;
}

#kurento .hor_centered {
  text-align: center;
  display: block;
  margin: 0 auto;
  clear: both;
}

#kurento .error {
  color: var(--error-color);
}

#kurento .info {
  color: var(--info-color);
}

#kurento .reg-feedback {
  font-size: 85%;
  margin-bottom: 1em;
}

#kurento .reg-feedback,
#kurento #kurento-login .conn-feedback {
  display: block;
  text-align: center;
  width: 100%;
}

#kurento .avatar {
  border-radius: var(--avatar-border-radius);
  border: var(--avatar-border);
  background-color: var(--avatar-background-color);
}

#kurento .activated {
  display: block !important;
}

#kurento .nav-pills .nav-link.active,
#kurento .nav-pills .show>.nav-link {
  background-color: var(--primary-color);
}

#kurento .list-group-item.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color-dark);
}

#kurento .badge {
  text-shadow: none;
}

#kurento .badge {
  color: white;
}

#kurento .btn-primary,
#kurento .button-primary,
#kurento .badge-primary {
  background-color: var(--primary-color);
  border-color: transparent;
}

#kurento .btn-primary:hover,
#kurento .button-primary:hover,
#kurento .badge-primary:hover {
  background-color: var(--primary-color-dark);
  border-color: transparent;
}

#kurento .btn-primary.active,
#kurento .button-primary.active,
#kurento .badge-primary.active {
  background-color: var(--primary-color-dark) !important;
  border-color: transparent !important;
}

#kurento .badge-groupchat {
  background-color: var(--chatroom-head-color);
  border-color: transparent;
}

#kurento .badge-groupchat:hover {
  background-color: var(--chatroom-head-color-dark);
  border-color: transparent;
}

#kurento .badge-groupchat.active {
  background-color: var(--chatroom-head-color-dark) !important;
  border-color: transparent !important;
}

#kurento .btn-info,
#kurento .badge-info {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

#kurento .btn-info:hover,
#kurento .badge-info:hover {
  background-color: var(--primary-color-dark);
  border-color: var(--primary-color-dark);
}

#kurento .button-cancel,
#kurento .btn-secondary,
#kurento .badge-secondary {
  color: white;
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

#kurento .button-cancel:hover,
#kurento .btn-secondary:hover,
#kurento .badge-secondary:hover {
  background-color: var(--secondary-color-dark);
  border-color: var(--secondary-color-dark);
}

#kurento .btn-warning {
  color: white;
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

#kurento .btn-warning:hover {
  color: white;
  background-color: var(--warning-color-dark);
  border-color: var(--warning-color-dark);
}

#kurento .btn-danger {
  color: white;
  background-color: var(--danger-color);
  border-color: var(--danger-color) !important;
}

#kurento .btn-danger:hover {
  background-color: var(--danger-color-dark);
  border-color: var(--danger-color-dark);
}

#kurento .chat-textarea-chatbox-selected {
  border: 1px solid #578308;
  margin: 0;
}

#kurento .chat-textarea-chatroom-selected {
  border: 2px solid var(--link-color);
  margin: 0;
}

@media screen and (max-width: 575px) {
  body .kurento-brand {
    font-size: 3.75em;
  }

  #kurento:not(.kurento-embedded) .chatbox .chat-body {
    border-radius: var(--chatbox-border-radius);
  }

  #kurento:not(.kurento-embedded) .flyout {
    border-radius: var(--chatbox-border-radius);
  }
}

@media screen and (min-width: 576px) {
  #kurento .offset-sm-2 {
    margin-left: 16.666667%;
  }
}

@media screen and (min-width: 768px) {
  #kurento .offset-md-2 {
    margin-left: 16.666667%;
  }

  #kurento .offset-md-3 {
    margin-left: 25%;
  }
}

@media screen and (min-width: 992px) {
  #kurento .offset-lg-2 {
    margin-left: 16.666667%;
  }

  #kurento .offset-lg-3 {
    margin-left: 25%;
  }
}

@media screen and (min-width: 1200px) {
  #kurento .offset-xl-2 {
    margin-left: 16.666667%;
  }
}

@media screen and (max-height: 450px) {
  #kurento {
    left: 0;
  }
}

#kurento .btn--small {
  font-size: 80%;
  font-weight: normal;
}

#kurento form .form-check-label {
  margin-top: 0.3rem;
}

#kurento form .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: var(--subdued-color);
}

#kurento form .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: var(--subdued-color);
}

#kurento form .form-control:-ms-input-placeholder {
  /* IE 10+ */
  color: var(--subdued-color);
}

#kurento form .form-control:-moz-placeholder {
  /* Firefox 18- */
  color: var(--subdued-color);
}

#kurento form .form-control::placeholder {
  color: var(--subdued-color);
}

#kurento form .clear-input {
  position: absolute;
  right: 0.2em;
  cursor: pointer;
  font-size: 0.75rem;
  bottom: .2em;
}

#kurento form#kurento-register legend,
#kurento form#kurento-login legend {
  width: 100%;
  text-align: center;
  margin: 0 auto 0.5em auto;
}

#kurento form#kurento-register fieldset.buttons,
#kurento form#kurento-login fieldset.buttons {
  text-align: center;
}

#kurento form#kurento-register .login-anon,
#kurento form#kurento-login .login-anon {
  height: auto;
  white-space: normal;
}

#kurento form#kurento-register .save-submit,
#kurento form#kurento-login .save-submit {
  color: var(--save-button-color);
}

#kurento form#kurento-register .form-url,
#kurento form#kurento-login .form-url {
  display: block;
  font-weight: normal;
  margin: 1em 0;
}

#kurento form.kurento-form {
  background: white;
  /*padding: 1.2rem;*/
}

#kurento form.kurento-form legend {
  color: var(--text-color);
  font-size: 125%;
  margin-bottom: 1.5em;
}

#kurento form.kurento-form select,
#kurento form.kurento-form input[type=password],
#kurento form.kurento-form input[type=number],
#kurento form.kurento-form input[type=text] {
  min-width: 50%;
}

#kurento form.kurento-form input[type=text],
#kurento form.kurento-form input[type=password],
#kurento form.kurento-form input[type=number],
#kurento form.kurento-form input[type=button],
#kurento form.kurento-form input[type=submit] {
  padding: 0.5em;
}

#kurento form.kurento-form input[type=button],
#kurento form.kurento-form input[type=submit] {
  padding-left: 1em;
  padding-right: 1em;
  margin: 0.5em 0;
  border: none;
}

#kurento form.kurento-form input.error {
  border: 1px solid var(--error-color);
  color: var(--text-color);
}

#kurento form.kurento-form .text-muted {
  color: var(--subdued-color) !important;
  font-size: 85%;
  padding-top: 0.5em;
  display: none;
}

#kurento form.kurento-form .text-muted a {
  color: var(--link-color-lighten-10-percent);
}

#kurento form.kurento-form .text-muted.error {
  color: var(--error-color);
}

#kurento form.kurento-form--modal {
  padding-bottom: 0;
}

#kurento form.kurento-centered-form {
  text-align: center;
}

#kurento .chatbox-navback {
  display: none;
}

#kurento .flyout {
  border-radius: var(--chatbox-border-radius);
  position: absolute;
}

@media screen and (max-height: 450px) {
  #kurento .flyout {
    border-radius: 0;
  }
}

@media screen and (max-width: 480px) {
  #kurento .flyout {
    border-radius: 0;
  }
}

@media screen and (max-height: 450px) {
  #kurento .flyout {
    bottom: 0;
  }
}

@media screen and (max-width: 480px) {
  #kurento .flyout {
    bottom: 0;
  }
}

#kurento .chatbox-btn {
  border-radius: 25%;
  border: none;
  cursor: pointer;
  font-size: var(--chatbox-button-size);
  margin: 0 0.2em;
  padding: 0 0 0 0.5em;
  text-decoration: none;
}

#kurento .chatbox-btn:active {
  position: relative;
  top: 1px;
}

.curve_border{
  border-radius: 8px !important;
}

#kurento .chat-head {
  display: flex;
  flex-wrap: nowrap;
  color: #fff;
  font-size: 100%;
  margin: 0;
  padding: 5px;
  position: relative;
  background: #156CD5;
  border-bottom: 2px solid #ececee;
  height: 58px;
  border-radius: 8px 8px 0px 0px;
  padding: 12px 16px 9px 16px !important;
}

#kurento .chat-head .user-state {
  margin-right: auto;
}

#kurento .chat-head.chat-head-chatbox {
  background-color: #4296de;
}

#kurento .chat-head .avatar {
  height: 0;
  width: 0;
  margin-right: 0;
  width: 30px !important;
  height: 30px !important;
}

#kurento .chat-head .chatbox-title .chatroom-description {
  font-size: 80%;
}

#kurento .chat-head .chatbox-buttons {
  flex-direction: row-reverse;
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0;
}

#kurento .chat-head #chat_minimized_btn, 
#kurento .chat-head #chat_close_btn {
  margin-left: 10px;
}

#kurento .chat-head .user-custom-message {
  color: var(--chat-head-color-lighten-50-percent);
  font-size: 75%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
  padding-top: 0.2em;
}

#kurento .chat-head a.chatbox-btn.fa,
#kurento .chat-head a.chatbox-btn.fas,
#kurento .chat-head a.chatbox-btn.far,
#kurento .chat-head a:visited.chatbox-btn.fa,
#kurento .chat-head a:visited.chatbox-btn.fas,
#kurento .chat-head a:visited.chatbox-btn.far,
#kurento .chat-head a:hover.chatbox-btn.fa,
#kurento .chat-head a:hover.chatbox-btn.fas,
#kurento .chat-head a:hover.chatbox-btn.far,
#kurento .chat-head a:not([href]):not([tabindex]).chatbox-btn.fa,
#kurento .chat-head a:not([href]):not([tabindex]).chatbox-btn.fas,
#kurento .chat-head a:not([href]):not([tabindex]).chatbox-btn.far {
  color: white;
}

#kurento .chat-head a.chatbox-btn.fa.button-on:before,
#kurento .chat-head a.chatbox-btn.fas.button-on:before,
#kurento .chat-head a.chatbox-btn.far.button-on:before,
#kurento .chat-head a:visited.chatbox-btn.fa.button-on:before,
#kurento .chat-head a:visited.chatbox-btn.fas.button-on:before,
#kurento .chat-head a:visited.chatbox-btn.far.button-on:before,
#kurento .chat-head a:hover.chatbox-btn.fa.button-on:before,
#kurento .chat-head a:hover.chatbox-btn.fas.button-on:before,
#kurento .chat-head a:hover.chatbox-btn.far.button-on:before,
#kurento .chat-head a:not([href]):not([tabindex]).chatbox-btn.fa.button-on:before,
#kurento .chat-head a:not([href]):not([tabindex]).chatbox-btn.fas.button-on:before,
#kurento .chat-head a:not([href]):not([tabindex]).chatbox-btn.far.button-on:before {
  padding: 0.2em;
  background-color: var(--chat-head-text-color);
  color: var(--chat-head-color);
}

#kurento .chat-head .chatbox-btn {
  color: white;
}

#kurento .chat-head .chatbox-btn.fa,
#kurento .chat-head .chatbox-btn.far,
#kurento .chat-head .chatbox-btn.fas {
  color: white;
}

#kurento .chat-head .chatbox-btn:active {
  position: relative;
  top: 1px;
}

#kurento .chat-head .chatbox-btn.button-on:before {
  border-radius: 5%;
  background-color: var(--chat-head-text-color);
  color: var(--chat-head-color);
}

#kurento .chatbox {
  text-align: left;
  margin: 0 .1em;
}

#kurento .chatbox img.emoji {
  height: 1.2em;
  width: 1.2em;
  margin: 0 .05em 0 .1em;
  vertical-align: -0.1em;
}

@media screen and (max-height: 450px) {
  #kurento .chatbox {
    margin: 0;
    width: var(--mobile-chat-width);
  }
}

@media screen and (max-width: 480px) {
  #kurento .chatbox {
    margin: 0;
    width: var(--mobile-chat-width);
  }
}

#kurento .chatbox .box-flyout {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: var(--chat-head-color);
  box-shadow: 0px 0px;
  z-index: 1;
  overflow-y: hidden;
  width: 100%;
}

@media screen and (max-height: 450px) {
  #kurento .chatbox .box-flyout {
    height: var(--mobile-chat-height);
    width: var(--mobile-chat-width);
    height: 100vh;
  }
}

@media screen and (max-width: 480px) {
  #kurento .chatbox .box-flyout {
    height: var(--mobile-chat-height);
    width: var(--mobile-chat-width);
    height: 100vh;
  }
}

#kurento .chatbox .chat-title {
  display: var(--heading-display);
  font-family: var(--heading-font);
  color: var(--heading-color);
  display: block;
  line-height: var(--line-height-large);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: bold;
}

#kurento .chatbox .chat-title.groupchat {
  padding-right: var(--chatroom-head-title-padding-right);
}

#kurento .chatbox .chat-title a {
  color: var(--chat-head-text-color);
  width: 100%;
}

#kurento .chatbox .chat-body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  background-color: var(--chat-head-color);
  border-bottom-left-radius: var(--chatbox-border-radius);
  border-bottom-right-radius: var(--chatbox-border-radius);
  border-top: 0;
}

@media screen and (max-height: 450px) {
  #kurento .chatbox .chat-body {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

@media screen and (max-width: 480px) {
  #kurento .chatbox .chat-body {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}

#kurento .chatbox .chat-body p {
  color: var(--text-color);
  font-size: var(--message-font-size);
  margin: 0;
  padding: 5px;
}

#kurento .chatbox .new-msgs-indicator {
  position: relative;
  width: 100%;
  cursor: pointer;
  background-color: var(--chat-head-color);
  color: var(--light-background-color);
  padding: 0.5em;
  font-size: 0.9em;
  text-align: center;
  z-index: 20;
  white-space: nowrap;
  margin-bottom: 0.25em;
}

#kurento .chatbox .chat-content {
  height: 100%;
  font-size: var(--message-font-size);
  color: rgb(66, 150, 222);
  overflow-y: auto;
  border: 0;
  background-color: #ffffff;
  line-height: 1.3em;
}

#kurento .chatbox .chat-content video {
  width: 100%;
}

#kurento .chatbox .chat-content progress {
  margin: 0.5em 0;
  width: 100%;
}

#kurento .chatbox .chat-content-sendbutton {
  height: calc(100% - (var(--chat-textarea-height) + var(--send-button-height) + 2 * var(--send-button-margin)));
}

#kurento .chatbox .dropdown {
  /* status dropdown styles */
  background-color: var(--light-background-color);
}

#kurento .chatbox .dropdown dd {
  margin: 0;
  padding: 0;
  position: relative;
}

#kurento .chatbox .sendXMPPMessage {
  -moz-background-clip: padding;
  -webkit-background-clip: padding-box;
  border-bottom-radius: var(--chatbox-border-radius);
  background-clip: padding-box;
  background-color: white;
  border: 0;
  margin: 0;
  padding: 0;
}

@media screen and (max-height: 450px) {
  #kurento .chatbox .sendXMPPMessage {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  #kurento .chatbox .sendXMPPMessage {
    width: 100%;
  }
}

#kurento .chatbox .sendXMPPMessage .suggestion-box__results:after {
  display: none;
}

#kurento .chatbox .sendXMPPMessage .spoiler-hint {
  width: 100%;
}

#kurento .chatbox .sendXMPPMessage .chat-textarea {
  color: var(--chat-textarea-color);
  background-color: var(--chat-textarea-background-color);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-radius: var(--chatbox-border-radius);
  padding: 0.5em;
  width: 100%;
  border: none;
  min-height: var(--chat-textarea-height);
  margin-bottom: -4px;
  resize: none;
  white-space: pre-line;
  overflow-wrap: break-word;
}

#kurento .chatbox .sendXMPPMessage .chat-textarea.spoiler {
  height: 42px;
}

#kurento .chatbox .sendXMPPMessage .chat-textarea.correcting {
  background-color: var(--chat-correcting-color);
}

#kurento .chatbox .sendXMPPMessage .send-button {
  position: static;
  left: var(--send-button-margin);
  width: 100%;
  background-color: var(--chat-head-color);
  color: var(--inverse-link-color);
  font-size: 80%;
  height: var(--send-button-height);
  bottom: calc(-var(--send-button-height) - var(--send-button-margin));
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar {
  box-sizing: border-box;
  margin: 0;
  padding: 0.25em;
  display: block;
  border-top: 4px solid #4296de;
  background-color: white;
  color: #4296de;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .fa,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .fa:hover,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .far,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .far:hover,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .fas,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .fas:hover {
  color: #4296de;
  ;
  font-size: var(--font-size-large);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .disabled {
  color: var(--text-color-lighten-15-percent) !important;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .unencrypted a,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .unencrypted {
  color: var(--text-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .unencrypted a .toolbar-menu a,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .unencrypted .toolbar-menu a {
  color: var(--link-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .unverified a,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .unverified {
  color: #cf5300;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .private a,
#kurento .chatbox .sendXMPPMessage .chat-toolbar .private {
  color: #4b7003;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar .toggle-occupants {
  float: right;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li {
  cursor: pointer;
  display: inline-block;
  list-style: none;
  padding: 0 0.5em;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li:hover {
  cursor: pointer;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu {
  background-color: #fff;
  bottom: 2rem;
  box-shadow: -1px -1px 2px 0 rgba(0, 0, 0, 0.4);
  height: auto;
  margin-bottom: 0;
  min-width: 21rem;
  position: absolute;
  right: 0;
  top: auto;
  z-index: 1000;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu.otr-menu {
  left: -6em;
  min-width: 15rem;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu.otr-menu.show {
  display: flex;
  flex-direction: column;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu a {
  color: var(--link-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu .emoji-picker-container {
  background: white;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul.emoji-picker {
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 0.5em;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul li {
  margin-left: 0;
  cursor: pointer;
  list-style: none;
  position: relative;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul li.insert-emoji {
  padding: 0.2em;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul li.insert-emoji.picked {
  background-color: var(--highlight-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul li.insert-emoji:hover {
  background-color: var(--highlight-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul li.insert-emoji a {
  font-size: var(--font-size-huge);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul li.insert-emoji a:hover {
  color: #8f2831;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley a.toggle-smiley {
  padding: 0;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar {
  box-shadow: 0 -1px 1px 0 rgba(0, 0, 0, 0.4);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-category-picker {
  padding-top: 0.5em;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-category-picker ul {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-category-picker li,
#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-skintone-picker li {
  padding: 0.25em;
  font-size: var(--font-size-huge);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-category-picker li:hover,
#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-skintone-picker li:hover {
  background-color: var(--highlight-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-otr ul {
  z-index: 99;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-otr ul li {
  display: block;
  padding: 7px;
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-otr ul li:hover {
  background-color: var(--highlight-color);
}

#kurento .chatbox .sendXMPPMessage .chat-toolbar li.toggle-otr ul li a {
  display: block;
}

#kurento .chatbox .dragresize {
  background: transparent;
  border: 0;
  margin: 0;
  position: absolute;
  top: 0;
  z-index: 20;
}

#kurento .chatbox .dragresize-top {
  cursor: n-resize;
  height: 5px;
  width: 100%;
}

#kurento .chatbox .dragresize-left {
  cursor: w-resize;
  width: 5px;
  height: 100%;
  left: 0;
}

#kurento .chatbox .dragresize-topleft {
  cursor: nw-resize;
  width: 15px;
  height: 15px;
  top: 0;
  left: 0;
}

/* ******************* Overlay and embedded styles *************************** */
#kurento.kurento-embedded .chat-textarea {
  max-height: var(--fullpage-max-chat-textarea-height);
}

#kurento.kurento-embedded .chat-head,
#kurento.kurento-overlayed .chat-head {
  border-top-left-radius: var(--chatbox-border-radius);
  border-top-right-radius: var(--chatbox-border-radius);
}

@media screen and (max-height: 450px) {

  #kurento.kurento-embedded .chat-head,
  #kurento.kurento-overlayed .chat-head {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

@media screen and (max-width: 480px) {

  #kurento.kurento-embedded .chat-head,
  #kurento.kurento-overlayed .chat-head {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

#kurento.kurento-embedded .chat-head .chatbox-title,
#kurento.kurento-overlayed .chat-head .chatbox-title {
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
}

#kurento.kurento-embedded .chat-head .chatbox-buttons,
#kurento.kurento-overlayed .chat-head .chatbox-buttons {
  flex: 0 0 33.33333%;
  max-width: 33.33333%;
}

#kurento.kurento-embedded .emoji-picker,
#kurento.kurento-overlayed .emoji-picker {
  height: var(--embedded-emoji-picker-height);
}

#kurento.kurento-embedded .chatbox,
#kurento.kurento-overlayed .chatbox {
  min-width: var(--overlayed-chat-width) !important;
  width: var(--overlayed-chat-width);
}

#kurento.kurento-embedded .chatbox .box-flyout,
#kurento.kurento-overlayed .chatbox .box-flyout {
  min-width: var(--overlayed-chat-width) !important;
  width: var(--overlayed-chat-width);
}

#kurento.kurento-embedded .chatbox .box-flyout .chat-body,
#kurento.kurento-overlayed .chatbox .box-flyout .chat-body {
  height: calc(100% - var(--overlayed-chat-head-height));
}

#kurento.kurento-overlayed .flyout {
  bottom: 0em;
}

#kurento.kurento-overlayed .box-flyout {
  height: var(--overlayed-chat-height);
  min-height: calc(var(--overlayed-chat-height) / 2);
}

#kurento.kurento-overlayed .chat-head {
  height: var(--overlayed-chat-head-height);
}

#kurento.kurento-overlayed .chat-textarea {
  max-height: var(--overlayed-max-chat-textarea-height);
}

#kurento.kurento-overlayed .emoji-picker {
  height: var(--overlayed-emoji-picker-height);
}

#kurento.kurento-overlayed .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu {
  min-width: 235px;
}

#kurento.kurento-overlayed .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul.emoji-toolbar {
  width: 100%;
}

#kurento.kurento-overlayed .chatbox .sendXMPPMessage .chat-toolbar li .toolbar-menu ul.emoji-toolbar .emoji-category {
  float: left;
}

#kurento.kurento-overlayed .chatbox .sendXMPPMessage .chat-toolbar li.toggle-smiley .emoji-toolbar .emoji-category-picker ul {
  flex-wrap: wrap;
  justify-content: flex-start;
}

@media (max-width: 767.98px) {
  #kurento.kurento-overlayed>.row {
    flex-direction: column;
  }

  #kurento.kurento-overlayed>.row.no-gutters {
    margin: -1em;
  }
}

/* ******************* Fullpage styles *************************** */
#kurento.kurento-fullscreen .flyout {
  border-radius: 0;
  border-top: 0.8em solid var(--chat-head-color);
  border: var(--flyout-padding) solid var(--chat-head-color);
  bottom: 0;
}

#kurento.kurento-fullscreen .chatbox-btn {
  font-size: var(--fullpage-chatbox-button-size);
  margin: 0 0.3em;
}

#kurento.kurento-fullscreen .chat-head {
  height: var(--fullpage-chat-head-height);
  font-size: var(--font-size-huge);
  padding: 0;
}

#kurento.kurento-fullscreen .chat-head .user-custom-message {
  font-size: 70%;
  height: auto;
  line-height: var(--line-height);
}

#kurento.kurento-fullscreen .chat-head .chatbox-title {
  flex: 0 0 83.33333%;
  max-width: 83.33333%;
}

#kurento.kurento-fullscreen .chat-head .chatbox-buttons {
  flex: 0 0 16.66667%;
  max-width: 16.66667%;
}

#kurento.kurento-fullscreen .chat-textarea {
  max-height: var(--fullpage-max-chat-textarea-height);
}

#kurento.kurento-fullscreen .emoji-picker {
  height: var(--fullpage-emoji-picker-height);
}

#kurento.kurento-fullscreen .chatbox {
  width: 100%;
  height: 100%;
  margin: 0;
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

@media (min-width: 768px) {
  #kurento.kurento-fullscreen .chatbox {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
  }
}

@media (min-width: 992px) {
  #kurento.kurento-fullscreen .chatbox {
    flex: 0 0 75%;
    max-width: 75%;
  }
}

@media (min-width: 1200px) {
  #kurento.kurento-fullscreen .chatbox {
    flex: 0 0 83.33333%;
    max-width: 83.33333%;
  }
}

#kurento.kurento-fullscreen .chatbox .box-flyout {
  background-color: var(--chat-head-color);
  box-shadow: none;
  height: var(--fullpage-chat-height);
  min-height: calc(var(--fullpage-chat-height) / 2);
  width: var(--fullpage-chat-width);
  overflow: hidden;
}

#kurento.kurento-fullscreen .chatbox .chat-body {
  height: calc(100% - var(--fullpage-chat-head-height));
  background-color: var(--chat-head-color);
  border-top-left-radius: var(--chatbox-border-radius);
  border-top-right-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen .chatbox .chat-content {
  border-top-left-radius: var(--chatbox-border-radius);
  border-top-right-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen .chatbox .chat-title {
  font-size: var(--font-size-huge);
  line-height: var(--line-height-huge);
}

#kurento.kurento-fullscreen .chatbox .sendXMPPMessage ul {
  width: 100%;
}

#kurento.kurento-fullscreen .chatbox .sendXMPPMessage .toggle-smiley ul.emoji-toolbar .emoji-category-picker {
  margin-right: 5em;
}

#kurento.kurento-fullscreen .chatbox .sendXMPPMessage .toggle-smiley ul.emoji-toolbar .emoji-category {
  padding-left: 10px;
  padding-right: 10px;
}

@media (max-width: 767.98px) {
  #kurento:not(.kurento-embedded)>.row {
    flex-direction: row-reverse;
  }

  #kurento:not(.kurento-embedded) #kurento-login-panel .kurento-form {
    padding: 3em 2em 3em;
  }

  #kurento:not(.kurento-embedded) .chatbox {
    width: calc(100% - 50px);
  }

  #kurento:not(.kurento-embedded) .chatbox .row .box-flyout {
    left: 50px;
    bottom: 0;
    height: 100vh;
    box-shadow: none;
  }

  #kurento.kurento-mobile .chatbox .box-flyout .chatbox-navback,
  #kurento.kurento-overlayed .chatbox .box-flyout .chatbox-navback,
  #kurento.kurento-embedded .chatbox .box-flyout .chatbox-navback,
  #kurento.kurento-fullscreen .chatbox .box-flyout .chatbox-navback {
    display: flex;
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }

  #kurento.kurento-mobile .chatbox .box-flyout .chatbox-navback .fa-arrow-left:before,
  #kurento.kurento-overlayed .chatbox .box-flyout .chatbox-navback .fa-arrow-left:before,
  #kurento.kurento-embedded .chatbox .box-flyout .chatbox-navback .fa-arrow-left:before,
  #kurento.kurento-fullscreen .chatbox .box-flyout .chatbox-navback .fa-arrow-left:before {
    color: white;
  }

  #kurento.kurento-mobile .chatbox .box-flyout .chatbox-title,
  #kurento.kurento-overlayed .chatbox .box-flyout .chatbox-title,
  #kurento.kurento-embedded .chatbox .box-flyout .chatbox-title,
  #kurento.kurento-fullscreen .chatbox .box-flyout .chatbox-title {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }

  #kurento.kurento-mobile .chatbox .box-flyout .chatbox-buttons,
  #kurento.kurento-overlayed .chatbox .box-flyout .chatbox-buttons,
  #kurento.kurento-embedded .chatbox .box-flyout .chatbox-buttons,
  #kurento.kurento-fullscreen .chatbox .box-flyout .chatbox-buttons {
    flex: 0 0 25%;
    max-width: 25%;
  }
}

#kurento .oauth-providers {
  text-align: center;
}

#kurento .oauth-providers .oauth-provider {
  margin: 1em 0;
}

#kurento .oauth-providers .oauth-provider .oauth-login {
  margin-left: 0;
  color: var(--link-color);
  font-size: var(--font-size-large);
}

#kurento .oauth-providers .oauth-provider .oauth-login:hover {
  color: var(--link-color-darken-20-percent);
}

#kurento .oauth-providers .oauth-provider .oauth-login i {
  color: var(--link-color);
  font-size: var(--font-size-huge);
  margin-right: 0.5em;
}

#kurento .set-xmpp-status .chat-status--online,
#kurento .xmpp-status .chat-status--online,
#kurento .roster-contacts .chat-status--online {
  color: var(--chat-status-online);
  background-color: var(--chat-status-online) !important;
}

#kurento .set-xmpp-status .chat-status--busy,
#kurento .xmpp-status .chat-status--busy,
#kurento .roster-contacts .chat-status--busy {
  color: var(--chat-status-busy);
  background-color: var(--chat-status-busy);
}

#kurento .set-xmpp-status .chat-status--away,
#kurento .xmpp-status .chat-status--away,
#kurento .roster-contacts .chat-status--away {
  color: var(--chat-status-away);
  background-color: var(--chat-status-away);
}

#kurento .set-xmpp-status .far.fa-circle,
#kurento .set-xmpp-status .fa-times-circle,
#kurento .xmpp-status .far.fa-circle,
#kurento .xmpp-status .fa-times-circle,
#kurento .roster-contacts .far.fa-circle,
#kurento .roster-contacts .fa-times-circle {
  color: var(--subdued-color);
}

#kurento .room-info {
  font-size: var(--font-size-small);
  font-style: normal;
  font-weight: normal;
}

#kurento .room-info li.room-info {
  display: block;
  margin-left: 5px;
}

#kurento .room-info p.room-info {
  line-height: var(--line-height);
  margin: 0;
  display: block;
  white-space: normal;
}

#kurento div.room-info {
  padding: 0.3em 0;
  clear: left;
  width: 100%;
}

#kurento #controlbox {
  margin-right: 0.5em;
}

#kurento #controlbox .open-rooms-toggle,
#kurento #controlbox .open-rooms-toggle .fa {
  color: var(--list-toggle-color) !important;
  display: none;
}

#kurento #controlbox .open-rooms-toggle:hover,
#kurento #controlbox .open-rooms-toggle .fa:hover {
  color: var(--list-toggle-color) !important;
}

#kurento #controlbox .box-flyout {
  background-color: white;
}

#kurento #controlbox.logged-out .box-flyout .controlbox-pane {
  overflow-y: auto;
}

#kurento #controlbox form.search-xmpp-contact {
  margin: 0;
  padding-left: 5px;
  padding: 0 0 5px 5px;
}

#kurento #controlbox form.search-xmpp-contact input {
  width: 8em;
}

#kurento #controlbox .msgs-indicator {
  margin-right: 0.5em;
}

#kurento #controlbox a.subscribe-to-user {
  padding-left: 2em;
  font-weight: bold;
}

#kurento #controlbox #kurento-register {
  opacity: 0;
  /* make things invisible upon start */
  -webkit-animation-name: fadein;
  -moz-animation-name: fadein;
  animation-name: fadein;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.75s;
  -moz-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-timing-function: ease;
  -moz-animation-timing-function: ease;
  animation-timing-function: ease;
  background: white;
}

#kurento #controlbox #kurento-register .title {
  font-weight: bold;
}

#kurento #controlbox #kurento-register .info {
  color: green;
  font-size: 90%;
  margin: 1.5em 0;
}

#kurento #controlbox #kurento-register .form-errors {
  color: var(--error-color);
  margin: 1em 0;
}

#kurento #controlbox #kurento-register .provider-title {
  font-size: var(--font-size-huge);
  margin: 0;
}

#kurento #controlbox #kurento-register .provider-score {
  width: 178px;
  margin-bottom: 8px;
}

#kurento #controlbox #kurento-register .form-help .url {
  font-weight: bold;
  color: var(--link-color);
}

#kurento #controlbox #kurento-register .input-group {
  display: table;
  margin: auto;
  width: 100%;
}

#kurento #controlbox #kurento-register .input-group span {
  overflow-x: hidden;
  text-overflow: ellipsis;
  max-width: 110px;
}

#kurento #controlbox #kurento-register .input-group span,
#kurento #controlbox #kurento-register .input-group input[name=username] {
  display: table-cell;
  text-align: left;
}

#kurento #controlbox #kurento-register .instructions {
  color: gray;
  font-size: 85%;
}

#kurento #controlbox #kurento-register .instructions:hover {
  color: var(--text-color);
}

#kurento #controlbox .conn-feedback {
  color: var(--controlbox-head-color);
}

#kurento #controlbox .conn-feedback.error {
  color: var(--error-color);
}

#kurento #controlbox .conn-feedback p {
  padding-bottom: 1em;
}

#kurento #controlbox .conn-feedback p.feedback-subject.error {
  font-weight: bold;
}

#kurento #controlbox #kurento-login-panel,
#kurento #controlbox #kurento-register-panel {
  padding-top: 0;
  padding-bottom: 0;
}

#kurento #controlbox #kurento-login-panel {
  flex-direction: column;
}

#kurento #controlbox #kurento-login-panel .brand-heading {
  color: var(--global-background-color);
}

#kurento #controlbox .toggle-register-login {
  font-weight: bold;
}

#kurento #controlbox .controlbox-pane .userinfo {
  padding-bottom: 1em;
}

#kurento #controlbox .controlbox-pane .userinfo .username {
  margin-left: 0.5em;
  overflow: hidden;
  text-overflow: ellipsis;
}

#kurento #controlbox .controlbox-pane .userinfo .profile {
  margin-bottom: 0.75em;
  pointer-events: none;
}

#kurento #controlbox #chatrooms {
  padding: 0;
}

#kurento #controlbox #chatrooms .add-chatroom {
  margin: 0;
  padding: 0;
}

#kurento #controlbox #chatrooms .add-chatroom input[type=button],
#kurento #controlbox #chatrooms .add-chatroom input[type=submit],
#kurento #controlbox #chatrooms .add-chatroom input[type=text] {
  width: 100%;
}

#kurento #controlbox .controlbox-section .controlbox-heading {
  font-family: var(--heading-font);
  color: var(--controlbox-heading-color);
  font-weight: var(--controlbox-heading-font-weight);
  padding: 0;
  font-size: 1.1em;
  line-height: 1.1em;
  text-transform: uppercase;
  margin: .50em 0;
}

#kurento #controlbox .controlbox-section .controlbox-heading--groupchats {
  /* color: var(--chatroom-head-color); */
}

#kurento #controlbox .controlbox-section .controlbox-heading--contacts {
  /* color: var(--chat-head-color-dark); */
}

#kurento #controlbox .controlbox-section .controlbox-heading__btn {
  cursor: pointer;
  font-size: 1em;
  padding: 0;
  margin: .75em .60em .75em .60em;
  min-width: 25px;
  text-align: center;
}

#kurento #controlbox .controlbox-section .controlbox-heading__btn.fa-vcard {
  margin-top: 1em;
}

#kurento #controlbox .dropdown a {
  width: 143px;
  display: inline-block;
}

#kurento #controlbox .dropdown li {
  list-style: none;
  padding-left: 0;
}

#kurento #controlbox .dropdown dd ul {
  padding: 0;
  list-style: none;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  z-index: 21;
  background-color: var(--light-background-color);
}

#kurento #controlbox .dropdown dd ul li:hover {
  background-color: var(--highlight-color);
}

#kurento #controlbox .dropdown dd.search-xmpp {
  height: 0;
}

#kurento #controlbox .dropdown dd.search-xmpp .contact-form-container {
  position: absolute;
  z-index: 22;
}

#kurento #controlbox .dropdown dd.search-xmpp .contact-form-container form {
  box-shadow: 1px 4px 10px 1px rgba(0, 0, 0, 0.4);
  background-color: white;
}

#kurento #controlbox .dropdown dd.search-xmpp li:hover {
  background-color: var(--light-background-color);
}

#kurento #controlbox .dropdown dt a span {
  cursor: pointer;
  display: block;
  padding: 4px 7px 0 5px;
}

#kurento #controlbox .controlbox-panes {
  height: 100%;
  overflow-y: scroll;
  background-color: var(--controlbox-pane-background-color);
}

#kurento #controlbox .controlbox-subtitle {
  font-size: 90%;
  padding: 0.5em;
  text-align: right;
}

#kurento #controlbox .controlbox-pane {
  background-color: var(--controlbox-pane-background-color);
  border: 0;
  font-size: var(--font-size);
  left: 0;
  text-align: left;
  overflow-x: hidden;
  padding: 0 0 0 0;
}

#kurento #controlbox .controlbox-pane .controlbox-padded {
  padding-left: 1em;
  padding-right: 1em;
  align-items: center;
  line-height: normal;
}

#kurento #controlbox .controlbox-pane .controlbox-padded .change-status {
  min-width: 25px;
  text-align: center;
}

#kurento #controlbox .controlbox-pane .add-kurento-contact {
  margin: 0 0 0.75em 0;
}

#kurento #controlbox .controlbox-pane .chatbox-btn {
  margin: 0;
}

#kurento #controlbox .controlbox-pane .switch-form {
  text-align: center;
  padding: 2em 0;
}

#kurento #controlbox .controlbox-pane .switch-form p {
  margin-top: 0.5em;
}

#kurento #controlbox .controlbox-pane dd {
  margin-left: 0;
  margin-bottom: 0;
}

#kurento #controlbox .controlbox-pane dd.odd {
  background-color: #DCEAC5;
}

#kurento #controlbox .add-xmpp-contact {
  padding: 1em 0.5em;
}

#kurento #controlbox .add-xmpp-contact input {
  margin: 0 0 1rem;
  width: 100%;
}

#kurento #controlbox .add-xmpp-contact button {
  width: 100%;
}

#kurento .toggle-controlbox {
  text-align: center;
  background-color: var(--link-color);
  border-top-left-radius: var(--button-border-radius);
  border-top-right-radius: var(--button-border-radius);
  color: #0a0a0a;
  float: right;
  height: 100%;
  margin: 0 var(--chat-gutter);
  padding: 1em;
}

#kurento .toggle-controlbox span {
  color: var(--inverse-link-color);
}

#kurento.kurento-overlayed #controlbox {
  order: -1;
  min-width: var(--controlbox-width) !important;
  width: var(--controlbox-width);
}

#kurento.kurento-overlayed #controlbox .box-flyout {
  min-width: var(--controlbox-width) !important;
  width: var(--controlbox-width);
}

#kurento.kurento-overlayed #controlbox .login-trusted {
  white-space: nowrap;
  font-size: 90%;
}

#kurento.kurento-overlayed #controlbox #kurento-login-trusted {
  margin-top: 0.5em;
}

#kurento.kurento-overlayed #controlbox:not(.logged-out) .controlbox-head {
  height: 15px;
}

#kurento.kurento-overlayed #controlbox .brand-heading-container {
  width: 100%;
}

#kurento.kurento-overlayed #controlbox .controlbox-head {
  display: flex;
  flex-direction: row-reverse;
  flex-wrap: nowrap;
  /*justify-content: space-between;*/
}

#kurento.kurento-overlayed #controlbox .controlbox-head .brand-heading {
  color: var(--text-color);
  font-size: 2em;
}

#kurento.kurento-overlayed #controlbox .controlbox-head .chatbox-btn {
  color: var(--controlbox-head-color);
  /*margin: 0;
      margin-top: -0.9vh;
      margin-right: -0.2vh;*/
}

#kurento.kurento-overlayed #controlbox #kurento-register,
#kurento.kurento-overlayed #controlbox #kurento-login {
  flex: 0 0 100%;
  max-width: 100%;
  padding-bottom: 0;
}

#kurento.kurento-overlayed #controlbox #kurento-register .button-cancel {
  font-size: 90%;
}

#kurento.kurento-overlayed #controlbox .controlbox-panes {
  border-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen #controlbox,
#kurento.kurento-mobile #controlbox {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  margin: 0;
}

@media (min-width: 768px) {

  #kurento.kurento-fullscreen #controlbox,
  #kurento.kurento-mobile #controlbox {
    flex: 0 0 33.33333%;
    max-width: 33.33333%;
  }
}

@media (min-width: 992px) {

  #kurento.kurento-fullscreen #controlbox,
  #kurento.kurento-mobile #controlbox {
    flex: 0 0 25%;
    max-width: 25%;
  }
}

@media (min-width: 1200px) {

  #kurento.kurento-fullscreen #controlbox,
  #kurento.kurento-mobile #controlbox {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }
}

#kurento.kurento-fullscreen #controlbox.logged-out,
#kurento.kurento-mobile #controlbox.logged-out {
  flex: 0 0 100%;
  max-width: 100%;
}

#kurento.kurento-fullscreen #controlbox .controlbox-pane,
#kurento.kurento-mobile #controlbox .controlbox-pane {
  border-radius: 0;
}

#kurento.kurento-fullscreen #controlbox .flyout,
#kurento.kurento-mobile #controlbox .flyout {
  border-radius: 0;
}

#kurento.kurento-fullscreen #controlbox #kurento-login-panel,
#kurento.kurento-mobile #controlbox #kurento-login-panel {
  border-radius: 0;
}

#kurento.kurento-fullscreen #controlbox #kurento-login-panel .kurento-form,
#kurento.kurento-mobile #controlbox #kurento-login-panel .kurento-form {
  padding: 3em 2em 3em;
}

#kurento.kurento-fullscreen #controlbox .toggle-register-login,
#kurento.kurento-mobile #controlbox .toggle-register-login {
  line-height: var(--line-height-huge);
}

#kurento.kurento-fullscreen #controlbox .brand-heading-container,
#kurento.kurento-mobile #controlbox .brand-heading-container {
  flex: 0 0 100%;
  max-width: 100%;
  margin-top: 5em;
  margin-bottom: 1em;
}

#kurento.kurento-fullscreen #controlbox .brand-heading-container .brand-heading,
#kurento.kurento-mobile #controlbox .brand-heading-container .brand-heading {
  font-size: 500%;
  padding: 0.7em 0 0 0;
  opacity: 0.8;
  color: var(--brand-heading-color);
}

#kurento.kurento-fullscreen #controlbox .brand-heading-container .brand-subtitle,
#kurento.kurento-mobile #controlbox .brand-heading-container .brand-subtitle {
  font-size: 90%;
  padding: 0.5em;
}

@media screen and (max-width: 480px) {

  #kurento.kurento-fullscreen #controlbox .brand-heading-container .brand-heading,
  #kurento.kurento-mobile #controlbox .brand-heading-container .brand-heading {
    font-size: 400%;
  }
}

#kurento.kurento-fullscreen #controlbox.logged-out,
#kurento.kurento-mobile #controlbox.logged-out {
  flex: 0 0 100%;
  max-width: 100%;
  opacity: 0;
  /* make things invisible upon start */
  -webkit-animation-name: fadein;
  -moz-animation-name: fadein;
  animation-name: fadein;
  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-animation-duration: 0.75s;
  -moz-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-timing-function: ease;
  -moz-animation-timing-function: ease;
  animation-timing-function: ease;
  width: 100%;
}

#kurento.kurento-fullscreen #controlbox.logged-out .box-flyout,
#kurento.kurento-mobile #controlbox.logged-out .box-flyout {
  width: 100%;
}

#kurento.kurento-fullscreen #controlbox .box-flyout,
#kurento.kurento-mobile #controlbox .box-flyout {
  border: 0;
  width: 100%;
  z-index: 1;
  background-color: var(--controlbox-head-color);
}

#kurento.kurento-fullscreen #controlbox .box-flyout .controlbox-head,
#kurento.kurento-mobile #controlbox .box-flyout .controlbox-head {
  display: none;
}

#kurento.kurento-fullscreen #controlbox #kurento-register,
#kurento.kurento-fullscreen #controlbox #kurento-login,
#kurento.kurento-mobile #controlbox #kurento-register,
#kurento.kurento-mobile #controlbox #kurento-login {
  position: relative;
  width: 100%;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
  flex: 0 0 66.66667%;
  max-width: 66.66667%;
  margin-left: 16.66667%;
}

@media (min-width: 576px) {

  #kurento.kurento-fullscreen #controlbox #kurento-register,
  #kurento.kurento-fullscreen #controlbox #kurento-login,
  #kurento.kurento-mobile #controlbox #kurento-register,
  #kurento.kurento-mobile #controlbox #kurento-login {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
    margin-left: 16.66667%;
  }
}

@media (min-width: 768px) {

  #kurento.kurento-fullscreen #controlbox #kurento-register,
  #kurento.kurento-fullscreen #controlbox #kurento-login,
  #kurento.kurento-mobile #controlbox #kurento-register,
  #kurento.kurento-mobile #controlbox #kurento-login {
    flex: 0 0 66.66667%;
    max-width: 66.66667%;
    margin-left: 16.66667%;
  }
}

@media (min-width: 992px) {

  #kurento.kurento-fullscreen #controlbox #kurento-register,
  #kurento.kurento-fullscreen #controlbox #kurento-login,
  #kurento.kurento-mobile #controlbox #kurento-register,
  #kurento.kurento-mobile #controlbox #kurento-login {
    flex: 0 0 50%;
    max-width: 50%;
    margin-left: 25%;
  }
}

#kurento.kurento-fullscreen #controlbox #kurento-register .title,
#kurento.kurento-fullscreen #controlbox #kurento-register .instructions,
#kurento.kurento-fullscreen #controlbox #kurento-login .title,
#kurento.kurento-fullscreen #controlbox #kurento-login .instructions,
#kurento.kurento-mobile #controlbox #kurento-register .title,
#kurento.kurento-mobile #controlbox #kurento-register .instructions,
#kurento.kurento-mobile #controlbox #kurento-login .title,
#kurento.kurento-mobile #controlbox #kurento-login .instructions {
  margin: 1em 0;
}

#kurento.kurento-fullscreen #controlbox #kurento-register input[type=submit],
#kurento.kurento-fullscreen #controlbox #kurento-register input[type=button],
#kurento.kurento-fullscreen #controlbox #kurento-login input[type=submit],
#kurento.kurento-fullscreen #controlbox #kurento-login input[type=button],
#kurento.kurento-mobile #controlbox #kurento-register input[type=submit],
#kurento.kurento-mobile #controlbox #kurento-register input[type=button],
#kurento.kurento-mobile #controlbox #kurento-login input[type=submit],
#kurento.kurento-mobile #controlbox #kurento-login input[type=button] {
  width: auto;
}

@media (max-width: 767.98px) {
  #kurento:not(.kurento-embedded) {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes {
    margin: 0 !important;
    flex-direction: row !important;
    justify-content: space-between;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes .kurento-chatroom {
    font-size: 14px;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes .chatbox .box-flyout {
    margin-left: 180px;
    left: 0;
    bottom: 0;
    border-radius: 0;
    width: 50vw !important;
    height: 50vh !important;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes #controlbox {
    width: 100vw !important;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes #controlbox .box-flyout {
    width: 50vw !important;
    height: 50vh !important;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes #controlbox .sidebar {
    display: block;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes.sidebar-open .chatbox:not(#controlbox) {
    display: none;
  }

  #kurento:not(.kurento-embedded) .kurento-chatboxes.sidebar-open #controlbox .controlbox-pane {
    display: block;
  }
}

#kurento.kurento-overlayed .brand-heading {
  padding-top: 0.8rem;
  padding-left: 0.8rem;
  width: 100%;
}

#kurento.kurento-overlayed .kurento-svg-logo {
  height: 1em;
}

#kurento #kurento-modals .set-xmpp-status {
  margin: 0em;
}

#kurento #kurento-modals .set-xmpp-status .custom-control-label {
  margin-top: 0.25em;
}

#kurento #kurento-modals #omemo-tabpanel {
  margin-top: 1em;
}

#kurento #kurento-modals .btn {
  font-weight: normal;
}

#kurento #kurento-modals #user-profile-modal .profile-form label {
  font-weight: bold;
}

#kurento #kurento-modals #user-profile-modal .fingerprint-removal label {
  display: flex;
  padding: 0.75rem 1.25rem;
}

#kurento #kurento-modals #user-profile-modal .list-group-item {
  display: flex;
  justify-content: left;
  font-size: 95%;
}

#kurento #kurento-modals #user-profile-modal .list-group-item input[type="checkbox"] {
  margin-right: 1em;
}

#kurento #kurento-modals .fingerprints {
  width: 100%;
  margin-bottom: 1em;
}

#kurento #kurento-modals .fingerprint-trust {
  display: flex;
  justify-content: space-between;
  font-size: 95%;
}

#kurento #kurento-modals .fingerprint-trust .fingerprint {
  margin-left: 1em;
}

#kurento #kurento-roster {
  text-align: left;
  width: 100%;
  position: relative;
  margin: 0;
  height: var(--roster-height);
  padding: 0;
  overflow: hidden;
  height: calc(100% - 70px);
  /* Custom addition for CSP */
}

#kurento #kurento-roster #online-count {
  display: none;
}

#kurento #kurento-roster .search-xmpp ul li.chat-info {
  padding-left: 10px;
}

#kurento #kurento-roster .roster-filter-form {
  width: 100%;
}

#kurento #kurento-roster .roster-filter-form .button-group {
  padding: 0.2em;
}

#kurento #kurento-roster .roster-filter-form span {
  padding: 0;
  cursor: pointer;
  min-width: 25px;
  text-align: center;
  margin: 1em 0em .4em 0em;
}

#kurento #kurento-roster .roster-filter-form .roster-filter {
  /*width: 9.5vw;*/
  width: 13.5vw;
  margin: -.2em;
  /*margin-top: -1.3em;*/
  font-size: calc(var(--font-size) - 2px);
}

#kurento #kurento-roster .roster-filter-form .state-type {
  font-size: calc(var(--font-size) - 2px);
  width: 100%;
}

#kurento #kurento-roster .roster-contacts {
  padding: 0;
  margin: 0 0 0.2em 0;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

#kurento #kurento-roster .roster-contacts .roster-group {
  border: none;
  color: var(--text-color);
  font-weight: normal;
  text-shadow: 0 1px 0 var(--text-shadow-color);
  margin: 0.75em 0 0.75em 0;
}

#kurento #kurento-roster .roster-contacts .roster-group .group-toggle {
  font-family: var(--heading-font);
  display: block;
  width: 94%;
  margin-left: 16px;
  padding-top: 0;
  padding-bottom: 0.3rem;
}

#kurento #kurento-roster .roster-contacts .roster-group .group-toggle,
#kurento #kurento-roster .roster-contacts .roster-group .group-toggle .fa {
  color: var(--list-toggle-color) !important;
}

#kurento #kurento-roster .roster-contacts .roster-group .group-toggle:hover,
#kurento #kurento-roster .roster-contacts .roster-group .group-toggle .fa:hover {
  color: var(--list-toggle-hover-color:) !important;
}

#kurento #kurento-roster .roster-contacts .roster-group li.requesting-xmpp-contact a {
  line-height: var(--line-height);
}

#kurento #kurento-roster .roster-contacts .roster-group li.requesting-xmpp-contact .req-contact-name {
  padding: 0 0 0 0;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat {
  margin: 0;
  padding: 0;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat.unread-msgs {
  font-weight: bold;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat.unread-msgs .contact-name {
  width: 70%;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat .msgs-indicator {
  color: black;
  background-color: var(--chat-head-color);
  opacity: 1;
  border-radius: 10%;
  padding: 0.2em;
  font-size: var(--font-size-small);
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat .contact-name {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding: 0;
  margin: 0;
  max-width: 85%;
  float: none;
  height: 100%;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat .contact-name.unread-msgs {
  max-width: 60%;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat .avatar {
  float: left;
  display: inline-block;
}

#kurento #kurento-roster .roster-contacts .roster-group li.current-xmpp-contact span {
  font-size: 12px !important;
  margin-right: 0.3em;
  vertical-align: middle;
}

#kurento #kurento-roster .roster-contacts .roster-group li.odd {
  background-color: #DCEAC5;
  /* Make this difference */
}

#kurento #kurento-roster .roster-contacts .roster-group li a,
#kurento #kurento-roster .roster-contacts .roster-group li span {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#kurento #kurento-roster .roster-contacts .roster-group li .decline-xmpp-request {
  margin-left: 5px;
}

#kurento #kurento-roster .roster-contacts .roster-group li:hover {
  background-color: var(--controlbox-head-color-lighten-45-percent);
}

#kurento #kurento-roster span.pending-contact-name {
  line-height: var(--line-height);
  width: 100%;
}

#kurento .list-container {
  text-align: left;
  padding: 0.3em 0;
}

#kurento .list-container .list-toggle {
  font-family: var(--heading-font);
  font-weight: var(--list-toggle-font-weight);
  display: block;
  color: var(--list-toggle-color);
  padding: 0 0 0.5rem 0;
}

#kurento .list-container .list-toggle:hover {
  color: var(--list-toggle-hover-color);
}

#kurento .items-list {
  text-align: left;
}

#kurento .items-list .list-item {
  border: none;
  clear: both;
  color: var(--text-color);
  overflow: hidden;
  padding: 0.5em 0;
  text-shadow: 0 1px 0 var(--text-shadow-color);
  word-wrap: break-word;
  height: 2.5em;
}

#kurento .items-list .list-item.unread-msgs {
  font-weight: bold;
}

#kurento .items-list .list-item .list-item-link {
  color: var(--list-item-link-color);
  margin: auto;
  font-size: var(--font-size);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: baseline;
}

#kurento .items-list .list-item .list-item-link:hover {
  color: var(--list-item-link-hover-color);
}

#kurento .items-list .list-item .list-item-badge {
  opacity: 1;
  border-radius: 25%;
  color: white;
  font-size: var(--font-size-small);
  line-height: var(--font-size-small);
}

#kurento .items-list .list-item .list-item-action {
  opacity: 0;
  font-size: var(--font-size-tiny);
  padding: 0;
  /* margin: 0 .3em 0 .3em;
      width: 2em;*/
  color: var(--subdued-color);
}

#kurento .items-list .list-item .list-item-action:before {
  font-size: var(--font-size);
}

#kurento .items-list .list-item .list-item-action.button-on {
  /*color: var(--list-item-link-color);*/
  padding: .2em;
  background-color: gray;
  color: white;
  padding-bottom: .35em;
  border-radius: 1px;
}

#kurento .items-list .list-item .list-item-action.button-on:hover {
  color: var(--list-item-link-hover-color);
}

#kurento .items-list .list-item .list-item-action:hover {
  color: var(--list-toggle-hover-color);
  opacity: 1;
}

#kurento .items-list .list-item .list-item-action--visible {
  opacity: 1 !important;
}

#kurento .items-list .list-item.open {
  background-color: var(--list-item-open-color);
}

#kurento .items-list .list-item.open:hover {
  background-color: var(--list-item-open-hover-color) !important;
}

#kurento .items-list .list-item.open a {
  color: white;
}

#kurento .items-list .list-item.open .list-item-link:hover {
  color: white;
}

#kurento .items-list .list-item.open .list-item-action {
  color: var(--list-item-action-color);
}

#kurento .items-list .list-item.open .list-item-action:hover {
  color: white;
}

#kurento .items-list .list-item.open .fa-circle {
  color: var(--list-circle-color);
}

#kurento .items-list .list-item.open .fa-minus-circle {
  color: var(--list-minus-circle-color);
}

#kurento .items-list .list-item.open .fa-dot-circle {
  color: var(--list-dot-circle-color);
}

#kurento .items-list .list-item.open .far .fa-circle,
#kurento .items-list .list-item.open .fa-times-circle {
  color: var(--subdued-color-lighten-25-percent);
}

#kurento .items-list .list-item:hover {
  background-color: var(--controlbox-head-color-lighten-45-percent);
}

#kurento .items-list .list-item:hover .fa,
#kurento .items-list .list-item:hover .far,
#kurento .items-list .list-item:hover .fas {
  opacity: 1;
}

#kurento.kurento-embedded .badge-room-color,
#kurento .badge-room-color {
  background-color: var(--chatroom-head-color);
}

#kurento.kurento-embedded .add-chatroom input[type="submit"],
#kurento.kurento-embedded .add-chatroom input[type="button"],
#kurento .add-chatroom input[type="submit"],
#kurento .add-chatroom input[type="button"] {
  margin: 0.3em 0;
}

#kurento.kurento-embedded #room-details-modal .features-list,
#kurento #room-details-modal .features-list {
  margin-left: 1em;
}

#kurento.kurento-embedded .chatroom-features,
#kurento .chatroom-features {
  width: 100%;
}

#kurento.kurento-embedded .chatroom-features .features-list,
#kurento .chatroom-features .features-list {
  padding-top: 0;
}

#kurento.kurento-embedded .chatroom-features .features-list .feature,
#kurento .chatroom-features .features-list .feature {
  width: 100%;
  margin-right: 0.5em;
  padding-right: 0;
  font-size: 1em;
  cursor: help;
}

#kurento.kurento-embedded .chatroom-features .features-list .feature .fa,
#kurento .chatroom-features .features-list .feature .fa {
  margin-right: 0.5em;
  color: var(--text-color);
}

#kurento.kurento-embedded .chat-head-chatroom,
#kurento .chat-head-chatroom {
  background-color: var(--chatroom-head-color);
  border-bottom: var(--chatroom-head-border-bottom);
}

#kurento.kurento-embedded .chat-head-chatroom .chatroom-description,
#kurento .chat-head-chatroom .chatroom-description {
  color: var(--chatroom-head-description-color);
  display: var(--chatroom-head-description-display);
  font-size: var(--font-size);
  font-size: 70%;
  margin-top: 3px;
  overflow-y: hidden;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-left: var(--chatroom-head-description-border-left);
  padding-left: var(--chatroom-head-description-padding-left);
}

#kurento.kurento-embedded .chat-head-chatroom .chatroom-description a,
#kurento .chat-head-chatroom .chatroom-description a {
  color: var(--chatroom-head-description-link-color);
}

#kurento.kurento-embedded .chat-head-chatroom a.chatbox-btn.fa,
#kurento.kurento-embedded .chat-head-chatroom a:visited.chatbox-btn.fa,
#kurento.kurento-embedded .chat-head-chatroom a:hover.chatbox-btn.fa,
#kurento.kurento-embedded .chat-head-chatroom a:not([href]):not([tabindex]).chatbox-btn.fa,
#kurento .chat-head-chatroom a.chatbox-btn.fa,
#kurento .chat-head-chatroom a:visited.chatbox-btn.fa,
#kurento .chat-head-chatroom a:hover.chatbox-btn.fa,
#kurento .chat-head-chatroom a:not([href]):not([tabindex]).chatbox-btn.fa {
  color: var(--chat-head-text-color);
}

#kurento.kurento-embedded .chat-head-chatroom a.chatbox-btn.fa.button-on:before,
#kurento.kurento-embedded .chat-head-chatroom a:visited.chatbox-btn.fa.button-on:before,
#kurento.kurento-embedded .chat-head-chatroom a:hover.chatbox-btn.fa.button-on:before,
#kurento.kurento-embedded .chat-head-chatroom a:not([href]):not([tabindex]).chatbox-btn.fa.button-on:before,
#kurento .chat-head-chatroom a.chatbox-btn.fa.button-on:before,
#kurento .chat-head-chatroom a:visited.chatbox-btn.fa.button-on:before,
#kurento .chat-head-chatroom a:hover.chatbox-btn.fa.button-on:before,
#kurento .chat-head-chatroom a:not([href]):not([tabindex]).chatbox-btn.fa.button-on:before {
  color: var(--chatroom-head-button-color);
}

#kurento.kurento-embedded .chat-head-chatroom .chatbox-btn.button-on:before,
#kurento .chat-head-chatroom .chatbox-btn.button-on:before {
  color: var(--chatroom-head-button-color);
}

#kurento.kurento-embedded .chat-head-chatroom .chat-title,
#kurento .chat-head-chatroom .chat-title {
  display: var(--heading-display);
  font-weight: var(--chatroom-head-title-font-weight);
  padding-right: var(--chatroom-head-title-padding-right);
}

#kurento.kurento-embedded .chat-head-chatroom .chat-title .chatroom-jid,
#kurento .chat-head-chatroom .chat-title .chatroom-jid {
  font-size: var(--font-size-small);
}

#kurento.kurento-embedded .chatroom,
#kurento .chatroom {
  width: var(--chatroom-width);
}

@media screen and (max-height: 450px) {

  #kurento.kurento-embedded .chatroom,
  #kurento .chatroom {
    width: var(--mobile-chat-width);
  }
}

@media screen and (max-width: 480px) {

  #kurento.kurento-embedded .chatroom,
  #kurento .chatroom {
    width: var(--mobile-chat-width);
  }
}

#kurento.kurento-embedded .chatroom .box-flyout,
#kurento .chatroom .box-flyout {
  overflow-y: hidden;
  background-color: var(--chatroom-head-color);
  width: 100%;
}

@media screen and (max-height: 450px) {

  #kurento.kurento-embedded .chatroom .box-flyout,
  #kurento .chatroom .box-flyout {
    height: var(--mobile-chat-height);
    width: var(--mobile-chat-width);
    height: 100vh;
  }
}

@media screen and (max-width: 480px) {

  #kurento.kurento-embedded .chatroom .box-flyout,
  #kurento .chatroom .box-flyout {
    height: var(--mobile-chat-height);
    width: var(--mobile-chat-width);
    height: 100vh;
  }
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body,
#kurento .chatroom .box-flyout .chatroom-body {
  flex-direction: row;
  flex-flow: nowrap;
  border-bottom-radius: var(--chatbox-border-radius);
  background-color: white;
  border-top: 0;
  width: 100%;
  overflow: hidden;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .row,
#kurento .chatroom .box-flyout .chatroom-body .row {
  flex-direction: row;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chat-topic,
#kurento .chatroom .box-flyout .chatroom-body .chat-topic {
  display: var(--chat-topic-display);
  font-weight: bold;
  color: var(--chatroom-head-color);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chat-info,
#kurento .chatroom .box-flyout .chatroom-body .chat-info {
  display: var(--chat-info-display);
  color: var(--separator-text-color);
  line-height: normal;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chat-info.badge,
#kurento .chatroom .box-flyout .chatroom-body .chat-info.badge {
  color: var(--chat-head-text-color);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .disconnect-container,
#kurento .chatroom .box-flyout .chatroom-body .disconnect-container {
  margin: 1em;
  width: 100%;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .disconnect-container h3.disconnect-msg,
#kurento .chatroom .box-flyout .chatroom-body .disconnect-container h3.disconnect-msg {
  padding-bottom: 1em;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chat-area,
#kurento .chatroom .box-flyout .chatroom-body .chat-area {
  display: flex;
  flex-direction: column;
  word-wrap: break-word;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chat-area .new-msgs-indicator,
#kurento .chatroom .box-flyout .chatroom-body .chat-area .new-msgs-indicator {
  background-color: var(--chatroom-head-color);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chat-area .chat-content,
#kurento .chatroom .box-flyout .chatroom-body .chat-area .chat-content {
  height: 100%;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants,
#kurento .chatroom .box-flyout .chatroom-body .occupants {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow-x: hidden;
  overflow-y: hidden;
  vertical-align: top;
  background-color: var(--occupants-background-color);
  border-left: var(--occupants-border-left);
  border-bottom-right-radius: var(--chatbox-border-radius);
  padding: 0.5em;
  max-width: var(--occupants-max-width);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants .occupants-header,
#kurento .chatroom .box-flyout .chatroom-body .occupants .occupants-header {
  display: flex;
  flex-direction: column;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants .occupants-header .hide-occupants,
#kurento .chatroom .box-flyout .chatroom-body .occupants .occupants-header .hide-occupants {
  align-self: flex-end;
  cursor: pointer;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants .occupants-heading,
#kurento .chatroom .box-flyout .chatroom-body .occupants .occupants-heading {
  font-family: var(--heading-font);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants .chatroom-features,
#kurento .chatroom .box-flyout .chatroom-body .occupants .chatroom-features {
  display: var(--occupants-features-display);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants .awesomplete ul,
#kurento .chatroom .box-flyout .chatroom-body .occupants .awesomplete ul {
  padding: 0;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants .awesomplete ul li,
#kurento .chatroom .box-flyout .chatroom-body .occupants .awesomplete ul li {
  padding: .5em;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul {
  padding: 0.2em 0 0 0;
  margin-bottom: 0.5em;
  overflow-x: hidden;
  overflow-y: auto;
  list-style: none;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul.occupant-list,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul.occupant-list {
  overflow-y: auto;
  flex-basis: 0;
  flex-grow: 1;
  border-bottom: var(--occupants-border-bottom);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li {
  cursor: default;
  display: block;
  font-size: var(--font-size-small);
  overflow: hidden;
  padding: 0.25em 0.25em 0.25em 0;
  text-overflow: ellipsis;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li .fa,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li .fa {
  margin-right: 0.5em;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.feature,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.feature {
  font-size: var(--font-size-tiny);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant {
  cursor: pointer;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge .occupant-badges,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge .occupant-badges {
  display: flex;
  justify-content: flex-end;
  flex-wrap: wrap;
  flex-direction: row;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge .occupant-badges span,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge .occupant-badges span {
  margin-right: 0.25rem;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant div.row.no-gutters,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant div.row.no-gutters {
  flex-wrap: nowrap;
  min-height: 1.5em;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .badge,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .badge {
  margin-bottom: 0.125rem;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status {
  display: inline-block;
  margin: 0 0.5em 0.125em 0;
  width: 0.5em;
  height: 0.5em;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-online,
#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-chat,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-online,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-chat {
  background-color: #1A9707;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-dnd,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-dnd {
  background-color: red;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-away,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-away {
  background-color: darkorange;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-xa,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-xa {
  background-color: orange;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-offline,
#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-status.occupant-offline {
  background-color: darkgrey;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chatroom-form-container,
#kurento .chatroom .box-flyout .chatroom-body .chatroom-form-container {
  background-color: white;
  border-bottom-left-radius: var(--chatbox-border-radius);
  border-bottom-right-radius: var(--chatbox-border-radius);
  border: 0;
  color: var(--text-color);
  font-size: var(--font-size);
  height: 100%;
  width: 100%;
  overflow-y: auto;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chatroom-form-container .validation-message,
#kurento .chatroom .box-flyout .chatroom-body .chatroom-form-container .validation-message {
  font-size: 90%;
  color: var(--error-color);
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chatroom-form-container input[type=button],
#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chatroom-form-container input[type=submit],
#kurento .chatroom .box-flyout .chatroom-body .chatroom-form-container input[type=button],
#kurento .chatroom .box-flyout .chatroom-body .chatroom-form-container input[type=submit] {
  margin: 0 0.5em;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chatroom-form-container .button-primary,
#kurento .chatroom .box-flyout .chatroom-body .chatroom-form-container .button-primary {
  background-color: var(--chatroom-head-button-color);
}

#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar,
#kurento .chatroom .sendXMPPMessage .chat-toolbar {
  background-color: white;
  border-top: var(--message-input-border-top);
  color: var(--message-input-color);
}

#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar .fas,
#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar .fas:hover,
#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar .far,
#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar .far:hover,
#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar .fa,
#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-toolbar .fa:hover,
#kurento .chatroom .sendXMPPMessage .chat-toolbar .fas,
#kurento .chatroom .sendXMPPMessage .chat-toolbar .fas:hover,
#kurento .chatroom .sendXMPPMessage .chat-toolbar .far,
#kurento .chatroom .sendXMPPMessage .chat-toolbar .far:hover,
#kurento .chatroom .sendXMPPMessage .chat-toolbar .fa,
#kurento .chatroom .sendXMPPMessage .chat-toolbar .fa:hover {
  color: var(--message-input-color);
}

#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-textarea,
#kurento .chatroom .sendXMPPMessage .chat-textarea {
  border-bottom-right-radius: 0;
}

#kurento.kurento-embedded .chatroom .sendXMPPMessage .chat-textarea.correcting,
#kurento .chatroom .sendXMPPMessage .chat-textarea.correcting {
  background-color: var(--chatroom-correcting-color);
}

#kurento.kurento-embedded .chatroom .sendXMPPMessage .send-button,
#kurento .chatroom .sendXMPPMessage .send-button {
  background-color: var(--message-input-color);
}

#kurento.kurento-embedded .chatroom .room-invite,
#kurento .chatroom .room-invite {
  /*padding-bottom: 1em;*/
}

#kurento.kurento-embedded .chatroom .room-invite .invited-contact,
#kurento .chatroom .room-invite .invited-contact {
  margin: -1px 0 0 -1px;
  width: 100%;
  border: 1px solid #999;
}

/* ******************* Overlay  styles *************************** */
#kurento.kurento-overlayed .chatbox.chatroom {
  min-width: var(--chatroom-width) !important;
  width: var(--chatroom-width);
}

#kurento.kurento-overlayed .chatbox.chatroom .chatroom-features {
  display: none !important;
}

#kurento.kurento-overlayed .chatbox.chatroom .box-flyout {
  min-width: var(--chatroom-width) !important;
  width: var(--chatroom-width);
}

#kurento.kurento-overlayed .chatbox.chatroom .chatbox-title {
  flex: 0 0 58.33333%;
  max-width: 58.33333%;
}

#kurento.kurento-overlayed .chatbox.chatroom .chatbox-title .chatroom-description {
  font-size: 80%;
}

#kurento.kurento-overlayed .chatbox.chatroom .chatbox-buttons {
  flex: 0 0 41.66667%;
  max-width: 41.66667%;
}

#kurento.kurento-overlayed .chatbox.chatroom .chatroom-body .occupants .occupants-heading {
  padding: 0;
  font-size: 85%;
  font-weight: 600;
}

#kurento.kurento-overlayed .chatbox.chatroom .chatroom-body .occupants .chatroom-features .feature {
  font-size: var(--font-size-tiny);
}

#kurento.kurento-overlayed .chatbox.chatroom .chatroom-body .occupants ul .occupant .occupant-nick-badge .occupant-badges {
  display: none;
}

#kurento.kurento-overlayed .chatbox.chatroom .chatroom-body .occupants ul .occupant .occupant-status {
  margin-top: 6px;
}

#kurento.kurento-overlayed .chatbox.chatroom .chatroom-body .chat-area {
  min-width: var(--overlayed-chat-width);
}

#kurento.kurento-overlayed .chatbox.chatroom .sendXMPPMessage .chat-toolbar li .toolbar-menu {
  min-width: 280px;
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatbox-title {
  flex: 0 0 75%;
  max-width: 75%;
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatbox-buttons {
  flex: 0 0 25%;
  max-width: 25%;
}

@media (max-width: 767.98px) {
  #kurento:not(.kurento-embedded) .chatroom {
    width: 100vw !important;
  }

  #kurento:not(.kurento-embedded) .chatroom .box-flyout .chatbox-navback {
    flex: 0 0 16.66667%;
    max-width: 16.66667%;
  }

  #kurento:not(.kurento-embedded) .chatroom .box-flyout .chatbox-title {
    flex: 0 0 58.33333%;
    max-width: 58.33333%;
  }

  #kurento:not(.kurento-embedded) .chatroom .box-flyout .chatbox-buttons {
    flex: 0 0 25%;
    max-width: 25%;
  }
}

#kurento.kurento-fullscreen .chatroom .box-flyout,
#kurento.kurento-mobile .chatroom .box-flyout {
  background-color: var(--chatroom-head-color);
  border: var(--flyout-padding) solid var(--chatroom-head-color);
  border-top: 0.8em solid var(--chatroom-head-color);
  width: 100%;
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatbox-title .chatroom-description,
#kurento.kurento-mobile .chatroom .box-flyout .chatbox-title .chatroom-description {
  font-size: 70%;
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body {
  border-top-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .chatroom-form-container,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .chatroom-form-container {
  border-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .chat-area,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .chat-area {
  border-top-left-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .chat-area .chat-content,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .chat-area .chat-content {
  border-top-left-radius: var(--chatbox-border-radius);
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .chat-area.full .new-msgs-indicator,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .chat-area.full .new-msgs-indicator {
  max-width: 100%;
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .occupants,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .occupants {
  border-top-right-radius: var(--chatbox-border-radius);
  padding: var(--occupants-padding);
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .occupants .occupants-heading,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .occupants .occupants-heading {
  font-size: var(--font-size-large);
}

#kurento.kurento-fullscreen .chatroom .box-flyout .chatroom-body .occupants ul.occupant-list li,
#kurento.kurento-mobile .chatroom .box-flyout .chatroom-body .occupants ul.occupant-list li {
  font-size: var(--font-size-small);
}

#kurento.kurento-fullscreen .chatroom .room-invite span .invited-contact,
#kurento.kurento-mobile .chatroom .room-invite span .invited-contact {
  margin: 0 0 0.5em -1px;
}

#kurento .chatbox.headlines .chat-head.chat-head-chatbox {
  background-color: var(--headline-head-color);
}

#kurento .chatbox.headlines .chat-body {
  background-color: var(--headline-head-color);
  border-radius: var(--chatbox-border-radius);
}

#kurento .chatbox.headlines .chat-body .chat-message {
  color: var(--headline-message-color);
}

#kurento .chatbox.headlines .chat-content {
  height: 100%;
}

#kurento.kurento-fullscreen .chatbox.headlines .box-flyout {
  background-color: var(--headline-head-color);
}

#kurento.kurento-fullscreen .chatbox.headlines .chat-head.chat-head-chatbox {
  background-color: var(--headline-head-color);
}

#kurento.kurento-fullscreen .chatbox.headlines .flyout {
  border: var(--flyout-padding) solid var(--headline-head-color);
  border-top: 0.8em solid var(--headline-head-color);
}

#kurento .message .mention {
  font-weight: bold;
}

#kurento .message .mention--self {
  font-weight: normal;
}

#kurento .message.date-separator {
  height: 2em;
  margin: 0;
  position: relative;
  text-align: center;
  z-index: 0;
  margin-top: 5px;
}

#kurento .message.date-separator .separator {
  border-top: 0px;
  border-bottom: var(--chat-separator-border-bottom);
  margin: 0 1em;
  position: relative;
  top: 1em;
  z-index: 5;
  border-color: #4296de;
}

#kurento .message.date-separator .separator-text {
  background: white;
  bottom: 1px;
  color: var(--separator-text-color);
  display: inline-block;
  line-height: 2em;
  padding: 0 1em;
  position: relative;
  z-index: 5;
  font-size: 8pt;
}

#kurento .message.chat-info {
  color: var(--separator-text-color);
  font-size: var(--message-font-size);
  line-height: var(--line-height-small);
  font-size: 90%;
  padding: 0.17rem 1rem;
}

#kurento .message.chat-info.badge {
  color: var(--chat-head-text-color);
}

#kurento .message.chat-info.chat-state-notification {
  font-style: italic;
}

#kurento .message.chat-info.chat-event {
  clear: left;
  font-style: italic;
}

#kurento .message.chat-info.chat-error {
  color: var(--error-color);
  font-weight: bold;
}

#kurento .message .chat-image {
  height: auto;
  width: auto;
  max-height: 15em;
  max-width: 100%;
}

#kurento .message.chat-msg--action {
  font-style: italic;
}

#kurento .message.chat-msg {
  display: inline-flex;
  width: 100%;
  flex-direction: row;
  overflow: hidden;
  padding: .25rem 0rem .25rem 1rem;
}

#kurento .message.chat-msg.onload {
  animation: colorchange-chatmessage 1s;
  -webkit-animation: colorchange-chatmessage 1s;
}

/* #kurento .message.chat-msg:hover {
    background-color: rgba(0, 0, 0, 0.035); }*/
#kurento .message.chat-msg:hover .chat-msg__actions .chat-msg__action {
  opacity: 1;
}

#kurento .message.chat-msg.correcting.groupchat {
  background-color: var(--chatroom-correcting-color);
}

#kurento .message.chat-msg.correcting:not(.groupchat) {
  background-color: var(--chat-correcting-color);
}

#kurento .message.chat-msg .spoiler {
  margin-top: 0.5em;
}

#kurento .message.chat-msg .spoiler-hint {
  margin-bottom: 0.5em;
}

#kurento .message.chat-msg .spoiler-toggle {
  color: white;
}

#kurento .message.chat-msg .spoiler-toggle i {
  color: white;
  padding-right: 0.5em;
}

#kurento .message.chat-msg .spoiler-toggle:before {
  padding-right: 0.25em;
  whitespace: nowrap;
}

#kurento .message.chat-msg .chat-msg__content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  margin-left: 2.75rem;
  width: calc(100% - var(--message-avatar-width));
  overflow: hidden;
  border-radius: 1em;
  background-color: aliceblue;
  margin-right: -2em
}

#kurento .message.chat-msg .chat-msg__content--action {
  margin-left: 2.75rem;
}

#kurento .message.chat-msg .chat-msg__body {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  /*width: 100%;*/
  padding: 0;
  margin-left: .5em;
  margin-top: .9em;
  margin-bottom: .5em;
}

#kurento .message.chat-msg .chat-msg__message {
  display: inline-flex;
  flex-direction: column;
  width: 100%;
  overflow-wrap: break-word;
}

#kurento .message.chat-msg .chat-msg__edit-modal {
  cursor: pointer;
  padding-right: 0.5em;
}

#kurento .message.chat-msg.headline .chat-msg__body {
  margin-left: 0;
}

#kurento .message.chat-msg .chat-msg__subject {
  font-weight: bold;
  clear: right;
}

#kurento .message.chat-msg .chat-msg__text {
  padding: 0;
  color: var(--message-text-color);
  width: 100%;
  white-space: pre-wrap;
  padding-left: 1em;
  word-wrap: break-word;
  padding-right: .5em;
}

#kurento .message.chat-msg .chat-msg__text a {
  word-wrap: break-word;
  word-break: break-all;
  display: inline-block;
}

#kurento .message.chat-msg .chat-msg__text img.emoji {
  height: 1.5em;
  width: 1.5em;
  margin: 0 .05em 0 .1em;
  vertical-align: -0.1em;
}

#kurento .message.chat-msg .chat-msg__text .emojione {
  margin-bottom: -6px;
}

#kurento .message.chat-msg .chat-msg__media {
  margin-top: 0.25rem;
  word-break: break-all;
}

#kurento .message.chat-msg .chat-msg__media a {
  word-wrap: break-word;
}

#kurento .message.chat-msg .chat-msg__media audio {
  width: 100%;
}

#kurento .message.chat-msg .chat-msg__actions .chat-msg__action {
  height: var(--message-font-size);
  font-size: var(--message-font-size);
  padding: 0;
  border: none;
  opacity: 0;
  background: transparent;
  cursor: pointer;
}

#kurento .message.chat-msg .chat-msg__actions .chat-msg__action:focus {
  display: block;
}

#kurento .message.chat-msg .chat-msg__avatar {
  margin-top: 0.5em;
  vertical-align: middle;
  height: var(--message-avatar-height);
  width: 0;
  min-height: var(--message-avatar-height);
  min-width: 0;
}

#kurento .message.chat-msg .chat-msg__heading {
  width: 100%;
  margin-top: 0.5em;
  padding-right: 0.25rem;
  padding-bottom: 0;
  display: none;
}

#kurento .message.chat-msg .chat-msg__heading .chat-msg__author {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: var(--heading-font);
  font-size: 115%;
  font-weight: 400;
}

#kurento .message.chat-msg .chat-msg__heading .chat-msg__author .badge {
  font-size: 80%;
  font-family: var(--normal_font);
}

#kurento .message.chat-msg .chat-msg__heading .chat-msg__time {
  padding-left: 0.25em;
  padding-right: 0.25em;
  color: var(--text-color-lighten-15-percent);
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__content {
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__text {
  width: auto;
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__heading {
  margin-top: 0;
  padding-bottom: 0;
  width: auto;
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__heading .fa {
  margin-left: 0.5em;
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__author {
  font-size: var(--message-font-size);
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__time {
  margin-left: 0;
}

/* #kurento .message.chat-msg.chat-msg--followup .chat-msg__heading,
  #kurento .message.chat-msg.chat-msg--followup .chat-msg__avatar {
    display: none; }*/
#kurento .message.chat-msg.chat-msg--followup .chat-msg__content {
  margin-left: 2.75rem;
}

#kurento .message.chat-msg .chat-msg__receipt {
  margin-right: 0.5em;
  color: var(--message-receipt-color);
  display: none;
}

#kurento .chatroom-body .message.onload {
  animation: colorchange-chatmessage-muc 1s;
  -webkit-animation: colorchange-chatmessage-muc 1s;
}

#kurento .chatroom-body .message .separator {
  border-top: 0px;
  border-bottom: var(--chatroom-separator-border-bottom);
}

#kurento.kurento-overlayed .message.chat-msg.chat-msg--followup .chat-msg__content {
  margin-left: 2.75rem;
}

@media screen and (max-width: 767px) {
  #kurento:not(.kurento-embedded) .message.chat-msg .chat-msg__author {
    white-space: normal;
  }
}

#kurento.kurento-overlayed #minimized-chats {
  order: 100;
  width: var(--minimized-chats-width);
  margin-bottom: 0;
  border-top-left-radius: var(--chatbox-border-radius);
  border-top-right-radius: var(--chatbox-border-radius);
  color: var(--inverse-link-color);
  margin-right: var(--chat-gutter);
  padding: 0;
}

#kurento.kurento-overlayed #minimized-chats .badge {
  bottom: 8px;
  border: 1px solid var(--overlayed-badge-color);
}

#kurento.kurento-overlayed #minimized-chats #toggle-minimized-chats {
  border-top-left-radius: var(--chatbox-border-radius);
  border-top-right-radius: var(--chatbox-border-radius);
  background-color: var(--link-color);
  padding: 1em 0 0 0;
  text-align: center;
  color: white;
  white-space: nowrap;
  overflow-y: hidden;
  text-overflow: ellipsis;
  display: block;
  height: 45px;
}

#kurento.kurento-overlayed #minimized-chats a.restore-chat {
  padding: 1px 0 1px 5px;
  color: var(--chat-head-text-color);
  line-height: 15px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#kurento.kurento-overlayed #minimized-chats a.restore-chat:hover {
  text-decoration: none;
}

#kurento.kurento-overlayed #minimized-chats a.restore-chat:visited {
  color: var(--chat-head-text-color);
}

#kurento.kurento-overlayed #minimized-chats .minimized-chats-flyout {
  flex-direction: column-reverse;
  bottom: 0;
  width: var(--minimized-chats-width);
}

#kurento.kurento-overlayed #minimized-chats .minimized-chats-flyout .chat-head {
  padding: 0.3em;
  border-radius: var(--chatbox-border-radius);
  height: 5vh;
  margin-bottom: 0;
  box-shadow: 1px 3px 5px 3px rgba(0, 0, 0, 0.4);
  width: 100%;
}

#kurento.kurento-overlayed #minimized-chats .minimized-chats-flyout.minimized {
  height: auto;
}

#kurento.kurento-overlayed #minimized-chats .unread-message-count {
  font-weight: bold;
  background-color: white;
  border: 1px solid;
  text-shadow: 1px 1px 0 var(--text-shadow-color);
  color: var(--warning-color);
  border-radius: 5px;
  padding: 2px 4px;
  font-size: 16px;
  text-align: center;
  position: absolute;
  right: 116px;
  bottom: 10px;
}

#kurento.kurento-overlayed #minimized-chats .unread-message-count-hidden,
#kurento.kurento-overlayed #minimized-chats .chat-head-message-count-hidden {
  display: none;
}

#kurento #controlbox .bookmarks-toggle,
#kurento #controlbox .bookmarks-toggle .fa {
  color: var(--list-toggle-color) !important;
}

#kurento #controlbox .bookmarks-toggle:hover,
#kurento #controlbox .bookmarks-toggle .fa:hover {
  color: var(--list-toggle-color) !important;
}

#kurento.fullscreen #controlbox #chatrooms .bookmarks-list dl.rooms-list.bookmarks dd.available-chatroom a.open-room {
  width: 80%;
}

#kurento [hidden] {
  display: none;
}

#kurento .visually-hidden {
  position: absolute;
  clip: rect(0, 0, 0, 0);
}

#kurento .form-group .suggestion-box,
#kurento .form-group .awesomplete {
  width: 100%;
}

#kurento .suggestion-box,
#kurento .awesomplete {
  position: relative;
}

#kurento .suggestion-box mark,
#kurento .awesomplete mark {
  background: var(--completion-light-color);
}

#kurento .suggestion-box>input,
#kurento .awesomplete>input {
  display: block;
}

#kurento .suggestion-box .suggestion-box__results,
#kurento .suggestion-box>ul,
#kurento .awesomplete .suggestion-box__results,
#kurento .awesomplete>ul {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 2;
  min-width: 100%;
  box-sizing: border-box;
  list-style: none;
  padding: 0;
  border-radius: .3em;
  margin: .2em 0 0;
  background: rgba(255, 255, 255, 0.9);
  background: linear-gradient(to bottom right, white, rgba(255, 255, 255, 0.9));
  border: 1px solid rgba(0, 0, 0, 0.3);
  box-shadow: 0.05em 0.2em 0.6em rgba(0, 0, 0, 0.1);
  text-shadow: none;
}

#kurento .suggestion-box .suggestion-box__results:before,
#kurento .suggestion-box>ul:before,
#kurento .awesomplete .suggestion-box__results:before,
#kurento .awesomplete>ul:before {
  content: "";
  position: absolute;
  top: -.43em;
  left: 1em;
  width: 0;
  height: 0;
  padding: .4em;
  background: white;
  border: inherit;
  border-right: 0;
  border-bottom: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  z-index: 1;
}

#kurento .suggestion-box .suggestion-box__results>li,
#kurento .suggestion-box>ul>li,
#kurento .awesomplete .suggestion-box__results>li,
#kurento .awesomplete>ul>li {
  text-overflow: ellipsis;
  overflow-x: hidden;
  position: relative;
  cursor: pointer;
  padding: 1em;
}

#kurento .suggestion-box .suggestion-box__results--above,
#kurento .awesomplete .suggestion-box__results--above {
  bottom: 4.5em;
}

#kurento .suggestion-box .suggestion-box__results--above:before,
#kurento .awesomplete .suggestion-box__results--above:before {
  display: none;
}

#kurento .suggestion-box .suggestion-box__results--above:after,
#kurento .awesomplete .suggestion-box__results--above:after {
  z-index: 1;
  content: "";
  position: absolute;
  bottom: -.43em;
  left: 1em;
  width: 0;
  height: 0;
  padding: .4em;
  background: white;
  border: inherit;
  border-left: 0;
  border-top: 0;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

#kurento .suggestion-box>ul[hidden],
#kurento .suggestion-box>ul:empty,
#kurento div.awesomplete>ul[hidden],
#kurento div.awesomplete>ul:empty {
  display: none;
}

@supports (transform: scale(0)) {

  #kurento .suggestion-box>ul,
  #kurento div.awesomplete>ul {
    transition: 0.3s cubic-bezier(0.4, 0.2, 0.5, 1.4);
    transform-origin: 1.43em -.43em;
  }

  #kurento .suggestion-box>ul[hidden],
  #kurento .suggestion-box>ul:empty,
  #kurento div.awesomplete>ul[hidden],
  #kurento div.awesomplete>ul:empty {
    opacity: 0;
    transform: scale(0);
    display: block;
    transition-timing-function: ease;
  }
}

#kurento .suggestion-box>ul>li[aria-selected="true"],
#kurento div.awesomplete>ul>li[aria-selected="true"] {
  background: var(--completion-dark-color);
  color: var(--inverse-link-color);
}

#kurento .suggestion-box li:hover mark,
#kurento div.awesomplete li:hover mark {
  background: var(--completion-light-color);
  color: var(--inverse-link-color);
}

#kurento .suggestion-box li[aria-selected="true"] mark,
#kurento div.awesomplete li[aria-selected="true"] mark {
  background: var(--completion-normal-color);
  color: inherit;
}

#kurento.kurento-fullscreen .suggestion-box__results--above {
  bottom: 4.5em;
}

#kurento.kurento-overlayed .suggestion-box__results--above {
  bottom: 5.5em;
}

#kurento.kurento-embedded {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  bottom: auto;
  height: 100%;
  position: relative;
  right: auto;
  width: 100%;
}

#kurento.kurento-embedded *,
#kurento.kurento-embedded *:before,
#kurento.kurento-embedded *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#kurento.kurento-embedded .kurento-chatboxes {
  z-index: 5;
  position: inherit;
  bottom: auto;
  height: 100%;
  width: 100%;
}

#kurento.kurento-embedded .chatbox {
  margin: 0;
  height: 100%;
  width: 100%;
}

#kurento.kurento-embedded .chatbox .flyout.box-flyout {
  bottom: 0;
  box-shadow: none;
  height: 100%;
  min-width: auto;
  width: 100%;
}

#kurento.kurento-embedded .chatbox .chat-title {
  padding: 0.3em;
  font-size: 120%;
}

#kurento.kurento-embedded .chatbox-btn {
  display: none;
}

#kurento.kurento-embedded .chatroom {
  margin: 0;
  width: 100%;
}

#kurento.kurento-embedded .chatroom .box-flyout .occupants-heading {
  font-size: 120%;
}

#kurento.kurento-embedded .chatroom .box-flyout .chat-content .chat-message {
  margin: 0.5em;
  font-size: 120%;
}

#kurento.kurento-embedded .chatroom .box-flyout .sendXMPPMessage .chat-textarea {
  padding: 0.5em;
  font-size: 110%;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body {
  height: 100%;
}

#kurento.kurento-embedded .chatroom .box-flyout .chatroom-body .chatroom-form-container {
  height: 100%;
  position: relative;
}

#kurento.kurento-embedded .chatroom .box-flyout .occupants .occupant-list {
  padding-left: 0.3em;
}

#kurento.kurento-embedded .chatroom .box-flyout .occupants .occupant-list li.occupant {
  font-size: 120%;
}

a.controlbox-heading__btn.show-client-info.fa.fa-info-circle.align-self-center {
  display: none;
}

#kurento,
#kurento input,
#kurento input[type=button],
#kurento input[type=password],
#kurento input[type=submit],
#kurento input[type=text],
#kurento textarea {
  -webkit-user-select: auto;
  -khtml-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/*image {
      width: 0;
      height: 0;
  }*/

#kurento #kurento-modals #user-profile-modal .profile-form label.col-form-label[for="vcard-nickname"] {
  display: none;
}

#kurento form.kurento-form input[type=button],
#kurento form.kurento-form input[type=number],
#kurento form.kurento-form input[type=password],
#kurento form.kurento-form input#vcard-nickname {
  display: none;
}

#kurento #kurento-modals #user-profile-modal .profile-form label.col-form-label[for="vcard-url"] {
  display: none;
}

.filter-by.d-flex.flex-nowrap span.fa.fa-circle {
  display: none;
}

a.chatbox-btn.show-user-details-modal.fa.fa-id-card {
  display: none;
}

#kurento,
.kurento-content {
  --chat-status-offline: lightgrey;
  --minimized-chats-width: 10em;
  --controlbox-width: 15vw;
}

span.fa.fa-circle {
  -webkit-transform: scale(1.3, 1.3);
  -ms-transform: scale(1.3, 1.3);
  transform: scale(0.8, 0.8);
}

/*#kurento form.kurento-form input#vcard-email {
    pointer-events: none;
    background-color: gainsboro;
  }*/

div#profile-tabpanel form.kurento-form.kurento-form--modal.profile-form .row {
  display: none;
}

#kurento #kurento-modals #user-profile-modal .profile-form label.col-form-label[for="vcard-url"] {
  display: none;
}

#kurento form.kurento-form input[type=button],
#kurento form.kurento-form input[type=number],
#kurento form.kurento-form input[type=password],
#kurento form.kurento-form input#vcard-url {
  display: none;
}

#kurento #kurento-modals #user-profile-modal .profile-form label.col-form-label[for="vcard-role"] {
  display: none;
}

#kurento form.kurento-form input[type=button],
#kurento form.kurento-form input[type=number],
#kurento form.kurento-form input[type=password],
#kurento form.kurento-form input#vcard-role {
  display: none;
}

#kurento #kurento-modals #user-profile-modal .profile-form label.col-form-label:not([for]) {
  display: none;
}

/*svg.avatar.align-self-center {
    background-image: url(app/ext/ribbon/images/settings.png);
    background-repeat: no-repeat;
    background-position: center;
    width: 1.8em;
    height: 1.8em;
    border: 0;
    margin-left: -8px;
  }*/

#kurento form.kurento-form.add-chatroom input[name="nickname"],
#kurento form.kurento-form.add-chatroom label[for="nickname"] {
  display: none;
}

#kurento .kurento-form.list-chatrooms .form-group input.form-control[type=text] {
  pointer-events: none;
  background-color: gainsboro;
  max-width: 15vw;
}

#kurento .modal-body .kurento-form.add-chatroom .form-group input.form-control {
  max-width: 15vw;
  min-width: 460px;
}

#kurento .message.chat-msg .chat-msg__content.sender {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
  margin-left: -2em;
  width: calc(100% - var(--message-avatar-width));
  overflow: hidden;
  border-radius: 1em;
  background-color: rgb(248, 248, 248);
  color: #0000008a
}

#kurento.kurento-overlayed .message.chat-msg.chat-msg--followup .chat-msg__content.sender {
  margin-left: -2em;
}

#kurento .items-list .list-item.unread-msgs {
  font-weight: bold;
}

#kurento #controlbox .controlbox-panes::-webkit-scrollbar {
  width: 0px;
}

#kurento.kurento-overlayed #controlbox .chat-head.controlbox-head a.chatbox-btn.close-chatbox-button.fa.fa-times {
  /*margin-top: -0.7vh;*/
}

#kurento form.kurento-form.list-chatrooms {
  display: none;
}

#kurento .message.chat-msg .chat-msg__heading.chatroom {
  width: 100%;
  margin-top: 0.5em;
  padding-right: 0.25rem;
  padding-bottom: 0;
  margin-left: 1em;
  display: flex;
}

#kurento .message.chat-msg .chat-msg__heading.chatroom .chat-msg__author {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: var(--heading-font);
  font-size: 105%;
  font-weight: 400;
  padding-left: .5em;
}

#kurento .message.chat-msg .chat-msg__heading.chatroom .chat-msg__author .badge {
  font-size: 80%;
  font-family: var(--normal_font);
}

#kurento .message.chat-msg .chat-msg__heading.chatroom .chat-msg__time {
  padding-left: 0.25em;
  color: var(--text-color-lighten-15-percent);
}

#kurento .message.chat-msg.chat-msg--action .chat-msg__heading.chatroom {
  margin-top: 0;
  padding-bottom: 0;
  width: auto;
  display: flex;
}

#kurento .chatroom .box-flyout .chatroom-body .occupants ul li.occupant .occupant-nick-badge span.occupant-nick {
  overflow: hidden;
  text-overflow: ellipsis;
}

#kurento .items-list .list-item a.list-item-action.fa.fa-times.close-room {
  /*margin: 0 .5em 0 .5em;*/
  padding-left: .5em;
}

#kurento .items-list .list-item a.list-item-action.fa.fa-bookmark.remove-bookmark.button-on {
  padding-left: .15em;
}