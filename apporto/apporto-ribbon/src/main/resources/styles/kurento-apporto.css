/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#kurento .fa:hover,
#kurento .far:hover,
#kurento .fas:hover {
  color: var(--text-color) !important;
}

#kurento .chat-head.controlbox-head:hover {
  cursor: grab;
}

#kurento .chat-head.controlbox-head:active {
  cursor: grabbing;
}

#kurento #controlbox {
  margin-right: 0px !important;
}

#kurento .chatbox {
  min-width: 240px !important;
}
#kurento h1.username {
  color: #222124 !important;
  font-size: 24px !important;
  font-weight: bold !important;
  margin: auto;
}

#kurento a {
  cursor: pointer;
}

#kurento #kurento-roster .roster-contacts .roster-group .group-toggle {
  font-size: 18px !important;
  padding: 0 10px !important;
  color: var(--chat-head-color-dark)!important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 25px !important
}

#kurento #controlbox .controlbox-pane .controlbox-padded {
  padding: 25px 15px !important;
  height: 36px!important;
}

#kurento #kurento-roster .roster-contacts .roster-group li .open-chat .contact-name {
  font-size: 18px !important;
}

#kurento #kurento-roster .roster-contacts .roster-group li.current-xmpp-contact span {
  margin-right: 0 !important;
}

#kurento .kurento-chatboxes {
  z-index: 5;
  position: fixed;
  bottom: 45px !important;
  right: 0 !important;
  height: 40px !important;
}

#kurento .toggle-controlbox {
  background-color: #fafafd !important;
  border-radius: 0 !important;
  -moz-box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  -webkit-box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  
}

#kurento .toggle-controlbox span {
  color: #222124 !important;
}

#kurento .scroll {
  color: rgba(0, 0, 0, 0);
  transition: color 0.5s cubic-bezier(0.65, 0.05, 0.36, 1) !important;
}

#kurento .scroll:hover {
  color: rgba(0, 0, 0, 0.3);
}

#kurento .scroll::-webkit-scrollbar {
  width: 8px;
  height: 0;
  border: 10px solid transparent;
}

#kurento .scroll::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 10px;
  border-radius: 10px;
}

#kurento .scroll::-webkit-scrollbar-thumb:hover {
  color: rgba(0, 0, 0, 0.45);
}
