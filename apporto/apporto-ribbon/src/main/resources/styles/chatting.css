/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */



.chatting-dialog {
  width: 348px;
  height: 380px;
  background: transparent;
  opacity: 1 !important;
  visibility: visible !important;
  position: fixed;
  left: unset;
  top: unset;
  right: 10px;
  bottom: 7px;
  z-index: 100;
}

#chatting-dialog .effect {
  background: #ffffff;
  -moz-box-shadow: 0.1em 0.1em 0.2em #00000099;
  -webkit-box-shadow: 0.1em 0.1em 0.2em #00000099;
  box-shadow: 0.1em 0.1em 0.2em #00000099;
}

#chatting-dialog h1 {
  margin: 0;
  padding: 0 10px;
  white-space: nowrap;
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

#chatting-dialog .chatting {
  height: 100%;
  float: right;
  background: #ffffff;
  width: 100%;
}

#chatting-dialog .member-list {
  min-height: 300px;
  max-height: 300px;
  width: 348px;
  padding: 10px;
  overflow-y: auto;
  overflow-x: hidden;
}

#chatting-dialog ul {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin: 0;
  list-style: none;
}

#chatting-dialog li {
  width: 100%;
  cursor: pointer;
  position: relative;
}

#chatting-dialog li .online:before {
  content: " ";
  position: absolute;
  height: 10px;
  width: 10px;
  top: calc(50% - 5px);
  background: #05964b;
  border-radius: 50%;
}

#chatting-dialog li .offline:before {
  content: " ";
  position: absolute;
  height: 10px;
  width: 10px;
  top: calc(50% - 5px);
  background: #a0a0a0;
  border-radius: 50%;
}

#chatting-dialog li:hover {
  background: #c7edfc80;
}

#chatting-dialog .member-name {
  display: inline-block;
  font-size: 20px;
  margin: 5px 20px;
  white-space: nowrap;
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
}

#chatting-dialog .context-menu {
  color: #000000;
  cursor: pointer;
  float: right;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
}

#chatting-dialog button.context-menu {
  background-color: transparent;
  padding: 0px;
  min-width: 25px;
  box-shadow: none;
}

#chatting-dialog button.context-menu:hover:enabled {
  background-color: transparent;
  box-shadow: none;
}

#chatting-dialog button.context-menu:active {
  background-color: transparent;
  box-shadow: none;
}

#chatting-dialog .context-menu.icon {
  background-size: contain;
  width: 16px;
  height: 16px;
  background-size: 24px !important;
  margin-left: 5px;
}

#chatting-dialog .context-menu.icon.minus {
  background-image: url(app/ext/ribbon/images/chats/minimize.svg);
}

#chatting-dialog .context-menu.icon.times {
  background-image: url(app/ext/ribbon/images/chats/close.svg);
}

#chatting-dialog .context-menu.icon.edit {
  background-image: url(app/ext/ribbon/images/chats/arrow_drop_down.svg);
  width: 24px;
  height: 24px;
  position: relative;
  top: -6px;
}

#chatting-dialog .dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
          background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
          box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
}
#chatting-dialog .dropdown-menu > li > a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}
#chatting-dialog .dropdown-menu > li > a:hover,
#chatting-dialog .dropdown-menu > li > a:focus {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5;
}

#chatting-dialog .avatar {
  position: relative;
  display: inline-block;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: visible;
  align-items: stretch;
  justify-content: center;
  border: none !important;
  border-radius: 18px !important;
  width: 30px;
  height: 30px;
}

#chatting-dialog .avatar .avatar-text {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: hidden;
  align-items: center;
  background: linear-gradient(90deg,#858994 0,#a8adb6 74%);
  background: -moz-linear-gradient(90deg,#858994 0,#a8adb6 74%);
  background: -webkit-linear-gradient(90deg,#858994 0,#a8adb6 74%);
  width: 36px;
  height: 36px;
  border-radius: 18px;
  justify-content: center;
  border-radius: 6px !important;
  background: #757575;
  width: 100%;
  height: 100%;
}

#chatting-dialog .avatar .avatar-text .avatar-short-text {
  position: relative;
  display: inline;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: hidden;
  white-space: pre;
  text-overflow: ellipsis;
  font-size: 16px;
  color: white;
  font-weight: 600;
  background-color: rgba(0,0,0,0);
  line-height: 20px;
  cursor: inherit;
}

#chatting-dialog .avatar span {
  position: absolute;
  bottom: -4px;
  right: -5px;
  font-size: 12px;
  border-radius: 50%;
}

#chatting-dialog .user-state {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: auto;
}

#status_change_btn {
  position: relative;
}

#status_change_btn .change-status-btn-tooltip {
  margin-left: -60px !important;
  bottom: 125%;
  left: 50%;
  width: 120px;
}

#status_change_btn:hover .change-status-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#status_change_btn:focus .change-status-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

.me_user span {
  border: 3px solid #156CD5 !important;
  bottom: -6px !important;
  right: -6px !important;
}

.other_users span {
  border: 3px solid white !important;
  bottom: -6px !important;
  right: -6px !important;
}

.chat-tooltip {
  font-size: 14px !important;
  font-weight: 400 !important;
  visibility: hidden;
  background-color: #323c47 !important;
  border: 1px solid #ccc !important;
  color: #fff !important;
  text-align: center !important;
  z-index: 3;
  position: absolute;
  opacity: 0;
  transition: opacity 0.3s;
  padding: 5px !important;
  border-radius: 4px;
}

.chat-tooltip::after {
  content: '';
  position: absolute;
  top: 100%; /* Bottom of the tooltip */
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #323c47 transparent transparent transparent; /* Arrow pointing down */
}

#show_status{
  display: flex;
  flex-direction: row-reverse;
  position: relative;
  width: max-content;
  align-items: flex-start;
}

#userInfo_userName{
  font-weight: bold !important;
  font-size: 16px !important;
  color: white !important;
}

#status_drop_down{
  display: flex !important;
  flex-direction: column;
  background: transparent !important;
}

#chat_close_btn{
  padding-top: 30px !important;
  position: relative;
}

#chat_close_btn .close-chat-btn-tooltip {
  margin-left: -25px !important;
  bottom: 125%;
  left: 50%;
}

#chat_close_btn:hover .close-chat-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#chat_close_btn:focus .close-chat-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#chat_minimized_btn{
  background-size: 21px !important;
  padding-top: 30px !important;
  position: relative;
}

#chatting-dialog .minimize-btn-tooltip {
  margin-left:-35px !important;
  bottom: 125%;
  left: 50%;
}

#chat_minimized_btn:hover .minimize-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

#chat_minimized_btn:focus .minimize-btn-tooltip {
  visibility: visible;
  opacity: 1;
}

.line_gap_div{
  width: 92%;
  height: 1px;
  background: #E2E2E2;
  margin: 9px 16px 0px 16px !important;
}

.unread_mez_count_num{
  background: #156cd5;
  color: white;
  padding: 0px 8px !important;
  position: absolute;
  right: 8px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 24px;
}

#chatting-dialog .user-state p {
  color: #fff;
  padding: 0px 12px;
  font-size: 16px;
  line-height: 1;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#chatting-dialog .username {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 1;
  overflow: hidden;
  align-self: stretch;
  justify-content: center;
  margin-left: 10px;
  width: max-content;
}

#chatting-dialog .username p {
  overflow: hidden;
  white-space: pre;
  text-overflow: ellipsis;
  font-size: 16px;
  margin-bottom: 0px;
  color: #222124;
  font-weight: 400;
  line-height: 14px;
  padding-inline: 0px !important;
  width: max-content !important;
  max-width: 185px !important;
}

#chatting-dialog .username p.unread-msgs {
  font-weight: bold;
}

#chatting-dialog .dropdown {
  position: relative;
  display: inline-block;
}

#chatting-dialog .dropdown-content {
  display: none;
  position: absolute;
  background-color: white;
  min-width: 120px;
  max-width: 120px;
  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
  left: 120px;
  top: 30px;
  border-radius: 4px;
}

#chatting-dialog .dropdown-content a {
  color: #222124 !important;
  padding: 5px 10px;
  text-decoration: none;
  display: block;
  width: 120px !important;
  border-radius: 4px;
}

#chatting-dialog .dropdown-content a:hover {
  background-color: #ddd
}

#chatting-dialog .show {
  display: block;
}

.call-dialog {
  width: 250px !important;
  height: 120px !important;
  left: calc(50% - 125px);
  top: calc(50vh - 250px);
  background: transparent !important;
  opacity: 1 !important;
  visibility: visible !important;
  position: fixed;
  z-index: 100;
}

.call-dialog:hover {
  cursor: grab;
}

.call-dialog:active {
  cursor: grabbing;
}

#call-dialog {
  position: fixed;
  right: 10px;
  top: 40px;
  background: #fafafd;
  display: flex;
  text-align: left;
  width: 360px;
  align-items: center;
  justify-content: center;
  -moz-box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  -webkit-box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
}

#call-dialog .username {
  padding: 10px 12px;
  font-size: 30px;
  width: 250px;
  line-height: 1;
  vertical-align: middle;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 245px;
  min-width: 245px;
}

#call-dialog button {
  width: 50px;
  margin: 5px;
  padding: 5px;
  min-width: unset;
  font-size: 27px;
  vertical-align: middle;
  color: white;
  cursor: pointer;
}

#call-dialog button.call {
  background: #2CC314;
}

#call-dialog button.call.disabled {
  background: #2cc31466;
  cursor: auto;
}

#call-dialog button.terminate {
  background: #cccccc;
}

#call-confirm-dialog {
  background: #fafafd;
  max-width: 250px;
  min-width: 250px;
  -moz-box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  -webkit-box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  box-shadow: 0.1em 0.1em 0.2em rgba(0,0,0,0.6);
  border-radius: 4px;
}

#call-confirm-dialog .username {
  padding: 10px 12px;
  font-size: 24px;
  width: 250px;
  line-height: 1;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 250px;
  min-width: 250px;
  text-align: center;
  color: var(--text-color) !important;
}

#call-confirm-dialog .info {
  padding: 3px 12px;
  font-size: 20px;
  line-height: 1;
  display: table-cell;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 250px;
  min-width: 250px;
  text-align: center;
  display: block;
  color: var(--text-color) !important;
}

#call-confirm-dialog .call-btn-group {
  justify-content: center;
  display: flex;
}

#call-confirm-dialog button {
  width: 100px;
  height: 36px;
  min-width: unset;
  font-size: 22px;
  padding: 0;
  vertical-align: middle;
  text-align: center;
  color: white;
  cursor: pointer;
  border-radius: 4px;
}

#call-confirm-dialog button.call {
  background: #64A2DF;
}

#call-confirm-dialog button.call.disabled {
  background: #2cc31466;
  cursor: auto;
}

#call-confirm-dialog button.terminate {
  background: #cccccc;
}

.fa.fa-phone.call {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}

.fa.fa-phone.terminate {
  -webkit-transform: rotate(225deg);
  -moz-transform: rotate(225deg);
  -ms-transform: rotate(225deg);
  -o-transform: rotate(225deg);
  transform: rotate(225deg);
}