/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.instruction-dialog {
    width: 40vw;
    overflow: visible;
    border-radius: 6px;
    position: relative;
}

.instruction-dialog::before {
    content: "";
    background: linear-gradient(to right,#00dcff,#00f);
    position: absolute;
    height: 5px;
    top: -5px;
    left: 4px;
    right: 4px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

#dlg-upload-instruction>* {
    margin: initial;
}

/* The Close Button */
.instruction-dialog span.close {
    color: #808080;
    cursor: default;
    float: right;
    font-weight: bold;
    font-size: 28px;
    line-height: 25px;
    position: absolute;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
}

.instruction-dialog #content {
    display: -ms-flexbox;
    -ms-flex-align: stretch;
    -ms-flex-direction: column;
    display: -moz-box;
    -moz-box-align: stretch;
    -moz-box-orient: vertical;
    display: -webkit-box;
    -webkit-box-align: stretch;
    -webkit-box-orient: vertical;
    display: -webkit-flex;
    -webkit-align-items: stretch;
    -webkit-flex-direction: column;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}

.title-instruction {
	margin-block-end: 0px;
}

.label-instruction {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-align: center;
  padding-left: 10%;
  padding-right: 10%;
  white-space: pre-wrap;
  font-size: 1.1em;
}

div.footer {
    padding: 10px;
    padding-top: 20px;
}

.dialog .footer {
    text-align: right;
}

.viewOnly.instruction {
	width: 8em;
    height: 2em;
    border-radius: 6px;
}

input[type="submit"], button.instruction, a.button {
    background-color: rgba(74, 144, 226, 1);
    color: white;
    border: 0;
    height: 2em;
    text-shadow: none;
}

input[type="submit"]:hover:enabled, button.instruction:hover:enabled, a.button:hover:enabled {
    background-color: rgba(74, 144, 226, 0.8);
}

input[type="submit"]:disabled, button.instruction:disabled, button.danger:disabled {
    background-color: rgba(74, 144, 226, 1);
}