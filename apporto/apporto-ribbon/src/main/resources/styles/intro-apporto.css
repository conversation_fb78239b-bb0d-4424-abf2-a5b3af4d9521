/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
 
.introjs-helperLayer {
    background-color: rgba(255,255,255,.5)!important;
}

div#dlg-share.introjs-fixParent {
    z-index: 10!important;
}

summary-chart-dialog.introjs-fixParent {
    z-index: 10!important;
}

file-explorer.shown.status-outer.introjs-fixParent {
    z-index: 10!important;
}
share-dialog.dialog-outer.ng-isolate-scope.introjs-fixParent.shown {
	z-index: 10!important;
}

.introjs-tooltipbuttons #later {
    margin-right: 5px;
    padding: .3em .8em;
    min-width: auto;
    box-shadow: none;
    border-radius: .2em;
    color: #000;
}

#conversejs.converse-overlayed.introjs-fixParent {
    z-index: 10!important;
}

