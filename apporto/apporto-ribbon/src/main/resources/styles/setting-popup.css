/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.setting-popup {
    width: 0;
    height: 0;
    display: inline-block;
}

.setting-popup-content {
    position: absolute;
    background: white;
    border-radius: 0px 0px 5px 5px;
    box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.6);
    left: unset;
    right: 0;
    top: 10px;
    visibility: visible;
    opacity: 1;
    border-radius: 10px;
    border: 0px;
}

.setting-popup-content::-webkit-scrollbar {
    width: 10px;
}

.setting-popup-content::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 10px;
}

.setting-popup-content::-webkit-scrollbar-thumb:hover {
    background-color: #aaa;
}

.setting-popup-content::-webkit-scrollbar-track {
    background-color: #f3f3f3;
    border-radius: 10px;
}

.setting-popup .header h2 {
    font-size: 1.2em;
    text-transform: none;
}

.setting-popup .header {
    -ms-flex-align:      left;
    -moz-box-align:      left;
    -webkit-box-align:   left;
    -webkit-align-items: left;
    align-items:         left;
    margin-bottom: 0;
}

.setting-popup ul {
    overflow: visible;
    width: auto;
    margin: 0;
    padding: 0;
    box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.14);
    padding: 10px;
    min-width: 232px;
}

.setting-popup li {
    display: block;
    height: 40px;
    width: 100%;
    overflow: visible;
    margin: 0;
    padding: 0;
    display: block;
    text-overflow: ellipsis;
    border-bottom: 1px solid lightgrey;
}

.setting-popup .setting-menu {
    border: 0px;
}

.setting-popup #menu-usb.disabled {
    pointer-events: none;
    opacity: 0.2;
}

.setting-popup li:last-child {
    border-bottom: unset;
}

.setting-popup li:only-child {
    border-bottom: 0px;
}

.setting-popup a {
    width: auto;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: initial;
    white-space: nowrap;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 15px;
    padding-right: 15px;
    cursor: pointer;
    align-items: center;
    color: #000;
}

.setting-popup a:hover {
    background-color: rgb(184 226 255 / 20%);;
    border-radius: 6px;
}

.remoteapp-popup ul {
    overflow: visible;
    width: auto;
    margin: 0;
    padding: 0;
    box-shadow: 0px 4px 5px 0px rgba(0, 0, 0, 0.14);
    padding: 10px;
    min-width: 192px;
}

.h264-electric-icon {
    background-image: url(app/ext/ribbon/images/electric.svg);
    background-repeat: no-repeat;
    background-size: contain;
    width: 16px;
    height: 16px;
    margin-right: 8px;
}

.microphone-icon {
    background-image: url(app/ext/ribbon/images/mic.svg);
    width: 20px;
    height: 20px;
    margin-right: 4px;
}

.camera-icon {
    background-image: url(app/ext/ribbon/images/camera.svg);
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 4px;
}

.clipboard-icon {
    background-image: url(app/ext/ribbon/images/clipboard.svg);
    width: 20px;
    height: 20px;
    margin-right: 4px;
}

.statistics-icon {
    background-image: url(app/ext/ribbon/images/presentation.png);
    background-size: contain;
    width: 20px;
    height: 20px;
    margin-right: 4px;
}

.streaming-mode-icon {
    background-image: url(app/ext/ribbon/images/sd.svg);
    background-size: contain;
    width: 24px;
    height: 24px;
    margin-right: 4px;
    background-size: 24px;
}

.lock-mode-icon {
    background-image: url(app/ext/ribbon/images/pointer-lock.svg);
    background-size: contain;
    width: 24px;
    height: 24px;
    margin-right: 4px;
    background-size: 24px;
}

.setting-popup .button-settings-resolution {
    background-image: url(data:image/svg+xml,%3Csvg%20class%3D%22photon-icon%20photon-icon-arrow-right%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%3E%0A%20%20%3Cpolygon%20fill-rule%3D%22evenodd%22%20points%3D%225.388%2011.071%206.096%2011.778%2010.309%207.314%206.066%203.071%205.359%203.778%208.895%207.314%22%3E%3C/polygon%3E%0A%3C/svg%3E);
    background-position: right center;
    background-repeat: no-repeat;
    background-color: #fff;
    align-items: center;
    margin: auto;
}

.setting-popup .button-settings-resolution p {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-left: 8px;
}

.setting-popup .with-submenu {
    width: 100%;
    height: 100%;
    background-color: #fff;
}

.setting-popup .with-submenu > ul.submenu {
    position: absolute;
    overflow: visible;
    min-width: 100px;
    width: auto;
    margin: 0;
    border-top: 1px solid #ccc;
    z-index: 100;
    padding: 0;
    background: inherit;
    box-shadow: 0px 4px 5px 0px rgb(0 0 0 / 14%);
    visibility: hidden;
    transition: 0s all;
    transition-delay: 0.1s;
}

.setting-popup .with-submenu > ul.submenu {
    top: 0px;
    right: calc(100% - -8px);
    border-top: none;
    border-radius: 10px;
    padding: 10px;
}

.setting-popup .with-submenu.on > ul.submenu {
    visibility: visible !important;
    animation-name: fade-in-submenu;
    animation-duration: 0.3s;
    animation-fill-mode: forwards;
    animation-timing-function: linear;
    z-index: 200;
}

.setting-popup .with-submenu > ul.submenu > li {
    display: block;
    height: 40px;
    width: 200px;
    overflow: visible;
    text-overflow: ellipsis;
    margin: 0;
    padding: 0;
    border: 0px;
}

.place-holder-icon {
    visibility: hidden;
}

/* SVG icons */
.setting-popup .photon-icon {
    width: 20px;
}

.usb-icon {
    background-image: url(app/ext/ribbon/images/usb.png);
    width: 20px;
    height: 20px;
    margin-right: 5px;
    margin-left: -1px;
}

.setting-popup .button-settings-usb {
    background-position: right center;
    background-repeat: no-repeat;
    background-color: #fff;
    align-items: center;
}

.setting-popup .button-settings-usb p {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}


.btn1-container {
    width: 50px;
    margin: 50px -8px 50px -5px;
}

.btn-switch {
    position: relative;
    display: block;
    width: 35px;
    height: 20px;
    cursor: pointer;
    background-color: #d9d3d3;
    border: 2px solid #d9d3d3;
    border-radius: 40px;
}

.btn-switch-circle {
    position: absolute;
    top: 0.2px;
    left: 0;
    display: block;
    height: 16px;
    width: 16px;
    background-color: #fff;
    border-radius: 40px;
    transition: transform 0.3s;
}

.btn-switch--on {
    background-color: #1f7ed9;
    border: 2px solid #1f7ed9;
}

.btn-switch-circle--on {
    transform: translateX(15px);
}
 
.setting-seperator{
    height: 1.5px;
    background: #b9bbbd;
    width: 100%;
    margin: 10px 0px;
    border: 0px;
}

.setting-popup .with-submenu > ul.submenu2{
    top: 50px;
    padding: 10px;
}

.setting-popup .with-submenu > ul.submenu2 > li {
  width: fit-content;
}

.setting-popup .with-submenu > ul.submenu3{
    top: 100px;
    padding: 10px;
    width: 395px;
}

.gap_line{
    background: transparent;
    position: absolute;
    width: 10px;
    height: 100%;
    top: 0px;
    right: -9px;
}

.rebootVm{
    width: 100%;
    color: black;
    background-position-x: 14px !important;
    text-align: left;
    padding-left: 48px;
    background-size: 20px;
}

.signout{
    width: 100%;
    color: black;
    background-position-x: 14px !important;
    text-align: left;
    padding-left: 48px;
    background-size: 20px;
}
