/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.vm-manager {
	width: 31em;
    overflow: visible;
    border-radius: 6px;
}

.vm-manager .menu-content {
	height: 14em;
}

.vm-manager .menu-content::before {
    content: "";
    background: linear-gradient(to right,#00dcff,#00f);
    position: absolute;
    height: 5px;
    top: -5px;
    left: 4px;
    right: 4px;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
}

.vm-manager.dialog>* {
    position: relative;
	margin: 0;
}

.vm-manager.dialog .vm-header {
   /* -ms-flex-align:      center;
    -moz-box-align:      center;
    -webkit-box-align:   center;
    -webkit-align-items: center;
    align-items:         center;*/
    margin-top: -25px;
    /*height: 4em;*/
    /*border-bottom: 1px solid rgba(0,0,0,0.125);*/
}

.vm-manager.dialog .menu-body {
    padding: 0.25em;
    position: relative;
    overflow: hidden;
}


.vm-manager div.footer {
	padding: 10px;
    padding-top: 20px;
}

.vm-manager button {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
}

.vm-manager div.line {
    border-top: 1px solid rgba(0,0,0,0.125);
    margin: 0 1em;
}

.vm-manager .list-item .caption {
    white-space: nowrap;
    border: 1px solid transparent;
    padding-left: 1vw;
    display: flex;
    align-items: center;
}

.vm-manager .list-item.focused .caption {
    border: 1px dotted rgba(0, 0, 0, 0.5);
}

.vm-manager circle-loader {
    position: absolute;
    top: 0;
    background: rgba(0,0,0,0.5);
    width: 100%;
    height: 100%;
    left: 0;
    padding: 4em 13em 1em 13em;
}

.vm-manager .vm-message-container {
    height: 47%;
    width: 100%;
}

.vm-manager .vm-message-text {
    font-size: 1em;
    position: relative;
    bottom: 1em;
    padding-left: 1vw;
    padding-right: 1vw;
}

.vm-manager.title{
	margin-block-end: 0px;
    text-align: center;
    padding-right: 5.5em;
}

.vm-manager.subtitle {
	margin-block-start: 1px;
	font-weight: normal;
	margin-block-end: 1em;
}

.restore.button, .save.button {
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    border-radius: 6px;
	width: 4.5em;
    height: 2em;
    margin: 4px;
    color: white !important;
}

.radio {
	vertical-align: baseline;
}

.list-item {
	height: 2em;
    /*bottom: 1.4em;*/
}

.list-item:not(.selected) .caption:hover  {
	background: transparent;
}

.circle-loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    opacity: .25;
}

input[type="submit"], button.restore, button.backup, button.save, a.button {
    background-color: rgba(0, 123, 255, 1);
    color: white;
    height: 2em;
    border: 0;
    text-shadow: none;
}

input[type="submit"]:hover, button.restore:hover:enabled, button.backup:hover:enabled, button.save:hover:enabled, a.button:hover {
    background-color: rgba(74, 144, 226, 0.8);
}

input[type="submit"]:disabled, button.restore:disabled, button.backup:disabled, button.save:disabled, button.danger:disabled {
    background-color: rgba(74, 144, 226, 1);
}