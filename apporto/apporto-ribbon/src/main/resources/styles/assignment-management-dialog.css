/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.assignment-management-dialog {
    background-color: rgb(224, 232, 243);
    width: max-content;
    overflow: hidden;
    position: relative;
    border-radius: 6px;
    height: 75vh;
    text-align: left;
}

.assignment-management-dialog p, a, h1 {
    margin: 0;
    text-decoration: none;
}
/* The Close Button */
.assignment-management-dialog .close-dialog {
    height: 24px;
    background-image: url(app/ext/ribbon/images/share_close.svg) !important;
    background-size: 24px !important;
    background-repeat: no-repeat !important;
    float: right;
    position: relative;
    right: 12px;
    top: 10px;
    background-color: transparent;
    padding: 0;
    margin: 0;
    min-width: 25px;
    box-shadow: none;
}

.assignment-management-dialog .head {
    height: 80px;
}

.assignment-management-dialog .head .title {
    display: block;
    margin-inline-start: 0;
    margin-inline-end: 0;
    text-align: left;
    padding-bottom: 0;
    margin: 0 0 8px 0;
    font-size: 24px;
    letter-spacing: .24px;
    padding-left: 2px;
    font-weight: 700;
}

.assignment-management-internal {
    display: grid;
    grid-auto-flow: column;
    gap: 10px;
}

.assignment-management-dialog .no-assignment-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(70vh - 60px);
    text-align: center;
}

.assignment-management-dialog .no-item-description {
    font-weight: 300;
    font-size: 12px;
    color: #373737;
}

.assignment-management-dialog .assignment-list {
    overflow-y: scroll;
    height: 70vh;
    height: calc(70vh - 60px);
    display: grid;
    grid-auto-flow: row;
    grid-gap: 20px;
    width: 310px;
    align-content: start;
}

.assignment-management-dialog .assignment-list::-webkit-scrollbar {
    width: 10px;
}
  
.assignment-management-dialog .assignment-list::-webkit-scrollbar-track {
    background: #eeeeee;
    border-radius: 10px;
}

.assignment-management-dialog .assignment-list::-webkit-scrollbar-thumb {
    background: #bdbdbd; 
    border-radius: 7px;
}

.assignment-management-dialog .assignment-list::-webkit-scrollbar-thumb:hover {
    background: #9d9d9d; 
}

.assignment-list .selected-card {
    background-color: #e2e9f3 !important;
}

.assignment-management-dialog .submit-loading {
    pointer-events: none;
}

.assignment-list-card { 
    width: 289px;
    height: auto;
    padding: 15px;
    border-radius: 4px;
    box-shadow: 0px 1px 2px #777;
    cursor: pointer;
    height: 80px;
}

.assignment-list-card:hover {
    background-color: #e4e4e4;
}

.assignment-list-card .card-head {
    height: 54px;
    width: auto;
    display: grid;
    grid-template-columns: 1fr auto;
}

.assignment-list-card .card-title {
    font-size: 16px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.assignment-list-card .card-disp {
    font-weight: 400;
    font-size: 12px;
    margin-top: 5px;
    grid-column: 1/3;
}

.assignment-list-card .submitted-disp {
    color: #007600;
}

.assignment-list-card .submitted-assignment-icon {
    background-image: url("app/ext/ribbon/images/assignmentSubmitted.svg");
    height: 20px;
    width: 18px;
    background-repeat: no-repeat;
    background-size: cover;
}

.assignment-list-card .lock-assignment-icon {
    background-image: url("app/ext/ribbon/images/assignmentLock.svg");
    height: 20px;
    width: 20px;
    background-repeat: no-repeat;
    background-size: cover;
}

.assignment-list-card .card-extra .assignment-statement{
    font-size: 14px;
}

.assignment-list-card .card-footer {   
    display: grid;
    grid-template-columns: auto auto;
    grid-gap: 5px;
    padding: 5px 0px;
}

.assignment-list-card .card-footer>.card-btn {
    border-radius: 4px;
    border: 1px solid #22538F;
    cursor: pointer;
}

.assignment-management-dialog .primary-btn {
    background-color: #22538F;
    color: #fff;
    border-radius: 4px;
    border: 1px solid #22538F;
    cursor: pointer;
    display: inline-block;
    height: 28px;
    line-height: 14px;
    font-size: 14px;
    width: fit-content;
    margin: 0px;
}

.assignment-management-dialog .secondary-btn {
    background-color: #fff;
    color: #22538f;
    border-radius: 4px;
    border: 1px solid #22538f;
    cursor: pointer;
    display: inline-block;
    height: 28px;
    line-height: 14px;
    font-size: 14px;
    width: fit-content;
    margin: 0px;
}

.assignment-management-dialog .primary-btn:hover, .assignment-management-dialog .secondary-btn:hover {
    background-color: rgba(26, 87, 158, 0.8);
    border: 1px solid rgba(26, 87, 158, 0.8);
    color: #fff;
}

.assignment-management-dialog .assignment-details {
    overflow-y: scroll;
    padding: 15px;
    min-width:750px;
    height: calc(70vh - 60px);
    box-shadow: 1px 1px 2px #777;
}

.assignment-management-dialog .assignment-details::-webkit-scrollbar {
    width: 4px;
}

.assignment-management-dialog .assignment-details::-webkit-scrollbar-track {
    background: #eeeeee;
    border-radius: 4px;
}

.assignment-management-dialog .assignment-details::-webkit-scrollbar-thumb {
    background: #bdbdbd; 
    border-radius: 40px;
}

.assignment-management-dialog .assignment-details::-webkit-scrollbar-thumb:hover {
    background: #9d9d9d; 
}

.assignment-management-dialog #menuBody::-webkit-scrollbar {
    width: 2px;
}

.assignment-details .details-head {
    display: grid;
    grid-auto-flow: row;
    gap: 10px;
}

.assignment-details .details-head .title {
    font-size: 16px;
    font-weight: 600;
}

.assignment-info {
    font-weight: 400;
    font-size: 12px;
}

.assignment-details .details-head .submitted-info {
    color: #007600;
}

.assignment-details .assignment-discription {
    margin: 10px 0px;
    font-size: 12px;
}

.assignment-details .assignment-discription .description-unavailable {
    font-style: italic;
}

.assignment-details .assignment-files-section {
    display: grid;
    grid-auto-flow: row;
    gap: 10px;
    margin: 20px 0px;
    border-bottom: 1px solid #00000033;
    padding-bottom: 10px;
}

.assignment-files-section .download-all-btn{
    background-color: #fff;
    color: #22538f;
    border-radius: 4px;
    border: 1px solid #22538f;
    cursor: pointer;
    display: inline-block;
    height: 25px;
    line-height: 14px;
    font-size: 14px;
}

.assignment-files-section .section-title {
    font-size: 14px;
    font-weight: 600;
    display: inline-block;
    width: auto;
}

.assignment-files-section .assignment-files {
    display: inline-block;
    width: fit-content;
}

.assignment-files .file-name {
    font-size: 12px;
    line-height: 14px;
}

.assignment-files .download-icon {
    background-image: url(app/ext/ribbon/images/downloadFile.svg);
    width: 16px;
    height: 20px;
}

.assignment-files-section .assignment-files:hover .file-name {
    text-decoration: underline;
}


.assignment-details .submission-files-section {
    display: grid;
    grid-auto-flow: row;
    gap: 10px;
    margin: 20px 0px;
}

.assignment-details .submit-assignment-section{
    display: grid;
    grid-auto-flow: row;
    gap: 10px;
    margin: 20px 0px;
}

.submit-assignment-section .section-title{
    font-size: 14px;
    font-weight: 600;
}

.submission-files-section .section-title {
    font-size: 14px;
    font-weight: 600;
}

.submit-assignment-section .submit-assignment-instruction{
    margin: 10px 0px;
    font-size: 12px;
}

.submission-files-section .submission-files {
    display: inline-block;
    width: fit-content;
}

.submission-files .file-name {
    font-size: 12px;
}

.search-container {
    width: 289px;
}
  
input[type=text].assignment-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease-in-out;
    max-width: none;
}
  
input[type=text].assignment-search-input:focus {
    border-color: #22538f;
}

.assignment-management-dialog .skeleton-load-item div {
    height: 16px;
    background-color: #e0e0e0;
    border-radius: 4px;
    margin-bottom: 5px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-load-item .title {
    width: 30%;
}

.assignment-management-dialog .skeleton-load-item .assignment-info {
    width: 50%;
}

.assignment-management-dialog .skeleton-container .card-title {
    height: 16px;
    width: 100%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-container .card-disp {
    height: 16px;
    width: 70%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-container .details-head {
    height: 16px;
    width: 30%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-container .assignment-discription {
    height: 16px;
    width: 70%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-container .assignment-files-section {
    height: 16px;
    width: 40%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-container .submission-files-section .section-title {
    height: 16px;
    width: 50%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}


.assignment-management-dialog .skeleton-container .submission-files-section .submission-files {
    height: 16px;
    width: 30%;
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
}

.assignment-management-dialog .skeleton-table td {
    padding: 2px;
}

.assignment-management-dialog .skeleton-table .pulse {
    background-color: #e0e0e0;
    border-radius: 4px;
    animation: skeleton-pulse 1.5s infinite ease-in-out;
    height : 20px;
}



@keyframes skeleton-pulse {
    0% {
      background-color: #e0e0e0;
    }
    50% {
      background-color: #f0f0f0;
    }
    100% {
      background-color: #e0e0e0;
    }
  }