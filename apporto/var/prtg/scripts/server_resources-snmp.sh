#!/bin/bash

case $1 in
        mem)
                MEM_FREE_MB=$(free -m | awk '/Mem:/{print $7}')
                echo "${MEM_FREE_MB}"
                ;;
        cpu)
                CPU_LOAD=$(top -bn2 | grep Cpu | tail -n1 | sed -e 's/.*, *\([0-9.]*\)%* id.*/\1/' | awk '{print 100-$1}')
                echo "${CPU_LOAD}"
                ;;
        guacd)
                GUACD_PROCESS_COUNT=$(($(pgrep guacd -c)-1))
                echo "${GUACD_PROCESS_COUNT}"
                ;;
        *)
                echo "0"
                ;;
esac

