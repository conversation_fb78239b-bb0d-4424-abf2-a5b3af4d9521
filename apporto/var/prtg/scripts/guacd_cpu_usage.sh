#!/bin/bash
# Find guacd processes that are using too much CPU.
# There are two cases when guacd uses CPU extensively:
# 1. When there a lot of high-speed graphics changes
#    e.g. gaming apps
# 2. When the guacd fails to disconnect properly
#
# The first case is identified by the fact that the process has the RDP or VNC
# connection. Usually in this case guacd has temporary pick.
# The second case happens when connection closes, but guacd still waits in the
# loop. This is not critical case because such guacd has low priority, but
# still should be handled.
#
# This script reports the number of the guacd processes with CPU usage higher then
# defined threshold. If the process does not have RPD or VNC connection, value is
# reported as a warning.
# 

HIGH_CPU_PID_COUNT_HUNG=0
HIGH_CPU_PID_COUNT_REGULAR=0
THRESHOLD=30
for PID in $(pgrep -x guacd); do
    PROCESS_CPU_USAGE=$(ps -p $PID -o %cpu | tail -n1)
    HIGH_CPU=$(echo $PROCESS_CPU_USAGE'>'$THRESHOLD | bc -l)
    if [[ $HIGH_CPU > 0 ]]; then
        # Check netstat
        HAS_SSH=$(sudo netstat -tup | grep $PID | grep ssh)
        HAS_RDP_OR_VNC=$(sudo netstat -tup | grep $PID | grep -E '(rdp|vnc)')
        if [[ x${HAS_SSH} == x || x${HAS_RDP_OR_VNC} == x ]]; then                  # hung guacd
            HIGH_CPU_PIDS_HUNG[HIGH_CPU_PID_COUNT_HUNG]=$PID
            HIGH_CPU_PID_COUNT_HUNG=$(($HIGH_CPU_PID_COUNT_HUNG+1))
        else
            HIGH_CPU_PIDS_REGULAR[HIGH_CPU_PID_COUNT_REGULAR]=$PID
            HIGH_CPU_PID_COUNT_REGULAR=$(($HIGH_CPU_PID_COUNT_REGULAR+1))
        fi
    fi
done

if [[ $HIGH_CPU_PID_COUNT_HUNG > 0 ]]; then
    echo "1:$HIGH_CPU_PID_COUNT_HUNG:Guacd high cpu usage PIDs ${HIGH_CPU_PIDS_HUNG[@]}"
    exit
fi

if [[ $HIGH_CPU_PID_COUNT_REGULAR > 0 ]]; then
    echo "0:$HIGH_CPU_PID_COUNT_REGULAR:Guacd high cpu usage PIDs ${HIGH_CPU_PIDS_REGULAR[@]}"
    exit
fi

echo "0:0:OK"

