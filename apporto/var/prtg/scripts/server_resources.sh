#!/bin/bash

case $1 in
	mem)
		MEM_FREE_MB=$(free -m | awk '/Mem:/{print $7}')
		MEM_LOW_LIMIT=$(free -m | awk '/Mem:/{print $2/10}' | cut -d. -f1)
		if [[ $(echo ${MEM_LOW_LIMIT}'>'${MEM_FREE_MB} | bc -l) == 0 ]]; then
			echo "0:${MEM_FREE_MB}:Free RAM = ${MEM_FREE_MB}MB"
		else
			echo "1:${MEM_FREE_MB}:Free RAM = ${MEM_FREE_MB}MB"
		fi
		;;
	cpu)
		CPU_LOAD=$(top -bn2 | grep Cpu | tail -n1 | sed -e 's/.*, *\([0-9.]*\)%* id.*/\1/' | awk '{print 100-$1}')
		echo "0:${CPU_LOAD}:Current CPU load = ${CPU_LOAD}"
		;;
	guacd)
		GUACD_PROCESS_COUNT=$(($(pgrep guacd -c)-1))
		echo "0:${GUACD_PROCESS_COUNT}:Number of guacd users = ${GUACD_PROCESS_COUNT}"
		;;
	*)
		echo "0:200:Wrong script parameter"
		;;
esac
