#!/usr/bin/env python

from __future__ import division

import subprocess
import sys
import re

if sys.version_info[0] == 2:
  int = long

# max heap allowed
mem_sufix_dict = {'m':1024,'g':1024*1024}
with open('/etc/default/tomcat8') as f:
  tomcat8 = f.read()

java_opts_lines = [line for line in tomcat8.split('\n') if 'JAVA_OPTS' in line and not line.startswith('#')]
java_opts = []
if java_opts_lines:
  java_opts = java_opts_lines[-1].split('"')[1].split(' ')

max_heap_opt = ''
for java_opt in reversed(java_opts):
  if '-Xmx' in java_opt:
    max_heap_opt = java_opt

regex = re.compile('-Xmx(\d+)(\w)')
match = regex.match(max_heap_opt)
if match:
  max_heap_allowed = int(match.group(1)) * mem_sufix_dict[match.group(2)]
else:
  max_heap_allowed = 128*1024

# jps | grep eclipse
jps_out = subprocess.Popen(['jps'], stdout = subprocess.PIPE).communicate()[0]
java_processes = [j for j in jps_out.split('\n') if 'guac' in jps_out]

java_pids = [java_process.split(' ')[0] for java_process in java_processes]

pid_heap_dict = {}
for java_pid in java_pids:
  jstat_out = [l for l in subprocess.Popen(['jstat', '-gc', java_pid], stdout = subprocess.PIPE)
                                    .communicate()[0] if l]
  if jstat_out:
    jstat_out = jstat_out[-1]
    heap = jstat_out[3] + jstat_out[4] + jstat_out[6] + jstat_out[8]
    pid_heap_dict[java_pid] = heap


# Max heap usage
relative_heap_usage = {pid:pid_heap_dict[pid]*100/max_heap_allowed for pid in pid_heap_dict}
max_relative_heap_usage = 0
max_relative_heap_usage_pid = 0
for pid in relative_heap_usage:
  if max_relative_heap_usage < relative_heap_usage[pid]:
    max_relative_heap_usage = relative_heap_usage[pid]
    max_relative_heap_usage_pid = pid

print('0:' + str(max_relative_heap_usage) + ':' + str(max_relative_heap_usage_pid))
