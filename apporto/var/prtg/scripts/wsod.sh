#!/bin/bash

if [[ -n $(curl -X POST 'localhost:8080/guacamole/api/tokens' --data 'q=GlR7eA7H%2FYB6P%2FDvbNUI4YuFO1VM85NYF6U1YWhoJsI%2BqT%2FQKF%2BP1pHM%2FYZQxoPQKKcCz3ZxJ5rqnu0Z6Nei1C1ItvD868obgFGbyZ39pMkIf2mLu428SC%2B0Pvf3y5IXbCVinUFrqn7wcVz7XEVLYULwXfFdGu4hRqVN5FfRLI%2BK1tfbnE5bYyWaGsleLNFSSlwYFybeC3Zyi%2BkGiAH2ghKDZneQXF6z9H5adzlwP6g%3D' 2>/dev/null | grep authToken) ]]; then
	echo '0:0:No WSoD'
else
	echo '0:1:WSoD'
fi
