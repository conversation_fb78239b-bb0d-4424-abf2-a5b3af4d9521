#!/usr/bin/env python

import subprocess
import sys
import re

if sys.version_info[0] == 2:
  int = long

# max heap allowed
mem_sufix_dict = {'m':1024,'g':1024*1024}
with open('/etc/default/tomcat8') as f:
  tomcat8 = f.read()

java_opts_lines = [line for line in tomcat8.split('\n') if 'JAVA_OPTS' in line and not line.startswith('#')]
java_opts = []
if java_opts_lines:
  java_opts = java_opts_lines[-1].split('"')[1].split(' ')

max_heap_opt = ''
for java_opt in reversed(java_opts):
  if '-Xmx' in java_opt:
    max_heap_opt = java_opt

regex = re.compile('-Xmx(\d+)(\w)')
match = regex.match(max_heap_opt)
if match:
  max_heap_allowed = int(match.group(1)) * mem_sufix_dict[match.group(2)]
else:
  max_heap_allowed = 128*1024

if max_heap_allowed < 512*1024:
  print('0:' + str(max_heap_allowed/1024) + ':512')
else:
  print('0:' + str(max_heap_allowed/1024) + ':512')
