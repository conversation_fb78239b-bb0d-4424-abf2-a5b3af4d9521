Guacamole server:
- The Guacamole servers connecting to the remote linux instances need to have the following scripts in $HOME
  - 1. lxc\_create.sh
  - 2. lxc\_init.sh
  - 3. lxc\_deprovision.sh
  - 4. create\_lxc\_container.sh - helper script
  - 5. initialise\_lxc.sh - helper script

Host:
- Create an AWS EC2 instance from the official RHEL image.
- Add EPEL repo
  - sudo rpm -Uvh https://dl.fedoraproject.org/pub/epel/epel-release-latest-7.noarch.rpm
- Install SSHFS
  - sudo yum install fuse-sshfs
- Install LXC
  - sudo yum -y install lxc lxc-templates libcap-devel libcgroup busybox wget bridge-utils lxc-extra
- Install debootstrap (needed for ubuntu template container)
  - sudo yum install debootstrap
- Install VNC server package (used to set VNC password in a container):
  - sudo yum install tigervnc
- Install libvirt (provides the efault configuration for the network interface inside containers):
  - sudo yum install libvirt-daemon
  - sudo systemctl start libvirtd
  - sudo systemctl enable libvirtd
  - sudo yum install libvirt-daemon-config-network libvirt-daemon-config-nwfilter libvirt-daemon-driver-interface libvirt-daemon-driver-lxc libvirt-daemon-driver-network libvirt-daemon-driver-nodedev libvirt-daemon-driver-nwfilter libvirt-daemon-driver-qemu libvirt-daemon-driver-secret libvirt-daemon-driver-storage libvirt-daemon-driver-storage-core libvirt-daemon-driver-storage-disk libvirt-daemon-driver-storage-gluster libvirt-daemon-driver-storage-iscsi libvirt-daemon-driver-storage-logical libvirt-daemon-driver-storage-mpath libvirt-daemon-driver-storage-rbd libvirt-daemon-driver-storage-scsi
- Create the Ubuntu template container (will be used as "the source" for every Ubuntu container that will be used by the users):
  - sudo lxc-create -n ubuntu -t ubuntu -- -r xenial
- Create the Fedora template container (will be used as "the source" for every Fedora container that will be used by the users):
  - sudo lxc-create -n fedora -t fedora # Consider using a non-ancient fedora
- Start containers *demonized*
  - sudo lxc-start -dn ubuntu
  - sudo lxc-start -dn fedora
  - sudo lxc-attach -n fedora # Set the root password and exit with ^D
- Create an copy the host's SSH key to the template containers
  - ssh-keygen
  - ssh-copy-id ubuntu@`sudo lxc-info -iHn ubuntu`
  - ssh-copy-id root@`sudo lxc-info -iHn fedora`

Ubuntu template:
- SSH into the template container
  - sudo lxc-start -dn ubuntu
  - ssh ubuntu@`sudo lxc-info -iHn ubuntu`
- Configure VNC server
  - sudo apt install tightvncserver
  - vncserver
  - vncserver -kill :1
  - cat > .vnc/xstartup <<EOF
  - #!/bin/sh
  - xrdb ~/.Xresources # not necessary
  - startxfce4 &
  - EOF
- Install XFCE (choose one)
  - sudo apt install xfce4 # minimal xfce, lacks some defult packages
  - sudo apt install xubuntu-desktop # full-fledged Xubuntu, with branding too
- Shut down the template container
  - sudo poweroff # From inside the container
  - sudo lxc-stop -n ubuntu # From the host

Fedora template:
- SSH into the template container
  - sudo lxc-start -dn feora
  - ssh root@`sudo lxc-info -iHn fedora`
- Configure the VNC server
  - sudo yum install tigervnc-server
  - vncserver
  - vncserver -kill :1
  - cat > .vnc/xstartup <<EOF
  - #!/bin/sh
  - unset SESSION_MANAGER
  - unset DBUS_SESSION_BUS_ADDRESS
  - exec startxfce4
  - EOF
- Install XFCE
  - yum install @xfce
- Shut down the template container
  - sudo poweroff # From inside the container
  - sudo lxc-stop -n fedora # From the host


- Misc notes:
  - Connect to both template containers' VNC to avoid nagging the users with "do you want a bar with icons" and other annoying questions.
  - The template containers need to be shut down in order to create the actual ones that will be used.
