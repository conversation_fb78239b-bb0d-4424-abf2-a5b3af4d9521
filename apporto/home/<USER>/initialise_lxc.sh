#!/bin/bash

SFTP_USER=$1
SFTP_PASS=$2
DISTRO=$3

CONTAINER_HOST="*************"
if [[ $DISTRO = "ubuntu" ]]; then
	CONTAINER_USER=ubuntu
else
	CONTAINER_USER=root
fi
if [[ $(sudo lxc-ls --active $SFTP_USER) != $SFTP_USER ]]; then
	sudo lxc-start -n $SFTP_USER -d
	sleep 20
fi

mkdir -p /home/<USER>/${SFTP_USER}/Session{Up,Down}loads

# Session Downloads
local_counter=0
if [[ $(mountpoint /home/<USER>/${SFTP_USER}/SessionDownloads) != *"SessionDownloads is a mountpoint"* ]]; then
	while true; do
		sleep 1
		echo $SFTP_PASS | sshfs -o nonempty,password_stdin,uid=$(id -u),gid=$(id -g) $<EMAIL>:SessionDownloads /home/<USER>/${SFTP_USER}/SessionDownloads/
		local_status=$?
		[[ $local_status -eq 0 ]] && break
		[[ $local_status -ne 0 ]] && local_counter=$(($local_counter + 1))
		[[ $local_counter -gt 10 ]] && exit 1
	done
	sudo mount --bind /home/<USER>/${SFTP_USER}/SessionDownloads /var/lib/lxc/${SFTP_USER}/rootfs/home/<USER>/Desktop/SessionDownloads
fi

# Session Uploads
local_counter=0
if [[ $(mountpoint /home/<USER>/${SFTP_USER}/SessionUploads) != *"SessionUploads is a mountpoint"* ]]; then
	while true; do
		sleep 1
		echo $SFTP_PASS | sshfs -o nonempty,password_stdin,uid=$(id -u),gid=$(id -g) $<EMAIL>:SessionUploads /home/<USER>/${SFTP_USER}/SessionUploads/
		local_status=$?
		[[ $local_status -eq 0 ]] && break
		[[ $local_status -ne 0 ]] && local_counter=$(($local_counter + 1))
		[[ $local_counter -gt 10 ]] && exit 1
	done
	sudo mount --bind /home/<USER>/${SFTP_USER}/SessionUploads /var/lib/lxc/${SFTP_USER}/rootfs/home/<USER>/Desktop/SessionUploads
fi

# VNC

read -a VNC_SERVERS < <(ps aux | grep -E 'X(tight)?vnc :[[:digit:]]' | cut -d: -f4 | awk '{print $1}') -d ' '
IFS=$'\n' VNC_SERVERS=($(sort <<<"${VNC_SERVERS[*]}"))
unset IFS
PORT_FOUND=0
for SERVER_INDEX in "${!VNC_SERVERS[@]}"; do
	if [[ ${VNC_SERVERS[SERVER_INDEX]} -ne $(($SERVER_INDEX+1)) ]]; then
		VNC_PORT=$SERVER_INDEX
		PORT_FOUND=1
		break
	fi
done

if [[ $PORT_FOUND -eq 0 ]]; then
	VNC_PORT=$((${#VNC_SERVERS[@]}+1))
fi
sudo iptables -t nat -D PREROUTING -p tcp -i eth0 --dport $((${VNC_PORT}+5900)) -j DNAT --to-destination $(sudo lxc-info -iHn $SFTP_USER):$((${VNC_PORT}+5900)) 2>/dev/null
sudo iptables -D FORWARD -p tcp -d $(sudo lxc-info -iHn $SFTP_USER) --dport $((${VNC_PORT}+5900)) -m state --state NEW,ESTABLISHED,RELATED -j ACCEPT 2>/dev/null
sudo iptables -t nat -A PREROUTING -p tcp -i eth0 --dport $((${VNC_PORT}+5900)) -j DNAT --to-destination $(sudo lxc-info -iHn $SFTP_USER):$((${VNC_PORT}+5900))
sudo iptables -A FORWARD -p tcp -d $(sudo lxc-info -iHn $SFTP_USER) --dport $((${VNC_PORT}+5900)) -m state --state NEW,ESTABLISHED,RELATED -j ACCEPT

if [[ $DISTRO = "ubuntu" ]]; then
	echo $SFTP_PASS | vncpasswd -f | sudo tee /var/lib/lxc/${SFTP_USER}/rootfs/home/<USER>/.vnc/passwd 1>/dev/null
else
	echo $SFTP_PASS | vncpasswd -f | sudo tee /var/lib/lxc/${SFTP_USER}/rootfs/root/.vnc/passwd 1>/dev/nulld
fi
ssh -oStrictHostKeyChecking=no $CONTAINER_USER@$(sudo lxc-info -n $SFTP_USER -iH) "vncserver :$VNC_PORT" 2>&1 | awk -F: '/^New/{print $NF}' > /home/<USER>/vncport
