#!/bin/bash

sudo useradd -s /bin/bash -k /etc/skel -m $1
#sudo mkdir -p /home/<USER>
#sudo useradd -s /bin/bash $1
#sudo chown $1 -R /home/<USER>
#while [[ $(mountpoint /home/<USER>"/home/<USER>" ]]; do
#	sudo su $1 -c "echo $2 | sshfs $<EMAIL>: /home/<USER>"
#done
#sudo su $1 -c "cp -r /etc/skel/.config /etc/skel/.vnc /etc/skel/.Xdefaults /etc/skel/.bash* /etc/skel/.profile /etc/skel/.xscreensaver /home/<USER>/"
#sudo chown $1 -R /home/<USER>
echo -e "$2\n$2" | sudo passwd -q $1 &>/dev/null
sudo su $1 -c "echo $2 | vncpasswd -f > /home/<USER>/.vnc/passwd"
sudo -u $1 chmod 600 /home/<USER>/.vnc/passwd
#mount.cifs -o rw,username=sshd.admin,password=W4NsoSVpuQ2gzI\!,domain=apporto,uid=$(id -u $1),gid=$(id -g $1),file_mode=0600 //nc-dc1.apporto.com/dfs/sftplinux/$1/Documents /home/<USER>/Documents/
#mount.cifs -o rw,username=sshd.admin,password=W4NsoSVpuQ2gzI\!,domain=apporto,uid=$(id -u $1),gid=$(id -g $1),file_mode=0600 //nc-dc1.apporto.com/dfs/sftplinux/$1/Desktop /home/<USER>/Desktop/

# VNC
read -a VNC_SERVERS < <(ps aux | grep -E 'Xvnc4 :[[:digit:]]' | cut -d: -f4 | awk '{print $1}') -d ' '
IFS=$'\n' VNC_SERVERS=($(sort -n <<<"${VNC_SERVERS[*]}"))
unset IFS
PORT_FOUND=0
for SERVER_INDEX in "${!VNC_SERVERS[@]}"; do
	if [[ ${VNC_SERVERS[SERVER_INDEX]} -ne $(($SERVER_INDEX+1)) ]]; then
		VNC_PORT=$((1+$SERVER_INDEX))
		PORT_FOUND=1
		break
	fi
done
if [[ $PORT_FOUND -eq 0 ]]; then
	VNC_PORT=$((${#VNC_SERVERS[@]}+1))
fi
sudo su $1 -c "vncserver -geometry 1366x768 :${VNC_PORT}" 
sudo su $1 -c "echo $VNC_PORT > /home/<USER>/.vncport"
