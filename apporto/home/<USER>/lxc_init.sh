#!/bin/bash

SFTP_USER=${1:-FOO}
SFTP_PASS=${2:-FOO}
DISTRO=${3:-ubuntu}
LINUX_HOST="*************"

if [[ $SFTP_USER = "FOO" ]]; then
	echo "{\"Message\":\"Container name missing.\",\"Success\":\"1\"}"
	exit 0
fi
if [[ $SFTP_PASS = "FOO" ]]; then
	echo "{\"Message\":\"Container password missing.\",\"Success\":\"1\"}"
	exit 0
fi

ssh -oStrictHostKeyChecking=no -i /home/<USER>/.ssh/angular-dev.pem ec2-user@$LINUX_HOST "bash -s" < initialise_lxc.sh $SFTP_USER $SFTP_PASS $DISTRO 2>/dev/null
ssh -oStrictHostKeyChecking=no -i /home/<USER>/.ssh/angular-dev.pem ec2-user@$LINUX_HOST sync
if [[ $? -eq 0 ]]; then
	V=$(ssh -oStrictHostKeyChecking=no -i /home/<USER>/.ssh/angular-dev.pem ec2-user@$LINUX_HOST 'cat ~/vncport')
	VNC_PORT=$((${V}+5900))
	echo "{\"Message\":\"Container initialised.\",\"Success\":\"0\",\"VNCPORT\":\"$VNC_PORT\"}"
else
	echo "{\"Message\":\"Could not initialise container.\",\"Success\":\"1\"}"
fi
