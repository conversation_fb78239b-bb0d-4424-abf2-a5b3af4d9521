#!/bin/bash

SFTP_USER=${1:-FOO}
DISTRO=${2:-ubuntu}
LINUX_HOST="*************"

if [[ $SFTP_USER = "FOO" ]]; then
	echo "{\"Message\":\"Instance name missing.\",\"Success\":\"1\"}"
	exit 0
fi

CONTAINER_EXISTS=$(ssh -oStrictHostKeyChecking=no -i /home/<USER>/.ssh/angular-dev.pem ec2-user@$LINUX_HOST "sudo [ -f /var/lib/lxc/${SFTP_USER}/config ] && echo exists" 2>/dev/null)
if [[ $CONTAINER_EXISTS = "exists" ]]; then
	echo "{\"Message\":\"Container already exists\",\"Success\":\"1\"}"
	exit 1
fi
ssh -oStrictHostKeyChecking=no -i /home/<USER>/.ssh/angular-dev.pem ec2-user@$LINUX_HOST "sudo lxc-clone -o $DISTRO -n $SFTP_USER" &>~/lxc_create.log
if [[ $? -eq 0 ]]; then
	echo "{\"Message\":\"Container created.\",\"Success\":\"0\"}"
else
	echo "{\"Message\":\"Could not create container.\",\"Success\":\"1\"}"
fi
