#!/bin/bash

USERNAME=$1
PASSWORD=$2

if [[ -z $USERNAME || -z $PASSWORD ]]; then
	echo "{\"Message\":\"No username or password specified.\",\"Success\":\"1\"}"
	exit 1
fi

ssh -oStrictHostKeyChecking=no -i ~/.ssh/id_rsa_apporto.pem ubuntu@************ "bash -s" < ./init_multi_user.sh $USERNAME $PASSWORD 
V=$(ssh -oStrictHostKeyChecking=no -i /home/<USER>/.ssh/id_rsa_apporto.pem ubuntu@************ "sudo cat /home/<USER>/.vncport")
VNC_PORT=$((5900+${V}))
echo "{\"Message\":\"Successfully initialized user.\",\"Success\":\"0\",\"VNCPORT\":${VNC_PORT}}"
