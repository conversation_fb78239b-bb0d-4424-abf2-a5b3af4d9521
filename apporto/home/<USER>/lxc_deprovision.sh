#!/bin/bash

SFTP_USER=${1:-FOO}
VNC_PORT=${2:0}
ACTION=${3:-stop}
LINUX_HOST="*************"

if [[ $SFTP_USER = "FOO" ]]; then
	echo "{\"Message\":\"Instance name missing.\",\"Success\":\"1\"}"
	exit 0
fi
if [[ $VNC_PORT = 0 ]]; then
	echo "{\"Message\":\"VNC port not specified.\",\"Success\":\"1\"}"
	exit 0
fi

ssh -i /home/<USER>/.ssh/angular-dev.pem ec2-user@$LINUX_HOST 'bash -s' < deprovision_container.sh $SFTP_USER $ACTION $VNC_PORT
if [ $? -eq 0 -o $? -eq 1 -o $? -eq 2 ]; then
	if [[ $ACTION = "stop" ]]; then
		echo "{\"Message\":\"Container stopped.\",\"Success\":\"0\"}"
	else
		echo "{\"Message\":\"Container terminated.\",\"Success\":\"0\"}"
	fi
else
	if [[ $ACTION = "stop" ]]; then
		echo "{\"Message\":\"Could not stop container.\",\"Success\":\"1\"}"
	else
		echo "{\"Message\":\"Could not terminate container.\",\"Success\":\"1\"}"
	fi
fi
