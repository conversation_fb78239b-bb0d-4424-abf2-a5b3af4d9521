#!/bin/sh

CONTAINER=$1
ACTION=$2
PORT=$3

sudo umount /home/<USER>/Desktop/SessionDownloads &>/dev/null
sudo umount /var/lib/lxc/${CONTAINER}/rootfs/home/<USER>/Desktop/SessionDownloads &>/dev/null
sudo umount /var/lib/lxc/${CONTAINER}/rootfs/root/Desktop/SessionDownloads &>/dev/null
sudo umount /home/<USER>/Desktop/SessionUploads &>/dev/null
sudo umount /var/lib/lxc/${CONTAINER}/rootfs/home/<USER>/Desktop/SessionUploads &>/dev/null
sudo umount /var/lib/lxc/${CONTAINER}/rootfs/root/Desktop/SessionUploads &>/dev/null

sudo iptables -t nat -A PREROUTING -p tcp -i eth0 --dport $PORT -j DNAT --to-destination $(sudo lxc-info -iHn $CONTAINER):$PORT
sudo iptables -A FORWARD -p tcp -d $(sudo lxc-info -iHn $CONTAINER) --dport $PORT -m state --state NEW,ESTABLISHED,RELATED -j ACCEPT

if [[ $ACTION = "stop" ]]; then
	sudo lxc-${ACTION} -n $CONTAINER 2>/dev/null
elif [[ $ACTION = "destroy" ]]; then
	sudo lxc-stop -n $CONTAINER 2>/dev/null
	sudo lxc-${ACTION} -n $CONTAINER 2>/dev/null
fi
