#!/usr/bin/php
<?php
require_once(__DIR__ . '/XmppPrebind.php');
require_once(__DIR__ . '/security.php');

if(isset($argv)) {
    $params = json_decode($argv[1]);

    $xmppPrebind = new XmppPrebind($params->{'domain'}, $params->{'bosh_server'}, 'converse.js');

    $secretKey = 'ApportoTestSecretKey';
    $security = new Security($secretKey);
    $encryptedPass = $security->encrypt($params->{'password'});
    $xmppPrebind->connect($params->{'username'}, $encryptedPass);

    $xmppPrebind->auth();
    $sessionInfo = $xmppPrebind->getSessionInfo(); // array containing sid, rid and jid

    echo json_encode($sessionInfo);
}

?>

