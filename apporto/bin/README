------------------------------------------------------------
 Apporto extensions
------------------------------------------------------------
- Changes are in TunnelRequest.java.
- Added calling of <PERSON><PERSON><PERSON> server on session start/stop.
- Added checking of SFTP server availability and correctness
of SFTP server directory structure.

It is required to have 'nc' and sshpass tools installed on
the system and all script from this directory in the

$GUACAMOLE_HOME/bin directory (typically /usr/share/tomcat8/.guacamole/bin).

The ~tomcat8/.ssh directory should be populated with proper
keys for accessing cygwin and SFTP server, typically
located on the domain controller for each region.

