#!/usr/bin/env pwsh

$ssl_options =  New-PSSessionOption -SkipCACheck -SkipCNCheck
$adminname="sshd.admin"
#$pw1="W4NsoSVpuQ2gzI!"
$pw1="3WzdKD=5/fLr-s6F"
$pw = convertto-securestring -AsPlainText -Force -String $pw1
$dc_cred = new-object -typename System.Management.Automation.PSCredential -argumentlist  $adminname,$pw
#
$pssession = New-PSSession -ComputerName $args[0] -ThrottleLimit 300 -Credential $dc_cred -UseSSL -Authentication basic  -SessionOption $ssl_options
$end_args=@()

for ( $i = 2; $i -lt $args.count; $i++ ) {
$end_args+=$args[$i]
}

$sb_runner={ param($script_name,[array]$script_args)
if ($script_name -like "restore_user_profile*"){
	$run = "C:\Scripts\" + $script_name + " " + $script_args[0] + " " + $script_args[1] + " '" + $script_args[2] + "' " + $script_args[3]
}
else {
	$run = "C:\Scripts\" + $script_name + " " + $script_args}
# $runresult= & $run
$startInfo = New-Object System.Diagnostics.ProcessStartInfo
$startInfo.FileName = "powershell.exe"
$startInfo.Arguments = $run

$startInfo.RedirectStandardOutput = $true
$startInfo.UseShellExecute = $false
$startInfo.CreateNoWindow = $false

$process = New-Object System.Diagnostics.Process
$process.StartInfo = $startInfo
$process.Start() | Out-Null
$runresult = $process.StandardOutput.ReadToEnd()
$process.WaitForExit()
return $runresult
}

$result= Invoke-Command -Session $pssession -ScriptBlock $sb_runner -ArgumentList $args[1],$end_args
Remove-PSSession -Session $pssession | out-null
$result



