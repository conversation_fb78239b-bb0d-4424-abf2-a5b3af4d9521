#!/bin/bash

ROLLBAR_ENVIRONMENT="development"

filename="../apporto-ribbon/src/main/resources/config/rollbarConfig.js"
cp -f rollbarConfig_template.js $filename

rollbar_code_version=$(git tag --points-at HEAD)
if [ -z $rollbar_code_version ]; then
    rollbar_code_version=$(git rev-parse --verify HEAD)
else
    # We have a tagged release.  Lets set the environment to be 'production':
    ROLLBAR_ENVIRONMENT="production"
fi

echo "+++ Setting ROLLBAR_ENVIRONMENT=$ROLLBAR_ENVIRONMENT"
echo "+++ Setting ROLLBAR_CODE_VERSION=$rollbar_code_version"

sed -i -e "s/ROLLBAR_ENVIRONMENT/$ROLLBAR_ENVIRONMENT/g" $filename
sed -i -e "s/ROLLBAR_CODE_VERSION/$rollbar_code_version/g" $filename
