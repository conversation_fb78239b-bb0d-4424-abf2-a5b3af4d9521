It is required to have ssh client installed and the following in the ~tomcat8 directory:

1. ~tomcat8/.ssh directory, owned by tomcat8:tomcat8
2. ~tomcat8/.ssh/id_rsa_apporto , key file owned by tomcat8:tomcat8, file mode should be 600
3. ~tomcat8/.ssh/known_hosts , owned by tomcat8:tomcat8. This file can be generated by making ssh connection to relevant server from terminal, as user tomcat8 (or copy host key from other user).

Executing command as tomcat user can be done in the following way:
sudo -H -u tomcat8 bash -c 'ssh nv-dc1.apporto.com -p 22122 -i ~tomcat8/.ssh/id_rsa_apporto'

Configuration changes in guacamole.properties:

1. dc-server-id - DC server hostname that handles snapshots for this Guacamole server
2. snapshot-username - username used for connecting to cygwin and executing snapshot scripts remotely
3. port - port used on the server-id
4. region - region parameter for snapshot scripts, should be the same region as the one where guacamole server resides (e.g. NC, NV, MSNV...)
5. server-id - identification of this server. This property is important if Guacamole is used behind Load Balancer in active/active configuration. Remember to update <PERSON><PERSON> Balancer and <PERSON><PERSON> with this server id.


