1.13.0 21.08.2020.
- Added thumbnails
- Upgraded notifications
- Added non-Windows support interface
- Added Classroom interface

1.12.0 23.05.2020.
- Classroom
- Updated to Guacamole 1.1

1.11.0 14.03.2020.
- Introduces script server
- Bugfixes (session closing etc...)

1.10.0 06.02.2020.
- Disconnect session script call.
- Added "StrictHostKeyChecking no" to all ssh calls.

1.9.0 28.12.2019.
- Added multimonitor parameters.
- Classroom view support.

1.8.0 12.04.2019.
- Added support for messenger.
- Added interface for windows callback.
- Added connection event handling.
- Removed some custom code from extension library.
- Upgraded to Guacamole 1.0.0

1.7.1 17.01.2019.
- Fixed issue with deleting files in root directory.

1.7.0 02.01.2019.
- Reduced Tomcat logging
- Upgrade to Guacamole 0.9.14
- Fixed authentication of VNC sessions (required for Linux support)
- Added support for server-ids, needed for active/active configuration

1.6.0 04.10.2018.
- Added notifications
- Added network quality checks
- Added file manipulation interfaces
- Added highlight interface
- Optimizations and bug fixes

1.5.1, 17.08.2018.
- Changed version of the scripts used for snapshots save/restore.

1.5, 02.07.2018. - Summer 2018 Release
- Added intro flag management.
- Added snapshots management.

1.4.1, 02.04.2018. - Spring 2018 Release
- Added example REST service in UserContext.

1.4.0, 04.01.2017.
- Merged encrypted-url-jdbc-base into this project
- Changed name of the target
- Removed unneeded libraries

1.3.1 - Winter 2018 Release
- Improved REST service, reduced dependancy on vanilla guacamole.

1.3.0
- Upgrade to Guacamole 0.9.13

