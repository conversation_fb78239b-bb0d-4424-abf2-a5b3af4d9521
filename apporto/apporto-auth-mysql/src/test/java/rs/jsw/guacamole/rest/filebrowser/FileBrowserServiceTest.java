/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.filebrowser;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;

import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.GuacamoleException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class FileBrowserServiceTest {

    @Mock
    private HttpURLConnection mockConnection;

    @Spy
    FileBrowserService fileBrowserService = new FileBrowserService();

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private final static String URL_RESULT_SUCCESS = "https://filebrowser.dummy.org/example?token=1234567890";
    private final static String URL_RESULT_SUCCESS_JSON = "{\"link\":\"" + URL_RESULT_SUCCESS + "\"}";
    private final static String URL_RESULT_FAILURE = "Internal server error";
    private final static InputStream stubInputStream = new ByteArrayInputStream(URL_RESULT_SUCCESS_JSON.getBytes());
    private final static InputStream stubErrorStream = new ByteArrayInputStream(URL_RESULT_FAILURE.getBytes());

    @Test
    public void testGetUrl_SuccessfulConnection() throws MalformedURLException, IOException, GuacamoleException {
        // Mock the createConnection method
        when(fileBrowserService.createConnection()).thenReturn(mockConnection);

        // Mock the connection's output stream and response code
        when(mockConnection.getInputStream()).thenReturn(stubInputStream);
        when(mockConnection.getOutputStream()).thenReturn(mock(OutputStream.class));
        when(mockConnection.getResponseCode()).thenReturn(Status.OK.getStatusCode());

        // Act
        FBUrlData result = fileBrowserService.getUrl("username", "password", "OU", "OS", true, false, "hostname");

        // Assert
        assertEquals("Expected result", URL_RESULT_SUCCESS, result.getUrl());
        verify(mockConnection, times(1)).getOutputStream(); // Ensure that getOutputStream was called
        verify(mockConnection, times(1)).getResponseCode(); // Ensure that getResponseCode was called
        verify(mockConnection, times(1)).getInputStream(); // Ensure that getOutputStream was called
    }

    @Test
    public void testGetUrl_FailedConnection() throws MalformedURLException, IOException, GuacamoleException {
        // Mock the createConnection method
        when(fileBrowserService.createConnection()).thenReturn(mockConnection);

        // Mock the connection's output stream and response code
        when(mockConnection.getErrorStream()).thenReturn(stubErrorStream);
        when(mockConnection.getOutputStream()).thenReturn(mock(OutputStream.class));
        when(mockConnection.getResponseCode()).thenReturn(Status.INTERNAL_SERVER_ERROR.getStatusCode());

        // Act
        FBUrlData result = fileBrowserService.getUrl("username", "password", "OU", "OS", true, false, "hostname");

        // Assert
        assertEquals("Expected result", null, result);
        verify(mockConnection, times(1)).getOutputStream(); // Ensure that getOutputStream was called
        verify(mockConnection, times(1)).getResponseCode(); // Ensure that getResponseCode was called
        verify(mockConnection, times(1)).getErrorStream(); // Ensure that getOutputStream was called
    }
}
