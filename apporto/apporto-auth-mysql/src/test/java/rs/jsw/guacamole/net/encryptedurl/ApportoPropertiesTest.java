/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl;

import static org.junit.Assert.assertEquals;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import com.apporto.hyperstream.core.ApportoProperties;

@RunWith(MockitoJUnitRunner.class)
public class ApportoPropertiesTest {

    @Test
    public void getFileBrowserApiKeyProp_shouldReturnApiKeyProp() {
        StringGuacamoleProperty prop = (StringGuacamoleProperty) ApportoProperties.getProp("filebrowser-api-key");
        
        assertEquals(prop, ApportoProperties.FILEBROWSER_API_KEY);
    }

    @Test
    public void getFileBrowserApiKey_shouldReturnApiKey() throws GuacamoleException {
        LocalEnvironment environment = Mockito.mock(LocalEnvironment.class);

        Mockito.when(environment.getProperty(ApportoProperties.FILEBROWSER_API_KEY)).thenReturn("1234567890");

        String result = environment.getProperty(ApportoProperties.FILEBROWSER_API_KEY);

        assertEquals(result, "1234567890");
    }
    
}
