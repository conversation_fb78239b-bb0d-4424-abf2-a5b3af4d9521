/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.notification;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

import java.lang.reflect.Field;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.notification.dto.ResponseDTO;

@RunWith(MockitoJUnitRunner.class)
public class NotificationResourceTest {

    private ApportoUserContext userContext;

    private ConcurrentMap<Long, ResponseDTO> sessionNotificationResponses;

    @Mock
    private LocalEnvironment mockEnvironment;

    // Wrapper class exposes protected methods needed for testing
    private static class NotificationResourceWrapper extends NotificationResource {
        public NotificationResourceWrapper(UserContext userContext) {
            super(userContext);
        }

        @Override
        protected String getParameter(Integer id, String parameter) {
            return super.getParameter(id, parameter);
        }
    }

    private NotificationResourceWrapper notificationResource;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);

        // Kreiranje mock instance za LocalEnvironment
        mockEnvironment = mock(LocalEnvironment.class);

        userContext = new ApportoUserContext();
        userContext = spy(userContext);

        // Initialize NotificationResourceWrapper with the mocked userContext
        notificationResource = new NotificationResourceWrapper(userContext);
        notificationResource = spy(notificationResource);
        // Refleksija za postavljanje mockEnvironment u NotificationResource

        Field environmentField = NotificationResource.class.getDeclaredField("environment");
        environmentField.setAccessible(true);
        environmentField.set(notificationResource, mockEnvironment);

        sessionNotificationResponses = new ConcurrentHashMap<>();
    }

    @Test
    public void setResponse_ValidData_Success() throws GuacamoleException {
        doReturn("test-user").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CLOUD_USERNAME_PARM));
        doReturn("classroom").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CONNECTION_TYPE_PARAM));
        when(userContext.getSessionNotificationResponses()).thenReturn(sessionNotificationResponses);
        when(mockEnvironment.getRequiredProperty(any())).thenReturn("srv-2");

        // Invoke the method to test
        notificationResource.setResponse(1L, "success", "option1");

        ResponseDTO expectedResponse = ResponseDTO.builder().status("success").key("option1").serverId("srv-2").build();
        ResponseDTO actualResponse = sessionNotificationResponses.get(1L);

        assertEquals(expectedResponse, actualResponse);
    }

    @Test
    public void setResponse_EmptyOptions_Success() throws GuacamoleException {
        doReturn("test-user").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CLOUD_USERNAME_PARM));
        doReturn("classroom").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CONNECTION_TYPE_PARAM));
        when(userContext.getSessionNotificationResponses()).thenReturn(sessionNotificationResponses);
        when(mockEnvironment.getRequiredProperty(any())).thenReturn("srv-2");

        // Invoke the method to test
        notificationResource.setResponse(1L, "success", "");

        ResponseDTO expectedResponse = ResponseDTO.builder().status("success").key("").serverId("").build();
        assertEquals(expectedResponse, sessionNotificationResponses.get(1L));
    }

    @Test
    public void setResponse_InvalidId_ThrowsException() {
        Exception thrownException = assertThrows(NullPointerException.class, () -> {
            notificationResource.setResponse(null, "success", "option1");
        });

        // Verify if the exception is really NullPointerException
        assertTrue(thrownException instanceof NullPointerException);
    }

    @Test
    public void getResponse_ValidId_Success() throws GuacamoleException {
        ResponseDTO testResponse = ResponseDTO.builder().status("success").build();
        sessionNotificationResponses.put(1L, testResponse);

        doReturn("test-user").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CLOUD_USERNAME_PARM));
        doReturn("classroom").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CONNECTION_TYPE_PARAM));
        when(userContext.getSessionNotificationResponses()).thenReturn(sessionNotificationResponses);;

        // Invoke the method to test
        ResponseDTO result = notificationResource.getResponse(1L);

        // Verify the behavior
        assertEquals(testResponse, result);
        assertEquals(0, sessionNotificationResponses.size());
    }

    @Test
    public void getResponse_InvalidId_ReturnsEmptyString() throws GuacamoleException {
        doReturn("test-user").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CLOUD_USERNAME_PARM));
        doReturn("classroom").when(notificationResource).getParameter(anyInt(), eq(EncurlParameters.CONNECTION_TYPE_PARAM));
        when(userContext.getSessionNotificationResponses()).thenReturn(sessionNotificationResponses);;

        // Invoke the method to test
        ResponseDTO result = notificationResource.getResponse(1L);

        // Verify the behavior
        assertEquals(null, result);
        assertEquals(0, sessionNotificationResponses.size());
    }
}