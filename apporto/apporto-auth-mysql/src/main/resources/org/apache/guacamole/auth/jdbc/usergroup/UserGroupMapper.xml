<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="org.apache.guacamole.auth.jdbc.usergroup.UserGroupMapper" >

    <!-- Result mapper for user group objects -->
    <resultMap id="UserGroupResultMap" type="org.apache.guacamole.auth.jdbc.usergroup.UserGroupModel" >

        <!-- User group properties -->
        <id     column="user_group_id" property="objectID"   jdbcType="INTEGER"/>
        <result column="entity_id"     property="entityID"   jdbcType="INTEGER"/>
        <result column="name"          property="identifier" jdbcType="VARCHAR"/>
        <result column="disabled"      property="disabled"   jdbcType="BOOLEAN"/>

        <!-- Arbitrary attributes -->
        <collection property="arbitraryAttributes" resultSet="arbitraryAttributes"
                    ofType="org.apache.guacamole.auth.jdbc.base.ArbitraryAttributeModel"
                    column="user_group_id" foreignColumn="user_group_id">
            <result property="name"     column="attribute_name"  jdbcType="VARCHAR"/>
            <result property="value"    column="attribute_value" jdbcType="VARCHAR"/>
        </collection>

    </resultMap>

    <!-- Select all group names -->
    <select id="selectIdentifiers" resultType="string">
        SELECT name
        FROM guacamole_entity
        WHERE guacamole_entity.type = 'USER_GROUP'
    </select>

    <!-- Select names of all readable groups -->
    <select id="selectReadableIdentifiers" resultType="string">
        SELECT guacamole_entity.name
        FROM guacamole_user_group
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        JOIN guacamole_user_group_permission ON affected_user_group_id = guacamole_user_group.user_group_id
        WHERE
            <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="guacamole_user_group_permission.entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND guacamole_entity.type = 'USER_GROUP'
            AND permission = 'READ'
    </select>

    <!-- Select multiple groups by name -->
    <select id="select" resultMap="UserGroupResultMap"
            resultSets="users,arbitraryAttributes">

        SELECT
            guacamole_user_group.user_group_id,
            guacamole_entity.entity_id,
            guacamole_entity.name,
            disabled
        FROM guacamole_user_group
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        WHERE guacamole_entity.name IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>
            AND guacamole_entity.type = 'USER_GROUP';

        SELECT
            guacamole_user_group_attribute.user_group_id,
            guacamole_user_group_attribute.attribute_name,
            guacamole_user_group_attribute.attribute_value
        FROM guacamole_user_group_attribute
        JOIN guacamole_user_group ON guacamole_user_group.user_group_id = guacamole_user_group_attribute.user_group_id
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        WHERE guacamole_entity.name IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>
            AND guacamole_entity.type = 'USER_GROUP';

    </select>

    <!-- Select multiple groups by name only if readable -->
    <select id="selectReadable" resultMap="UserGroupResultMap"
            resultSets="users,arbitraryAttributes">

        SELECT
            guacamole_user_group.user_group_id,
            guacamole_entity.entity_id,
            guacamole_entity.name,
            disabled
        FROM guacamole_user_group
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        JOIN guacamole_user_group_permission ON affected_user_group_id = guacamole_user_group.user_group_id
        WHERE guacamole_entity.name IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>
            AND guacamole_entity.type = 'USER_GROUP'
            AND <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="guacamole_user_group_permission.entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ';

        SELECT
            guacamole_user_group_attribute.user_group_id,
            guacamole_user_group_attribute.attribute_name,
            guacamole_user_group_attribute.attribute_value
        FROM guacamole_user_group_attribute
        JOIN guacamole_user_group ON guacamole_user_group.user_group_id = guacamole_user_group_attribute.user_group_id
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        JOIN guacamole_user_group_permission ON affected_user_group_id = guacamole_user_group.user_group_id
        WHERE guacamole_entity.name IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>
            AND guacamole_entity.type = 'USER_GROUP'
            AND <include refid="org.apache.guacamole.auth.jdbc.base.EntityMapper.isRelatedEntity">
                <property name="column"   value="guacamole_user_group_permission.entity_id"/>
                <property name="entityID" value="#{user.entityID,jdbcType=INTEGER}"/>
                <property name="groups"   value="effectiveGroups"/>
            </include>
            AND permission = 'READ';

    </select>

    <!-- Select single group by name -->
    <select id="selectOne" resultMap="UserGroupResultMap"
            resultSets="users,arbitraryAttributes">

        SELECT
            guacamole_user_group.user_group_id,
            guacamole_entity.entity_id,
            guacamole_entity.name,
            disabled
        FROM guacamole_user_group
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        WHERE
            guacamole_entity.name = #{name,jdbcType=VARCHAR}
            AND guacamole_entity.type = 'USER_GROUP';

        SELECT
            guacamole_user_group_attribute.user_group_id,
            guacamole_user_group_attribute.attribute_name,
            guacamole_user_group_attribute.attribute_value
        FROM guacamole_user_group_attribute
        JOIN guacamole_user_group ON guacamole_user_group.user_group_id = guacamole_user_group_attribute.user_group_id
        JOIN guacamole_entity ON guacamole_user_group.entity_id = guacamole_entity.entity_id
        WHERE
            guacamole_entity.name = #{name,jdbcType=VARCHAR}
            AND guacamole_entity.type = 'USER_GROUP'

    </select>

    <!-- Delete single group by name -->
    <delete id="delete">
        DELETE FROM guacamole_entity
        WHERE
            name = #{identifier,jdbcType=VARCHAR}
            AND type = 'USER_GROUP'
    </delete>

    <!-- Insert single group -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="object.objectID"
            parameterType="org.apache.guacamole.auth.jdbc.usergroup.UserGroupModel">

        INSERT INTO guacamole_user_group (
            entity_id,
            disabled
        )
        VALUES (
            #{object.entityID,jdbcType=VARCHAR},
            #{object.disabled,jdbcType=BOOLEAN}
        )

    </insert>

    <!-- Update single group -->
    <update id="update" parameterType="org.apache.guacamole.auth.jdbc.usergroup.UserGroupModel">
        UPDATE guacamole_user_group
        SET disabled = #{object.disabled,jdbcType=BOOLEAN}
        WHERE user_group_id = #{object.objectID,jdbcType=VARCHAR}
    </update>

    <!-- Delete attributes associated with group -->
    <delete id="deleteAttributes">
        DELETE FROM guacamole_user_group_attribute
        WHERE user_group_id = #{object.objectID,jdbcType=INTEGER}
    </delete>

    <!-- Insert attributes for group -->
    <insert id="insertAttributes" parameterType="org.apache.guacamole.auth.jdbc.base.ArbitraryAttributeModel">
        INSERT INTO guacamole_user_group_attribute (
            user_group_id,
            attribute_name,
            attribute_value
        )
        VALUES
            <foreach collection="object.arbitraryAttributes" item="attribute" separator=",">
                (#{object.objectID,jdbcType=INTEGER},
                 #{attribute.name,jdbcType=VARCHAR},
                 #{attribute.value,jdbcType=VARCHAR})
            </foreach>
    </insert>

</mapper>
