<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionParameterMapper">

    <!-- Result mapper for connection parameters -->
    <resultMap id="ParameterResultMap" type="org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel">
        <result column="connection_id"   property="connectionIdentifier" jdbcType="INTEGER"/>
        <result column="parameter_name"  property="name"                 jdbcType="VARCHAR"/>
        <result column="parameter_value" property="value"                jdbcType="VARCHAR"/>
    </resultMap>

    <!-- Select all parameters of a given connection -->
    <select id="select" resultMap="ParameterResultMap">
        SELECT
            connection_id,
            parameter_name,
            parameter_value
        FROM guacamole_connection_parameter
        WHERE
            connection_id = #{identifier,jdbcType=VARCHAR}
    </select>

    <!-- Delete all parameters of a given connection -->
    <delete id="delete">
        DELETE FROM guacamole_connection_parameter
        WHERE connection_id = #{identifier,jdbcType=VARCHAR}
    </delete>

    <!-- Insert all given parameters -->
    <insert id="insert" parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel">

        INSERT INTO guacamole_connection_parameter (
            connection_id,
            parameter_name,
            parameter_value
        )
        VALUES 
            <foreach collection="parameters" item="parameter" separator=",">
                (#{parameter.connectionIdentifier,jdbcType=VARCHAR},
                 #{parameter.name,jdbcType=VARCHAR},
                 #{parameter.value,jdbcType=VARCHAR})
            </foreach>

    </insert>

    <!-- Insert all given parameters -->
    <insert id="insertOrUpdate" parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel">

        INSERT INTO guacamole_connection_parameter (
            connection_id,
            parameter_name,
            parameter_value
        )
        VALUES 
            (#{parameter.connectionIdentifier,jdbcType=VARCHAR},
             #{parameter.name,jdbcType=VARCHAR},
             #{parameter.value,jdbcType=VARCHAR})
        ON DUPLICATE KEY UPDATE
            parameter_name = #{parameter.name,jdbcType=VARCHAR},
            parameter_value = #{parameter.value,jdbcType=VARCHAR}

    </insert>
</mapper>