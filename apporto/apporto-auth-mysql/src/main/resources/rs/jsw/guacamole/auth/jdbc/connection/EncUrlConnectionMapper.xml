<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->

<mapper namespace="rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionMapper" >

    <!-- Result mapper for connection objects -->
    <resultMap id="ConnectionResultMap" type="org.apache.guacamole.auth.jdbc.connection.ConnectionModel" >

        <!-- Connection properties -->
        <id     column="connection_id"            property="objectID"              jdbcType="INTEGER"/>
        <result column="connection_name"          property="name"                  jdbcType="VARCHAR"/>
        <result column="parent_id"                property="parentIdentifier"      jdbcType="INTEGER"/>
        <result column="protocol"                 property="protocol"              jdbcType="VARCHAR"/>
        <result column="max_connections"          property="maxConnections"        jdbcType="INTEGER"/>
        <result column="max_connections_per_user" property="maxConnectionsPerUser" jdbcType="INTEGER"/>
        <result column="proxy_hostname"           property="proxyHostname"         jdbcType="VARCHAR"/>
        <result column="proxy_port"               property="proxyPort"             jdbcType="INTEGER"/>
        <result column="proxy_encryption_method"  property="proxyEncryptionMethod" jdbcType="VARCHAR"
                javaType="org.apache.guacamole.net.auth.GuacamoleProxyConfiguration$EncryptionMethod"/>

        <!-- Associated sharing profiles -->
        <collection property="sharingProfileIdentifiers" resultSet="sharingProfiles" ofType="java.lang.String"
                    column="connection_id" foreignColumn="primary_connection_id">
            <result column="sharing_profile_id"/>
        </collection>

    </resultMap>

    <!-- Select all connection identifiers -->
    <select id="selectIdentifiers" resultType="string">
        SELECT connection_id 
        FROM guacamole_connection
    </select>

    <!-- Select identifiers of all readable connections -->
    <select id="selectReadableIdentifiers" resultType="string">
        SELECT connection_id
        FROM guacamole_connection_permission
        WHERE
            user_id = #{user.objectID,jdbcType=INTEGER}
            AND permission = 'READ'
    </select>

    <!-- Select all connection identifiers within a particular connection group -->
    <select id="selectIdentifiersWithin" resultType="string">
        SELECT connection_id 
        FROM guacamole_connection
        WHERE
            <if test="parentIdentifier != null">parent_id = #{parentIdentifier,jdbcType=VARCHAR}</if>
            <if test="parentIdentifier == null">parent_id IS NULL</if>
    </select>

    <!-- Select identifiers of all readable connections within a particular connection group -->
    <select id="selectReadableIdentifiersWithin" resultType="string">
        SELECT guacamole_connection.connection_id
        FROM guacamole_connection
        JOIN guacamole_connection_permission ON guacamole_connection_permission.connection_id = guacamole_connection.connection_id
        WHERE
            <if test="parentIdentifier != null">parent_id = #{parentIdentifier,jdbcType=VARCHAR}</if>
            <if test="parentIdentifier == null">parent_id IS NULL</if>
            AND user_id = #{user.objectID,jdbcType=INTEGER}
            AND permission = 'READ'
    </select>

    <!-- Select multiple connections by identifier -->
    <select id="select" resultMap="ConnectionResultMap"
            resultSets="connections,sharingProfiles">

        SELECT
            connection_id,
            connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user
        FROM guacamole_connection
        WHERE connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>;

        SELECT primary_connection_id, sharing_profile_id
        FROM guacamole_sharing_profile
        WHERE primary_connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>;

    </select>

    <!-- Select multiple connections by identifier only if readable -->
    <select id="selectReadable" resultMap="ConnectionResultMap"
            resultSets="connections,sharingProfiles">

        SELECT
            guacamole_connection.connection_id,
            connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user
        FROM guacamole_connection
        JOIN guacamole_connection_permission ON guacamole_connection_permission.connection_id = guacamole_connection.connection_id
        WHERE guacamole_connection.connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>
            AND user_id = #{user.objectID,jdbcType=INTEGER}
            AND permission = 'READ';

        SELECT primary_connection_id, guacamole_sharing_profile.sharing_profile_id
        FROM guacamole_sharing_profile
        JOIN guacamole_sharing_profile_permission ON guacamole_sharing_profile_permission.sharing_profile_id = guacamole_sharing_profile.sharing_profile_id
        WHERE primary_connection_id IN
            <foreach collection="identifiers" item="identifier"
                     open="(" separator="," close=")">
                #{identifier,jdbcType=VARCHAR}
            </foreach>
            AND user_id = #{user.objectID,jdbcType=INTEGER}
            AND permission = 'READ';

    </select>

    <!-- Select single connection by name -->
    <select id="selectOneByName" resultMap="ConnectionResultMap">

        SELECT
            connection_id,
            connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user
        FROM guacamole_connection
        WHERE 
            <if test="parentIdentifier != null">parent_id = #{parentIdentifier,jdbcType=VARCHAR}</if>
            <if test="parentIdentifier == null">parent_id IS NULL</if>
            AND connection_name = #{name,jdbcType=VARCHAR}

    </select>

    <!-- Delete single connection by identifier -->
    <delete id="delete">
        DELETE FROM guacamole_connection
        WHERE connection_id = #{identifier,jdbcType=VARCHAR}
    </delete>

    <!-- Insert single connection -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="object.objectID"
            parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionModel">

        INSERT INTO guacamole_connection (
            connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user
        )
        VALUES (
            #{object.name,jdbcType=VARCHAR},
            #{object.parentIdentifier,jdbcType=VARCHAR},
            #{object.protocol,jdbcType=VARCHAR},
            #{object.maxConnections,jdbcType=INTEGER},
            #{object.maxConnectionsPerUser,jdbcType=INTEGER}
        )

    </insert>

    <!-- Insert single connection without key generation -->
    <insert id="insertWithKey" parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionModel">

        INSERT INTO guacamole_connection (
            connection_id,
            connection_name,
            parent_id,
            protocol,
            max_connections,
            max_connections_per_user
        )
        VALUES (
            #{object.objectID,jdbcType=INTEGER},
            #{object.name,jdbcType=VARCHAR},
            #{object.parentIdentifier,jdbcType=VARCHAR},
            #{object.protocol,jdbcType=VARCHAR},
            #{object.maxConnections,jdbcType=INTEGER},
            #{object.maxConnectionsPerUser,jdbcType=INTEGER}
        )
        ON DUPLICATE KEY UPDATE
            connection_name = #{object.name,jdbcType=VARCHAR},
            parent_id = #{object.parentIdentifier,jdbcType=VARCHAR},
            protocol = #{object.protocol,jdbcType=VARCHAR},
            max_connections = #{object.maxConnections,jdbcType=INTEGER},
            max_connections_per_user = #{object.maxConnectionsPerUser,jdbcType=INTEGER}

    </insert>

    <!-- Update single connection -->
    <update id="update" parameterType="org.apache.guacamole.auth.jdbc.connection.ConnectionModel">
        UPDATE guacamole_connection
        SET connection_name          = #{object.name,jdbcType=VARCHAR},
            parent_id                = #{object.parentIdentifier,jdbcType=VARCHAR},
            protocol                 = #{object.protocol,jdbcType=VARCHAR},
            max_connections          = #{object.maxConnections,jdbcType=INTEGER},
            max_connections_per_user = #{object.maxConnectionsPerUser,jdbcType=INTEGER}
        WHERE connection_id = #{object.objectID,jdbcType=INTEGER}
    </update>

</mapper>