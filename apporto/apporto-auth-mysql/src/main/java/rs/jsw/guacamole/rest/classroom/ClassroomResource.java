/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.sharing.SharedConnectionMap;
import org.apache.guacamole.auth.jdbc.sharing.connection.SharedConnectionDefinition;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.SendStatusData;
import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.auth.jdbc.user.UserSessionClassroom;
import rs.jsw.guacamole.net.encryptedurl.jdbc.ApportoSharedConnectionMap;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

/**
 * A REST Service for providing users data to virtual classroom.
 *
 * <AUTHOR> Babic
 */
public class ClassroomResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(ClassroomResource.class);

    @Inject
    private SharedConnectionMap connectionMap;

    @AssistedInject
    public ClassroomResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    @GET
    @Path("getFullName")
    public Response getFullName(@QueryParam("id") Integer id) throws GuacamoleException {

        JSONObject jsonData = null;
        if (userContext instanceof UserSessionClassroom) {
            jsonData = ((UserSessionClassroom) userContext).getUserData(id);
        }

        if (jsonData == null) {
            return Response.ok("User")
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        String name = jsonData.optString("firstname");
        String lastname = jsonData.optString("lastname");
        String email = jsonData.optString("email");

        String fullname = "";

        if (name != null && !name.isEmpty()) {
            fullname = name;
            if (lastname != null && !lastname.isEmpty()) {
                fullname = fullname + " " + lastname;
            }
        }

        if (fullname.isEmpty()) {
            fullname = email;
        }

        JSONObject result = new JSONObject();
        result.put("full_name", fullname);
        return Response.ok(result.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Content-Type", "application/json")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @POST
    @Path("raisehand")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Response setResponse(@QueryParam("id") Long id) throws GuacamoleException {
        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] RaisedHand.", conn_id, conn_type, cloud_user);

        ((UserSessionClassroom) userContext).getRaisedHands().put(id, "ON");

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @GET
    @Path("getraisedhands")
    public Response getRaisedHands(@QueryParam("id") Long id) throws GuacamoleException {
        Set<Long> raisedHands = new HashSet<>();
        raisedHands.addAll(((UserSessionClassroom) userContext).getRaisedHands().keySet());

        return Response.ok(raisedHands)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @DELETE
    @Path("removehand")
    public Response removeRaisedHand(@QueryParam("id") Long id) throws GuacamoleException {
        ((UserSessionClassroom) userContext).getRaisedHands().remove(id);

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @GET
    @Path("checkhand")
    public Response checkHand(@QueryParam("id") Integer id) throws GuacamoleException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        Long idl = Long.valueOf(id.longValue());
        String result = ((UserSessionClassroom) userContext).getRaisedHands().get(idl);
        logger.info("[{}:{}:{}] checkHand ID: {}, result: {}", conn_id, conn_type, cloud_user, id, result);

        if (result == null) {
            return Response.ok("REMOVED")
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }
        else {
            return Response.ok("RAISED")
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }
    }

    /**
     * Retrieve groups for passed id.
     *
     * @param id
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/getGroups")
    @GET
    @Consumes("text/plain")
    public Response getGroups(@QueryParam("id") Long id) throws GuacamoleException, UnknownHostException {
        JSONArray groups = null;

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        if (userContext instanceof UserSessionClassroom) {
            groups = ((UserSessionClassroom) userContext).getUserGroups(id);
        }

        if (groups != null && !groups.isEmpty()) {
            return Response.ok(groups.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject error = new JSONObject();
        error.put("Message", "Classroom: Invalid groups data");
        logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
        return Response.status(Response.Status.BAD_REQUEST)
                       .entity(error.toString())
                       .build();
    }

    /**
     * Retrieve permission groups for passed id.
     *
     * @param id
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/getPermissionGroups")
    @GET
    @Consumes("text/plain")
    public Response getPermissionGroups(@QueryParam("id") Long id) throws GuacamoleException, UnknownHostException {
        JSONArray permissionGroups = null;

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        if (userContext instanceof UserSessionClassroom) {
            permissionGroups = ((UserSessionClassroom) userContext).getUserPermissionGroups(id);
        }

        // If userData was set by calling user_details Drupal API
        if (permissionGroups != null && !permissionGroups.isEmpty()) {
            return Response.ok(permissionGroups.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject error = new JSONObject();
        error.put("Message", "Classroom: Invalid permission groups data");
        logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
        return Response.status(Response.Status.BAD_REQUEST)
                       .entity(error.toString())
                       .build();
    }

    /**
     * Retrieve groups for passed id.
     *
     * @param id
     * @param group
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/close")
    @POST
    @Consumes("text/plain")
    public Response close(@QueryParam("id") Integer id, @QueryParam("group") String group,
                          @QueryParam("appname") String appname)
                          throws GuacamoleException, UnknownHostException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] Classroom closed: group={}, appname={}",
                    conn_id, conn_type, cloud_user, group, appname);

        if (userContext instanceof UserSessionClassroom) {
            String key = Integer.toString(id) + "=>" + group + "=>" + appname;
            ((UserSessionClassroom) userContext).getDisplayedInClassroom().remove(key);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Retrieves
     *
     * @param request - Servlet request
     * @param id - Guacamole session ID
     * @return - thumbnail if exists or Status.NO_CONTENT.
     * @throws GuacamoleException
     */
    @Path("/retrieveThumbnail")
    @GET
    @Consumes("text/plain")
    public Response retrieveThumbnail(@Context HttpServletRequest request, @QueryParam("srv_id") String srv_id,
                                      @QueryParam("list") final List<Long> list)
            throws GuacamoleException {

        logger.debug("Thumbnail retrieval for current classroom page started");

        Map<Long, ThumbnailData> thumbnails = ((ApportoUserContext) userContext).getThumbs();
        Map<Long, byte[]> thumbs = new HashMap<>();

        for (Long id : list) {
            ThumbnailData thumb = thumbnails.get(id);
            if (thumb != null && thumb.isComplete())
                thumbs.put(id, thumb.getThumbnailContent());
        }

        JSONObject result = new JSONObject();
        result.put("server_id", srv_id);
        result.put("data", thumbs);

        return Response.ok(result.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Retrieve active connections for one customer instance, on one server.
     *
     * @param id - professor session id
     * @param group - group displayed on professor screen
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/getConnections")
    @GET
    @Consumes("text/plain")
    public Response getConnections(@QueryParam("id") Integer id, @QueryParam("group") String group,
                                   @QueryParam("appname") String appname)
                                   throws GuacamoleException, UnknownHostException {

        // Get the professor connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.debug("[{}:{}:{}] getConnections:session_id={}, group={}, appname={}",
                     conn_id, conn_type, cloud_user, conn_id, group, appname);

        String subdomain = getParameter(id, EncurlParameters.SUBDOMAIN_PARM);
        if (subdomain.contains(".")) {
            subdomain = subdomain.substring(0, subdomain.indexOf("."));
        }
        logger.debug("[{}:{}:{}] Subdomain = {}", conn_id, conn_type, cloud_user, subdomain);
        // End professor connection info


        // Get all shared connections on this server.
        ConcurrentMap<String, SharedConnectionDefinition> mp = ((ApportoSharedConnectionMap) connectionMap)
                .getAllConnections();

        /**
         * Structure for response:
         *
         * KEY: shared_key value VALUE: ClassroomData
         */
        Map<String, ClassroomData> sharedKeys = new HashMap<String, ClassroomData>();

        /**
         * Structure for storing API data:
         *
         * KEY: sessionId VALUE: JSON data
         */
        Map<String, JSONObject> drupalData = new HashMap<String, JSONObject>();

        /**
         * Saving ids for new active users
         */
        List<Long> newKeys = new ArrayList<>();

        /**
         * The list of already displayed sessions for the user with ID Here
         * remains only ids for killed sessions
         */
        List<ClassroomData> displayed = new ArrayList<>();
        String key = Long.toString(id.longValue()) + "=>" + group + "=>" + appname;
        if (userContext instanceof UserSessionClassroom) {
            displayed = ((UserSessionClassroom) userContext).getDisplayedInClassroom(key);
        }

        List<Long> displayedId = new ArrayList<>();
        if (displayed != null) {
            for (ClassroomData cd : displayed) {
                logger.debug("[{}:{}:{}] Displayed sessions: {}", conn_id, conn_type, cloud_user, cd.toString());
                displayedId.add(Long.parseLong(cd.getSession_id()));
            }
        }

        /**
         * The list of the shared keys in all the connections
         */
        List<String> entryKeys = new ArrayList<>();
        for (Map.Entry<String, SharedConnectionDefinition> entry : mp.entrySet()) {
            if (!entry.getKey().isEmpty()) {
                entryKeys.add(entry.getKey());
            }
        }

        /**
         * Iterate over all sharing profiles. Get parent identifier (or identifier of the "master" connection where sharing
         * profile exists). Check if this ID is already displayed, if not, add it.
         */
        for (Map.Entry<String, SharedConnectionDefinition> entry : mp.entrySet()) {

            String sessionId = entry.getValue().getSharingProfile().getModel().getParentIdentifier();

            // don't save data for current session - skip the professor session
            if (Integer.parseInt(sessionId) == id.intValue()) {
                continue;
            }

            logger.debug("[{}:{}:{}] SESSION ID: {}, SHARING PROFILE: {}", conn_id, conn_type, cloud_user, sessionId,
                        entry.getValue().getSharingProfile().getName());

            if (!newKeys.contains(Long.parseLong(sessionId))) {
                newKeys.add(Long.parseLong(sessionId));
                logger.debug("[{}:{}:{}] NEW KEYS {}", conn_id, conn_type, cloud_user, sessionId);
            }

            // don't save data for mm shared links
            if (entry.getValue().getSharingProfile().getName().equals("share-MM")
                    || entry.getValue().getSharingProfile().getName().equals("share-MM-3")) {
                continue;
            }

            String userSubdomain = "";

            userSubdomain = getParameter(Integer.parseInt(sessionId), EncurlParameters.SUBDOMAIN_PARM);
            if (userSubdomain == null) {
                logger.error("[{}:{}:{}] Parent subdomain is empty for session id {}", conn_id, conn_type, cloud_user, sessionId);
                continue;
            }

            if (userSubdomain.contains(".")) {
                userSubdomain = userSubdomain.substring(0, userSubdomain.indexOf("."));
            }

            // group users by sub domain info - this means to skip all users from another subdomain - professor can view only students from its domain.
            if (subdomain == null || !subdomain.equals(userSubdomain)) {
                continue;
            }

            // don't save data if they are already provided
            /**
             * Check if session ID exists in an array of IDs that are visible in professor screen.
             * If ID is on professor screen, get sharing keys for that ID.
             * 
             * If the sharing keys (both VO and FC) for that session ID still exists in displayed sessions, all is ok.
             * If the sharing keys for that session ID do not exists anymore in displayed sessions, the session is probably
             * reloaded, so the session ID is still the same, but the sharing keys have changed. Session with invalid sharing
             * keys is removed from the list of displayed IDs.
             * 
             * This block of code essentially check if the sharing keys for the session should be updated.
             * 
             */
            if (displayed.size() > 0 && displayedId.contains(Long.parseLong(sessionId))) {
                int index = displayedId.indexOf(Long.parseLong(sessionId));
                String vo_key = displayed.get(index).getView_only_key();
                String fc_key = displayed.get(index).getFull_controll_key();

                if (entryKeys.contains(vo_key) && entryKeys.contains(fc_key)) {
                    logger.debug("[{}:{}:{}] The session id {} is already displayed in the virtual classroom.",
                                conn_id, conn_type, cloud_user, sessionId);
                    continue;
                }
                else {
                    displayed.remove(index);
                    displayedId.remove(index);
                    logger.error("[{}:{}:{}] Because the shared key of the session id {} doesn't exist in " +
                                 "the connection map, this session was removed.",
                                 conn_id, conn_type, cloud_user, sessionId);
                }
            }

            /**
             * Skip all empty shared keys.
             */
            GuacamoleConfiguration configuration = getConfiguration(Integer.parseInt(sessionId));
            String username = configuration.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);
            String profileName = entry.getValue().getSharingProfile().getName();
            String sharedKey = entry.getKey();

            if (sharedKey.isEmpty()) {
                logger.error("[{}:{}:{}] Shared key is empty for session id {}, user {}",
                             conn_id, conn_type, cloud_user, sessionId, username);
                continue;
            }

            JSONObject jsonData = null;
            if (userContext != null && userContext instanceof UserSessionClassroom) {
                jsonData = ((UserSessionClassroom) userContext).getUserData(Long.parseLong(sessionId));
            }

            if (jsonData == null || jsonData.isEmpty()) {
                logger.error("[{}:{}:{}] Classroom data are empty for session id {}", conn_id, conn_type, cloud_user, sessionId);
                continue;
            }

            /**
             * Call of API to get first name, last name and groups for the current session
             */
            if (!drupalData.containsKey(sessionId)) {
                drupalData.put(sessionId, jsonData);
            }
            else {
                jsonData = drupalData.get(sessionId);
            }
            logger.debug("[{}:{}:{}] jsonData={}", conn_id, conn_type, cloud_user, jsonData.toString());

            if (!appname.equals(jsonData.optString("appname"))) {
                logger.debug("[{}:{}:{}] The session id {} is already displayed in the virtual classroom of \"{}\" class.",
                            conn_id, conn_type, cloud_user, sessionId, appname);
                continue;
            }

            String name = jsonData.optString("firstname");
            String lastname = jsonData.optString("lastname");
            String email = jsonData.optString("email");


            GuacamoleTunnel tunnel = entry.getValue().getActiveConnection().getTunnel();
            if (tunnel != null && tunnel.isOpen()) {
                boolean is_VO = profileName.equals("share-VO");

                ClassroomData profile;
                if (sharedKeys.containsKey(sessionId)) {
                    profile = sharedKeys.get(sessionId);
                }
                else {
                    profile = new ClassroomData();
                }

                if (is_VO) {
                    profile.setView_only_key(sharedKey);
                }
                else {
                    profile.setFull_controll_key(sharedKey);
                }

                String fullname = "";

                if (name != null) {
                    fullname = name;
                    if (lastname != null && !lastname.isEmpty()) {
                        fullname = fullname + " " + lastname;
                    }
                }

                if (fullname.isEmpty()) {
                    fullname = email;
                }

                profile.setUsername(fullname);
                profile.setWindows_name(username);
                profile.setEmail(email);

                JSONArray groups = jsonData.optJSONArray("groups");
                if (groups != null) {
                    profile.setGroups(groups.toString());
                }

                JSONArray permissionGroups = jsonData.optJSONArray("permission-groups");
                if (permissionGroups != null) {
                    profile.setPermissionGroups(permissionGroups.toString());
                }

                Environment environment = new LocalEnvironment();
                profile.setServer_id(environment.getRequiredProperty(ApportoProperties.SERVER_ID));

                // find thumbnail for the user with current id
                Map<Long, ThumbnailData> thumbnails = ((ApportoUserContext) userContext).getThumbs();
                Long lSessionId = Long.parseLong(sessionId);

                if (thumbnails.containsKey(lSessionId)) {
                    ThumbnailData thumb = thumbnails.get(lSessionId);
                    if (thumb.isComplete())
                        profile.setThumbnail(thumb.getThumbnailContent());
                }
                profile.setSession_id(sessionId);

                sharedKeys.put(sessionId, profile);

                if (!profile.getView_only_key().isEmpty() && !profile.getFull_controll_key().isEmpty() && !displayed.contains(profile)) {
                    displayed.add(profile);
                    logger.debug("[{}:{}:{}] ADDED ID: {}", conn_id, conn_type, cloud_user, sessionId);
                }

            }

        }

        // newKeys => All currently active keys
        // displayId => Previously added users
        // displayed => Union of newKeys and displayedId
        // sharedKeys => New found active users
        // If all displayed sessions are at the same time active sessions
        // displayedId will be empty, and next condition won't be true
        displayedId.removeAll(newKeys);
        if (displayedId.size() > 0) {
            Iterator<ClassroomData> iter = displayed.iterator();
            while (iter.hasNext()) {
                ClassroomData data = iter.next();
                if (displayedId.contains(Long.parseLong(data.getSession_id()))) {
                    logger.error("[{}:{}:{}] FOR REMOVING: {}", conn_id, conn_type, cloud_user, displayedId);

                    ClassroomData forRemoving = data.toBuilder().removed(true).build();

                    sharedKeys.put(data.getSession_id(), forRemoving);
                    iter.remove();
                    ((UserSessionClassroom) userContext).removeDisplayedInClassroom(key, forRemoving);
                }
            }
        }

        /**
         * At the end, sharedKeys contains new users and users that have to be removed.
         */
        return Response.ok(sharedKeys)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Retrieve active connections for one customer instance, on one server.
     *
     * @param studentId
     * @param id
     * @param group
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/getConnections/{studentId}")
    @GET
    @Consumes("text/plain")
    public Response getStudentConnection(@PathParam("studentId") String studentId, @QueryParam("id") Integer id,
                                         @QueryParam("group") String group, @QueryParam("appname") String appname)
                                         throws GuacamoleException, UnknownHostException {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] getStudentConnection: group={}", conn_id, conn_type, cloud_user , group);

        ConcurrentMap<String, SharedConnectionDefinition> mp = ((ApportoSharedConnectionMap) connectionMap)
                .getAllConnections();

        /**
         * Structure for response:
         *
         * KEY: key name value VALUE: shared_key
         */
        Map<String, String> sharedKeys = new HashMap<String, String>();

        /**
         * The list of already displayed sessions for the user with ID Here
         * remains only ids for killed sessions
         */
        List<ClassroomData> displayed = new ArrayList<>();
        String key = Long.toString(id.longValue()) + "=>" + group + "=>" + appname;
        if (userContext instanceof UserSessionClassroom) {
            displayed = ((UserSessionClassroom) userContext).getDisplayedInClassroom(key);
        }

        List<Long> displayedId = new ArrayList<>();
        if (displayed != null) {
            for (ClassroomData cd : displayed) {
                Map<Long, ThumbnailData> thumbnails = ((ApportoUserContext) userContext).getThumbs();
                Long lSessionId = Long.parseLong(cd.getSession_id());

                if (thumbnails.containsKey(lSessionId)) {
                    ThumbnailData thumb = thumbnails.get(lSessionId);
                    if (thumb.isComplete())
                        cd.setThumbnail(thumb.getThumbnailContent());
                }

                logger.info("[{}:{}:{}] getStudentConnection: Student Info: {}", conn_id, conn_type, cloud_user, cd.toString());
                displayedId.add(Long.parseLong(cd.getSession_id()));
            }
        }

        /**
         * The list of the shared keys in all the connections
         */
        List<String> entryKeys = new ArrayList<>();
        for (Map.Entry<String, SharedConnectionDefinition> entry : mp.entrySet()) {
            if (!entry.getKey().isEmpty()) {
                entryKeys.add(entry.getKey());
            }
        }

        // don't save data if they are already provided
        if (displayed.size() > 0 && displayedId.contains(Long.parseLong(studentId))) {
            int index = displayedId.indexOf(Long.parseLong(studentId));
            String vo_key = displayed.get(index).getView_only_key();
            String fc_key = displayed.get(index).getFull_controll_key();

            if (entryKeys.contains(vo_key) && entryKeys.contains(fc_key)) {
                logger.info("[{}:{}:{}] The session id {} is already displayed in the virtual classroom.",
                            conn_id, conn_type, cloud_user, studentId);
                sharedKeys.put("view_only_key", vo_key);
                sharedKeys.put("full_controll_key", fc_key);
            }
            else {
                for (Map.Entry<String, SharedConnectionDefinition> entry : mp.entrySet()) {

                    String sessionId = entry.getValue().getSharingProfile().getModel().getParentIdentifier();

                    // save data for current student
                    if (!sessionId.equals(studentId)) {
                        continue;
                    }

                    String sharedKey = entry.getKey();
                    String profileName = entry.getValue().getSharingProfile().getName();
                    String username = entry.getValue().getSharingProfile().getCurrentUser().getCredentials().getUsername();

                    // don't save data for mm shared links
                    if (profileName.equals("share-MM") || profileName.equals("share-MM-3")) {
                        continue;
                    }

                    if (sharedKey.isEmpty()) {
                        logger.error("[{}:{}:{}] Shared key is empty for session id {}, user {}",
                                     conn_id, conn_type, cloud_user, sessionId, username);
                        continue;
                    }

                    GuacamoleTunnel tunnel = entry.getValue().getActiveConnection().getTunnel();

                    // Set sharedKeys if tunnel is not null and open
                    if (tunnel != null && tunnel.isOpen()) {
                        if (!entryKeys.contains(fc_key) && profileName.equals("share-FC")) {
                            sharedKeys.put("full_controll_key", sharedKey);
                        }

                        if (!entryKeys.contains(vo_key) && profileName.equals("share-VO")) {
                            sharedKeys.put("view_only_key", sharedKey);
                        }
                    }

                    if (sharedKeys.get("full_controll_key") != null && sharedKeys.get("view_only_key") != null) {
                        break;
                    }
                }
            }
        }

        return Response.ok(sharedKeys)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @POST
    @Path("startshare")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public Response setStartShare(@QueryParam("id") Long id) throws GuacamoleException {
        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.debug("[{}:{}:{}] StartShare.", conn_id, conn_type, cloud_user);

        ((UserSessionClassroom) userContext).getShareStatus().put(id, "ON");

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @GET
    @Path("getsharestatus")
    public Response getShareStatus(@QueryParam("id") Long id) throws GuacamoleException {
        Set<Long> shareStatus = new HashSet<>();
        shareStatus.addAll(((UserSessionClassroom) userContext).getShareStatus().keySet());

        return Response.ok(shareStatus)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @POST
    @Path("stopshare")
    public Response stopShare(@QueryParam("id") Long id) throws GuacamoleException {
        ((UserSessionClassroom) userContext).getShareStatus().remove(id);

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @GET
    @Path("checkshare")
    public Response checkShare(@QueryParam("id") Integer id) throws GuacamoleException {
        Long idl = Long.valueOf(id.longValue());
        String result = ((UserSessionClassroom) userContext).getShareStatus().get(idl);

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.debug("[{}:{}:{}] result: {}", conn_id, conn_type, cloud_user, result);

        if (result == null) {
            return Response.ok("STOPPED")
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }
        else {
            return Response.ok("STARTED")
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }
    }

    /**
     * Retrieve git version
     *
     * @throws GuacamoleException
     */
    @GET
    @Path("getVersion")
    public Response getVersion(@QueryParam("id") Integer id) throws GuacamoleException {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] getVersion", conn_id, conn_type, cloud_user);

        // Read the git properties
        JSONObject json = readGitProperties(conn_id, conn_type, cloud_user);

        JSONObject result = new JSONObject();
        result.put("version", json.optString("git.commit.id.abbrev"));
        return Response.ok(result.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Content-Type", "application/json")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Retrieve permission groups for passed id.
     *
     * @param id
     * @param cloudUserName
     * @param groupName
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @GET
    @Path("/getClassroomGroupsData")
    public Response getClassroomGroupsData(@QueryParam("id") Integer id, @QueryParam("cloud_username") String cloudUserName,
                                           @QueryParam("group_name") String groupName)
                                           throws GuacamoleException, UnknownHostException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = cloudUserName;
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Classroom launched: group={}", conn_id, conn_type, cloud_user, groupName);

        GuacamoleConfiguration configuration = getConfiguration(id);
        String data = SendStatusData.getClassroomGroupData(configuration, cloudUserName, groupName);
        JSONObject jsonData = new JSONObject(data);
        return Response.ok(jsonData.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }
}
