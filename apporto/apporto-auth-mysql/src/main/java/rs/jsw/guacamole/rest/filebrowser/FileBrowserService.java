/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest.filebrowser;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.ws.rs.core.NewCookie;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.LocalEnvironment;
import org.json.JSONObject;

import com.apporto.GuacamoleCommonUtility;
import com.apporto.hyperstream.core.ApportoProperties;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileBrowserService {

    private static final String DEFAULT_FILE_BROWSER_SERVER = "%s-filebrowser.apporto.com";

    private static final String FILE_BROWSER_TOKEN_URL = "https://%s/jwt/generate-token";

    private static final String FILE_BROWSER_LOGOUT_URL = "https://%s/api/logout";

    private static final String FILE_BROWSER_URL_PARAMS = "locale=%s&username=%s&password=%s&OU=%s&OS=%s";

    private static final String FILE_BROWSER_COOKIE_NAME = "filebrowser-sticky-cookie";
 
    private static final String FILE_BROWSER_TOKEN_NAME = "token";

    private static Map<String, NewCookie> fileBrowserCookieJar = new HashMap<>();

    /**
     * Retrieves file browser URL from the JWT generator.
     * 
     * @param username
     * @param password
     * @param OU - organization unit
     * @param OS - operating system
     * @param isUpload - is upload allowed
     * @param isDownload - is download allowed
     * @hostname - hostname where user data is stored (not used at the moment)
     * 
     * @return url and cookie to the file browser application with embedded token
     * 
     * @throws MalformedURLException
     * @throws IOException
     * @throws GuacamoleException
     */
    public FBUrlData getUrl(String username, String password, String OU, String OS, Boolean isUpload, Boolean isDownload, String hostname)
            throws MalformedURLException, IOException, GuacamoleException {
        HttpURLConnection connection = createConnection();

        try (OutputStreamWriter writer = new OutputStreamWriter(connection.getOutputStream())) {
            String requestBody = buildRequestBody(username, password, OU, OS, isUpload, isDownload);
            writer.write(requestBody);
            writer.flush();
        }

        int responseCode = connection.getResponseCode();
        String responseContent = getResponseMessage(connection, responseCode);
        FBUrlData fbUrlData = new FBUrlData();

        if (isSuccessfulResponse(responseCode)) {
            JSONObject jsonUrl = new JSONObject(responseContent);
            fbUrlData.setUrl(jsonUrl.getString("link"));

            NewCookie stickyCookie = extractCookie(connection, FILE_BROWSER_COOKIE_NAME);
            if (stickyCookie != null) {
                logger.info("Found sticky cookie filebrowser-sticky-cookie in response headers.");
            } else {
                logger.warn("Sticky cookie filebrowser-sticky-cookie not found in response headers.");
            }
            fbUrlData.setFileBrowserCookie(stickyCookie);

            putIntoCookieJar(fbUrlData);

            return fbUrlData;
        }
        else {
            logger.error(responseContent);
            return null;
        }
    }

    /**
     * Returns response message from URL call. Depending on the response code, returns
     * message from either input or error stream.
     * 
     * @param conn - http connection
     * @param responseCode - response code from server
     * @return response message
     * 
     * @throws IOException
     */
    private String getResponseMessage(HttpURLConnection connection, int responseCode) throws IOException {
        try (InputStream responseStream = isSuccessfulResponse(responseCode) 
                ? connection.getInputStream() 
                : connection.getErrorStream();
             BufferedReader reader = new BufferedReader(new InputStreamReader(responseStream))) {

            return reader.lines()
                         .collect(Collectors.joining(System.lineSeparator()));
        }
    }

    /**
     * Builds the request body for the file browser API.
     */
    private String buildRequestBody(String username, String password, String organizationalUnit, 
                                    String operatingSystem, boolean isUpload, boolean isDownload) {
        StringBuilder requestBody = new StringBuilder();
        requestBody.append(String.format(FILE_BROWSER_URL_PARAMS, "en", username, password, organizationalUnit, operatingSystem));
        if (isUpload) {
            requestBody.append("&isUpload=on");
        }
        if (isDownload) {
            requestBody.append("&isDownload=on");
        }
        return requestBody.toString();
    }

    /**
     * Extracts a specific cookie from the response headers.
     * @throws GuacamoleException 
     */
    private NewCookie extractCookie(HttpURLConnection connection, String cookieName) throws GuacamoleException {
        String cookieValue = null;

        List<String> cookies = connection.getHeaderFields().get("Set-Cookie");
        if (cookies != null) {
            for (String cookie : cookies) {
                if (cookie.startsWith(cookieName + "=")) {
                    cookieValue = cookie.substring(cookieName.length() + 1).split(";", 2)[0]; // Extract only the "value" part
                    break;
                }
            }
        }

        NewCookie fbCookie = null;
        if (cookieValue != null) {
            fbCookie = new NewCookie(
                    FILE_BROWSER_COOKIE_NAME,
                    cookieValue,
                    "/",
                    "." + GuacamoleCommonUtility.removeHostName(getFBServerName()),
                    "FileBrowser cookie",
                    3600,
                    true
            );
        }
            
        return fbCookie;
    }

    /**
     * Checks if the response code indicates a successful response.
     */
    private boolean isSuccessfulResponse(int responseCode) {
        return responseCode / 100 == 2;
    }

    /**
     * Puts the file browser cookie into the cookie jar.
     * 
     * @param fbUrlData
     */
    private void putIntoCookieJar(FBUrlData fbUrlData) {
        URI uri;
        String token = null;

        try {
            uri = new URI(fbUrlData.getUrl());
            String query = uri.getQuery(); // Dobijamo query string iz URL-a

            token = Optional.ofNullable(query)
                    .map(q -> Arrays.stream(query.split("&"))) // Split query on pairs "key=value"
                    .orElseGet(Stream::empty) // If query is null, return empty stream
                    .map(param -> param.split("=", 2)) // Split pairs on key and value
                    .filter(keyValue -> keyValue.length == 2 && keyValue[0].equalsIgnoreCase(FILE_BROWSER_TOKEN_NAME)) // Filter by token name
                    .map(keyValue -> keyValue[1]) // Take token value
                    .findFirst() // Return first rezult
                    .orElse(""); // If parameter does not exists, return null.
        } catch (URISyntaxException e) {
            logger.warn("No cookie will be stored into jar. URL is not valid: {}", fbUrlData.getUrl());
        }

        if (token != null)
            fileBrowserCookieJar.put(token, fbUrlData.getFileBrowserCookie());
        else
            logger.warn("No cookie will be stored into jar. URL is not valid: {}", fbUrlData.getUrl());
    }

    /**
     * Creates https connection to the file browser server.
     * 
     * @return url
     * @throws GuacamoleException
     * @throws IOException
     */
    public HttpURLConnection createConnection() throws MalformedURLException, GuacamoleException, IOException {
        //TODO - this method should be private, but then it cannot be mocked in unit tests. Find a better solution,
        //       maybe inject connection or URL object into the class and avoid using new.
        //       If injected, the object can be mocked with Mockito and this method can be private.
        LocalEnvironment environment = new LocalEnvironment();

        String fileBrowserServer = getFBServerName();

        // Get file browser API key from guacamole.properties.
        String fileBrowserApiKey = environment.getProperty(ApportoProperties.FILEBROWSER_API_KEY);

        URL url = new URL(String.format(FILE_BROWSER_TOKEN_URL, fileBrowserServer));

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setRequestProperty("x-api-key", fileBrowserApiKey);
        
        return conn;
    }

    /**
     * Try to get filebrowser server name. If it is not configured in guacamole.properties,
     * assume it is in the format <region>-filebrowser.apporto.com.
     * 
     * If region is not configured, give up and throw exception.
     */
    private String getFBServerName() throws GuacamoleException {
        LocalEnvironment environment = new LocalEnvironment();

        String fileBrowserServer = null;
        try {
            fileBrowserServer = environment.getProperty(ApportoProperties.FILEBROWSER_SERVER);
        }
        catch (GuacamoleException e) {
            logger.warn("File browser server property not present in guacamole.properties, assuming <region>-filebrowser.apporto.com");
        }
        if (fileBrowserServer == null) {
            String region = environment.getProperty(ApportoProperties.REGION);
            fileBrowserServer = String.format(DEFAULT_FILE_BROWSER_SERVER, region);
        }

        return fileBrowserServer;
    }

      public Boolean getStatusConfigured() throws GuacamoleException {
         LocalEnvironment environment = new LocalEnvironment();

         String fileBrowserServer = null;
         String fileBrowserApiKey = null;

         try {
          fileBrowserServer = environment.getProperty(ApportoProperties.FILEBROWSER_SERVER);
          fileBrowserApiKey = environment.getProperty(ApportoProperties.FILEBROWSER_API_KEY); 
         }
         catch (GuacamoleException e) {
             logger.warn("File browser properties not present in guacamole.properties");
         }
         
         return (fileBrowserServer != null && fileBrowserApiKey != null);
       }

    /**
     * Logout from the file browser.
     * 
     * @param authToken - token that represent fb session
     * @return true if successfull, false otherwise
     * @throws GuacamoleException 
     * @throws IOException 
     */
    public boolean logout(String authToken) throws GuacamoleException, IOException {
        String fileBrowserServer = getFBServerName();

        URL url = new URL(String.format(FILE_BROWSER_LOGOUT_URL, fileBrowserServer));

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("X-Auth", authToken);

        // Add cookie if it exists
        NewCookie cookie = fileBrowserCookieJar.remove(authToken);
        if (cookie != null) {
            conn.setRequestProperty("Cookie", cookie.getName() + "=" + cookie.getValue());
        }

        int responseCode = conn.getResponseCode();
        String response = getResponseMessage(conn, responseCode);
        logger.info(response);

        return (responseCode / 100 == 2);
    }
}
