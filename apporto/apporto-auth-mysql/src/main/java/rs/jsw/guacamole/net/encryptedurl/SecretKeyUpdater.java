/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl;

import java.io.*;
import java.nio.file.*;
import java.util.*;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.SecretKeyEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.hyperstream.core.ApportoProperties;


public class SecretKeyUpdater {

    private static final Logger logger = LoggerFactory.getLogger(SecretKeyUpdater.class);

    private WatchService watchService;
    private static Path configFilePath;
    private ConfigChangeListener listener;

    // Security list for each secret key
    private static List<Security> securityList;

    private static final String CONFIG_FILE_PATH = "/etc/guacamole/secretkey.properties";

    public SecretKeyUpdater(Path configFilePath, ConfigChangeListener listener) throws IOException {
        this.watchService = FileSystems.getDefault().newWatchService();
        this.configFilePath = configFilePath;
        this.listener = listener;

        // Register the directory containing the config file with the watch service
        Path dirPath = configFilePath.getParent();
        dirPath.register(watchService, StandardWatchEventKinds.ENTRY_CREATE, StandardWatchEventKinds.ENTRY_MODIFY);
    }

    // The watchservice to watch the status of file
    public void start() throws InterruptedException {
        Thread thread = new Thread(){
            public void run(){

                logger.info("The SecretKey WatchService is started.");

                // Wait for events
                WatchKey key;
                try {
                    while ((key = watchService.take()) != null) {

                        for (WatchEvent event : key.pollEvents()) {
                            // Check if the event is related to the config file
                            Path eventPath = (Path) event.context();
                            if (eventPath.equals(configFilePath.getFileName())) {

                                if (event.kind() == StandardWatchEventKinds.ENTRY_MODIFY) {
                                    logger.info("The secretkey.properties file is modified.");
                                }
                                else if (event.kind() == StandardWatchEventKinds.ENTRY_CREATE) {
                                    logger.info("The secretkey.properties file is created.");
                                }

                                // Trigger the callback function
                                listener.onConfigChange(configFilePath);

                            }
                        }

                        // Reset the key
                        boolean valid = key.reset();
                        if (!valid) {
                            break;
                        }

                    }
                }
                catch (InterruptedException e) {
                    logger.error("The SecretKey WatchService is interrupted, error = {}", e.getMessage());
                    return;
                }

                logger.info("The SecretKey WatchService is stopped.");

            }
        };
        thread.start();
    }
    
    public interface ConfigChangeListener {
        void onConfigChange(Path configFilePath);
    }

    // Start the watchservice to watch the status of secretkey.properties file
    public static void startWatcher() throws IOException, InterruptedException, GuacamoleException {
        setSecurityList();
        
        configFilePath = Paths.get(CONFIG_FILE_PATH);
        SecretKeyUpdater watcher = new SecretKeyUpdater(configFilePath, new ConfigChangeListener() {
            @Override
            public void onConfigChange(Path FilePath) {
                // Do something when the file changes
                try {     
                    setSecurityList();
                }
                catch (GuacamoleException e) {
                    logger.error("Secret-key parameter does not exist in the secretkey.properties file, " +
                                "please check /etc/guacamole/secretkey.properties. error = {}", e.getMessage());
                }
                return;
            }
        });
        watcher.start();
    }

    // Split and import the secret keys from the secretkey.properties and create Security list
    public static void setSecurityList() throws GuacamoleException {
        SecretKeyEnvironment secretKeyEnvironment = new SecretKeyEnvironment();

        // Import the secret keys from the secretkey.properties
        String secretKeys = secretKeyEnvironment.getRequiredProperty(ApportoProperties.SECRET_KEY);
        
        // Split secret keys into security key list and create Security list
        // e.g. "sec_key1  , sec_key2  ,sec_key3  " -> ["sec_key1", "sec_key2", "sec_key3"]
        List<String> secretKeyList = Arrays.asList(secretKeys.split(","));
        securityList = new ArrayList<Security>();
        for (String key : secretKeyList) {
            securityList.add(new Security(key.trim()));
        }
    }

    public static List<Security> getSecurityList() {
        return securityList;
    }

}
