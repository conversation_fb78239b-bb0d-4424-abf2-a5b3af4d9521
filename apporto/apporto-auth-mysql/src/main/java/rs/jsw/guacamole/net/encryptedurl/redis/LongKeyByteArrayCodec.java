/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.redis;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import io.lettuce.core.codec.ByteArrayCodec;
import io.lettuce.core.codec.RedisCodec;

public class LongKeyByteArrayCodec implements RedisCodec<Long, byte[]> {

    private final ByteArrayCodec byteArrayCodec = new ByteArrayCodec();

    @Override
    public Long decodeKey(ByteBuffer bytes) {
        // Decode ByteBuffer to Long for keys
        return Long.parseLong(StandardCharsets.UTF_8.decode(bytes).toString());
    }

    @Override
    public ByteBuffer encodeKey(Long key) {
        // Encode Long key to ByteBuffer
        return ByteBuffer.wrap(String.valueOf(key).getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public byte[] decodeValue(ByteBuffer bytes) {
        // Delegate value decoding to the ByteArrayCodec
        return byteArrayCodec.decodeValue(bytes);
    }

    @Override
    public ByteBuffer encodeValue(byte[] bytes) {
        // Delegate value encoding to the ByteArrayCodec
        return byteArrayCodec.encodeValue(bytes);
    }
}
