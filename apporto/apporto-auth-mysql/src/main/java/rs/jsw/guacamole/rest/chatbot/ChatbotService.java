package rs.jsw.guacamole.rest.chatbot;

import com.apporto.hyperstream.core.ApportoProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.LocalEnvironment;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

@Slf4j
public class ChatbotService {

    private static final String DEFAULT_CHATBOT_SERVER = "%s-chatbot.apporto.com";

    private static final String CHATBOT_URL = "https://%s/aibot";


    public ChatbotUrlData getUrl() throws GuacamoleException, IOException {

        HttpURLConnection connection = null;
        try {
            connection = createConnection();

            int responseCode = connection.getResponseCode();
            ChatbotUrlData urlData = new ChatbotUrlData();

            if (isSuccessfulResponse(responseCode)) {
                String chatbotUrl = String.format(CHATBOT_URL, getChatbotServerName());
                urlData.setUrl(chatbotUrl);
                return urlData;
            } else {
                logger.error("Failed to connect to chatbot server, response code: {}", responseCode);
                return null;
            }
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    private String getChatbotServerName() throws GuacamoleException {
        LocalEnvironment environment = new LocalEnvironment();

        String chatbotServer = null;
        try {
            chatbotServer = environment.getProperty(ApportoProperties.CHATBOT_SERVER);
        }
        catch (GuacamoleException e) {
            logger.warn("Chatbot server property not present in guacamole.properties, assuming <region>-chatbot.apporto.com");
        }
        if (chatbotServer == null) {
            String region = environment.getProperty(ApportoProperties.REGION);
            chatbotServer = String.format(DEFAULT_CHATBOT_SERVER, region);
        }

        return chatbotServer;
    }

    /**
     * Checks if the response code indicates a successful response.
     */
    private boolean isSuccessfulResponse(int responseCode) {
        return responseCode / 100 == 2;
    }

    /**
     * Creates https connection to the chatbot server.
     *
     * @return url
     * @throws GuacamoleException
     * @throws IOException
     */
    public HttpURLConnection createConnection() throws GuacamoleException, IOException {
        String chatbotServer = getChatbotServerName();

        URL url = new URL(String.format(CHATBOT_URL, chatbotServer));

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");

        return conn;
    }
}
