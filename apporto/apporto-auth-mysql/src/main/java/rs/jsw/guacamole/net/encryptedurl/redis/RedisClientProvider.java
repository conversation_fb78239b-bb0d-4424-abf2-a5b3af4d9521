/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.redis;

import org.apache.guacamole.environment.LocalEnvironment;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.Provider;

import io.lettuce.core.RedisClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedisClientProvider implements Provider<RedisClient> {

    private RedisClient createRedisClient() {
        RedisClient redisClient = null;

        try {
            LocalEnvironment localEnvironment = new LocalEnvironment();
            logger.debug("Creating Redis client");
            redisClient = RedisClient.create(localEnvironment.getRequiredProperty(ApportoProperties.REDIS_URL));
        } catch (Exception e) {
            logger.error("Error creating Redis client", e);
            redisClient = null;
            throw new RuntimeException("Error creating Redis client", e);
        }
        
        return redisClient;
    }

    @Override
    public RedisClient get() {
        return createRedisClient();
    }
}
