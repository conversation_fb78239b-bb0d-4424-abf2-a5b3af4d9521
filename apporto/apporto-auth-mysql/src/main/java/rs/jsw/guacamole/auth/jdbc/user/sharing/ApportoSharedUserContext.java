/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.auth.jdbc.user.sharing;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.sharing.user.SharedUserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;

import rs.jsw.guacamole.auth.jdbc.user.UserSessionDesktop;
import rs.jsw.guacamole.rest.ApportoResourceFactory;
import rs.jsw.guacamole.rest.multimonitor.SessionDesktop;

/**
 * Apporto UserContext implementation which provides additional apporto
 * specific data and rest calls.
 */
public class ApportoSharedUserContext extends SharedUserContext implements UserSessionDesktop {

    private static final Logger logger = LoggerFactory
            .getLogger(ApportoSharedUserContext.class);

    @Inject
    private SessionDesktop sessionDesktop;

    @Override
    public SessionDesktop getSessionDesktop() {
		return sessionDesktop;
	}

    @Inject
    private ApportoResourceFactory apportoResourceFactory;

    @Override
    public Object getResource() throws GuacamoleException {
        logger.debug("Creating apportoResource");
        return apportoResourceFactory.create(this);
    }

	@Override
	public void invalidate() {
		super.invalidate();
		logger.debug("Shared logout");
	}
}
