/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.jdbc;

import java.math.BigInteger;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleSessionClosedException;
import org.apache.guacamole.GuacamoleSessionConflictException;
import org.apache.guacamole.auth.jdbc.JDBCAuthenticationProviderService;
import org.apache.guacamole.auth.jdbc.connection.ConnectionModel;
import org.apache.guacamole.auth.jdbc.user.ModeledUserContext;
import org.apache.guacamole.auth.jdbc.user.UserModel;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.AuthenticatedUser;
import org.apache.guacamole.net.auth.AuthenticationProvider;
import org.apache.guacamole.net.auth.Credentials;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.net.auth.credentials.CredentialsInfo;
import org.apache.guacamole.net.auth.credentials.GuacamoleInvalidCredentialsException;
import org.apache.guacamole.net.auth.credentials.GuacamoleUrlExpireException;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.ApportoError;
import com.apporto.environment.ConfigurationChecker;
import com.apporto.hyperstream.core.ApportoProperties;
import com.apporto.hyperstream.core.UrlParams;
import com.google.inject.Inject;

import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisURI;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.DirContextDnsResolver;
import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.net.encryptedurl.JsonDecode;
import rs.jsw.guacamole.net.encryptedurl.SecretKeyUpdater;
import rs.jsw.guacamole.net.encryptedurl.Security;
import rs.jsw.guacamole.net.encryptedurl.sso.PemCertificate;
import rs.jsw.guacamole.net.encryptedurl.sso.SSOCertificateService;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
/**
 * Guacamole authentication module that accepts encrypted URL with connection parameters
 * and creates necessary data in jdbc-auth database. Created data are temporarily, while
 * connection last.
 *
 * Encrypted url code is taken from {@link https://bitbucket.org/dmwl/encryptedurl.git}
 *
 * <AUTHOR> Nikolić
 *
 */
public class EncurlJDBCAuthenticationProviderService extends JDBCAuthenticationProviderService {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory
            .getLogger(EncurlJDBCAuthenticationProviderService.class);

    /**
     * Some constants
     */
    public static final long TEN_MINUTES = 10 * 60 * 1000;
    public static final long MAX_CACHED_TIME = 2 * 24 * 3600 * 1000;
    private static final Integer CHECK_DUPLICATE_USER_TIME = 1000; // 1 second

    private static final String DEFAULT_PRINTER_NAME = "Apporto Printer";
    private static final String DEFAULT_PORT = "3389";

    /**
     * The default of the "navigate_type" property.
     */
    private static final String NAVIGATE_VALUE_DEFAULT = "navigate";

    /**
     * The reload "navigate_type" property.
     */
    private static final String NAVIGATE_VALUE_RELOAD = "reload";

    /**
     * The back_forward "navigate_type" property.
     */
    private static final String NAVIGATE_VALUE_BACK_FORWARD = "back_forward";

    /**
     * The default of the session type
     */
    private static final String DEFAULT_CONNECTION_TYPE = "primary";

    /**
     * The name of the parameters for remote apps
     */
    private static final String REMOTE_APPS_PARAM = "remote-apps";
    private static final String REMOTE_APPS_PARAM_APPS_KEY = "apps";
    private static final String REMOTE_APPS_PARAM_PROGRAM_KEY = "program";

    /**
     * These will be overridden by properties file if present.
     */
    private String defaultProtocol    = null;
    private long timestampAgeLimit    = TEN_MINUTES;

    private String redisURL           = null;
    private boolean defaultOTUCheck   = false;
    private String hapCapacityAPI     = null;

    /**
     * Other
     */
    private static Map<String, UserConnectionRequest> userConnectionRequests =
                                                    new HashMap<String, UserConnectionRequest>();

    // Helper for creating needed data in DB
    @Inject
    private GuacamoleModelCreator modelCreator;

    @Override
    public AuthenticatedUser authenticateUser(AuthenticationProvider authenticationProvider, Credentials credentials)
            throws GuacamoleException {

        // First check if the "key" parameter exists in the request; if it exists, shared connection
        // is requested. Fail authentication with this provider if shared connection is requested.
        if (credentials.getRequest().getParameter(EncurlParameters.SHARED_KEY_PARM) != null) {
            throw new GuacamoleInvalidCredentialsException("Invalid login", CredentialsInfo.USERNAME_PASSWORD);
        }

        // Get configuration from the URL; if configuration is not valid, throw exception
        GuacamoleConfiguration configuration = getGuacamoleConfiguration(credentials);
        if (configuration == null) {
            throw new GuacamoleInvalidCredentialsException("Invalid login", CredentialsInfo.USERNAME_PASSWORD);
        }

        // Get the connection info
        String conn_id = configuration.getParameter(EncurlParameters.ID_PARM);
        String cloud_user = configuration.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);

        /**********************************************************************************
         * Check if the credential has the explicit session type parameter (connection_type)
         * from the URL payload.
         *********************************************************************************/
        String connection_type = configuration.getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);
        if (connection_type == null) {
            connection_type = credentials.getRequest().getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);
            if (connection_type == null || connection_type.isEmpty()) {
                connection_type = DEFAULT_CONNECTION_TYPE;
                logger.warn("[{}:{}:{}] connection_type is a default value.", conn_id, connection_type, cloud_user);
            }

            configuration.setParameter(EncurlParameters.CONNECTION_TYPE_PARAM, connection_type);
        }

        // Check the duplications of this connection request
        String userName = configuration.getParameter(EncurlParameters.USERNAME_PARM);
        String userIdentifier = configuration.getComparableKey();
        // Get previous requests same with current request
        UserConnectionRequest prevRequest = getUserConnectionRequest(userIdentifier);
        long prevRequestTime = 0;

        logger.info("[{}:{}:{}] Processing the user (userIdentifier={}) ...", conn_id, connection_type, cloud_user, userIdentifier);

        // Check if there are requests
        if (prevRequest != null) { // exist
            prevRequestTime = prevRequest.getTimeStamp().getTime();
            long gap = System.currentTimeMillis() - prevRequestTime;
            if (gap < CHECK_DUPLICATE_USER_TIME) { // If gap is smaller than 1000
                logger.info("[{}:{}:{}] Detected the same requests within {} second. (non-intentional)", conn_id, connection_type, cloud_user, CHECK_DUPLICATE_USER_TIME);
                // Remove all previous requests
                userConnectionRequests.remove(userIdentifier + prevRequestTime);
            } 
            else {
                logger.info("[{}:{}:{}] Detected the same requests. (intentional)", conn_id, connection_type, cloud_user);
            }
        }

        // Now there does not exist any previous request and we can proceed current request
        long curRequestTime = System.currentTimeMillis();
        UserConnectionRequest curRequest = new UserConnectionRequest(conn_id, new Timestamp(curRequestTime));
        setUserConnectionRequest(userIdentifier + curRequestTime, curRequest);

        // After 1 second, try to get the ITSELF request
        try {
            Thread.sleep(CHECK_DUPLICATE_USER_TIME);
            if (getUserConnectionRequest(userIdentifier, curRequestTime) == null) { // not exist
                // Go to oops
                throw new GuacamoleSessionConflictException("Many same connections");
            }
        } catch (InterruptedException e) {
            logger.error("[{}:{}:{}] Thread interrupted while sleeping", conn_id, connection_type, cloud_user);
            Thread.currentThread().interrupt();
            return null;
        }

        /********************************************************************************************
         * Check if the RDP port is available and correct the sftp parameters
         ********************************************************************************************/
        String rdpPort = (configuration.getParameter(EncurlParameters.PORT_PARM) != null &&
                         !configuration.getParameter(EncurlParameters.PORT_PARM).isEmpty()) ?
                         configuration.getParameter(EncurlParameters.PORT_PARM) : DEFAULT_PORT;
        ApportoError error = ApportoError.OK;
        if (configuration.getParameter(EncurlParameters.HOSTNAME_PARM) != null &&
            defaultProtocol.equalsIgnoreCase(configuration.getProtocol()) &&
            hapCapacityAPI == null) {

            if (!ConfigurationChecker.check_rdp_port(configuration.getParameter(EncurlParameters.HOSTNAME_PARM),
                                                     rdpPort, conn_id, connection_type, cloud_user)) {
                error = ApportoError.RDP_NA;
            }
        }

        /********************************************************************************************
         * If file browser is enabled, SFTP must be disabled.
         ********************************************************************************************/
        if ("true".equals(configuration.getParameter(EncurlParameters.FILEBROWSER_LICENCE_PARAM))) {
            configuration.setParameter(EncurlParameters.ENABLE_SFTP_PARM, "false");
            configuration.setParameter(EncurlParameters.SFTP_CHECK_NEEDED, "false");
        }
        else {
            if ("true".equals(configuration.getParameter(EncurlParameters.ENABLE_SFTP_PARM))) {
                configuration.setParameter(EncurlParameters.ENABLE_SFTP_PARM, "false");
                configuration.setParameter(EncurlParameters.SFTP_CHECK_NEEDED, "true");
            }
            else {
                configuration.setParameter(EncurlParameters.UPLOAD_LICENCE_PARM, "false");
                configuration.setParameter(EncurlParameters.DOWNLOAD_LICENCE_PARM, "false");
                configuration.setParameter(EncurlParameters.SFTP_CHECK_NEEDED, "false");
            }
        }

        /**
         * Disable FileBrowser if the upload or download is disabled
         */
        if ("true".equals(configuration.getParameter(EncurlParameters.FILEBROWSER_LICENCE_PARAM)) &&
                ("false".equals(configuration.getParameter(EncurlParameters.UPLOAD_LICENCE_PARM)) &&
                 "false".equals(configuration.getParameter(EncurlParameters.DOWNLOAD_LICENCE_PARM)))) {
            configuration.setParameter(EncurlParameters.FILEBROWSER_LICENCE_PARAM, "false");
            logger.warn("[{}:{}:{}] FileBrowser is disabled because the upload or download is disabled.", conn_id, connection_type, cloud_user);
        }

        /********************************************************************************************
         * Check if the SSO is enabled and get certificates.
         * Currently, the file transfer is not supported with SSO.
         ********************************************************************************************/
        if ("true".equals(configuration.getParameter(EncurlParameters.ENABLE_SSO_LOGIN))) {
            logger.info("[{}:{}:{}] SSO to the desktop login is enabled.", conn_id, connection_type, cloud_user);
            getSSOCertificates(configuration);
            configuration.setParameter(EncurlParameters.FILEBROWSER_LICENCE_PARAM, "false");
            configuration.setParameter(EncurlParameters.ENABLE_SFTP_PARM, "false");
            configuration.setParameter(EncurlParameters.SFTP_CHECK_NEEDED, "false");
        }

        /**********************************************************************************
         * Check if this connection is done by the normal user or the collaborator.
         *********************************************************************************/
        String collaborator = configuration.getParameter(EncurlParameters.COLLABORATOR_PARAM);
        if (collaborator != null && !collaborator.isEmpty()) {
            logger.info("[{}:{}:{}] This connection is done by the collaborator ({}).",
                        conn_id, connection_type, cloud_user, collaborator);
        }

        /**********************************************************************************
         * Set the 'conf-status' parameter.
         *********************************************************************************/
        configuration.setParameter(EncurlParameters.CONF_STATUS_PARM, String.valueOf(error.getId()));

        /**********************************************************************************
         * Check if the credential has the explicit resize-method from the URL.
         * If it has the parameter, it means that the resize-method must be its value
         *********************************************************************************/
        if (credentials.getRequest().getParameter(EncurlParameters.RESIZE_METHOD) != null) {
            logger.warn("[{}:{}:{}] Force update resize-method to {}", conn_id, connection_type, cloud_user,
                credentials.getRequest().getParameter(EncurlParameters.RESIZE_METHOD));
            configuration.setParameter(EncurlParameters.RESIZE_METHOD, credentials.getRequest().getParameter(EncurlParameters.RESIZE_METHOD));
        }

        /**********************************************************************************
         * Determine the h264 or non-h264 mode based on the table of AP-7038
         *********************************************************************************/
        // &enable-h264=true/false has the most top priority
        if (credentials.getRequest().getParameter(EncurlParameters.H264_LICENCE_PARAM) != null) {
            logger.info("[{}:{}:{}] URL has &enable-h264={}, so forcibly use it.",
                        conn_id, connection_type, cloud_user, credentials.getRequest().getParameter(EncurlParameters.H264_LICENCE_PARAM));
            configuration.setParameter(EncurlParameters.H264_LICENCE_PARAM,
                                       credentials.getRequest().getParameter(EncurlParameters.H264_LICENCE_PARAM));
        }
        else {
            // Correct the enable-h264 or h264-supported when the payload has no value
            if (configuration.getParameter(EncurlParameters.H264_LICENCE_PARAM) == null) {
                logger.info("[{}:{}:{}] Encrypted URL payload has no 'enable-h264', so use the default value (false).",
                            conn_id, connection_type, cloud_user);
                configuration.setParameter(EncurlParameters.H264_LICENCE_PARAM, "false");
            }

            if (configuration.getParameter(EncurlParameters.H264_SUPPORTED_PARAM) == null) {
                logger.info("[{}:{}:{}] Encrypted URL payload has no 'h264-supported', so use the default value (true).",
                            conn_id, connection_type, cloud_user);
                configuration.setParameter(EncurlParameters.H264_SUPPORTED_PARAM, "true");
            }

            // Determine the final mode
            if ("true".equalsIgnoreCase(configuration.getParameter(EncurlParameters.H264_LICENCE_PARAM)) &&
                "true".equalsIgnoreCase(configuration.getParameter(EncurlParameters.H264_SUPPORTED_PARAM))) {
                logger.info("[{}:{}:{}] The h264 mode is enabled.", conn_id, connection_type, cloud_user);
                configuration.setParameter(EncurlParameters.H264_LICENCE_PARAM, "true");
            }
            else {
                logger.info("[{}:{}:{}] The h264 mode is disabled.", conn_id, connection_type, cloud_user);
                configuration.setParameter(EncurlParameters.H264_LICENCE_PARAM, "false");
            }
        }

        /**********************************************************************************
         * Check if the Remote Apps are set
         *********************************************************************************/
        // &remote-apps=Notepad++
        JSONObject remoteAppsJsonObject = getRemoteApps(configuration);
        if (remoteAppsJsonObject != null) {
            configuration.setParameter(EncurlParameters.REMOTE_APPS, remoteAppsJsonObject.toString());

            logger.info("[{}:{}:{}] Remote Apps are set.", conn_id, connection_type, cloud_user);

            String remoteAppName = credentials.getRequest().getParameter(EncurlParameters.REMOTE_APPS);
            if (remoteAppName == null || remoteAppName.isEmpty()) {
                remoteAppName = getFirstRemoteAppName(configuration);
            }

            logger.info("[{}:{}:{}] Selected Remote App: {}", conn_id, connection_type, cloud_user, remoteAppName);

            configuration.setParameter(EncurlParameters.REMOTE_APP, "||" + remoteAppName);
        }

        /**********************************************************************************
         * Add IP and Session ID as client-name parameter
         * Assumes there is only one IP beside localhost
         *********************************************************************************/
        Integer id = Integer.parseInt(configuration.getParameter(EncurlParameters.ID_PARM));
        byte[] address = getIP(conn_id, connection_type, cloud_user);
        String appcode = configuration.getParameter(EncurlParameters.APPCODE);

        if (appcode != null && !appcode.isEmpty()) {
            if (appcode.length() == 2) {
                logger.info("[{}:{}:{}] CLIENTNAME: {}", conn_id, connection_type, cloud_user,
                            String.format("%02X%02X:%08X%s", address[2], address[3], id, appcode));
                configuration.setParameter(EncurlParameters.CLIENT_NAME_PARAM, String.format("%02X%02X:%08X%s",
                    address[2], address[3], id, appcode));
            }
            else {
                logger.info("[{}:{}:{}] Bad Appcode size - {}", conn_id, connection_type, cloud_user, appcode.length());
                logger.info("[{}:{}:{}] CLIENTNAME: {}", conn_id, connection_type, cloud_user,
                             String.format("%02X%02X:%08X", address[2], address[3], id));
                configuration.setParameter(EncurlParameters.CLIENT_NAME_PARAM, String.format("%02X%02X:%08X%s",
                    address[2], address[3], id, "00"));
            }

        }
        else {
            logger.info("[{}:{}:{}] Appcode is null", conn_id, connection_type, cloud_user);
            logger.info("[{}:{}:{}] CLIENTNAME: {}", conn_id, connection_type, cloud_user,
                        String.format("%02X%02X:%08X", address[2], address[3], id));
            configuration.setParameter(EncurlParameters.CLIENT_NAME_PARAM, String.format("%02X%02X:%08X%s",
                address[2], address[3], id, "00"));
        }

        /**********************************************************************************
         * Check if the printer-name parameter is set.
         * If it is not set, change it to the default printer name.
         *********************************************************************************/
        if (configuration.getParameter(EncurlParameters.PRINTER_NAME) == null ||
            "".equals(configuration.getParameter(EncurlParameters.PRINTER_NAME))) {
           configuration.setParameter(EncurlParameters.PRINTER_NAME, DEFAULT_PRINTER_NAME);
        }

        /**********************************************************************************
         * Set the navigation-type property from the credential.
         *********************************************************************************/
        configuration.setParameter(EncurlParameters.NAVIGATE_TYPE, NAVIGATE_VALUE_DEFAULT);
        if (credentials.getRequest().getParameter(EncurlParameters.NAVIGATE_TYPE) != null) {
           configuration.setParameter(EncurlParameters.NAVIGATE_TYPE,
                                      credentials.getRequest().getParameter(EncurlParameters.NAVIGATE_TYPE));
        }
        logger.info("[{}:{}:{}] navigate_type is \"{}\"",
                    conn_id, connection_type, cloud_user, configuration.getParameter(EncurlParameters.NAVIGATE_TYPE));

        /**********************************************************************************
         * Proceed with login from the URL parameters
         * Create user from session ID and random password
         *********************************************************************************/
        SecureRandom random = new SecureRandom();
        credentials.setUsername(configuration.getParameter(EncurlParameters.USERNAME_PARM));
        credentials.setPassword(new BigInteger(130, random).toString(32));

        /**********************************************************************************
         * Proceed with login from the URL parameters
         * Set some URL parameters to the credential.
         *********************************************************************************/
        credentials.setConnId(conn_id);
        credentials.setConnType(connection_type);
        credentials.setCloudUserName(cloud_user);
        credentials.setUserName(configuration.getParameter(EncurlParameters.USERNAME_PARM));
        credentials.setNid(configuration.getParameter(EncurlParameters.NID));
        credentials.setSubdomain(configuration.getParameter(EncurlParameters.SUBDOMAIN_PARM));
        credentials.setHttpApiKey(configuration.getParameter(EncurlParameters.HTTP_API_KEY));
        credentials.setUserId(configuration.getParameter(EncurlParameters.USER_ID));
        credentials.setPayloadVersion(configuration.getParameter(EncurlParameters.PAYLOAD_VERSION));

        UserModel user;
        ConnectionModel connection;

        if ((user = modelCreator.createUser(credentials)) != null) {
            if ((connection = modelCreator.createConnection(configuration)) != null) {
                if (modelCreator.createUserConnection(user.getEntityID(), connection.getObjectID())) {
                    modelCreator.createSharingProfiles(user.getEntityID(), connection.getObjectID(), connection_type, cloud_user);
                }
                else {
                    modelCreator.deleteConnection(connection.getObjectID());
                    modelCreator.deleteUser(user.getIdentifier());
                    removeUserConnectionRequest(userIdentifier, userName, conn_id, connection_type, cloud_user);
                    throw new GuacamoleInvalidCredentialsException("Invalid login", CredentialsInfo.USERNAME_PASSWORD);
                }
            }
            else {
                modelCreator.deleteUser(user.getIdentifier());
                removeUserConnectionRequest(userIdentifier, userName, conn_id, connection_type, cloud_user);
                throw new GuacamoleInvalidCredentialsException("Invalid login", CredentialsInfo.USERNAME_PASSWORD);
            }
        }
        else {
            removeUserConnectionRequest(userIdentifier, userName, conn_id, connection_type, cloud_user);
            throw new GuacamoleInvalidCredentialsException("Invalid login", CredentialsInfo.USERNAME_PASSWORD);
        }

        // Remove the info about the user connection request
        removeUserConnectionRequest(userIdentifier, userName, conn_id, connection_type, cloud_user);

        /**********************************************************************************
         * Authenticate user using normal jdbc mechanism
         **********************************************************************************/
        AuthenticatedUser authenticatedUser;
        try {
            authenticatedUser = super.authenticateUser(authenticationProvider, credentials);

            /**********************************************************************************
             * Manage logout event behavior based on a browser's navigate_type
             *********************************************************************************/
            String navigateType = configuration.getParameter(EncurlParameters.NAVIGATE_TYPE);
            if (navigateType.equals(NAVIGATE_VALUE_RELOAD) || navigateType.equals(NAVIGATE_VALUE_BACK_FORWARD)) {
                ModeledUserContext userContext = getUserContext(authenticationProvider, authenticatedUser);
                if ((((ApportoUserContext) userContext).getLogoutEventMap()).containsKey(userIdentifier)) {
                    /**********************************************************************************
                     * Check if logout event is running.
                     *********************************************************************************/
                    ScheduledFuture<?> logoutEventTask = ((ApportoUserContext) userContext).getLogoutEvent(userIdentifier);
                    if (!logoutEventTask.isCancelled() && logoutEventTask.getDelay(TimeUnit.SECONDS) <= 0) {
                        // Task has started or is currently running
                    }
                    else {
                        logoutEventTask.cancel(true);
                        ((ApportoUserContext) userContext).removeLogoutEvent(userIdentifier);
                        logger.info("[{}:{}:{}] A logout event is canceled due to the browser's navigate_type={}.",
                                    conn_id, connection_type, cloud_user, navigateType);
                    }
                }
            }

        }
        catch(GuacamoleException e) {
            modelCreator.deleteConnection(connection.getObjectID());
            modelCreator.deleteUser(user.getIdentifier());
            throw e;
        }

        return authenticatedUser;
    }

    /**
     * Get certificate for the SSO login.
     * The certificate is retrieved from cert generation service, via REST call.
     * Currently, the fake certificate is stored into configuration.
     * 
     * @param configuration
     */
    private void getSSOCertificates(GuacamoleConfiguration configuration) {
        String conn_id = configuration.getParameter(EncurlParameters.ID_PARM);
        String cloud_user = configuration.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = configuration.getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);
        String username = configuration.getParameter(EncurlParameters.USERNAME_PARM);

        PemCertificate pemCertificate = SSOCertificateService.getPemCertificate(username, conn_id, cloud_user, connection_type);

        if (pemCertificate != null) {
            configuration.setParameter(EncurlParameters.SSO_CERTIFICATE, pemCertificate.getCertificate());
            configuration.setParameter(EncurlParameters.SSO_PRIVATE_KEY, pemCertificate.getPrivateKey());
        }
    }

    /**
     * Returns the remote apps as a JSONObject. The JSON object contains an
     * array of apps, each represented as a JSON object with a "program" key.
     * The apps are sorted alphabetically by the "program" key.
     *
     * @return
     *     A JSONObject representing the remote apps, or null if no remote apps
     *     are set or if the JSON parsing fails.
     */
    private JSONObject getRemoteApps(GuacamoleConfiguration configuration) {
        String conn_id = configuration.getParameter(EncurlParameters.ID_PARM);
        String cloud_user = configuration.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = configuration.getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);

        String remoteAppsJsonString = configuration.getParameter(REMOTE_APPS_PARAM);

        if (remoteAppsJsonString != null && !remoteAppsJsonString.isEmpty()) {
            try {
                JSONObject remoteAppsJsonObject = new JSONObject(remoteAppsJsonString);

                JSONArray appsJsonArray = remoteAppsJsonObject.getJSONArray(REMOTE_APPS_PARAM_APPS_KEY);
                JSONArray sortedAppsJsonArray = new JSONArray();

                List<JSONObject> appsList = new ArrayList<JSONObject>();
                for (int i = 0; i < appsJsonArray.length(); i++) {
                    JSONObject appJsonObject = appsJsonArray.getJSONObject(i);
                    appsList.add(appJsonObject);
                }

                Collections.sort(appsList, (a, b) -> {
                    String nameA = a.optString(REMOTE_APPS_PARAM_PROGRAM_KEY, "");
                    String nameB = b.optString(REMOTE_APPS_PARAM_PROGRAM_KEY, "");
                    return nameA.compareTo(nameB);
                });

                for (JSONObject appJsonObject : appsList) {
                    sortedAppsJsonArray.put(appJsonObject);
                }

                remoteAppsJsonObject.put(REMOTE_APPS_PARAM_APPS_KEY, sortedAppsJsonArray);

                return remoteAppsJsonObject;
            } catch (JSONException e) {
                logger.info("[{}:{}:{}] JSON error parsing remote-apps: {}",
                                    conn_id, connection_type, cloud_user, e.getMessage());
            }
        }

        return null;
    }

    /**
     * Returns the name of the first remote app from the remote apps JSON object.
     *
     * @return
     *     The name of the first remote app, or an empty string if no apps are empty.
     */
    private String getFirstRemoteAppName(GuacamoleConfiguration configuration) {
        String conn_id = configuration.getParameter(EncurlParameters.ID_PARM);
        String cloud_user = configuration.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = configuration.getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);

        JSONObject remoteAppsJSONObject = getRemoteApps(configuration);

        if (remoteAppsJSONObject != null) {
            try {
                JSONArray appsJsonArray = remoteAppsJSONObject.getJSONArray(REMOTE_APPS_PARAM_APPS_KEY);

                if (appsJsonArray.length() > 0) {
                    JSONObject firstAppJsonObject = appsJsonArray.getJSONObject(0);
                    return firstAppJsonObject.getString(REMOTE_APPS_PARAM_PROGRAM_KEY);
                }
            } catch (JSONException e) {
                logger.info("[{}:{}:{}] Error getting first remote app : {}",
                                    conn_id, connection_type, cloud_user, e.getMessage());
            }
        }

        return "";
    }


    /**
     * Get host IP address. Loop through network interfaces, until first IPv4 interface
     * different than localhost is found.
     *
     * @return byte array with IP addresses.
     */
    private byte[] getIP(String conn_id, String connection_type, String username) {
        try {
            Enumeration<NetworkInterface> n = NetworkInterface.getNetworkInterfaces();

            byte[] address = {0, 0, 0, 0};
            while (n.hasMoreElements()) {
                NetworkInterface e = n.nextElement();
                Enumeration<InetAddress> a = e.getInetAddresses();

                while (a.hasMoreElements()) {
                    address = a.nextElement().getAddress();
                    if (address.length == 4 && address[0] != 0xFE) {
                        return address;
                    }
                }
            }
        }
        catch (SocketException e1) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, username, e1);
        }

        return null;
    }

    /**
     * If the request is for shared authentication, don't return user context.
     * Check if the "key" parameter exists in the request; if it exists, shared connection
     * is requested. Don't return context in this case.
     *
     * @see org.apache.guacamole.auth.jdbc.JDBCAuthenticationProviderService#getUserContext(
     *                      org.apache.guacamole.net.auth.AuthenticationProvider,
     *                      org.apache.guacamole.net.auth.AuthenticatedUser)
     */
    @Override
    public ModeledUserContext getUserContext(AuthenticationProvider authenticationProvider,
            AuthenticatedUser authenticatedUser) throws GuacamoleException {

        if (authenticatedUser.getCredentials().getRequest().getParameter(EncurlParameters.SHARED_KEY_PARM) != null) {
            return null;
        }

        return super.getUserContext(authenticationProvider, authenticatedUser);
    }

    @Override
    public UserContext updateUserContext(AuthenticationProvider authenticationProvider, UserContext context,
            AuthenticatedUser authenticatedUser, Credentials credentials) throws GuacamoleException {
        return getUserContext(authenticationProvider, authenticatedUser);
    }

    private boolean checkTimestamp(String ts) {
        if (timestampAgeLimit == 0) {
            return true;
        }

        if (ts == null) {
            return false;
        }

        long timestamp = Long.parseLong(ts, 10);
        long now = System.currentTimeMillis();
        return timestamp + timestampAgeLimit > now;
    }

    private GuacamoleConfiguration getGuacamoleConfiguration(Credentials credentials) throws GuacamoleException {
        if (defaultProtocol == null) {
            initFromProperties();
        }

        HttpServletRequest request = credentials.getRequest();
        if (request.getParameter(UrlParams.MESSAGE_PARAM) != null) {
            // When OTT gets response from auth microservice, it uses "q" attribute to pass the encurl info to this EncURL authenticator.
            // To handle both of OTT and EncURL methods smoothly, here sets the "q" attribute value from the parameter value.
            // The parameter is read-only value, so here uses the attribute.
            request.setAttribute(UrlParams.MESSAGE_PARAM, request.getParameter(UrlParams.MESSAGE_PARAM).toString());
        }
        String message = request.getAttribute(UrlParams.MESSAGE_PARAM).toString();
        List<Security> securityList = SecretKeyUpdater.getSecurityList();

        if (securityList == null) {
            try {
                SecretKeyUpdater.startWatcher();
                securityList = SecretKeyUpdater.getSecurityList();
            }
            catch (Exception e) {
                logger.error("Configration file does not exist, " +
                            "please check /etc/guacamole/secretkey.properties. error = {}", e.getMessage());
            }
        }

        String decryptMessage = null; 
        // Try the list of secret keys until decryption succeeds.
        for (Security security : securityList) {
            String messageTmp = security.decrypt(message);
            if (messageTmp != null) {
                decryptMessage = messageTmp;
                break;
            }
        }

        if (decryptMessage == null) {
            logger.error("Oops sorry page occurrence. Reason: Cannot get the correct secret-key from configuration, " +
                        "please check /etc/guacamole/secretkey.properties. Payload = {}", message);
            throw new GuacamoleSessionClosedException("Cannot get the correct secret-key");
        }

        message = decryptMessage.trim();

        if (message == null) {
            return null;
        }

        JsonDecode connectionObj = new JsonDecode(message);
        GuacamoleConfiguration config = connectionObj.getConfig();
        config.setParamsForRouter(request.getParameter(EncurlParameters.HOSTNAME_PARM), request.getParameter(EncurlParameters.PORT_PARM));
        String timestamp = connectionObj.getTimestamp();
        if (!checkTimestamp(timestamp)) {
            String subdomain = config.getParameter(EncurlParameters.SUBDOMAIN_PARM).toString();
            throw new GuacamoleUrlExpireException(subdomain, "Url expired.");
        }

        // Hostname is required!
        if (config.getParameter(EncurlParameters.HOSTNAME_PARM) == null) {
            return null;
        }

        // Protocol is required!
        if (config.getProtocol() == null) {
            return null;
        }

        String id = connectionObj.getId();
        if (id == null) {
            id = "DEFAULT";
        }
        else {

            // This should really use BasicGuacamoleTunnelServlet's
            // IdentfierType, but it is private!
            // Currently, the only prefixes are both 2 characters in length, but
            // this could become invalid at some point.
            // see:
            // guacamole-client@a0f5ccb:guacamole/src/main/java/org/glyptodon/guacamole/net/basic/BasicGuacamoleTunnelServlet.java:244-252
            id = id.substring(2);
        }

        // This isn't normally part of the config, but it makes it much easier
        // to return a single object
        config.setParameter("id", id);

        // Get the connection info
        String conn_id = id;
        String cloud_user = config.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = config.getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);

        if (connection_type == null || connection_type.isEmpty()) {
            connection_type = "primary";
        }

        // If password is empty, and SSO is disabled, a windows login should appear.
        // However, because of the AP-11611, the password field must not be empty, and a space is inserted.
        // This is a workaround for the problem with the empty password; windows will show "Invalid credentials", but will allow manual login.
        boolean isSsoDisabled = !"true".equalsIgnoreCase(config.getParameter(EncurlParameters.ENABLE_SSO_LOGIN));
        boolean isPasswordEmpty = "".equals(config.getParameter(EncurlParameters.PASSWORD_PARAM));

        if (isSsoDisabled && isPasswordEmpty) {
            config.setParameter(EncurlParameters.PASSWORD_PARAM, " ");
        }


        String MASK = "**********";

        Map<String, String> sensitiveParams = connectionObj.getSensitiveParameters();

        for (String key : sensitiveParams.keySet()) {
            String value = sensitiveParams.get(key);
            if (value != null && !value.isEmpty()) {
                String regex = String.format("(%s\":\")(.*?)\"", Pattern.quote(key));
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(message);
                message = matcher.replaceAll("$1" + MASK + "\"");
            }
        }
        logger.info("[{}:{}:{}] Decrypted message: {}", conn_id, connection_type, cloud_user, message);

        String do_url_security_check = config.getParameter(EncurlParameters.ENABLE_ONETIME_URL_PARAM);
        if (do_url_security_check == null) {
            // Allow URL Payload parameter to dictate behavior.  Else, fallback to configured
            // default mode via enforce-otu-check guacamole.properties.
            do_url_security_check = Boolean.toString(defaultOTUCheck);
        }

        if (do_url_security_check.equals("true")) {
            String guacUrlGuid = (String) request.getAttribute("guacUrlGuid");
            if (guacUrlGuid == null) {
                logger.error("[{}:{}:{}] Connection Security enabled, but guacUrlGuid request attribute was not found (check SUA cookie)!"
                             + " Denying connection.", conn_id, connection_type, cloud_user);
                return null;
            }

            String hostname = config.getParameter(EncurlParameters.HOSTNAME_PARM);
            String username = config.getParameter(EncurlParameters.USERNAME_PARM);

            if (!checkOneTimeURL(id, hostname, username, timestamp, guacUrlGuid, connection_type, cloud_user)) {
                return null;
            }
        }

        return config;
    }

    private boolean checkOneTimeURL(String id, String hostname, String username, String ts, String guac_guid, String connection_type, String cloud_user) {

        if (redisURL == null) {
            // Allow Connection if redisURL is not defined.  No way to check...
            return true;
        }

        RedisURI redisURI = RedisURI.create(redisURL);
        redisURI.setVerifyPeer(false);

        DefaultClientResources clientResources = DefaultClientResources.builder()
            .dnsResolver(new DirContextDnsResolver()) // Does not cache DNS lookups
            .build();

        try {
            RedisClient client = RedisClient.create(clientResources, redisURI);
            StatefulRedisConnection<String, String> connection = client.connect();
            RedisCommands<String, String> commands = connection.sync();

            // Parent container will get guac_guid from session cookie
            // or generate a new guid if no cookie.   We will then attempt to get
            // the guid from globacl cache.  Comparing the guid in cache
            // to the one we generate or pull from session cookie will tell us if
            // the session is new, same (eg. srv-1, srv-2, etc), or attempt to reuse
            // url (eg. guac_guid mismatch).
            String url_tracker_key = "guacamole:sessions:" + hostname + ":" + username + ":" + id;
            String stored_guac_guid = commands.get(url_tracker_key);

            if (stored_guac_guid == null) {
                logger.info("[{}:{}:{}] Connection tracker recording {}={} into Global Cache.\n",
                            id, connection_type, cloud_user, url_tracker_key, guac_guid);
                commands.set(url_tracker_key, guac_guid);
            }
            else if (!stored_guac_guid.equals(guac_guid)) {
                logger.info("[{}:{}:{}] Connection tracker reports connection is Used. Denying connection: {} with mismatch guid: {}",
                            id, connection_type, cloud_user, url_tracker_key, guac_guid);
                return false;
            }

            logger.info("[{}:{}:{}] Connection tracker reports connection is Not Used. Allowing connection: {} with guid: {}",
                        id, connection_type, cloud_user, url_tracker_key, guac_guid);

            // set expriation on key we just set, so that redis will expire and
            // cleanup they key for us after encrypted url is no longer valid.
            long expire_time;
            if (ts == null){
                expire_time = System.currentTimeMillis() + timestampAgeLimit;
            }
            else {
                long timestamp = Long.parseLong(ts, 10);
                expire_time = timestamp + timestampAgeLimit;
            }

            // Ensure we don't have unexpiring cache entries (eg. for debugging where expire is at year 2040).
            long now = System.currentTimeMillis();
            if (expire_time > now + MAX_CACHED_TIME) {
                expire_time = now + MAX_CACHED_TIME;
            }
            commands.pexpireat(url_tracker_key, expire_time);

            connection.close();
            clientResources.shutdown();
            client.shutdown();
        }
        catch(io.lettuce.core.RedisException e){
            logger.error("[{}:{}:{}] Error encountered while talking to redis server. Failing open (allowing connection).\nReason: {}",
                          id, connection_type, cloud_user, e);
        }

        return true;
    }

    private void initFromProperties() throws GuacamoleException {
        Environment environment = new LocalEnvironment();

        defaultProtocol = environment.getProperty(ApportoProperties.DEFAULT_PROTOCOL);
        if (defaultProtocol == null) {
            defaultProtocol = "rdp";
        }

        if (environment.getProperty(ApportoProperties.TIMESTAMP_AGE_LIMIT) == null) {
            timestampAgeLimit = TEN_MINUTES;
        }
        else {
            timestampAgeLimit = environment.getProperty(ApportoProperties.TIMESTAMP_AGE_LIMIT);
        }

        if (environment.getProperty(ApportoProperties.DEFAULT_OTU_CHECK) != null) {
            defaultOTUCheck = environment.getProperty(ApportoProperties.DEFAULT_OTU_CHECK);
        }

        if (defaultOTUCheck) {
            redisURL = environment.getRequiredProperty(ApportoProperties.REDIS_URL);
        }
        else {
            redisURL = environment.getProperty(ApportoProperties.REDIS_URL);
        }

        if (environment.getProperty(ApportoProperties.HAP_CAPACITY_API_SERVICE) != null) {
            hapCapacityAPI = environment.getProperty(ApportoProperties.HAP_CAPACITY_API_SERVICE);
        }
    }

    private void setUserConnectionRequest(String userIdentifier, UserConnectionRequest request) {
        userConnectionRequests.put(userIdentifier, request);
    }

    private UserConnectionRequest getUserConnectionRequest(String userIdentifier) {
        String regex = userIdentifier + "\\d+";
        Pattern pattern = Pattern.compile(regex);
        for (String key : userConnectionRequests.keySet()) {
            if (pattern.matcher(key).matches()) {
                // If the key matches the regex, retrieve the value
                return userConnectionRequests.get(key);
            }
        }
        return null;
    }

    private UserConnectionRequest getUserConnectionRequest(String userIdentifier, long timestamp) {
        return userConnectionRequests.get(userIdentifier + timestamp);
    }

    private boolean removeUserConnectionRequest(String userIdentifier, String userName, String conn_id, String connection_type, String cloud_user) {
        UserConnectionRequest request = userConnectionRequests.remove(userIdentifier);
        boolean ret = true;

        if (request != null) {
            ret = true;
            logger.debug("[{}:{}:{}] Removed the user (username={}).", conn_id, connection_type, cloud_user, userName);
        }
        else {
            ret = false;
            logger.error("[{}:{}:{}] Failed to remove the user (username={}).", conn_id, connection_type, cloud_user, userName);
        }

        return ret;
    }

}
