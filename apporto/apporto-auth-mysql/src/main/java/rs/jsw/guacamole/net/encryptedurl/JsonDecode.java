/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.net.encryptedurl;

import java.util.Map;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlJDBCAuthenticationProviderService;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.net.encryptedurl.jdbc.SensitiveData;

/**
 * <AUTHOR>
 *
 */
public class JsonDecode {
    private String timestamp, id;

    private GuacamoleConfiguration config;

    private SensitiveData sensitiveData = new SensitiveData();

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory
            .getLogger(EncurlJDBCAuthenticationProviderService.class);

    /**
     * @throws GuacamoleException 
     * 
     */
    public JsonDecode(String json) throws GuacamoleException {
        try {
            JSONObject obj = new JSONObject(json);

            id = obj.getString("id");
            timestamp = String.valueOf(obj.getLong("timestamp"));

            config = new GuacamoleConfiguration();
            JSONObject guacObj = obj.getJSONObject("guac");

            // Iterate through key and values in guacObj
            for (String key : guacObj.keySet()) {
                Object value = guacObj.opt(key);

                if (EncurlParameters.PROTOCOL_PARM.equals(key)) {
                    config.setProtocol(value != null ? value.toString() : "");
                } else {
                    // If HS uses the rdp router service, hostname and port params will be missing in the payload.
                    if ((EncurlParameters.HOSTNAME_PARM.equals(key) || EncurlParameters.PORT_PARM.equals(key)) && value == null) {
                        config.setParameter(key, "");
                    } else {
                        config.setParameter(key, value != null ? value.toString() : "");

                        if (sensitiveData.isSensitiveData(key)) {
                            sensitiveData.setSensitiveParameters(key, value != null ? value.toString() : "");
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            json = json.replaceAll("\"password\":\"[^\"]+\"", "\"password\":\"********\"");
            json = json.replaceAll("\"sftp-password\":\"[^\"]+\"", "\"sftp-password\":\"********\"");
            json = json.replaceAll("\"Http-Api-Key\":\"[^\"]+\"", "\"Http-Api-Key\":\"********\"");
            logger.error("Oops sorry page occurrence. Reason: The json text parsing is failed. Json text = {}", json);
            throw new GuacamoleException("The json text parsing is failed.");
        }
    }

    public GuacamoleConfiguration getConfig() {
        return config;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public String getId() {
        return id;
    }

    public Map<String, String> getSensitiveParameters() {
        return sensitiveData.getSensitiveParameters();
    }
}
