package rs.jsw.guacamole.net.encryptedurl.jdbc;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class SensitiveData {
    private final static Set<String> SENSITIVE_DATA = Collections.unmodifiableSet(new HashSet<String>(){
        {
            add(EncurlParameters.HTTP_API_KEY);
            add(EncurlParameters.PASSWORD_PARAM);
            add(EncurlParameters.SFTP_PASSWORD_PARM);
        }
    });

    private Map<String, String> sensitiveParameters = new HashMap<String, String>();

    public Boolean isSensitiveData(String data) {
        return SENSITIVE_DATA.contains(data);
    }

    public Map<String, String> getSensitiveParameters() {
        return sensitiveParameters;
    }

    public void setSensitiveParameters(String key, String value) {
        if (sensitiveParameters.get(key) == null) {
            sensitiveParameters.put(key, value);
        }
    }
}
