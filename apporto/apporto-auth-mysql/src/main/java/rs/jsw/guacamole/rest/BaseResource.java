/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.util.concurrent.TimeUnit;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.Connection;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * Base resource class with common functionalities.
 *
 * <AUTHOR> Nikolić
 *
 */
public abstract class BaseResource {

    /**
     * Logger for this class.
     */
    final Logger logger = LoggerFactory.getLogger(BaseResource.class);

    /**
     * Others
     */
    protected static final String USER_AGENT = "Mozilla/5.0";

    /**
     * The UserContext being exposed through this resource.
     */
    protected final UserContext userContext;

    @AssistedInject
    public BaseResource(@Assisted UserContext userContext) {
        this.userContext = userContext;
    }

    /**
     * Obtaining parametar value.
     *
     * @param id The Integer value which uniquely identifies current connection.
     * @param parameter The parameter whose value is requested.
     *
     * @return Parameter value if successful, empty String if id is invalid.
     *         null value if parameter is not specified for current user.
     */
    protected String getParameter(Integer id, String parameter) {

        logger.debug("getParameter id=" + id + " parameter=" + parameter);

        return getParameter(id.toString(), parameter);

    }

    /**
     * Obtaining parametar value.
     *
     * @param id The Long value which uniquely identifies current connection.
     * @param parameter The parameter whose value is requested.
     *
     * @return Parameter value if successful, empty String if id is invalid.
     *         null value if parameter is not specified for current user.
     */
    protected String getParameter(Long id, String parameter) {

        logger.debug("getParameter long id=" + id + " parameter=" + parameter);

        return getParameter(id.toString(), parameter);

    }

    /**
     * Obtaining parametar value.
     *
     * @param id The String which uniquely identifies current connection.
     * @param parameter The parameter whose value is requested.
     *
     * @return Parameter value if successful, empty String if id is invalid.
     *         null value if parameter is not specified for current user.
     */
    protected String getParameter(String id, String parameter) {

        logger.debug("getParameter string id=" + id + " parameter=" + parameter);

        String value = null;
        GuacamoleConfiguration configuration;
        Connection connection;
        String conn_id = "unknown";
        String cloud_user = "unknown";
        String connection_type = "unknown";

        // Get the connection info
        conn_id = id;

        // try to get configuration object with QueryParam id. If id is valid,
        // configuration shall be obtained, otherwise value is null
        try {
            configuration = ((connection = userContext.getConnectionDirectory().get(id)) == null) ? null
                            : connection.getConfiguration();

            if (configuration != null) {
                value = configuration.getParameters().get(parameter);
            }
            else { // return value equals empty String
                String stackTraceStr = printCusotmStackTrace();
                logger.warn("[{}:{}:{}] Id does not exist, Configuration is null.(Id={}), stacktrace: {}",
                            conn_id, connection_type, cloud_user, id, stackTraceStr);
            }
        }
        catch (GuacamoleException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }

        logger.debug(parameter + " = " + value != null ? value : "[null]");

        return value;

    }

    /**
     * Obtaining full configuration
     *
     * @param id The String which uniquely identifies current connection.
     *
     * @return Guacamole configuration
     */
    protected GuacamoleConfiguration getConfiguration(Integer id) {

        logger.debug("getConfiguration id=" + id);

        GuacamoleConfiguration configuration = null;
        Connection connection;
        String conn_id = "unknown";
        String cloud_user = "unknown";
        String connection_type = "unknown";

        // Get the connection info
        conn_id = id.toString();

        // try to get configuration object with QueryParam id. If id is valid,
        // configuration shall be obtained, otherwise value is null
        try {
            configuration = ((connection = userContext.getConnectionDirectory().get(id.toString())) == null) ? null
                            : connection.getConfiguration();

            if (configuration == null) {
                String stackTraceStr = printCusotmStackTrace();
                logger.warn("[{}:{}:{}] Id does not exist, Configuration is null.(Id={}), stacktrace: {}",
                            conn_id, connection_type, cloud_user, id, stackTraceStr);
            }
        }
        catch (GuacamoleException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }

        return configuration;

    }

    protected String createProcess(String command, String conn_id, String connection_type, String cloud_user) {

        logger.debug("[{}:{}:{}] Creating process. command={}", conn_id, connection_type, cloud_user, command);

        Process proc = null;
        String s;
        StringBuilder stdout = new StringBuilder();
        StringBuilder stderr = new StringBuilder();
        ProcessBuilder pb = new ProcessBuilder("/bin/bash", "-c", command);

        try {
            proc = pb.start();

            BufferedReader br = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            while ((s = br.readLine()) != null) {
                stdout.append(s);
            }
            logger.debug("[{}:{}:{}] stdout: {}", conn_id, connection_type, cloud_user, stdout.toString());

            BufferedReader bre = new BufferedReader(new InputStreamReader(proc.getErrorStream()));
            while ((s = bre.readLine()) != null) {
                stderr.append(s);
            }

            if (stderr.length() > 0) {
                logger.error("[{}:{}:{}] stderr: {}", conn_id, connection_type, cloud_user, stderr.toString());
            }

            proc.waitFor(90, TimeUnit.SECONDS);

        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception while executing process command: {}",
                        conn_id, connection_type, cloud_user, command);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }

        return stdout.toString();
    }

    protected CustomResult createProcessWithResult(String command, String conn_id, String connection_type, String cloud_user) {

        logger.info("[{}:{}:{}] Creating process. command={}", conn_id, connection_type, cloud_user, command);

        CustomResult retVal = new CustomResult();
        Process proc;
        String s;
        StringBuilder stdout = new StringBuilder();
        StringBuilder stderr = new StringBuilder();
        ProcessBuilder pb = new ProcessBuilder("/bin/bash", "-c", command);

        try {
            proc = pb.start();

            BufferedReader br = new BufferedReader(new InputStreamReader(proc.getInputStream()));
            while ((s = br.readLine()) != null) {
                stdout.append(s);
            }

            logger.debug("[{}:{}:{}] stdout: {}", conn_id, connection_type, cloud_user, stdout.toString());

            BufferedReader bre = new BufferedReader(new InputStreamReader(proc.getErrorStream()));
            while ((s = bre.readLine()) != null) {
                stderr.append(s);
            }

            if (stderr.toString() != null && !stderr.toString().equals("")) {
                logger.warn("[{}:{}:{}] stderr: {}", conn_id, connection_type, cloud_user, stderr.toString());
            }

            proc.waitFor(5, TimeUnit.MINUTES);

            retVal.setProcessExitValue(proc.exitValue());
            retVal.setResultMessage(stdout.toString());
            retVal.setErrorMessage(stderr.toString());

            if (stderr.toString().equals("")) {
                JSONObject json = new JSONObject(stdout.toString());

                int resultCode = Integer.parseInt(json.get("Success").toString());
                retVal.setResultCode(resultCode);
            }
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception while executing process command: {}",
                        conn_id, connection_type, cloud_user, command);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);

            retVal.setErrorMessage(e.getMessage());
        }

        logger.debug("[{}:{}:{}] CreateProcess: retVal -> {}", conn_id, connection_type, cloud_user, retVal.toString());
        return retVal;
    }

    protected String sendToDrupal(URL url, Integer id, String conn_id, String connection_type, String cloud_user) {

        if(!url.toString().contains("user_details")) {
            logger.info("[{}:{}:{}] sendToDrupal url={}", conn_id, connection_type, cloud_user, url);
        }

        StringBuffer response = new StringBuffer();
        try {

            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            String http_api_key = getParameter(id, EncurlParameters.HTTP_API_KEY);

            if(http_api_key!= null && !http_api_key.isEmpty()) {
                con.setRequestProperty("http-api-key", http_api_key);
            }

            // calls to drupal must be with GET method
            // optional default is GET
            con.setRequestMethod("GET");

            // set request header
            con.setRequestProperty("User-Agent", USER_AGENT);

            logger.info("[{}:{}:{}] Send 'GET' request to URL: \"{}\"", conn_id, connection_type, cloud_user, url);
            if (con.getResponseCode() != 200) {
                logger.error("[{}:{}:{}] HTTP call returned abnormal response. response code={}, URL={}",
                             conn_id, connection_type, cloud_user, con.getResponseCode(), url.toString());
                return "";
            }

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;

            response = new StringBuffer();
            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            logger.info("[{}:{}:{}] sendToDrupal()-> response = {}",
                         conn_id, connection_type, cloud_user, getShortString(response.toString()));

            if(!url.toString().contains("user_details")) {
                logger.info("[{}:{}:{}] sendToDrupal()-> response = {}",
                            conn_id, connection_type, cloud_user, getShortString(response.toString()));
            }

        }
        catch (MalformedURLException e) {
            logger.warn("[{}:{}:{}] MalformedURLException with http request, URL={}",
                        conn_id, connection_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }
        catch (ProtocolException e) {
            logger.warn("[{}:{}:{}] ProtocolException with http request, URL={}",
                        conn_id, connection_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }
        catch (IOException e) {
            logger.warn("[{}:{}:{}] IOException with http request, URL={}",
                        conn_id, connection_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }

        return response.toString();
    }

    /**
     * Prints the stack trace.
     *
     * This is used to track the stack trace through the logger feature.
     */
    private String printCusotmStackTrace() {
        String stackStr = "";
        int stackIndex = 1;
        StackTraceElement[] stacks = new Throwable().getStackTrace();

        for (int i = 0; i < stacks.length; i++) {
            if (stacks[i].getClassName().contains("guacamole") && !stacks[i].getMethodName().equals("printCusotmStackTrace")) {
                if (stackStr.length() > 0) {
                    stackStr += "\n";
                }
                stackStr += "[" + stackIndex + "] " + "File: " + stacks[i].getFileName() + ", Class: " + stacks[i].getClassName() +
                            ", Method: " + stacks[i].getMethodName() + ", Line: " + stacks[i].getLineNumber();
                stackIndex++;
            }
        }

        return stackStr;
    }

    /**
     * Return only n leading characters of the string and append "..." at the end.
     *
     * This is used in the log files, to indicate that result is longer. Typically this is used to dump
     * results of URL APIs and it is possible to get
     */
    private String getShortString(String str) {
       return str.length() < 200 ? str : (str.substring(0, 200)+"...");
    }

    protected JSONObject readGitProperties(String conn_id, String connection_type, String cloud_user) {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream inputStream = classLoader.getResourceAsStream("git.properties");

        try {
            String gitInfo = readFromInputStream(inputStream);
            JSONObject json = new JSONObject(gitInfo);
            return json;
        }
        catch (IOException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            return null;
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            return null;
        }
    }

    protected String readFromInputStream(InputStream inputStream) throws IOException {
        StringBuilder resultStringBuilder = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {
            String line;
            while ((line = br.readLine()) != null) {
                resultStringBuilder.append(line).append("\n");
            }
        }

        return resultStringBuilder.toString();
    }
}
