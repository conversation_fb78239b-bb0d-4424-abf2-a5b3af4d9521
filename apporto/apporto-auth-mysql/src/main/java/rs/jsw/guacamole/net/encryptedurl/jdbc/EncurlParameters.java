/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.jdbc;

/**
 * Constants used in Guacamole encrypted URL.
 *
 * <AUTHOR>
 *
 */
public final class EncurlParameters {
    // Parameter names passed from URL used in auth module.
    public static final String ID_PARM                         = "id";
    public static final String SHARED_KEY_PARM                 = "key";
    public static final String USERNAME_PARM                   = "username";
    public static final String CLOUD_USERNAME_PARM             = "cloud_username";
    public static final String HOSTNAME_PARM                   = "hostname";
    public static final String PROTOCOL_PARM                   = "protocol";
    public static final String PORT_PARM                       = "port";
    public static final String ENABLE_SFTP_PARM                = "enable-sftp";
    public static final String SFTP_HOSTNAME_PARM              = "sftp-hostname";
    public static final String SFTP_PORT_PARM                  = "sftp-port";
    public static final String SFTP_USERNAME_PARM              = "sftp-username";
    public static final String SFTP_PASSWORD_PARM              = "sftp-password";
    public static final String CONF_STATUS_PARM                = "conf-status";
    public static final String UPLOAD_LICENCE_PARM             = "enable-upload";
    public static final String DOWNLOAD_LICENCE_PARM           = "enable-download";
    public static final String SHARING_LICENCE_PARM            = "enable-screensharing";
    public static final String COLLABORATION_LICENCE_PARM      = "enable-async-collaboration";
    public static final String MESSENGER_LICENCE_PARM          = "enable-messenger";
    public static final String SNAPSHOTS_LICENCE_PARM          = "enable-snapshots";
    public static final String ANALYTICS_LICENCE_PARM          = "enable-analytics";
    public static final String HIGHLIGHT_LICENCE_PARM          = "enable-annotations";
    public static final String MOUNTER_LICENCE_PARM            = "enable-mounter";
    public static final String MMONITOR_LICENCE_PARM           = "enable-multimonitor";
    public static final String CLASSROOM_LICENCE_PARAM         = "enable-classroom";
    public static final String PRESENTER_LICENCE_PARAM         = "enable-presenter";
    public static final String ACTIVITY_TRACK_LICENCE_PARM     = "enable-activitytracker";
    public static final String CLIPBOARD_LICENCE_PARM          = "enable-clipboard";
    public static final String CLIPBOARDIN_LICENCE_PARM        = "enable-clipboard-in";
    public static final String CLIPBOARDOUT_LICENCE_PARM       = "enable-clipboard-out";
    public static final String REBOOT_LICENCE_PARAM            = "enable-reboot";
    public static final String BACKUP_LICENCE_PARAM            = "enable-backup";
    public static final String H264_LICENCE_PARAM              = "enable-h264";
    public static final String FILEBROWSER_LICENCE_PARAM       = "enable-filebrowser";
    public static final String H264_MAX_RESOLUTION             = "h264-max-resolution";
    public static final String SUBDOMAIN_PARM                  = "subdomain";
    public static final String PASSWORD_PARAM                  = "password";
    public static final String CLIENT_NAME_PARAM               = "client-name";
    public static final String WINDOWS_SERVER_NAME             = "win-server-name";
    public static final String WINDOWS_SESSION_ID              = "win-session-id";
    public static final String PRINTER_NAME                    = "printer-name";
    public static final String POOR_NETWORK                    = "poor-network";
    public static final String POOR_NETWORK_DISCONNECT         = "poor-network-diconnnect";
    public static final String APPCODE                         = "ClientName";
    public static final String VM_RESOURCE                     = "vm-resource";
    public static final String RESIZE_METHOD                   = "resize-method";
    public static final String APPOS_LICENCE_PARAM             = "AppOS";
    public static final String HTTP_API_KEY                    = "Http-Api-Key";
    public static final String CAMERA_LICENCE_PARAM            = "enable-camera-input";
    public static final String STATISTICS_LICENCE_PARM         = "enable-statistics-frames";
    public static final String MONITOR_COUNT_PARAM             = "monitor-count";
    public static final String APP_NAME_KEY                    = "AppName";
    public static final String ENABLE_ONETIME_URL_PARAM        = "enable-url-security";
    public static final String IDLE_TIMEOUT_KEY                = "idle-timeout";
    public static final String INDUSTRY_TYPE_KEY               = "industry-type";
    public static final String LTI_USER_ID_PARM                = "lti_user_id";
    public static final String LTI_COURSE_ID_PARM              = "lti_course_id";
    public static final String LTI_COURSE_NAME_PARM            = "lti_course_name";
    public static final String LTI_ISSUER_PARM                 = "lti_issuer";
    public static final String PREVENT_CAPS_LOCK_PARM          = "prevent-caps-lock";
    public static final String CLOUD_PROVIDER_PARM             = "cloud-provider";
    public static final String SUBSCRIPTION                    = "subscription";
    public static final String ENABLE_AUDIO_INPUT_OPUS_PARM    = "enable-audio-input-opus"; // only for the internal use
    public static final String WATERMARK_TYPE_PARM             = "watermark-type";
    public static final String WATERMARK_TEXT_PARM             = "watermark-text";
    public static final String WATERMARK_SIZE_PARM             = "watermark-size";
    public static final String WATERMARK_COLOR_PARM            = "watermark-color";
    public static final String WATERMARK_LAYOUT_PARM           = "watermark-layout";
    public static final String WATERMARK_SEMI_TRANSPARENT_PARM = "watermark-semi-transparent";
    public static final String WATERMARK_PICTURE_PARM          = "watermark-picture";
    public static final String WATERMARK_SCALE_PARM            = "watermark-scale";
    public static final String WATERMARK_WASHOUT_PARM          = "watermark-washout";
    public static final String NID                             = "nid";
    public static final String USER_ID                         = "user-id";
    public static final String SUPPORT_MENU_NAME_PARM          = "support-menu-name";
    public static final String SUPPORT_LINK_PARM               = "support-link";
    public static final String SUPPORT_EMAIL_PARM              = "support-email";
    public static final String WEBSITE_MENU_NAME_PARM          = "website-menu-name";
    public static final String WEBSITE_MENU_LINK_PARM          = "website-menu-link";
    public static final String SFTP_CHECK_NEEDED               = "sftp-check-needed"; // only for the internal use
    public static final String USB_LICENCE_PARAM               = "enable-usb";
    public static final String USB_PORT_PARAM                  = "usb-port";
    public static final String COLLABORATOR_PARAM              = "collaborator";
    public static final String CONNECTION_TYPE_PARAM           = "connection_type"; // "primary", "shared", "classroom", "presenter", "messenger", "remote", "collaborate"
    public static final String H264_SUPPORTED_PARAM            = "h264-supported"; // indicates whether a browser supports GPU
    public static final String REQUIRED_SERVERS                = "required-servers"; // required server list e.g. "srv1,srv2"
    public static final String EXTENDED_COMPUTE_PARAM          = "extended_compute"; // true / false
    public static final String REMAINING_TIME_PARAM            = "remaining_time";
    public static final String PAYLOAD_TYPE_PARAM              = "payload_type" ; // "traditional" | "rdp-router"
    public static final String RDP_FARM_NAME_PARAM             = "rdp-farm-name";
    public static final String LAUNCH_FULL_SCREEN_PARAM        = "launch-full-screen";
    public static final String USB_DEVICES                     = "usb-devices"; // "02,03,08"
    public static final String ENABLE_SSO_LOGIN                = "enable-sso-login";
    public static final String SSO_CERTIFICATE                 = "sso-certificate";
    public static final String SSO_PRIVATE_KEY                 = "sso-private-key";
    public static final String REMOTE_APP                      = "remote-app";
    public static final String REMOTE_APPS                     = "remote-apps";
    public static final String REMOTE_APP_MODE                 = "remote-app-mode";
    public static final String PAYLOAD_VERSION                 = "payload_version"; // "1.0.0"
    public static final String LOGOFF_LICENCE_PARAM            = "enable-logoff";

    /**
     * Parameters for the internal usage
     */

    /**
     * The property name used for identifying the type of navigation.
     * The value of this property name indicates whether a user connects to the hyperstream directly
     * or by refreshing the browser.
     * The possible values of this property: "navigate", "reload", "back_forward", "prerender"
     *
     * For more information, please refer to the following URL:
     * https://developer.mozilla.org/en-US/docs/Web/API/PerformanceNavigationTiming/type
     */
    public static final String NAVIGATE_TYPE = "navigate_type";

    // Constructor
    private EncurlParameters() {}
}
