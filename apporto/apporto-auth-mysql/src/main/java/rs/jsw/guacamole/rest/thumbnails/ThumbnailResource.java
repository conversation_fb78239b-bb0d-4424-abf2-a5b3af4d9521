/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.thumbnails;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.UserContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.sun.jersey.multipart.FormDataParam;

import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;
import rs.jsw.guacamole.rest.classroom.ThumbnailData;

/**
 * A REST Service for storing and providing client thumbnails.
 *
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
public class ThumbnailResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(ThumbnailResource.class);

    /**
     * Creates a new UserContextResource which exposes the data within the given
     * UserContext.
     *
     * @param userContext The UserContext which should be exposed through this
     * UserContextResource.
     */
    @AssistedInject
    public ThumbnailResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Receives thumbnail from a specific session id.
     *
     * @return ok if the notification is successfully received.
     *
     * @throws GuacamoleException
     */
    @POST
    @Deprecated
    @Consumes(MediaType.APPLICATION_OCTET_STREAM)
    public Response uploadThumbnail(@QueryParam("id") Long id,
            @FormDataParam("file") InputStream inputStream) throws GuacamoleException {

        logger.warn("Deprecated endpoint POST /thumbnail called");

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] Thumbnail upload started", conn_id, conn_type, cloud_user);

        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int read = 0;
            byte[] bytes = new byte[1024];

            while ((read = inputStream.read(bytes)) != -1) {
                out.write(bytes, 0, read);
            }

            Map<Long, ThumbnailData> thumbnails = ApportoUserContext.getThumbs();
            thumbnails.put(id, ThumbnailData.builder()
                                .thumbnailContent(out.toByteArray())
                                .isComplete(true)
                                .build());
            // TimeUnit.SECONDS.sleep(10);

        }
        catch (IOException e) {
            logger.warn("[{}:{}:{}] Error processing thumbnail", conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);

            return Response.status(Status.BAD_REQUEST)
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        logger.info("[{}:{}:{}] Thumbnail uploaded successfully", conn_id, conn_type, cloud_user);
        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Retrieves
     *
     * @param request - Servlet request
     * @param id - Guacamole session ID
     * @return - thumbnail if exists or Status.NO_CONTENT.
     * @throws GuacamoleException
     */
    @DELETE
    @Deprecated
    public Response retrieveThumbnail(@Context HttpServletRequest request, @QueryParam("id") Long id) throws GuacamoleException {
        logger.warn("Deprecated endpoing DELETE /thumbnail called");

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.debug("[{}:{}:{}] Thumbnail retrieval started", conn_id, conn_type, cloud_user);

        Map<Long, ThumbnailData> thumbnails = ApportoUserContext.getThumbs();

        ThumbnailData thumb = thumbnails.get(id);
        if (thumb == null || !thumb.isComplete()) {
            return Response.status(Status.NO_CONTENT)
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }
        else {
            return Response.ok(thumb.getThumbnailContent())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }
    }

    @POST
    @Path("unsubscribe")
    @Consumes("text/plain")
    @Produces(MediaType.APPLICATION_JSON)
    @Deprecated
    public Response logout(@QueryParam("id") String id,
            @Context HttpServletRequest request) throws GuacamoleException {
        logger.warn("Deprecated endpoint POST /unsubscribe called");

        Map<Long, ThumbnailData> thumbnails = ApportoUserContext.getThumbs();
        thumbnails.remove(Long.valueOf(id));

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

}
