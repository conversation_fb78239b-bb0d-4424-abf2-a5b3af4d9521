package rs.jsw.guacamole.rest.chatbot;

import org.apache.guacamole.net.auth.UserContext;

/**
 * Factory which creates resources that allows managing chatbot.
 */
public interface ChatbotResourceFactory {

    /**
     * Creates a new ChatbotResource which managing chatbot.
     *
     * @param userContext
     *     The UserContext whose contents should be exposed.
     *
     * @return
     *     A new ChatbotResource
     */
    ChatbotResource create(UserContext userContext);
}
