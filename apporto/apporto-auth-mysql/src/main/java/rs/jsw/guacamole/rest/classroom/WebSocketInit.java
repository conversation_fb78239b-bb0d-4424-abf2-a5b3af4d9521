/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;

/**
 * This class initializes websockets.
 * 
 * The initialization is done by sending a GET request to the server, actually
 * calling itself.
 * 
 * The reason for this is that it is necessary to have ServletContext injected,
 * which happens only in the API calls. This is a workaround for getting the
 * ServletContext, which is necessary because of the design of the Guacamole
 * extension API.
 * 
 * Calling of the GET request is scheduled to happen 5 seconds after the server
 * starts. It is required to delay call, to allow the main Guacamole application
 * and extensions to initialize.
 */
public class WebSocketInit {
    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(WebSocketInit.class);

    private static final String WEBSOCKET_INIT_URL = "http://localhost:8080/hyperstream/api/ext/encryptedurl-jdbc/websocket-init";

    private static final int SERVER_INIT_DELAY = 5;

    @Inject
    public WebSocketInit() {
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

        scheduler.schedule(() -> initializeWebSockets(), SERVER_INIT_DELAY, TimeUnit.SECONDS);
    }

    private void initializeWebSockets() {
        URL url;
        try {
            url = new URL(WEBSOCKET_INIT_URL);
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            con.setRequestMethod("GET");
            if (con.getResponseCode() != 200) {
                logger.info("[init:auth:] Failed to initialize websockets");
            }
        } catch (MalformedURLException e) {
            logger.error("[init:auth:] Malformed URL: ", e);
        } catch (IOException e) {
            logger.error("[init:auth:] IOException: ", e);
        }

        logger.info("[init:auth:] Websockets initialized");
    }
}
