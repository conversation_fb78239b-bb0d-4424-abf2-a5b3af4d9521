/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.auth.jdbc.user;

import java.util.HashMap;
import java.util.concurrent.ScheduledFuture;

public interface LogoutEventManager {

    /**
     * Returns the logout event map.
     *
     * @return
     *     Logout Event Map.
     */
    HashMap<String, ScheduledFuture<?>> getLogoutEventMap();

    /**
     * Returns the logout event map.
     *
     * @param key
     *     Identifier to identify a logout event in event map.
     *
     * @return
     *     ScheduledFuture.
     */
    ScheduledFuture<?> getLogoutEvent(String key);

    /**
     * Add a logout event to logout event map.
     *
     * @param key
     *     Identifier to identify a logout event in event map.
     *
     * @param value
     *     Schedule Task.
     */
    void setLogoutEvent(String key, ScheduledFuture<?> value);

    /**
     * Add a logout event to logout event map.
     *
     * @param key
     *     Identifier to identify a logout event in event map.
     *
     */
    void removeLogoutEvent(String key);

    /**
     * Returns the size of logout event map.
     *
     * @return
     *     size of logout event map.
     */
    int getLogoutEventCount();

}
