package rs.jsw.guacamole.rest;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.stream.Collectors;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.GuacamoleCommonUtility;
import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * A REST Service for getting user identity for messenger.
 *
 * <AUTHOR> Babic
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class MessengerAuthResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(MessengerAuthResource.class);

    /**
     * URL template for get the user list from messenger. https://{subdomain
     * prefix}.{domain}/guac/userinfo/{cloud_username}
     */
    private static final String get_user_list = "https://%s.%s/guac/userinfo/%s";

    /**
     * URL template for get the user data from messenger. https://{subdomain
     * prefix}.{domain}/guac/messenger/user_details/{cloud_username}
     */
    private static final String check_user_data_template = "https://%s.%s/guac/messenger/user_details/%s";

    /**
     * Others
     */
    private static String DEFAULT_DOMAIN = "apporto.com";
    private static String MESSENGER_SERVER; //public-13.52.72.114, private-172.16.5.51

    private static final String GROUP_KEY = "groups";
    private static final String PERMISSION_KEY = "permission_groups";
    private static final String SPLITTER_KEY = "=>";

    /**
     * Creates a new NetworkQualityResource which checks network quality within
     * the given UserContext.
     *
     * @param userContext The UserContext which should be exposed through this
     * UserContextResource.
     */
    @AssistedInject
    public MessengerAuthResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Load the proper configuration variable(s)
     */
    static {
        try {
            Environment environment = new LocalEnvironment();
            String propValue;

            // if exists, "messenger-server" property from guacamole.properties
            MESSENGER_SERVER = environment.getProperty(ApportoProperties.MESSENGER);
            logger.debug("messenger-server property:" + MESSENGER_SERVER);

            // if exists, "api-base-domain" property from guacamole.properties
            propValue = environment.getProperty(ApportoProperties.API_BASE_DOMAIN);
            if (propValue != null) {
                DEFAULT_DOMAIN = propValue;
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get the proper parameters from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    @GET
    @Path("getUserInfo")
    @Produces("application/json")
    public Response getUserInfo(@QueryParam("id") Integer id, @QueryParam("windows_name") String windows_name) {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.debug("[{}:{}:{}] getUserInfo.", conn_id, connection_type, cloud_user);

        // Get the subdomain
        String subdomain = getParameter(id, EncurlParameters.SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        // Get the username
        String username;
        if (windows_name == null || windows_name.isEmpty()) {
            username = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        }
        else {
            username = windows_name;
        }

        if (username == null || username.isEmpty()) {
            JSONObject error = new JSONObject();
            error.put("Message", "MessengerAuthResource->getUserInfo: Invalid username.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.UNAUTHORIZED).entity(error).build();
        }

        // Get the user data from the Drupal
        String data = getData(id, check_user_data_template, subdomain, DEFAULT_DOMAIN, username);

        //BEFORE: {"data":{"username":"t3","windows_username":"t3_rohit","email":"<EMAIL>","group":["MessengerTesting"]}}
        //NOW: {"data":[{"windows_username":"himanshu_rohit","firstname":"himanshu","lastname":"Messenger","email":"<EMAIL>","groups":["group118","GroupTest"],"roles":"Tech Support","status":"1"}]}
        JSONObject json = new JSONObject(data);
        JSONObject jsonData = json.getJSONArray("data").getJSONObject(0);
        
        if (jsonData != null && !jsonData.isEmpty()) {
            JSONArray groupData = jsonData.optJSONArray(GROUP_KEY);
            JSONArray permissionGroupData = jsonData.optJSONArray(PERMISSION_KEY);

            if (groupData != null && !groupData.isEmpty()) {
                for (int i = 0; i < groupData.length(); i++) {
                    // Clean up the group data, trim all spaces between groups and subgroups
                    String cleanedGroup = Arrays.stream(groupData.getString(i).split(SPLITTER_KEY))
                            .map(String::trim)
                            .collect(Collectors.joining(SPLITTER_KEY));

                    groupData.put(i, cleanedGroup);
                }

                jsonData.put(GROUP_KEY, groupData);
            }

            if (permissionGroupData != null && !permissionGroupData.isEmpty()) {
                for (int i = 0; i < permissionGroupData.length(); i++) {
                    // Clean up the group data, trim all spaces between groups and subgroups
                    String cleanedGroup = Arrays.stream((permissionGroupData.getString(i)).split(SPLITTER_KEY))
                            .map(String::trim)
                            .collect(Collectors.joining(SPLITTER_KEY));


                    permissionGroupData.put(i, cleanedGroup);
                }

                jsonData.put(PERMISSION_KEY, permissionGroupData);
            }

            return Response.ok(jsonData.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject error = new JSONObject();
        error.put("Message", "getUserInfo: Invalid userinfo data");
        logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
        return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
    }

    @GET
    @Path("getUserList")
    @Produces("application/json")
    public Response getUserList(@QueryParam("id") Integer id) {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.debug("[{}:{}:{}] getUserList.", conn_id, connection_type, cloud_user);

        // Get the subdomain
        String subdomain = getParameter(id, EncurlParameters.SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        // Get the username
        String username = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        if (username == null || username.isEmpty()) {
            JSONObject error = new JSONObject();
            error.put("Message", "MessengerAuthResource->getUserList: Invalid username.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.UNAUTHORIZED).entity(error).build();
        }

        // Get the user data from the Drupal
        String data = getData(id, get_user_list, subdomain, DEFAULT_DOMAIN, username);
        JSONObject jsonData = null;

        //NOW: {"data":{"Abu Dhabi":[{"uid":"4184","windows_name":"jd3339","username":"jd3339"}]}}
        if (data!=null && !data.isEmpty()) {
            try {
                // Parsiranje JSON stringa u JSONObject
                JSONObject json = new JSONObject(data);

                // Preuzimanje "data" objekta
                jsonData = json.optJSONObject("data");

                if (jsonData == null) {
                    logger.warn("[{}:{}:{}] 'data' field is missing or not a valid JSON object.", conn_id, connection_type, cloud_user);
                }
            } catch (Exception e) {
                logger.error("[{}:{}:{}] Exception while parsing JSON data: ", conn_id, connection_type, cloud_user, e);
            }
        }

        if (jsonData != null && !jsonData.isEmpty()) {
            return Response.ok(jsonData.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject error = new JSONObject();
        error.put("Message", "getUserList: Invalid userinfo data");
        logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
        return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
    }

    private String getData(Integer id, String template, String... param) {
        String url_str = String.format(template, (Object[]) param);

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        try {
            URL url = new URL(url_str);
            return sendToDrupal(url, id, conn_id, connection_type, cloud_user);

        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            return "";
        }
    }
}
