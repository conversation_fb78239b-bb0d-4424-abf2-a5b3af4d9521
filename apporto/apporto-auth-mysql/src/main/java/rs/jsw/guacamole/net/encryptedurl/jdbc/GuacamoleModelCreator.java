package rs.jsw.guacamole.net.encryptedurl.jdbc;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.Vector;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.base.EntityMapper;
import org.apache.guacamole.auth.jdbc.connection.ConnectionModel;
import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterMapper;
import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel;
import org.apache.guacamole.auth.jdbc.permission.ConnectionPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.ObjectPermissionModel;
import org.apache.guacamole.auth.jdbc.permission.SharingProfilePermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.SystemPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.SystemPermissionModel;
import org.apache.guacamole.auth.jdbc.permission.UserPermissionMapper;
import org.apache.guacamole.auth.jdbc.security.PasswordEncryptionService;
import org.apache.guacamole.auth.jdbc.security.SaltService;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileMapper;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileModel;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileParameterMapper;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileParameterModel;
import org.apache.guacamole.auth.jdbc.user.UserMapper;
import org.apache.guacamole.auth.jdbc.user.UserModel;
import org.apache.guacamole.net.auth.Credentials;
import org.apache.guacamole.net.auth.permission.ObjectPermission;
import org.apache.guacamole.net.auth.permission.SystemPermission;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;

import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionMapper;

/**
 * Helper class that creates necessary data in Guacamole DB needed by Encrypted url login.
 *
 * <AUTHOR> Nikolić
 *
 */
public class GuacamoleModelCreator {

	private static final Logger logger = LoggerFactory
            .getLogger(GuacamoleModelCreator.class);

    private static final Integer MAX_CONNECTIONS = 50;
    private static final Integer MAX_CONNECTIONS_PER_USER = 50;
    private static final String WIN_SERVER_NAME_PARM = "win-server-name";
    private static final String WIN_SESSION_ID_PARM = "win-session-id";

    /**
     * Mappers for accessing database tables.
     */
    @Inject
    private EntityMapper entityMapper;

    @Inject
    private UserMapper userMapper;

    @Inject
    private UserPermissionMapper userPermissionMapper;

    @Inject
    private SystemPermissionMapper systemPermissionMapper;

    @Inject
    private EncUrlConnectionMapper connectionMapper;

    @Inject
    private ConnectionParameterMapper connectionParameterMapper;

    @Inject
    private ConnectionPermissionMapper connectionPermissionMapper;

    @Inject
    private SharingProfileMapper sharingProfileMapper;

    @Inject
    private SharingProfileParameterMapper sharingProfileParameterMapper;

    @Inject
    private SharingProfilePermissionMapper sharingProfilePermissionMapper;

    /**
     * Service for hashing passwords.
     */
    @Inject
    private PasswordEncryptionService encryptionService;

    /**
     * Service for providing secure, random salts.
     */
    @Inject
    private SaltService saltService;

    /**
     * Creates user based on the parameters from the URL. The user is temporary and should
     * be deleted when its connections are closed. Based on the provided credentials, appropriate
     * username and password are created in the database.
     *
     * @param credentials - credentials for the logged user
     * @return user data
     */
    public UserModel createUser(Credentials credentials) {
        if (credentials.getUsername() == null)
            return null;

        UserModel dbUser = userMapper.selectOne(credentials.getUsername());
        if (dbUser != null) {
            byte[] salt = saltService.generateSalt();
            byte[] hash = encryptionService.createPasswordHash(credentials.getPassword(), salt);

            dbUser.setPasswordSalt(salt);
            dbUser.setPasswordHash(hash);
            dbUser.setPasswordDate(new Timestamp(System.currentTimeMillis()));
            dbUser.setOrganization(credentials.getConnId()); // Set connection id to organization

            userMapper.update(dbUser);
            insertPermissions(dbUser);

            return dbUser;
        }

        dbUser = new UserModel();
        dbUser.setIdentifier(credentials.getUsername());

        entityMapper.insert(dbUser);

        byte[] salt = saltService.generateSalt();
        byte[] hash = encryptionService.createPasswordHash(credentials.getPassword(), salt);

        // Set stored salt and hash
        dbUser.setPasswordSalt(salt);
        dbUser.setPasswordHash(hash);
        dbUser.setDisabled(false);
        dbUser.setExpired(false);
        dbUser.setPasswordDate(new Timestamp(System.currentTimeMillis()));
        dbUser.setOrganization(credentials.getConnId()); // Set connection id to organization

        userMapper.insert(dbUser);
        insertPermissions(dbUser);

        // Create user permissions
        // First delete all existing permissions (should be there at all)

        return dbUser;
    }

    /**
     * Creates or updates permissions of the user.
     * The user is given admin permissions, so it is possible to access other users details.
     *
     * @param dbUser - user whose permissions are set
     */
    private void insertPermissions(UserModel dbUser)
    {
        Collection<ObjectPermissionModel> perms = new Vector<ObjectPermissionModel>();

        ObjectPermissionModel read = new ObjectPermissionModel();
        read.setEntityID(dbUser.getEntityID());
        read.setType(ObjectPermission.Type.READ);
        read.setObjectIdentifier(dbUser.getIdentifier());
        perms.add(read);

        ObjectPermissionModel admin = new ObjectPermissionModel();
        admin.setEntityID(dbUser.getEntityID());
        admin.setType(ObjectPermission.Type.ADMINISTER);
        admin.setObjectIdentifier(dbUser.getIdentifier());
        perms.add(admin);

        userPermissionMapper.insert(perms);

        Collection<SystemPermissionModel> systemPerms = new Vector<SystemPermissionModel>();

        SystemPermissionModel create = new SystemPermissionModel();
        create.setEntityID(dbUser.getEntityID());
        create.setType(SystemPermission.Type.CREATE_CONNECTION);
        systemPerms.add(create);

        SystemPermissionModel sysadmin = new SystemPermissionModel();
        sysadmin.setEntityID(dbUser.getEntityID());
        sysadmin.setType(SystemPermission.Type.ADMINISTER);
        systemPerms.add(sysadmin);

        systemPermissionMapper.insert(systemPerms);
    }

    public void deleteUser(String identifier) {
        userMapper.delete(identifier);
    }

    /**
     * Creates connection based on the configuration from the URL. The connection name is the same
     * as the user name.
     *
     * @param configuration - configuration read from encrypted url
     * @return - newly created connection
     * @throws GuacamoleException
     */
    public ConnectionModel createConnection(GuacamoleConfiguration configuration) throws GuacamoleException {
        ConnectionModel connection = decryptConnection(configuration);

        if (connection.getProtocol() == null)
            return null;

        String conn_id = connection.getObjectID().toString();
        String conn_type = configuration.getParameter(EncurlParameters.CONNECTION_TYPE_PARAM);
        String cloud_username = configuration.getParameter(EncurlParameters.CLOUD_USERNAME_PARM);
        String username = configuration.getParameter(EncurlParameters.USERNAME_PARM);
        String oldUsername = getParameter(conn_id, EncurlParameters.USERNAME_PARM);
        String oldWinServerName = getParameter(conn_id, WIN_SERVER_NAME_PARM);
        String oldWinSessionId = getParameter(conn_id, WIN_SESSION_ID_PARM);

        connectionMapper.delete(conn_id);
        connectionMapper.insertWithKey(connection);

        Collection<ConnectionParameterModel> parameters = new ArrayList<ConnectionParameterModel>();
        for (Map.Entry<String, String> entry : configuration.getParameters().entrySet()) {
            ConnectionParameterModel parModel = new ConnectionParameterModel();
            parModel.setConnectionIdentifier(conn_id);
            parModel.setName(entry.getKey());
            parModel.setValue(entry.getValue());
            parameters.add(parModel);
        }

        // Keep the winServerName and winSessionId if they are already set (AP-6557)
        // If new values are set from RDP server side, the values are updated
        if (oldUsername.equals(username) && (oldWinServerName != "" || oldWinSessionId != "")) {
            logger.info("[{}:{}:{}] There are already winServerName: {}, winSessionId: {}, so keeping them",
                conn_id, conn_type, cloud_username, oldWinServerName, oldWinSessionId);

            ConnectionParameterModel parModel = new ConnectionParameterModel();
            parModel.setConnectionIdentifier(conn_id);
            parModel.setName(EncurlParameters.WINDOWS_SERVER_NAME);
            parModel.setValue(oldWinServerName);
            parameters.add(parModel);

            parModel = new ConnectionParameterModel();
            parModel.setConnectionIdentifier(conn_id);
            parModel.setName(EncurlParameters.WINDOWS_SESSION_ID);
            parModel.setValue(oldWinSessionId);
            parameters.add(parModel);
        }

        connectionParameterMapper.insert(parameters);

        return connection;
    }

    public void deleteConnection(Integer id) {
        connectionMapper.delete(id.toString());
    }

    public boolean createUserConnection(Integer entityId, Integer connectionId) {
        Collection<ObjectPermissionModel> perms = new Vector<ObjectPermissionModel>();

        ObjectPermissionModel read = new ObjectPermissionModel();
        read.setEntityID(entityId);
        read.setType(ObjectPermission.Type.READ);
        read.setObjectIdentifier(connectionId.toString());
        perms.add(read);

        ObjectPermissionModel update = new ObjectPermissionModel();
        update.setEntityID(entityId);
        update.setType(ObjectPermission.Type.UPDATE);
        update.setObjectIdentifier(connectionId.toString());
        perms.add(update);

        ObjectPermissionModel admin = new ObjectPermissionModel();
        admin.setEntityID(entityId);
        admin.setType(ObjectPermission.Type.ADMINISTER);
        admin.setObjectIdentifier(connectionId.toString());
        perms.add(admin);

        ObjectPermissionModel delete = new ObjectPermissionModel();
        delete.setEntityID(entityId);
        delete.setType(ObjectPermission.Type.DELETE);
        delete.setObjectIdentifier(connectionId.toString());
        perms.add(delete);

        if (connectionPermissionMapper.insert(perms) > 0)
            return true;

        return false;
    }

    /**
     * Creates sharing profiles for a given connection.
     *
     * Two sharing profiles are created: one with full control and
     * one view only.
     *
     * @param userId - user which will have permission to use sharing profiles
     * @param connectionId - id of a connection for which sharing profile is made
     * @param cloud_user - value of the URL parameter "cloud_username". Used for logging.
     */
    public void createSharingProfiles(Integer entityId, Integer connectionId, String connection_type, String cloud_user) {
        String conn_id = Integer.toString(connectionId);

        SharingProfileModel shareFC = new SharingProfileModel();
        shareFC.setName("share-FC");
        shareFC.setParentIdentifier(connectionId.toString());
        sharingProfileMapper.insert(shareFC);
        logger.info("[{}:{}:{}] Name-{}, Parent-{}", conn_id, connection_type, cloud_user, shareFC.getName(), shareFC.getParentIdentifier());

        SharingProfileModel shareVO = new SharingProfileModel();
        shareVO.setName("share-VO");
        shareVO.setParentIdentifier(connectionId.toString());
        sharingProfileMapper.insert(shareVO);
        logger.info("[{}:{}:{}] Name-{}, Parent-{}", conn_id, connection_type, cloud_user, shareVO.getName(), shareVO.getParentIdentifier());

        Collection<SharingProfileParameterModel> sharingParameters = new Vector<SharingProfileParameterModel>();
        SharingProfileParameterModel parameter = new SharingProfileParameterModel();
        parameter.setSharingProfileIdentifier(shareVO.getObjectID().toString());
        parameter.setName("read-only");
        parameter.setValue("true");
        sharingParameters.add(parameter);
        sharingProfileParameterMapper.insert(sharingParameters);
        logger.info("[{}:{}:{}] Name-{}, Sharing profile identifier-{}, value-{}", conn_id, connection_type, cloud_user, parameter.getName(), 
              parameter.getSharingProfileIdentifier(), parameter.getValue());

        SharingProfileModel shareMM = new SharingProfileModel();
        shareMM.setName("share-MM");
        shareMM.setParentIdentifier(connectionId.toString());
        sharingProfileMapper.insert(shareMM);

        sharingParameters = new Vector<SharingProfileParameterModel>();
        parameter = new SharingProfileParameterModel();
        parameter.setSharingProfileIdentifier(shareMM.getObjectID().toString());
        parameter.setName("enable-multimonitor");
        parameter.setValue("true");
        sharingParameters.add(parameter);
        sharingProfileParameterMapper.insert(sharingParameters);

        SharingProfileModel shareMM3 = new SharingProfileModel();
        shareMM3.setName("share-MM-3");
        shareMM3.setParentIdentifier(connectionId.toString());
        sharingProfileMapper.insert(shareMM3);

        sharingParameters = new Vector<SharingProfileParameterModel>();
        parameter = new SharingProfileParameterModel();
        parameter.setSharingProfileIdentifier(shareMM3.getObjectID().toString());
        parameter.setName("enable-multimonitor");
        parameter.setValue("true");
        sharingParameters.add(parameter);
        sharingProfileParameterMapper.insert(sharingParameters);

        Collection<ObjectPermissionModel> sharingPermissions = new Vector<ObjectPermissionModel>();
        ObjectPermissionModel readFC = new ObjectPermissionModel();
        readFC.setEntityID(entityId);
        readFC.setType(ObjectPermission.Type.READ);
        readFC.setObjectIdentifier(shareFC.getObjectID().toString());
        sharingPermissions.add(readFC);
        logger.info("[{}:{}:{}] EntityID-{}, Type-{}, ObjectIdentifier-{}", conn_id, connection_type, cloud_user,
                    readFC.getEntityID(), readFC.getType(), readFC.getObjectIdentifier());

        ObjectPermissionModel readVO = new ObjectPermissionModel();
        readVO.setEntityID(entityId);
        readVO.setType(ObjectPermission.Type.READ);
        readVO.setObjectIdentifier(shareVO.getObjectID().toString());
        sharingPermissions.add(readVO);
        logger.info("[{}:{}:{}] EntityID-{}, Type-{}, ObjectIdentifier-{}", conn_id, connection_type, cloud_user,
                    readVO.getEntityID(), readVO.getType(), readVO.getObjectIdentifier());

        ObjectPermissionModel readMM = new ObjectPermissionModel();
        readMM.setEntityID(entityId);
        readMM.setType(ObjectPermission.Type.READ);
        readMM.setObjectIdentifier(shareMM.getObjectID().toString());
        sharingPermissions.add(readMM);

        sharingProfilePermissionMapper.insert(sharingPermissions);
    }

    private ConnectionModel decryptConnection(GuacamoleConfiguration configuration) throws GuacamoleException {

        ConnectionModel connection = new ConnectionModel();

        connection.setObjectID(Integer.valueOf(configuration.getParameter("id")));
        connection.setName(configuration.getParameter("id"));
        connection.setProtocol(configuration.getProtocol());
        connection.setMaxConnections(MAX_CONNECTIONS);
        connection.setMaxConnectionsPerUser(MAX_CONNECTIONS_PER_USER);

        return connection;
    }

    private String getParameter(String id, String name) {
        String strValue = "";
        Collection<ConnectionParameterModel> parameters = connectionParameterMapper.select(id);

        for (ConnectionParameterModel parameter : parameters) {
            if (parameter.getName().equals(name)) {
                strValue = parameter.getValue();
                break;
            }
        }

        return strValue;
    }
}
