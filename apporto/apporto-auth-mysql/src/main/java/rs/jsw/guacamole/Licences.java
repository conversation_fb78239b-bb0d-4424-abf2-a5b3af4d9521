/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole;

import lombok.Data;

/**
 * Defines set of licences present in Guacamole session
 *
 * <AUTHOR>
 *
 */
@Data
public class Licences {

    private Boolean hasUpload = false;
    private Boolean hasDownload = false;
    private Boolean hasSftp = false;
    private Boolean hasSftpCheckNeeded = false;
    private Boolean hasSharing = false;
    private Boolean hasAsyncCollaboration = false;
    private Boolean hasSnapshots = false;
    private Boolean hasMessenger = false;
    private Boolean hasAnalytics = false;
    private Boolean hasHighlight = false;
    private Boolean hasMounter = false;
    private Boolean hasClassroom = false;
    private Boolean hasPresenter = false;
    private Boolean hasActivityTracker = false;
    private Boolean hasClipboard = true;
    private Boolean hasClipboardIn = true;
    private Boolean hasClipboardOut = true;
    private Boolean hasReboot = false;
    private Boolean hasMMonitor = false;
    private Boolean hasBackup = false;
    private Boolean hasMacOS = false;
    private Boolean hasLinux = false;
    private Boolean hasH264 = false;
    private Boolean hasFileBrowser = false;
    private Integer h264MaxResolution = 0;
    private Boolean hasCamera = false;
    private Boolean hasStatistics = false;
    private Boolean hasLTI = false;
    private String  ltiCourseName = "";
    private Boolean hasWatermark = false;
    private Integer monitorCount = 1;
    private Boolean hasUSB = false;
    private String appName = "";
    private String subdomain = "";
    private String idleTimeout = "";
    private String industryType = "";
    private Boolean hasPreventCapsLock = false;
    private String cloudProvider = "";
    private String ltiIssuer = "";
    private Integer latencyThreshold = 0; // ms
    private Integer latencyCheckInterval = 0; // second
    private Integer latencyCheckCount = 0;
    private String username = "";
    private String usbPort = "";
    private Boolean supportH264 = false;
    private Boolean extendedCompute = false;
    private Integer remainingTime = 0;
    private String hostName = "";
    private String RDPPort = "3389";
    private String rollbarAccessToken = "";
    private String payloadType = "traditional";
    private String RDPFarmName = "";
    private Boolean launchFullScreen = false;
    private String usbDevices = "";
    private String cloudUsername = "";
    private String userId = "";
    private String remoteApps = "";
    private String remoteAppMode = "";
    private String payloadVersion = "";
    private Boolean hasLogoff = false;

    /**
     * The values of the URL parameters for the support links.
     */
    private String supportMenuName = "";
    private String supportLink = "";
    private String supportEmail = "";

    /**
     * The values of the parameters for the customization user guide.
     */
    private String userGuideName = "";
    private String userGuideLink = "";
}
