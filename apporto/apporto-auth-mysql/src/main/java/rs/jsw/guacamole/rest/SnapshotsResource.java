/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * A REST Service for handling snapshots
 *
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class SnapshotsResource extends BaseResource {

    /**
     * Logger for this class.
     */
    final Logger logger = LoggerFactory.getLogger(SnapshotsResource.class);

    /**
     * Commands to execute for acquiring, restoring and backing up the
     * snapshots. POWERSHELL_RUNNER_HOST, SCRIPT_NAME, USERNAME, REGION
     */
    private static final String URL = System.getProperty("user.home") + "/.guacamole/bin/runner.ps1 %s %s %s %s %s";

    /**
     * Commands to execute for acquiring, restoring and backing up the
     * snapshots. POWERSHELL_RUNNER_HOST, SCRIPT_NAME, USERNAME, REGION,
     * SNAPSHOT_ID
     */
    private static final String URL_RESTORE = System.getProperty("user.home") + "/.guacamole/bin/runner.ps1 %s %s %s %s %s %s";

    // will be deleted by prop. file if present, otherwise default value will be set
    // "none" value to ensure that reading from config file takes place only once
    private static String dcServerId = "none";
    private static final String defaultDCServerId = "msnv-dc1";

    private static String snapshotUsername = "none";
    private static final String defaultSnapshotUsername = "sshd.admin";

    private static String port = "none";
    private static final String defaultPort = "22122";

    private static String region = "";

    private static String snapshotEnumerate = "none";
    private static final String defaultSnapshotEnumerate = "get_restorepoint_user_profile.ps1";

    private static String snapshotRestore = "none";
    private static final String defaultSnapshotRestore = "restore_user_profile.ps1";

    private static String snapshotBackup = "none";
    private static final String defaultSnapshotBackup = "backup_user_profile.ps1";

    private static String powershellRunnerHost = "";

    /**
     * Creates a new SnapshotsResource for managing snapshots.
     *
     * @param userContext The UserContext which should be exposed through this
     * UserContextResource.
     */
    @AssistedInject
    public SnapshotsResource(@Assisted UserContext userContext) {
        super(userContext);

        Environment environment;
        try {
            environment = new LocalEnvironment();
            String propValue;

            if (dcServerId.equals("none")) {
                propValue = environment.getProperty(ApportoProperties.DC_SERVER_ID);
                if (propValue == null) {
                    dcServerId = defaultDCServerId;
                }
                else {
                    dcServerId = propValue;
                }
            }

            if (snapshotUsername.equals("none")) {
                propValue = environment.getProperty(ApportoProperties.SNAPSHOT_USERNAME);
                if (propValue == null) {
                    snapshotUsername = defaultSnapshotUsername;
                }
                else {
                    snapshotUsername = propValue;
                }
            }

            if (port.equals("none")) {
                propValue = environment.getProperty(ApportoProperties.PORT);
                if (propValue == null) {
                    port = defaultPort;
                }
                else {
                    port = propValue;
                }
            }

            propValue = environment.getProperty(ApportoProperties.REGION);
            if (propValue != null) {
                region = propValue;
            }

            if (snapshotEnumerate.equals("none")) {
                propValue = environment.getProperty(ApportoProperties.SNAPSHOT_ENUMERATE);
                if (propValue == null) {
                    snapshotEnumerate = defaultSnapshotEnumerate;
                }
                else {
                    snapshotEnumerate = propValue;
                }
            }

            if (snapshotRestore.equals("none")) {
                propValue = environment.getProperty(ApportoProperties.SNAPSHOT_RESTORE);
                if (propValue == null) {
                    snapshotRestore = defaultSnapshotRestore;
                }
                else {
                    snapshotRestore = propValue;
                }
            }

            if (snapshotBackup.equals("none")) {
                propValue = environment.getProperty(ApportoProperties.SNAPSHOT_BACKUP);
                if (propValue == null) {
                    snapshotBackup = defaultSnapshotBackup;
                }
                else {
                    snapshotBackup = propValue;
                }
            }

            propValue = environment.getProperty(ApportoProperties.POWERSHELL_RUNNER_HOST);
            if (propValue != null) {
                powershellRunnerHost = propValue;
            }

        }
        catch (GuacamoleException e) {
            logger.warn("Cannot initialize snapshot parameters from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    @Path("enumerate")
    @GET
    @Produces("application/json")
    public Response enumerate(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Enumerating snapshots.", conn_id, connection_type, cloud_user);

        String username = getParameter(id, EncurlParameters.USERNAME_PARM);

        if (username == null) {
            logger.error("[{}:{}:{}] Invalid id. Username not found.", conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid id. Username not found.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.UNAUTHORIZED).entity(error.toString()).build();
        }

        String subscription = getParameter(id, EncurlParameters.SUBSCRIPTION);
        if (subscription == null || subscription.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid subscription!", conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid subscription.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        String snapshots_enumerate = String.format(URL, powershellRunnerHost, snapshotEnumerate, username, region, subscription);
        logger.info("[{}:{}:{}] Snapshot enumerate: {}", conn_id, connection_type, cloud_user, snapshots_enumerate);
        CustomResult retVal = createProcessWithResult(snapshots_enumerate, conn_id, connection_type, cloud_user);

        // something goes wrong
        if (retVal.getProcessExitValue() != 0 || retVal.getResultCode() != 0) {
            JSONObject error = new JSONObject();
            error.put("Message", retVal.getResultMessage() + retVal.getErrorMessage());
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        return Response.ok(retVal.getResultMessage())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @Path("backup")
    @GET
    @Produces("application/json")
    public Response backup(@QueryParam("id") Integer id) throws WebApplicationException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Backing up snapshots.", conn_id, connection_type, cloud_user);

        String username = getParameter(id, EncurlParameters.USERNAME_PARM);
        if (username == null) {
            logger.error("[{}:{}:{}] Invalid id. Username not found.", conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid id. Username not found.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.UNAUTHORIZED).entity(error.toString()).build();
        }

        String subscription = getParameter(id, EncurlParameters.SUBSCRIPTION);
        if (subscription == null || subscription.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid subscription!", conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid subscription.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        String snapshots_backup = String.format(URL, powershellRunnerHost, snapshotBackup, username, region, subscription);
        logger.info("[{}:{}:{}] Snapshot backup: {}", conn_id, connection_type, cloud_user, snapshots_backup);
        CustomResult retVal = createProcessWithResult(snapshots_backup, conn_id, connection_type, cloud_user);

        // something goes wrong
        if (retVal.getProcessExitValue() != 0 || retVal.getResultCode() != 0) {
            JSONObject error = new JSONObject();
            error.put("Message", retVal.getResultMessage() + "\n" + retVal.getErrorMessage());
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        return Response.ok(retVal.getResultMessage())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @Path("restore")
    @GET
    @Produces("application/json")
    public Response restore(@QueryParam("id") Integer id, @QueryParam("snapshotId") String snapshotId) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Restoring snapshot: snapshotId={}", conn_id, connection_type, cloud_user, snapshotId);

        String username = getParameter(id, EncurlParameters.USERNAME_PARM);
        if (username == null) {
            logger.error("[{}:{}:{}] Invalid id. Username not found.", conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid id. Username not found.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.UNAUTHORIZED).entity(error.toString()).build();
        }

        String subscription = getParameter(id, EncurlParameters.SUBSCRIPTION);
        if (subscription == null || subscription.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid subscription!", conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid subscription.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        String snapshots_restore = String.format(URL_RESTORE, powershellRunnerHost, snapshotRestore,
                username, region, "\'" + snapshotId + "\'", subscription);
        logger.info("[{}:{}:{}] Snapshot restore: {}", conn_id, connection_type, cloud_user, snapshots_restore);
        CustomResult retVal = createProcessWithResult(snapshots_restore, conn_id, connection_type, cloud_user);

        // something goes wrong
        if (retVal.getProcessExitValue() != 0 || retVal.getResultCode() != 0) {
            JSONObject error = new JSONObject();
            error.put("Message", retVal.getResultMessage() + "\n" + retVal.getErrorMessage());
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        return Response.ok(retVal.getResultMessage())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

}
