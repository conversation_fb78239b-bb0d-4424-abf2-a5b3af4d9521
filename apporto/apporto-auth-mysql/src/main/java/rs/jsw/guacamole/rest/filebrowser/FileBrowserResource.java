/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest.filebrowser;

import java.io.IOException;
import java.net.MalformedURLException;

import javax.ws.rs.GET;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.UserContext;

import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import lombok.extern.slf4j.Slf4j;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

/**
 * A REST Resource for managaing file browser.
 * This service can return URL of the file browser, with embeded token.
 *
 * <AUTHOR> Nikolić
 */
@Slf4j
@Produces(MediaType.APPLICATION_JSON)
public class FileBrowserResource extends BaseResource {

    @Inject
    FileBrowserService service;

    /**
     * Creates a new FileBrowserResource.
     *
     * @param userContext
     */
    @AssistedInject
    public FileBrowserResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Returns url of the file browser with embedded token,
     * for a given username and password.
     * 
     * @param id connection id
     * 
     * @return url of the file browser as JSON string in format {"link": "url"}
     * 
     */
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/url")
    public Response getUrl(@QueryParam("id") Integer id) {
        logger.debug("getFileBrowserUrl enter for conn id {}", id);

        String username = getParameter(id, EncurlParameters.USERNAME_PARM);
        String password = getParameter(id, EncurlParameters.PASSWORD_PARAM);
        String OU = username.substring(username.lastIndexOf('_') + 1);
        String OS = getParameter(id, EncurlParameters.APPOS_LICENCE_PARAM).toLowerCase();
        Boolean isUpload = "true".equalsIgnoreCase(getParameter(id, EncurlParameters.UPLOAD_LICENCE_PARM));
        Boolean isDownload = "true".equalsIgnoreCase(getParameter(id, EncurlParameters.DOWNLOAD_LICENCE_PARM));
        String hostname = "";
        
        FBUrlData fbUrlData = null;
        try {
            fbUrlData = service.getUrl(username, password, OU, OS, isUpload, isDownload, hostname);
        } catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Malformed url {} {}", id, username, "", OU, OS, e);
        } catch (IOException e) {
            logger.error("[{}:{}:{}] Cannot create connection {} {}", id, username, "", OU, OS, e);
        } catch (GuacamoleException e) {
            logger.error("[{}:{}:{}] Cannot read file browser configuration from guacamole.properties. Check if API key, FB server or region are present.", id, username, "", e);
        } catch (Exception e) {
            logger.error("[{}:{}:{}] Cannot parse file browser url, check if the file browser server is working.", id, username, "", e);
        }

        // Log URL-a, just a start of the string
        String shortUrl = fbUrlData != null && fbUrlData.getUrl() != null
                ? fbUrlData.getUrl().substring(0, Math.min(fbUrlData.getUrl().length(), 40))
                : "null";
        logger.info("getFileBrowserUrl exit with url {}...", shortUrl);

        return createResponse(fbUrlData);
    }

    private Response createResponse(FBUrlData fbUrlData) {
        String jsonResponse = fbUrlData != null && fbUrlData.getUrl() != null
                ? String.format("{\"url\":\"%s\"}", fbUrlData.getUrl())
                : "{\"url\":null}";

        if (fbUrlData != null && fbUrlData.getFileBrowserCookie() != null) {
            // Jersey 1.17.1 does not support HttpOnly cookies, so we have to add it manually
            return Response.ok(jsonResponse)
                    .header("Set-Cookie", fbUrlData.getFileBrowserCookie().toString() + "; HttpOnly")
                    .build();
        }

        // If cookie does not exists, return response without it
        return Response.ok(jsonResponse).build();
    }

    /**
     * Logout FB session with given token.
     * 
     * @param id connection id
     * @param fbToken FB session token
     * 
     */
    @POST
    @Path("/logout")
    public void logout(@QueryParam("id") Integer id, @QueryParam("fbToken") String fbToken) {
        logger.debug("Logout FB called");

        String username = getParameter(id, EncurlParameters.USERNAME_PARM);

        try { 
            if (service.logout(fbToken))
                logger.info("[{}:{}] Logout FB success", id, username);
            else
                logger.error("[{}:{}] Logout FB error", id, username);
        } catch (MalformedURLException e) {
            logger.error("[{}:{}] Malformed url", id, username, e);
        } catch (IOException e) {
            logger.error("[{}:{}] Cannot create connection", id, username, e);
        } catch (GuacamoleException e) {
            logger.error("[{}:{}] Cannot read file browser configuration from guacamole.properties. Check if API key, FB server or region are present.", id, username, e);
        }

        logger.info("Logout FB exit");
    }


    @GET
    @Path("/status")
    @Produces(MediaType.TEXT_PLAIN)
    public String healthCheck() throws GuacamoleException {
        Boolean status = service.getStatusConfigured();
        if(status){
        return  "status-configured";
        }else{
        return  "status-not-configured";
        }
    }
}

