/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.net.encryptedurl.jdbc;

import org.apache.guacamole.auth.jdbc.InjectedAuthenticationProvider;
import org.apache.guacamole.auth.jdbc.JDBCEnvironment;
import org.apache.guacamole.auth.jdbc.activeconnection.ActiveConnectionDirectory;
import org.apache.guacamole.auth.jdbc.activeconnection.ActiveConnectionPermissionService;
import org.apache.guacamole.auth.jdbc.activeconnection.ActiveConnectionPermissionSet;
import org.apache.guacamole.auth.jdbc.activeconnection.ActiveConnectionService;
import org.apache.guacamole.auth.jdbc.activeconnection.TrackedActiveConnection;
import org.apache.guacamole.auth.jdbc.base.EntityMapper;
import org.apache.guacamole.auth.jdbc.base.EntityService;
import org.apache.guacamole.auth.jdbc.connection.ConnectionDirectory;
import org.apache.guacamole.auth.jdbc.connection.ConnectionMapper;
import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterMapper;
import org.apache.guacamole.auth.jdbc.connection.ConnectionRecordMapper;
import org.apache.guacamole.auth.jdbc.connection.ConnectionService;
import org.apache.guacamole.auth.jdbc.connection.ModeledConnection;
import org.apache.guacamole.auth.jdbc.connection.ModeledGuacamoleConfiguration;
import org.apache.guacamole.auth.jdbc.connectiongroup.ConnectionGroupDirectory;
import org.apache.guacamole.auth.jdbc.connectiongroup.ConnectionGroupMapper;
import org.apache.guacamole.auth.jdbc.connectiongroup.ConnectionGroupService;
import org.apache.guacamole.auth.jdbc.connectiongroup.ModeledConnectionGroup;
import org.apache.guacamole.auth.jdbc.connectiongroup.RootConnectionGroup;
import org.apache.guacamole.auth.jdbc.permission.ConnectionGroupPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.ConnectionGroupPermissionService;
import org.apache.guacamole.auth.jdbc.permission.ConnectionGroupPermissionSet;
import org.apache.guacamole.auth.jdbc.permission.ConnectionPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.ConnectionPermissionService;
import org.apache.guacamole.auth.jdbc.permission.ConnectionPermissionSet;
import org.apache.guacamole.auth.jdbc.permission.SharingProfilePermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.SharingProfilePermissionService;
import org.apache.guacamole.auth.jdbc.permission.SharingProfilePermissionSet;
import org.apache.guacamole.auth.jdbc.permission.SystemPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.SystemPermissionService;
import org.apache.guacamole.auth.jdbc.permission.SystemPermissionSet;
import org.apache.guacamole.auth.jdbc.permission.UserGroupPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.UserGroupPermissionService;
import org.apache.guacamole.auth.jdbc.permission.UserGroupPermissionSet;
import org.apache.guacamole.auth.jdbc.permission.UserPermissionMapper;
import org.apache.guacamole.auth.jdbc.permission.UserPermissionService;
import org.apache.guacamole.auth.jdbc.permission.UserPermissionSet;
import org.apache.guacamole.auth.jdbc.security.PasswordEncryptionService;
import org.apache.guacamole.auth.jdbc.security.PasswordPolicyService;
import org.apache.guacamole.auth.jdbc.security.SHA256PasswordEncryptionService;
import org.apache.guacamole.auth.jdbc.security.SaltService;
import org.apache.guacamole.auth.jdbc.security.SecureRandomSaltService;
import org.apache.guacamole.auth.jdbc.sharing.ConnectionSharingService;
import org.apache.guacamole.auth.jdbc.sharing.SecureRandomShareKeyGenerator;
import org.apache.guacamole.auth.jdbc.sharing.ShareKeyGenerator;
import org.apache.guacamole.auth.jdbc.sharing.SharedConnectionMap;
import org.apache.guacamole.auth.jdbc.sharing.user.SharedUserContext;
import org.apache.guacamole.auth.jdbc.sharingprofile.ModeledSharingProfile;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileDirectory;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileMapper;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileParameterMapper;
import org.apache.guacamole.auth.jdbc.sharingprofile.SharingProfileService;
import org.apache.guacamole.auth.jdbc.tunnel.GuacamoleTunnelService;
import org.apache.guacamole.auth.jdbc.tunnel.RestrictedGuacamoleTunnelService;
import org.apache.guacamole.auth.jdbc.user.ModeledUser;
import org.apache.guacamole.auth.jdbc.user.ModeledUserContext;
import org.apache.guacamole.auth.jdbc.user.PasswordRecordMapper;
import org.apache.guacamole.auth.jdbc.user.UserDirectory;
import org.apache.guacamole.auth.jdbc.user.UserMapper;
import org.apache.guacamole.auth.jdbc.user.UserParentUserGroupMapper;
import org.apache.guacamole.auth.jdbc.user.UserRecordMapper;
import org.apache.guacamole.auth.jdbc.user.UserService;
import org.apache.guacamole.auth.jdbc.usergroup.ModeledUserGroup;
import org.apache.guacamole.auth.jdbc.usergroup.UserGroupDirectory;
import org.apache.guacamole.auth.jdbc.usergroup.UserGroupMapper;
import org.apache.guacamole.auth.jdbc.usergroup.UserGroupMemberUserGroupMapper;
import org.apache.guacamole.auth.jdbc.usergroup.UserGroupMemberUserMapper;
import org.apache.guacamole.auth.jdbc.usergroup.UserGroupParentUserGroupMapper;
import org.apache.guacamole.auth.jdbc.usergroup.UserGroupService;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.ibatis.transaction.jdbc.JdbcTransactionFactory;
import org.mybatis.guice.MyBatisModule;
import org.mybatis.guice.datasource.builtin.PooledDataSourceProvider;

import com.google.inject.Scopes;
import com.google.inject.assistedinject.FactoryModuleBuilder;

import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionMapper;
import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionParameterMapper;
import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.auth.jdbc.user.sharing.ApportoSharedUserContext;
import rs.jsw.guacamole.net.encryptedurl.mysql.EncurlMySQLAuthenticationProvider;
import rs.jsw.guacamole.net.encryptedurl.redis.RedisModule;
import rs.jsw.guacamole.net.encryptedurl.rest.EncurlProviderResourceFactory;
import rs.jsw.guacamole.rest.ApportoResourceFactory;
import rs.jsw.guacamole.rest.FileDetailsResourceFactory;
import rs.jsw.guacamole.rest.LaterResourceFactory;
import rs.jsw.guacamole.rest.LoadBalancerResourceFactory;
import rs.jsw.guacamole.rest.MessagingResourceFactory;
import rs.jsw.guacamole.rest.MessengerAuthResourceFactory;
import rs.jsw.guacamole.rest.NetworkQualityResourceFactory;
import rs.jsw.guacamole.rest.RDPRouterResourceFactory;
import rs.jsw.guacamole.rest.SnapshotsResourceFactory;
import rs.jsw.guacamole.rest.chatbot.ChatbotResourceFactory;
import rs.jsw.guacamole.rest.classroom.ClassroomModule;
import rs.jsw.guacamole.rest.classroom.ClassroomResourceFactory;
import rs.jsw.guacamole.rest.classroom.WebSocketInit;
import rs.jsw.guacamole.rest.filebrowser.FileBrowserResourceFactory;
import rs.jsw.guacamole.rest.mmonitornotify.MMonitorNotificationResourceFactory;
import rs.jsw.guacamole.rest.multimonitor.DisplayResourceFactory;
import rs.jsw.guacamole.rest.multimonitor.SessionDesktop;
import rs.jsw.guacamole.rest.notification.NotificationResourceFactory;
import rs.jsw.guacamole.rest.presenter.PresenterResourceFactory;
import rs.jsw.guacamole.rest.properties.ServerPropertiesResourceFactory;
import rs.jsw.guacamole.rest.thumbnails.ThumbnailResourceFactory;
import rs.jsw.guacamole.rest.vm.VmBackupResourceFactory;
import rs.jsw.guacamole.rest.vm.VmRebootResourceFactory;
import rs.jsw.guacamole.rest.vm.VmSessionResourceFactory;

public class EncurlJDBCAuthenticationProviderModule extends MyBatisModule {

    /**
     * The environment of the Guacamole server.
     */
    private final JDBCEnvironment environment;

    /**
     * Encurl auth provider service
     */
    private final EncurlJDBCAuthenticationProviderService authProviderService;

    /**
     * Constructor
     */
    public EncurlJDBCAuthenticationProviderModule(JDBCEnvironment environment) {
        this.environment = environment;
        authProviderService = new EncurlJDBCAuthenticationProviderService();
    }

    @Override
    protected void initialize() {

        // Datasource
        bindDataSourceProviderType(PooledDataSourceProvider.class);

        // Transaction factory
        bindTransactionFactoryType(JdbcTransactionFactory.class);

        // Add MyBatis mappers
        addMapperClass(ConnectionMapper.class);
        addMapperClass(ConnectionGroupMapper.class);
        addMapperClass(ConnectionGroupPermissionMapper.class);
        addMapperClass(ConnectionPermissionMapper.class);
        addMapperClass(ConnectionRecordMapper.class);
        addMapperClass(ConnectionParameterMapper.class);
        addMapperClass(EntityMapper.class);
        addMapperClass(PasswordRecordMapper.class);
        addMapperClass(SystemPermissionMapper.class);
        addMapperClass(SharingProfileMapper.class);
        addMapperClass(SharingProfileParameterMapper.class);
        addMapperClass(SharingProfilePermissionMapper.class);
        addMapperClass(UserGroupMapper.class);
        addMapperClass(UserGroupMemberUserGroupMapper.class);
        addMapperClass(UserGroupMemberUserMapper.class);
        addMapperClass(UserGroupParentUserGroupMapper.class);
        addMapperClass(UserGroupPermissionMapper.class);
        addMapperClass(UserMapper.class);
        addMapperClass(UserParentUserGroupMapper.class);
        addMapperClass(UserPermissionMapper.class);
        addMapperClass(UserRecordMapper.class);

        // Bind core implementations of guacamole-ext classes
        bind(ActiveConnectionDirectory.class);
        bind(ActiveConnectionPermissionSet.class);
        bind(JDBCEnvironment.class).toInstance(environment);
        bind(ConnectionDirectory.class);
        bind(ConnectionGroupDirectory.class);
        bind(ConnectionGroupPermissionSet.class);
        bind(ConnectionPermissionSet.class);
        bind(ModeledConnection.class);
        bind(ModeledConnectionGroup.class);
        bind(ModeledGuacamoleConfiguration.class);
        bind(ModeledSharingProfile.class);
        bind(ModeledUser.class);
//        bind(ModeledUserContext.class);
        bind(ModeledUserGroup.class);
        bind(RootConnectionGroup.class);
        bind(SharingProfileDirectory.class);
        bind(SharingProfilePermissionSet.class);
        bind(SystemPermissionSet.class);
        bind(TrackedActiveConnection.class);
        bind(UserDirectory.class);
        bind(UserGroupDirectory.class);
        bind(UserGroupPermissionSet.class);
        bind(UserPermissionSet.class);

        // Bind services
        bind(ActiveConnectionService.class);
        bind(ActiveConnectionPermissionService.class);
        bind(ConnectionGroupPermissionService.class);
        bind(ConnectionGroupService.class);
        bind(ConnectionPermissionService.class);
        bind(ConnectionSharingService.class);
        bind(ConnectionService.class);
        bind(EntityService.class);
        bind(GuacamoleTunnelService.class).to(RestrictedGuacamoleTunnelService.class);
        bind(PasswordEncryptionService.class).to(SHA256PasswordEncryptionService.class);
        bind(PasswordPolicyService.class);
        bind(SaltService.class).to(SecureRandomSaltService.class);
        bind(SharedConnectionMap.class).to(ApportoSharedConnectionMap.class).in(Scopes.SINGLETON);
        bind(ShareKeyGenerator.class).to(SecureRandomShareKeyGenerator.class).in(Scopes.SINGLETON);
        bind(SharingProfilePermissionService.class);
        bind(SharingProfileService.class);
        bind(SystemPermissionService.class);
        bind(UserGroupService.class);
        bind(UserGroupPermissionService.class);
        bind(UserPermissionService.class);
        bind(UserService.class);

        // Bind apporto specifics
        addMapperClass(EncUrlConnectionMapper.class);
        addMapperClass(EncUrlConnectionParameterMapper.class);

        bind(EncurlJDBCAuthenticationProviderService.class).toInstance(authProviderService);
        bind(ModeledUserContext.class).to(ApportoUserContext.class);
        bind(SharedUserContext.class).to(ApportoSharedUserContext.class);
        bind(InjectedAuthenticationProvider.class).to(EncurlMySQLAuthenticationProvider.class);
        bind(SessionDesktop.class).in(Scopes.SINGLETON);

        install(new FactoryModuleBuilder().build(EncurlProviderResourceFactory.class));
        install(new FactoryModuleBuilder().build(ApportoResourceFactory.class));
        install(new FactoryModuleBuilder().build(SnapshotsResourceFactory.class));
        install(new FactoryModuleBuilder().build(LaterResourceFactory.class));
        install(new FactoryModuleBuilder().build(MessagingResourceFactory.class));
        install(new FactoryModuleBuilder().build(FileDetailsResourceFactory.class));
        install(new FactoryModuleBuilder().build(NetworkQualityResourceFactory.class));
        install(new FactoryModuleBuilder().build(NotificationResourceFactory.class));
        install(new FactoryModuleBuilder().build(ServerPropertiesResourceFactory.class));
        install(new FactoryModuleBuilder().build(MessengerAuthResourceFactory.class));
        install(new FactoryModuleBuilder().build(VmRebootResourceFactory.class));
        install(new FactoryModuleBuilder().build(DisplayResourceFactory.class));
        install(new FactoryModuleBuilder().build(VmBackupResourceFactory.class));
        install(new FactoryModuleBuilder().build(FileBrowserResourceFactory.class));
        install(new FactoryModuleBuilder().build(ChatbotResourceFactory.class));
        install(new FactoryModuleBuilder().build(PresenterResourceFactory.class));
        install(new FactoryModuleBuilder().build(VmSessionResourceFactory.class));
        install(new FactoryModuleBuilder().build(ThumbnailResourceFactory.class));
        install(new FactoryModuleBuilder().build(LoadBalancerResourceFactory.class));
        install(new FactoryModuleBuilder().build(RDPRouterResourceFactory.class));
        install(new FactoryModuleBuilder().build(MMonitorNotificationResourceFactory.class));
        install(new FactoryModuleBuilder().build(ClassroomResourceFactory.class));

        bind(WebSocketInit.class).asEagerSingleton();
        bind(LocalEnvironment.class);

        install(new RedisModule());
        install(new ClassroomModule());
    }
}
