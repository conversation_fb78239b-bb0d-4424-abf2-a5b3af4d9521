package rs.jsw.guacamole.rest;

import java.net.MalformedURLException;
import java.net.URL;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.GuacamoleCommonUtility;
import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * A REST Service for Intro functionality.
 *
 * <AUTHOR> Vujaković
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class LaterResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(LaterResource.class);

    // Default domain if not supplied in subdomain parameter
    private static String DEFAULT_DOMAIN = "apporto.com";

    // delete
    // https://{subdomain}.apporto.com/apporto/intro/later/delete/{session id}
    private static final String later_delete_template = "https://%s/apporto/intro/later/delete/%s";

    // post-put
    // https://{subdomain}.apporto.com/apporto/intro/later/{session id}
    private static final String later_add_template = "https://%s/apporto/intro/later/%s";

    /**
     * Creates a new LaterResource which exposes the Subdomain parameter
     * associated with current user, and enables managing behavior of Intro
     * functionality.
     *
     * @param userContext The UserContext which provides information about
     * current user.
     */
    @AssistedInject
    public LaterResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Load the proper configuration variable(s)
     */
    static {
        try {
            Environment environment = new LocalEnvironment();
            String propValue;

            propValue = environment.getProperty(ApportoProperties.API_BASE_DOMAIN);
            if (propValue != null) {
                DEFAULT_DOMAIN = propValue;
            }

        }
        catch (GuacamoleException e) {
            logger.warn("Cannot get `api-base-domain` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    /**
     * Removes later value from Drupal.
     *
     * @return The name of the Subdomain from which the removal was done, if
     * successful; Value "subdomain" otherwise.
     *
     * @throws GuacamoleException if invalid request
     */
    @DELETE
    public Response deleteLater(@QueryParam("id") Integer id) throws GuacamoleException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] deleteLater", conn_id, connection_type, cloud_user);

        String subdomain = getSubdomain(id);
        if (subdomain != null && !subdomain.isEmpty()) {
            String url_str = String.format(later_delete_template, subdomain, id);

            try {
                URL url = new URL(url_str);
                sendToDrupal(url, id, conn_id, connection_type, cloud_user);
            }
            catch (MalformedURLException e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }
        }
        else {
            logger.error("[{}:{}:{}] User associated with id param does not exist or Subdomain not-specified for that user.",
                          conn_id, connection_type, cloud_user);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Save later value to Drupal.
     *
     * @return Subdomain name if successful, "subdomain" value otherwise
     *
     * @throws GuacamoleException
     */
    @PUT
    public Response putLater(@QueryParam("id") Integer id) throws GuacamoleException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] putLater", conn_id, cloud_user);

        String subdomain = getSubdomain(id);
        if (subdomain != null && !subdomain.isEmpty()) {
            String url_str = String.format(later_add_template, subdomain, id);

            try {
                URL url = new URL(url_str);
                sendToDrupal(url, id, conn_id, connection_type, cloud_user);
            }
            catch (MalformedURLException e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }
        }
        else {
            logger.error("[{}:{}:{}] User associated with id param does not exist or Subdomain not-specified for that user.",
                          conn_id, connection_type, cloud_user);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Save later value to Drupal.
     *
     * @return Subdomain name if successful, "subdomain" value otherwise
     *
     * @throws GuacamoleException
     */
    @POST
    public Response postLater(@QueryParam("id") Integer id) throws GuacamoleException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] postLater", conn_id, connection_type, cloud_user);

        String subdomain = getSubdomain(id);
        if (subdomain != null && !subdomain.isEmpty()) {
            String url_str = String.format(later_add_template, subdomain, id);

            try {
                URL url = new URL(url_str);
                sendToDrupal(url, id, conn_id, connection_type, cloud_user);
            }
            catch (MalformedURLException e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }
        }
        else {
            logger.error("[{}:{}:{}] User associated with id param does not exist or Subdomain not-specified for that user.",
                          conn_id, connection_type, cloud_user);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Obtaining Subdomain parametar associated with current user.
     *
     * @param id The String which uniquely identifies current user.
     * @return Subdomain value if successful, empty String if id param is
     * invalid, null value if subdomain is not specified for current user.
     *
     * @throws GuacamoleException
     */
    private String getSubdomain(Integer id) throws GuacamoleException {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] getSubdomain", conn_id, connection_type, cloud_user);

        String subdomain = getParameter(id, EncurlParameters.SUBDOMAIN_PARM);
        if (subdomain != null) {
            subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);
        }
        else { // return value equals null
            logger.warn("[{}:{}:{}] Drupal subdomain not specified", conn_id, connection_type, cloud_user);
        }

        logger.info("[{}:{}:{}] Subdomain = {}", conn_id, connection_type, cloud_user, subdomain);

        return subdomain;
    }

}
