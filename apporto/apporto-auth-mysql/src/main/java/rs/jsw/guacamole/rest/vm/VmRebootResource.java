package rs.jsw.guacamole.rest.vm;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.json.JSONArray;
import org.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A REST Service for vm reboot.
 *
 * <AUTHOR> Babic
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class VmRebootResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final static Logger logger = LoggerFactory.getLogger(VmRebootResource.class);

    /**
     * HAProxy capacity api secret key
     */
    private static String SECRET_KEY;

    /**
     * HAProxy capacity api URL
     */
    private static String HAP_CAPACITY_API_SERVICE_REBOOT;

    @AssistedInject
    public VmRebootResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    static {
        StringGuacamoleProperty prop = (StringGuacamoleProperty) ApportoProperties.getProp("hap-capacity-api-service-reboot");
        StringGuacamoleProperty propSecretKey = (StringGuacamoleProperty) ApportoProperties.getProp("hap-capacity-api-secret-key");

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the HAProxy server from guacamole.properties
            HAP_CAPACITY_API_SERVICE_REBOOT = environment.getProperty(prop);
            logger.info("hap-capacity-api-service-reboot property: {}", HAP_CAPACITY_API_SERVICE_REBOOT);
        }
        catch (Exception e) {
            logger.warn("Cannot get `hap-capacity-api-service-reboot` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the HAProxy service secret key from guacamole.properties
            SECRET_KEY = environment.getProperty(propSecretKey);

            if (SECRET_KEY == null || SECRET_KEY.isEmpty()) {
                logger.error("hap-capacity-api-secret-key is empty.");
            }
        }
        catch (Exception e) {
            logger.warn("Cannot get `hap-capacity-api-secret-key` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    @GET
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response reboot(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        if (HAP_CAPACITY_API_SERVICE_REBOOT == null || HAP_CAPACITY_API_SERVICE_REBOOT.isEmpty()) {
            logger.info("[{}:{}:{}] Because the address of haproxy is null, " +
                        "it couldn't query the server capactiy to the haproxy.",
                        conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("success", false);
            error.put("status_msg", "The address of the haproxy couldn't be found.");

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        logger.info("[{}:{}:{}] querying the server capactiy to the haproxy.", conn_id, connection_type, cloud_user);

        String hostname        = getParameter(id, EncurlParameters.HOSTNAME_PARM);
        String username        = getParameter(id, EncurlParameters.USERNAME_PARM);
        String lb_port         = getParameter(id, EncurlParameters.PORT_PARM);
        String cloud_provider  = getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM);
        String requiredServers = getParameter(id, EncurlParameters.REQUIRED_SERVERS);

        JSONObject httpBody = new JSONObject();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;
        StringBuffer responseMessage = new StringBuffer();

        TrustManager[] trustAllCerts = new TrustManager[] {
            new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };

        try {
            httpBody.put("rdp_host", hostname);
            httpBody.put("rdp_port", lb_port);
            httpBody.put("username", username);
            if (requiredServers != null && !requiredServers.isEmpty()) {
                List<String> requiredServerList = Arrays.asList(requiredServers.replaceAll("\\s", "").split(","));
                JSONArray requiredServerJsonArray = new JSONArray();
                for (int i = 0; i < requiredServerList.size(); i++) {
                    requiredServerJsonArray.put(requiredServerList.get(i));
                }
                httpBody.put("required_servers", requiredServerJsonArray);
            }
            if (cloud_provider != null && !cloud_provider.isEmpty()) {
                httpBody.put("cloud_provider", cloud_provider);
            }

            // Calculate the signature of the http body
            String message = httpBody.toString();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Install the all-trusting trust manager
            URL url = new URL(String.format(HAP_CAPACITY_API_SERVICE_REBOOT, hostname));
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            logger.info("[{}:{}:{}] Checking serverfarm capacity for {}:{} ({}) using capacity api: {}",
                         conn_id, connection_type, cloud_user, hostname, lb_port, cloud_provider, url.toString());

            int connectTimeout = 5000; // Set the connect timeout to 5 seconds
            int readTimeout = 20000; // Set the read timeout to 20 seconds
            // Connect WebAPI
            HttpsURLConnection  con = (HttpsURLConnection ) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("User-Agent", "Java client");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setConnectTimeout(connectTimeout);
            con.setReadTimeout(readTimeout);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());

            responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                responseMessage.append(inputLine);
            }
            in.close();

            logger.info("[{}:{}:{}] Capacity api ({}, {}) with response code ({}) for {}:{} ({})",
                         conn_id, connection_type, cloud_user, url.toString(), httpBody, responseCode, hostname, lb_port, cloud_provider);

            logger.info("[{}:{}:{}] ResponseMsg={}", conn_id, connection_type, cloud_user, responseMessage.toString());
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);

            JSONObject error = new JSONObject();
            error.put("success", false);
            error.put("status_msg", "Exception while reading capacity API. Exception: " + e.toString());

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        return Response.ok(responseMessage.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }
}
