/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom.thumbnails;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.inject.Singleton;
import javax.websocket.Session;

/**
 * Although this class is declared as Singleton, its methods must be static.
 * 
 * The issue is that it is registered in Guice as Singleton, but it is through
 * two different injectors, one in the class ThumbnailsRetrieveWS and the other
 * in the class ThumbnailsStoreWS. Different injectors do not share the same
 * information about singleton instance, so each of them will create a new instance.
 * 
 * This is done because the above classes are created by the 3rd party library,
 * so we have to inject the dependencies manually.
 */
@Singleton
public class WSSessionStore {
    private final ConcurrentMap<Long, Session> sessions = new ConcurrentHashMap<>();

    public void addSession(Long sessionId, Session session) {
        sessions.put(sessionId, session);
    }

    public void removeSession(Long sessionId) {
        sessions.remove(sessionId);
    }

    public Session getSession(Long sessionId) {
        return sessions.get(sessionId);
    }

    public boolean isEmpty() {
        return sessions.isEmpty();
    }

    public int size() {
        return sessions.size();
    }
}
