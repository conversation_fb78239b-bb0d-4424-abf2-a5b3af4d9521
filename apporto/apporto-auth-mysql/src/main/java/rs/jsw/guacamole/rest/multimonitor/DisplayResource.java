/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest.multimonitor;

import java.util.HashMap;
import java.util.Map;

import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.UserContext;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.auth.jdbc.user.UserSessionDesktop;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

/**
 * A REST Service for getting primary and secondary display properties.
 * 
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
public class DisplayResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(DisplayResource.class);
    
    /**
     * Creates a new UserContextResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public DisplayResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Returns size of all displays
     * 
     * @return value - size of all monitors.
     * 
     * @throws GuacamoleException 
     */
    @GET
    @Produces("application/json")
    @Path("/all")
    public Response getAllDisplaySizes(@QueryParam("id") Integer id) throws GuacamoleException {

        Map<String, Double> jsonProps = new HashMap<>();

        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;

            try {
                MonitorsProperties monitors = cntx.getSessionDesktop().getMonitors(id);
                jsonProps.put("primWidth", monitors.getPrimaryWidth());
                jsonProps.put("primHeight", monitors.getPrimaryHeight());
                jsonProps.put("primScale", monitors.getPrimaryScale());
                jsonProps.put("secWidth", monitors.getSecondaryWidth());
                jsonProps.put("secHeight", monitors.getSecondaryHeight());
                jsonProps.put("secScale", monitors.getSecondaryScale());
                jsonProps.put("terWidth", monitors.getTertiaryWidth());
                jsonProps.put("terHeight", monitors.getTertiaryHeight());
                jsonProps.put("terScale", monitors.getTertiaryScale());
            }
            catch (Exception e) {
                // Get the connection info
                String conn_id = Integer.toString(id);
                String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
                String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

                logger.error("[{}:{}:{}] Cannot retrieve all monitors sizes.", conn_id, conn_type, cloud_user);
            }
        }

        JSONObject jsonResult = new JSONObject(jsonProps);

        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }
    
    
    /**
     * Returns size of the primary display
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @GET
    @Produces("application/json")
    @Path("/primary")
    public Response getPrimaryDisplaySize(@QueryParam("id") Integer id) throws GuacamoleException {
        Map<String, Double> jsonProps = new HashMap<>();

        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;
            
            try {
                MonitorsProperties monitors = cntx.getSessionDesktop().getMonitors(id);
                jsonProps.put("width", monitors.getPrimaryWidth());
                jsonProps.put("height", monitors.getPrimaryHeight());
                jsonProps.put("scale", monitors.getPrimaryScale());
            }
            catch (Exception e) {
                // Get the connection info
                String conn_id = Integer.toString(id);
                String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
                String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

                logger.error("[{}:{}:{}] Cannot retrieve primary monitors sizes.", conn_id, conn_type, cloud_user);
            }
        }

        JSONObject jsonResult = new JSONObject(jsonProps);
        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Set size of the primary display
     * 
     * @param width - width of the display
     * @param height - height of the display
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @POST
    @Path("/primary")
    public Response setPrimaryDisplaySize(@QueryParam("id") Integer id,
                                          @QueryParam("width") Double width,
                                          @QueryParam("height") Double height,
                                          @QueryParam("scale") Double scale) throws GuacamoleException {
        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;
            
            MonitorsProperties monitors;
            if (cntx.getSessionDesktop().hasSession(id)) {
                monitors = cntx.getSessionDesktop().getMonitors(id);
            }
            else {
                monitors= new MonitorsProperties();
            }

            monitors.setPrimaryWidth(width);
            monitors.setPrimaryHeight(height);
            monitors.setPrimaryScale(scale);
            
            cntx.getSessionDesktop().putMonitors(id, monitors);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Returns size of the secondary display
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @GET
    @Produces("application/json")
    @Path("/secondary")
    public Response getSecondaryDisplaySize(@QueryParam("id") Integer id) throws GuacamoleException {
        Map<String, Object> jsonProps = new HashMap<>();

        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;
            
            try {
                MonitorsProperties monitors = cntx.getSessionDesktop().getMonitors(id);
                jsonProps.put("width", monitors.getSecondaryWidth());
                jsonProps.put("height", monitors.getSecondaryHeight());
                jsonProps.put("scale", monitors.getSecondaryScale());
            }
            catch (Exception e) {
                // Get the connection info
                String conn_id = Integer.toString(id);
                String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
                String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

                logger.error("[{}:{}:{}] Cannot retrieve secondary monitors sizes.", conn_id, conn_type, cloud_user);
            }
        }
        else {
            jsonProps.put("width", -1);
            jsonProps.put("height", -1);
            jsonProps.put("scale", -1);
        }

        JSONObject jsonResult = new JSONObject(jsonProps);
        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Set size of the secondary display
     * 
     * @param width - width of the display
     * @param height - height of the display
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @POST
    @Path("/secondary")
    public Response setSecondaryDisplaySize(@QueryParam("id") Integer id,
                                            @QueryParam("width") Double width,
                                            @QueryParam("height") Double height,
                                            @QueryParam("scale") Double scale) throws GuacamoleException {
        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;

            MonitorsProperties monitors;
            if (cntx.getSessionDesktop().hasSession(id)) {
                monitors = cntx.getSessionDesktop().getMonitors(id);
            }
            else {
                monitors= new MonitorsProperties();
            }

            monitors.setSecondaryWidth(width);
            monitors.setSecondaryHeight(height);
            monitors.setSecondaryScale(scale);

            cntx.getSessionDesktop().putMonitors(id, monitors);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Returns size of the tertiary display
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @GET
    @Produces("application/json")
    @Path("/tertiary")
    public Response getTertiaryDisplaySize(@QueryParam("id") Integer id) throws GuacamoleException {
        Map<String, Object> jsonProps = new HashMap<>();

        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;

            try {
                MonitorsProperties monitors = cntx.getSessionDesktop().getMonitors(id);
                jsonProps.put("width", monitors.getTertiaryWidth());
                jsonProps.put("height", monitors.getTertiaryHeight());
                jsonProps.put("scale", monitors.getTertiaryScale());
            }
            catch (Exception e) {
                String conn_id = Integer.toString(id);
                String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
                String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

                logger.error("[{}:{}:{}] Cannot retrieve tertiary monitors sizes.", conn_id, conn_type, cloud_user);
            }
        }
        else {
            jsonProps.put("width", -1);
            jsonProps.put("height", -1);
            jsonProps.put("scale", -1);
        }

        JSONObject jsonResult = new JSONObject(jsonProps);
        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Set size of the tertiary display
     * 
     * @param width - width of the display
     * @param height - height of the display
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @POST
    @Path("/tertiary")
    public Response setTertiaryDisplaySize(@QueryParam("id") Integer id,
                                            @QueryParam("width") Double width,
                                            @QueryParam("height") Double height,
                                            @QueryParam("scale") Double scale) throws GuacamoleException {
        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;

            MonitorsProperties monitors;
            if (cntx.getSessionDesktop().hasSession(id)) {
                monitors = cntx.getSessionDesktop().getMonitors(id);
            }
            else {
                monitors= new MonitorsProperties();
            }

            monitors.setTertiaryWidth(width);
            monitors.setTertiaryHeight(height);
            monitors.setTertiaryScale(scale);

            cntx.getSessionDesktop().putMonitors(id, monitors);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Delete monitors configuration
     * 
     * @throws GuacamoleException 
     */
    @POST
    @Path("/remove")
    public Response deleteMonitors(@QueryParam("id") Integer id) throws GuacamoleException {
        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;
            cntx.getSessionDesktop().removeMonitors(id);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

}
