/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom.thumbnails;

import java.io.IOException;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;

import org.codehaus.jackson.JsonProcessingException;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;
import com.google.inject.Injector;

import rs.jsw.guacamole.rest.classroom.ClassroomInjector;
import rs.jsw.guacamole.rest.classroom.ClassroomModule;
import rs.jsw.guacamole.rest.classroom.WSEndpoint;
import rs.jsw.guacamole.rest.classroom.commands.ThumbList;

public class ThumbnailsRetrieveWS implements WSEndpoint {
    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ThumbnailsRetrieveWS.class);

    ObjectMapper mapper = new ObjectMapper();

    @Inject
    private ThumbnailsManager thumbnailsManager;

    @Inject
    private WSSessionStore sessionStore;

    public ThumbnailsRetrieveWS() {
        logger.info("ThumbnailsRetrieveWS");

        // This class is created by the 3rd party library, so we have to inject the dependencies manually.
        Injector injector = ClassroomInjector.getInjector();
        injector.injectMembers(this);
    }

    @OnOpen
    public void onOpen(Session session, @PathParam("id") Long id) throws IOException {
        logger.debug("OnOpen");
        sessionStore.addSession(id, session);
        thumbnailsManager.open();
    }

    @OnMessage
    public void onMessage(Session session,
                          String message,
                          @PathParam("id") Long id
                          ) throws IOException {
        logger.info("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage, message: {}", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", message);

        if (id == null) {
            logger.error("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
            return;
        }

        try {
            String command = mapper.readTree(message).get("command").asText();

            processCommand(session, id, message, command);

        } catch (JsonProcessingException e) {
            logger.error("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage - Failed to parse message JSON", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", e);
            return;
        }
    }

    private void processCommand(Session session, Long id, String message, String command) {
        switch (command) {
        case "request":
            logger.info("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage - request command", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");

            ThumbList requestThumb;
            try {
                requestThumb = mapper.readValue(message, ThumbList.class);
            } catch (IOException e) {
                logger.error("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage - Failed to parse requestThumb", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", e);
                return;
            }

            thumbnailsManager.sendThumbs(id, requestThumb);

            break;

        case "subscribe":
            logger.info("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage - subscribe command", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");

            ThumbList subscribeThumb;
            try {
                subscribeThumb = mapper.readValue(message, ThumbList.class);
            } catch (IOException e) {
                logger.error("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage - Failed to parse requestThumb", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", e);
                return;
            }

            thumbnailsManager.addSubscription(id, subscribeThumb);

            break;

        default:
            logger.error("[{}:{}:{}] ThumbnailsRetrieveWS OnMessage - Unknown command: {}", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", command);
        }
    }


    @OnClose
    public void onClose(Session session, @PathParam("id") Long id) throws IOException {
        logger.info("[{}:{}:{}] ThumbnailsRetrieveWS OnClose, remaining sessions {}", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", sessionStore.size());
        sessionStore.removeSession(id);

        if (sessionStore.isEmpty()) {
            thumbnailsManager.close();
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable, @PathParam("id") Long id) {
        logger.error("[{}:{}:{}] ThumbnailsRetrieveWS OnError", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", throwable);
    }
}
