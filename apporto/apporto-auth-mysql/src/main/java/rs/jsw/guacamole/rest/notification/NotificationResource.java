/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest.notification;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Locale;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.FormParam;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.codehaus.jackson.map.ObjectMapper;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import lombok.extern.slf4j.Slf4j;
import rs.jsw.guacamole.auth.jdbc.user.UserSessionNotifications;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;
import rs.jsw.guacamole.rest.notification.dto.ResponseDTO;

/**
 * A REST Service for notifying remote clients. It is possible to send messages
 * to remote clients and to execute simple commands, like disconnecting clients.
 *
 * <AUTHOR> Nikolić
 */
@Slf4j
@Produces(MediaType.APPLICATION_JSON)
public class NotificationResource extends BaseResource {

    /**
     * Sync message for notifications, also used for properly flushing buffer
     */
    private static final String dummyString10 = "##########";
    private static final Message syncMessage = new Message(dummyString10, dummyString10,
                                                           MessageLevelCode.NONE,
                                                           MessageCommandCode.SYNC, 0L);

    @Inject
    LocalEnvironment environment;

    /**
     * Creates a new UserContextResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public NotificationResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Receives notification for a given user. The notification will be stored
     * in the notification queue, where user can read it.
     *
     * @return ok if the notification is successfully received.
     *
     * @throws GuacamoleException
     */
    @POST
    @Path("/{id}")
    public Response notifyUser(@PathParam("id") Long id,
                               @QueryParam("title") String title,
                               @QueryParam("text") String text,
                               @QueryParam("level") String level,
                               @QueryParam("command") String command,
                               @QueryParam("delay") Long delay,
                               @QueryParam("data") String data,
                               @Context HttpServletRequest request) throws GuacamoleException {

        request.setAttribute("log", "stop");

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.debug("[{}:{}:{}] Notification received.", conn_id, conn_type, cloud_user);

        title = (title == null) ? "" : title;
        text = (text == null) ? "" : text;
        level = ((level == null) || ("".equals(level.trim()))) ? "UNKNOWN" : level;
        command = ((command == null) || ("".equals(command.trim()))) ? "UNKNOWN" : command;
        delay = (delay == null) ? 0 : delay;
        data = (data == null) ? "" : data;

        Status status = Status.OK;

        try {
            logger.debug("[{}:{}:{}] Trying to add notification message to queue...", conn_id, conn_type, cloud_user);
            BlockingQueue<Message> myMessages = ((UserSessionNotifications)userContext).getSessionNotifications(id);
            myMessages.put(new Message(title, text, MessageLevelCode.valueOf(level.toUpperCase(Locale.ENGLISH)),
                                       MessageCommandCode.valueOf(command.toUpperCase(Locale.ENGLISH)), delay, data));
            logger.debug("[{}:{}:{}] Added notification message to queue.", conn_id, conn_type, cloud_user);
        }
        catch (InterruptedException e) {
            status = Status.INTERNAL_SERVER_ERROR;
            logger.warn("[{}:{}:{}] InterruptedException when adding new message, notification is lost ({})",
                        conn_id, conn_type, cloud_user, text);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return Response.status(status)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @Path("subscribe")
    @GET
    @Produces("text/event-stream")
    public synchronized String subscribe(@Context HttpServletResponse response, @QueryParam("id") long id) {
        response.setContentType("text/event-stream");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setCharacterEncoding("UTF-8");

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);

        UserSessionNotifications userSessionNotifications =
                (userContext instanceof UserSessionNotifications) ? (UserSessionNotifications) userContext : null;

        BlockingQueue<Message> myMessages =
                userSessionNotifications != null ? userSessionNotifications.getSessionNotifications(id) : null;
        
        if (myMessages == null) {
            logger.warn("[{}:{}:{}] Cannot get notification queue, notifications will not be available.", conn_id,
                    conn_type, cloud_user);
            return "";
        }

        logger.info("[{}:{}:{}] Notifications subscribed.", conn_id, conn_type, cloud_user);
        try (PrintWriter writer = response.getWriter();) {
            ObjectMapper mapper = new ObjectMapper();

            writer.write("data: {\"initMessage\": \"Notifications initialized for session id " + id + "\"}\n\n");
            writer.flush();
            response.flushBuffer();

            Message message;
            do {
                message = myMessages.poll(2, TimeUnit.SECONDS);

                if (message == null)
                    message = syncMessage;

                String str = mapper.writeValueAsString(message);
                writer.write("data: " + str + "\n\n");

                if (writer.checkError()) {
                    logger.error("[{}:{}:{}] An error encountered while writing some data.", conn_id, conn_type, cloud_user);
                    break;
                }

                writer.flush();
                response.flushBuffer();

                logger.debug("[{}:{}:{}] sent notification data: {}", conn_id, conn_type, cloud_user, str);
            } while(message.getCommand() != MessageCommandCode.END.getCommand());
        }
        catch (IOException e) {
            logger.warn("[{}:{}:{}] IOException when streaming message, notifications will be disabled",
                        conn_id, conn_type, cloud_user, e);
        }
        catch (InterruptedException e) {
            logger.warn("[{}:{}:{}] InterruptedException (queue.take()) when streaming message, " +
                        "notifications will be disabled",
                        conn_id, conn_type, cloud_user, e);
        }
        finally {
            userSessionNotifications.removeSessionNotifications(id);
            logger.info("[{}:{}:{}] Notifications unsubscribed.", conn_id, conn_type, cloud_user);
        }

        return "";
    }

    @POST
    @Path("unsubscribe")
    @Consumes("text/plain")
    @Produces(MediaType.APPLICATION_JSON)
    public Response logout(@QueryParam("id") Long id,
                           @Context HttpServletRequest request ) throws GuacamoleException {
        return notifyUser(id, "", "", "UNKNOWN", "END", 0L,
                     "", request);
    }

    @POST
    @Path("setresponse")
    @Consumes(MediaType.APPLICATION_FORM_URLENCODED)
    public void setResponse(@QueryParam("id") Long id, @FormParam("response") String response,
                            @FormParam("options") String options) throws GuacamoleException {
        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);

        String serverId  = environment.getRequiredProperty(ApportoProperties.SERVER_ID);

        ResponseDTO responseDTO = ResponseDTO.builder()
                .status(response)
                .key(options.isEmpty() ? "" : options)
                .serverId(options.isEmpty() ? "" : serverId)
                .build();

        // Set the response corresponding to the id
        ((UserSessionNotifications)userContext).getSessionNotificationResponses().put(id, responseDTO);

        // Print log
        logger.info("[{}:{}:{}] setResponse: jsonResult={}", conn_id, conn_type, cloud_user, responseDTO);
    }

    @DELETE
    @Path("getresponse")
    @Produces(MediaType.APPLICATION_JSON)
    public ResponseDTO getResponse(@QueryParam("id") Long id) throws GuacamoleException {
        // Get the response corresponding to the id
        ResponseDTO result = ((UserSessionNotifications)userContext).getSessionNotificationResponses().remove(id);

        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);

        // Print log
        if (result != null) {
            logger.info("[{}:{}:{}] getResponse: response={}", conn_id, conn_type, cloud_user, result);
        }
        else {
            logger.debug("[{}:{}:{}] getResponse: response is null", conn_id, conn_type, cloud_user);
        }

        return result;
    }
}
