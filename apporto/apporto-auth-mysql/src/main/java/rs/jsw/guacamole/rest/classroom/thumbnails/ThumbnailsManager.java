/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom.thumbnails;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.util.Set;

import javax.websocket.Session;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.LocalEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.Inject;

import io.lettuce.core.RedisClient;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.sync.RedisCommands;
import lombok.Synchronized;
import rs.jsw.guacamole.net.encryptedurl.redis.LongKeyByteArrayCodec;
import rs.jsw.guacamole.net.encryptedurl.redis.RedisClientProvider;
import rs.jsw.guacamole.net.encryptedurl.redis.StringIntegerCodec;
import rs.jsw.guacamole.net.encryptedurl.redis.StringLongCodec;
import rs.jsw.guacamole.rest.classroom.ClassroomModule;
import rs.jsw.guacamole.rest.classroom.commands.ThumbList;

public class ThumbnailsManager {
    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ThumbnailsManager.class);


    private static final String SUBSCRIBER_PREFIX = "subscriber:";
    private static final String THUMB_PREFIX = "thumb:";

    private static final String THUMB_LAST_PREFIX   = "last:";
    private static final Integer THUMB_LAST_YES     = 1;
    private static final Integer THUMB_LAST_NO      = 0;

    private static final Integer DEFAULT_THUMB_DB = 1;

    @Inject
    private WSSessionStore sessionStore;

    /**
     * Expiration time is set according to the following:
     * - thumb expiration time is 30 minutes, because if the users is inactive for that long,
     *   it will be logged out from windows anyway.
     * - subscription expiration time is 1 minute; it is normally updated every 5 seconds,
     *   so if there are no updates, the classroom is either closed or no students are present.
     * If in any time the users thumb or subscription changes, the expiration time will be reset.
     */
    private static final Long THUMB_EXPIRATION = Duration.ofMinutes(30).getSeconds();
    private static final Long SUBSCRIPTION_EXPIRATION = Duration.ofMinutes(1).getSeconds();

    private RedisClientProvider redisClientProvider;
    private RedisClient redisClient;

    private StatefulRedisConnection<String, Long> redisSubscribeConnection;
    private RedisCommands<String, Long> redisSubscribeCommand;

    private StatefulRedisConnection<Long, byte[]> redisThumbConnection;
    private RedisCommands<Long, byte[]> redisThumbCommand;

    private StatefulRedisConnection<String, Integer> redisThumbLastConnection;
    private RedisCommands<String, Integer> redisThumbLastCommand;

    @Inject
    public ThumbnailsManager(RedisClientProvider redisClientProvider) {
        this.redisClientProvider = redisClientProvider;
    }

    @Synchronized
    public void open() {
        logger.info("[{}:{}:{}] Opening ThumbnailsManager...", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");

        if (redisClient == null)
            redisClient = redisClientProvider.get();

        Integer thumbDb = getThumbDbId();

        if (redisSubscribeConnection == null || !redisSubscribeConnection.isOpen()) {
            redisSubscribeConnection = redisClient.connect(new StringLongCodec());
            redisSubscribeCommand = redisSubscribeConnection.sync();
            redisSubscribeCommand.select(thumbDb);
        }

        if (redisThumbConnection == null || !redisThumbConnection.isOpen()) {
            redisThumbConnection = redisClient.connect(new LongKeyByteArrayCodec());
            redisThumbCommand = redisThumbConnection.sync();
            redisThumbCommand.select(thumbDb);
        }

        if (redisThumbLastConnection == null || !redisThumbLastConnection.isOpen()) {
            redisThumbLastConnection = this.redisClient.connect(new StringIntegerCodec());
            redisThumbLastCommand = redisThumbLastConnection.sync();
            redisThumbLastCommand.select(thumbDb);
        }

        logger.info("[{}:{}:{}] ThumbnailsManager opened.", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
    }

    @Synchronized
    public void close() {
        logger.info("[{}:{}:{}] Closing ThumbnailsManager...", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");

        if (redisClient == null) {
            logger.warn("[{}:{}:{}] ThumbnailsManager already closed.", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
            return;
        }

        if (redisSubscribeConnection != null) {
            redisSubscribeConnection.close();
            redisSubscribeConnection = null;
        }
        if (redisThumbConnection != null) {
            redisThumbConnection.close();
            redisThumbConnection = null;
        }
        if (redisThumbLastConnection != null) {
            redisThumbLastConnection.close();
            redisThumbLastConnection = null;
        }

        if (redisClient != null) {
            redisClient.shutdown();
            redisClient = null;
        }

        logger.info("[{}:{}:{}] ThumbnailsManager shutdown completed with Redis client cleanup.", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
    }

    private Integer getThumbDbId() {
        try {
            return new LocalEnvironment().getProperty(ApportoProperties.REDIS_THUMB_DB, DEFAULT_THUMB_DB);
        } catch (GuacamoleException e) {
            logger.error("[{}:{}:{}] ThumbnailsRetrieveWS openRedisConnection failed to read {}", "", "", "", ApportoProperties.REDIS_THUMB_DB, e);
            return DEFAULT_THUMB_DB;
        }
    }

    /**
     * Persist the thumb list for the subscriber.
     * 
     * This method will do the following:
     * - add subscriber ID to the set of subscribers for each thumb.
     * - keep the set of thumbs that subscriber is subscribed to.
     * 
     * First, it has to remove the subscriber from the all thumbs he was subscribed to, and then add him to the new thumbs.
     * 
     * @param subscriberId - ID of the subscriber
     * @param subscribeThumb - thumb list subscriber is subscribed to
     */
    @Synchronized
    public void addSubscription(Long subscriberId, ThumbList subscribeThumb) {
        logger.info("[{}:{}:{}] Adding subscription to the clients: {} for subscriber: {}", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", subscribeThumb.toString(), subscriberId);

        Set<Long> subscriberThumbs = redisSubscribeCommand.smembers((SUBSCRIBER_PREFIX+subscriberId.toString()));

        // Remove subscriber from the all thumbs he was subscribed to and delete subscriber.
        for (Long thumb : subscriberThumbs) {
            redisSubscribeCommand.srem((THUMB_PREFIX + thumb.toString()), subscriberId);
        }
        redisSubscribeCommand.del((SUBSCRIBER_PREFIX+subscriberId.toString()));

        // Add subscriber with set of subscribed thumbs; then add subscriber to the thumbs.
        for (Long thumb : subscribeThumb.getThumbList()) {
            redisSubscribeCommand.sadd((SUBSCRIBER_PREFIX+subscriberId.toString()), thumb);
            redisSubscribeCommand.expire((SUBSCRIBER_PREFIX+subscriberId.toString()), SUBSCRIPTION_EXPIRATION);
            redisSubscribeCommand.sadd((THUMB_PREFIX + thumb.toString()), subscriberId);
            redisSubscribeCommand.expire((THUMB_PREFIX + thumb.toString()), SUBSCRIPTION_EXPIRATION);
        }

        logger.info("[{}:{}:{}] Subscription added to the clients: {} for subscriber: {}", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", subscribeThumb.toString(), subscriberId);
    }

    /**
     * Send the thumb from connection ID to the subscriber via websocket.
     * 
     * @param connectionId - id of connection that stored new thumb
     */
    public void notifySubscribers(Long connectionId) {
        logger.info("[{}:{}:{}] Notifying subscribers for the new thumb.", connectionId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");

        Set<Long> subscribers = redisSubscribeCommand.smembers((THUMB_PREFIX + connectionId.toString()));

        // If the current thumb is not the completed one, do not send it to the subscribers.
        if (redisThumbLastCommand.get(THUMB_LAST_PREFIX + connectionId.toString()) == THUMB_LAST_NO)
            return;

        byte[] thumb = redisThumbCommand.get(connectionId);
        redisThumbCommand.del(connectionId);

        for (Long subscriber : subscribers) {
            Session session = sessionStore.getSession(subscriber);
            if (session != null) {
                ByteBuffer buffer = serializeThumb(connectionId, thumb);
                try {
                    session.getBasicRemote().sendBinary(buffer);
                    logger.info("[{}:{}:{}] New thumbnail sent to subscriber: {}", connectionId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", subscriber);
                } catch (IOException e) {
                    logger.error("[{}:{}:{}] ThumbnailsManager notifySubscribers - Failed to send thumbnail to subscriber: {}",
                            connectionId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", subscriber, e);
                }
            }
            else {
                logger.error("[{}:{}:{}] ThumbnailsManager notifySubscribers - Session not found for subscriberId: {}",
                        connectionId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", subscriber);
            }
        }

        logger.info("[{}:{}:{}] Notifying subscribers for the new thumb completed.", connectionId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
    }

    /**
     * Send the requested thumbs to the requester.
     * 
     * @param id - requester ID
     * @param requestedThumbs - list of thumb IDs
     */
    public void sendThumbs(Long requesterId, ThumbList requestedThumbs) {
        logger.info("[{}:{}:{}] Sending thumbs to the requester {}.", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", requesterId);

        Session session = sessionStore.getSession(requesterId);

        if (session != null) {
            requestedThumbs.getThumbList().forEach(thumbId -> {
                // If the current thumb is the completed one
                if (redisThumbLastCommand.get(THUMB_LAST_PREFIX + thumbId.toString()) == THUMB_LAST_YES) {
                    byte[] thumb = redisThumbCommand.get(thumbId);
                    redisThumbCommand.del(thumbId);

                    if (thumb != null) {
                        ByteBuffer buffer = serializeThumb(thumbId, thumb);
    
                        try {
                            session.getBasicRemote().sendBinary(buffer);
                            logger.info("[{}:{}:{}] Sent thumb from connection id: {}", requesterId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", thumbId);
                        } catch (IOException e) {
                            logger.error("[{}:{}:{}] ThumbnailsManager sendThumbs - Failed to send thumbnail from connection id: {}",
                                    requesterId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", thumbId, e);
                        }
                    }
                }
            });
        }
        else
        {
            logger.error("[{}:{}:{}] ThumbnailsManager sendThumbs - Own session not found", requesterId, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
        }

        logger.info("[{}:{}:{}] Sending thumbs to the requester {} completed.", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", requesterId);
    }

    /**
     * Serialize thumbnail data into ByteBuffer.
     * 
     * The data is serialized in the following format:
     * srv_id - integer (4 bytes)
     * thumbId - long (8 bytes)
     * thumb array length - integer (4 bytes)
     * thumb array - byte array
     * 
     * Serializing to base64 is avoided to minimize the data size.
     * @param srv_id 
     * 
     * @param thumbId
     * @param thumb
     * @return ByteBuffer with serialized thumbnail data
     */
    public ByteBuffer serializeThumb(Long thumbId, byte[] thumb) {
        // Calculate the total length of the serialized data
        int totalLength = Integer.BYTES + 
                          Long.BYTES + thumb.length;

        // Create a ByteBuffer with enough capacity to hold the serialized data
        ByteBuffer buffer = ByteBuffer.allocate(totalLength);

        // Put the ID as a 64-bit integer
        buffer.putLong(thumbId);

        // Put the thumbnail byte array
        buffer.put(thumb);

        buffer.flip();

        return buffer;
    }

    /**
     * Store thumbnail into the Redis.
     */
    public void storeThumb(Long thumbId, byte[] thumb, Boolean last) {
        String lastKey = THUMB_LAST_PREFIX + thumbId.toString();

        // Append to the existing if the thumbId exists (create if not exist)
        redisThumbCommand.append(thumbId, thumb);
        redisThumbCommand.expire(thumbId, THUMB_EXPIRATION);
        if (last) 
            redisThumbLastCommand.setex(lastKey, THUMB_EXPIRATION, THUMB_LAST_YES);
        else 
            redisThumbLastCommand.setex(lastKey, THUMB_EXPIRATION, THUMB_LAST_NO);

        logger.info("[{}:{}:{}] Thumbnail part for id: {} stored in the Redis, isLast = {}", "", ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", thumbId, last);
    }
}
