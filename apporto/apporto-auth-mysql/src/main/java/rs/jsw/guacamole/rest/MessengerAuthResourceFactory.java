package rs.jsw.guacamole.rest;

import org.apache.guacamole.net.auth.UserContext;

public interface MessengerAuthResourceFactory {
	
	/**
     * Creates a new MessengerAuthResource for geting user identity for messenger.
     *
     * @param userContext
     *     The UserContext whose contents should be exposed.
     *
     * @return
     *     A new MessengerAuthResource.
     */
	MessengerAuthResource create(UserContext userContext);
}
