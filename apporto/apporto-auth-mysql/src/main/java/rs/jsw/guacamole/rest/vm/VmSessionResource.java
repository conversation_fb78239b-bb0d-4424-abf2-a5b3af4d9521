package rs.jsw.guacamole.rest.vm;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import java.io.OutputStream;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Map;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import org.apache.commons.codec.binary.Base64;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

/**
 * A REST Service for restarting Linux & Mac session.
 *
 * <AUTHOR>
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class VmSessionResource extends BaseResource  {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(VmSessionResource.class);

    /**
     * URL formats
     */
    private static final String TERMINATE_URL_FORMAT = "https://%s:%d/session-mgmt/terminate-user";
    private static final String CLEAR_URL_FORMAT = "https://%s:%d/session-mgmt/user/sticky/clear-by-frontend-cfg";

    /**
     * Others
     */
    private static String SECRET_KEY;
    private static Integer PORT;
    private static final Integer DEFAULT_PORT = 5000;
    private static final int TIMEOUT = 5000; // set timeout to 5 seconds

    static {
        StringGuacamoleProperty propSecretKey = (StringGuacamoleProperty) ApportoProperties.getProp("session-mgmt-api-secret-key");
        IntegerGuacamoleProperty propPort = (IntegerGuacamoleProperty) ApportoProperties.getProp("session-mgmt-api-port");

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the session management api secret key from guacamole.properties
            SECRET_KEY = environment.getProperty(propSecretKey);

            if (SECRET_KEY == null || SECRET_KEY.isEmpty()) {
                logger.error("session-mgmt-api-secret-key is empty.");
            }
        }
        catch (Exception e) {
            logger.warn("Cannot get `session-mgmt-api-secret-key` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the session management api port from guacamole.properties
            PORT = environment.getProperty(propPort);

            if (PORT == null) {
                logger.warn("session-mgmt-api-port is empty. The default port is {}.", DEFAULT_PORT);
                PORT = DEFAULT_PORT;
            }
        }
        catch (Exception e) {
            logger.warn("Cannot get `session-mgmt-api-port` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties. " +
                        "The default port is {}.", DEFAULT_PORT);
        }
    }

    @AssistedInject
    public VmSessionResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    public static void clear(GuacamoleConfiguration configuration) {

        Map<String, String> params = configuration.getParameters();

        String hostname = params.get(EncurlParameters.HOSTNAME_PARM);
        String username = params.get(EncurlParameters.USERNAME_PARM);
        String lb_port  = params.get(EncurlParameters.PORT_PARM);

        // Get the connection info
        String conn_id = params.get(EncurlParameters.ID_PARM);
        String cloud_user = params.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = params.get(EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Clearing the sticky session.", conn_id, conn_type, cloud_user);

        JSONObject httpBody = new JSONObject();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;

        TrustManager[] trustAllCerts;
        trustAllCerts = new TrustManager[] {new X509TrustManager() {
                @Override
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };

        try {
            httpBody.put("lb_port", lb_port);
            httpBody.put("session_id", conn_id);
            httpBody.put("user", username);

            // Calculate the signature of the http body
            String message = httpBody.toString();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Install the all-trusting trust manager
            URL url = new URL(String.format(CLEAR_URL_FORMAT, hostname, PORT));
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Connect WebAPI
            logger.info("[{}:{}:{}] To clear the sticky session, connecting to: {}.",
                               conn_id, conn_type, cloud_user, url.toString());

            HttpsURLConnection  con = (HttpsURLConnection ) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("User-Agent", "Java client");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setConnectTimeout(TIMEOUT);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());

            responseCode = con.getResponseCode();
            logger.info("[{}:{}:{}] Finished to clear the sticky session ({}). (response code={})",
                        conn_id, conn_type, cloud_user, url.toString(), responseCode);

        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Failed to clear the sticky session due to the following exception.",
                         conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

    }

    public static int terminate(GuacamoleConfiguration configuration) {

        Map<String, String> params = configuration.getParameters();

        String hostname = params.get(EncurlParameters.HOSTNAME_PARM);
        String username = params.get(EncurlParameters.USERNAME_PARM);
        String lb_port  = params.get(EncurlParameters.PORT_PARM);

        // Get the connection info
        String conn_id = params.get(EncurlParameters.ID_PARM);
        String cloud_user = params.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = params.get(EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Terminating Mac/Linux session.", conn_id, conn_type, cloud_user);

        JSONObject httpBody = new JSONObject();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;

        TrustManager[] trustAllCerts;
        trustAllCerts = new TrustManager[] {new X509TrustManager() {
                @Override
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                @Override
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                @Override
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };

        try {
            httpBody.put("user", username);
            httpBody.put("lb_port", lb_port);

            // Calculate the signature of the http body
            String message = httpBody.toString();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Install the all-trusting trust manager
            URL url = new URL(String.format(TERMINATE_URL_FORMAT, hostname, PORT));
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                @Override
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Connect WebAPI
            logger.info("[{}:{}:{}] To terminate the Mac/Linux session, connecting to: {}.",
                        conn_id, conn_type, cloud_user, url.toString());

            HttpsURLConnection  con = (HttpsURLConnection ) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("User-Agent", "Java client");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setConnectTimeout(TIMEOUT);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());

            responseCode = con.getResponseCode();
            logger.info("[{}:{}:{}] Finished to terminate the Mac/Linux session ({}). (response code={})",
                        conn_id, conn_type, cloud_user, url.toString(), responseCode);

        }
        catch (Exception e) {
            logger.info("[{}:{}:{}] Failed to terminate the Mac/Linux session due to the following exception.",
                        conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return responseCode;

    }

    @POST
    @Path("terminate")
    @Consumes("text/plain")
    @Produces(MediaType.APPLICATION_JSON)
    public Response terminateFromURL(@QueryParam("id") Integer id) {

        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;
        GuacamoleConfiguration configuration = getConfiguration(id);

        responseCode = terminate(configuration);

        return Response.status(responseCode)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

}
