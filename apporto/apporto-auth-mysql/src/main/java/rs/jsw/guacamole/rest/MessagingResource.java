/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.Collection;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.map.ObjectMapper;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import com.google.inject.Injector;

import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.Connection;
import org.apache.guacamole.protocol.GuacamoleConfiguration;

import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionParameterMapper;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.net.encryptedurl.mysql.EncurlMySQLInjectorProvider;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A REST Service for handling message exchange.
 * 
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class MessagingResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(MessagingResource.class);

    /**
     * The UserContext being exposed through this resource.
     */
    private final UserContext userContext;

    /**
     * The map of threads queue created by each session id.
     * 
     * The outer map is a map where key is session id and values are threads with their queues.
     * The inner map is a map where key is thread id and value is a queue of commands for each thread.
     */
    private static final Map<Long, HashMap<Long, BlockingQueue<HLCommand>>> sessionThreads = new HashMap<Long, HashMap<Long, BlockingQueue<HLCommand>>>();

    /**
     * The flag indicating that the proper session is stopped.
     */
    private static boolean SESSION_STOP = false;

    /**
     * The connection parameter mapper.
     */
    private EncUrlConnectionParameterMapper encUrlConnectionParameterMapper;

    /**
     * Class for representing mouse data for a session.
     * 
     * <AUTHOR> Nikolić
     *
     */
    public static class MouseData {
        Long sessionId = null;

        List<HLCommand> hlCommands = null;

        public Long getSessionId() {
            return sessionId;
        }

        public void setSessionId(Long sessionId) {
            this.sessionId = sessionId;
        }

        public List<HLCommand> getHlCommands() {
            return hlCommands;
        }

        public void setHlCommands(List<HLCommand> hlCommands) {
            this.hlCommands = hlCommands;
        }

        private static ObjectMapper mapper = new ObjectMapper();

        public MouseData() {
            sessionId = null;
            hlCommands = new ArrayList<HLCommand>();
        }

        public MouseData(String s) {
            try {
                MouseData data = mapper.readValue(s, MouseData.class);
                sessionId = data.sessionId;
                hlCommands = data.hlCommands;
            } catch (JsonParseException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (JsonMappingException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
    }

    /**
     * Creates a new UserContextResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public MessagingResource(@Assisted UserContext userContext) throws GuacamoleException {
        this.userContext = userContext;

        EncurlMySQLInjectorProvider provider = new EncurlMySQLInjectorProvider();
        Injector injector = provider.get();

        encUrlConnectionParameterMapper = injector.getInstance(EncUrlConnectionParameterMapper.class);
    }

    /**
     * Handler for receiving mouse data. Mouse commands are received as an array.
     * 
     * To test use cURL (below test is obsolete, applies to the previous version, but is similar to this one):
     * 
     * curl -X POST -H "Content-Type: application/json" -d '{"sessionId": 15345, "positions": ["{x:3,y:4}", "{x:22,y:34}", "{x:33,y:55}"]}' http://localhost:8080/guacamole/api/session/ext/encryptedurl-jdbc/messaging/amove?token=81D7C2133AF7C57DA40DD7B454B1CFE11B84830BD6411FB3B069716ECC14B58B
     * 
     * @param mouseData - array of positions for particular sessionId
     */
    @Path("mousedata")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    public synchronized void mouseData(MouseData mouseData) throws GuacamoleException {
        Map<Long, BlockingQueue<HLCommand>> myThreads = sessionThreads.get(mouseData.getSessionId());

        String conn_id = Long.toString(mouseData.getSessionId());
        String cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(conn_id, EncurlParameters.CONNECTION_TYPE_PARAM);

        for(HLCommand command : mouseData.getHlCommands()) {
            for (BlockingQueue<HLCommand> commands : myThreads.values()) {
                try {
                    commands.put(command);
                } catch (InterruptedException e) {
                    logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
                }
            }
        }

        // Get the connection info
        logger.info("[{}:{}:{}] Number of commands: {}", conn_id, connection_type, cloud_user, mouseData.getHlCommands().size());
    }

    @Path("end")
    @POST
    @Consumes("text/plain")
    public synchronized void endHighlight(@QueryParam("sessionId") String id,
                        @QueryParam("contourId") long contourId) throws GuacamoleException {

        // Check if a current connection is a shared connection
        if (!id.matches("[0-9]+")) {
            Connection connection = userContext.getConnectionDirectory().get(id);
            GuacamoleConfiguration configuration = connection.getConfiguration();
            id = configuration.getParameters().get(EncurlParameters.ID_PARM);
        }

        long sessionId = Long.parseLong(id);
        Map<Long, BlockingQueue<HLCommand>> myThreads = sessionThreads.get(sessionId);
        // Get the connection info
        String conn_id = id;
        String cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(conn_id, EncurlParameters.CONNECTION_TYPE_PARAM);

        if (myThreads == null) {
            logger.error("[{}:{}:{}] Because myThreads is null, it can't stop the highlight.", conn_id, connection_type, cloud_user);
            return;
        }

        for (BlockingQueue<HLCommand> commands : myThreads.values()) {
            if (commands == null)
                continue;

            try {
                commands.put(new HLCommand(contourId, HLCommandCode.END));
            } catch (InterruptedException e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }
        }

        logger.info("[{}:{}:{}] endHighlight", conn_id, connection_type, cloud_user);
    }

    @Path("stop")
    @POST
    @Consumes("text/plain")
    public synchronized String stop(@QueryParam("sessionId") String id) throws GuacamoleException {

        // Check if a current connection is a shared connection
        if (!id.matches("[0-9]+")) {
            Connection connection = userContext.getConnectionDirectory().get(id);
            GuacamoleConfiguration configuration = connection.getConfiguration();
            id = configuration.getParameters().get(EncurlParameters.ID_PARM);
        }

        long sessionId = Long.parseLong(id);
        sessionThreads.remove(sessionId);
        SESSION_STOP = true;

        // Get the connection info
        String conn_id = id;
        String cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(conn_id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] stop", conn_id, connection_type, cloud_user);

        return "";
    }

    @Path("subscribe")
    @GET
    @Produces("text/event-stream")
    public synchronized String subscribe(@Context HttpServletResponse response, @QueryParam("sessionId") String id)
        throws GuacamoleException {
        response.setContentType("text/event-stream");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setCharacterEncoding("UTF-8");

        // Get the connection info
        long sessionId = Long.parseLong(id);
        String conn_id = id;
        String cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(conn_id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] subscribe: thread Id={}", conn_id, connection_type, cloud_user, Thread.currentThread().getId());

        // Get all threads that belong to this session id
        HashMap<Long, BlockingQueue<HLCommand>> myThreads = sessionThreads.get(sessionId);

        SESSION_STOP = false;

        if (myThreads == null) {
            myThreads = new HashMap<Long, BlockingQueue<HLCommand>>();
        }

        // Queue of commands is stored into global hash map. In principle, queue object is created and deleted in this method,
        // so it looks like local object. However, access to this object is required from other methods as well (move, click
        // commands), so it must be global.
        BlockingQueue<HLCommand> commands = new LinkedBlockingQueue<HLCommand>();
        myThreads.put(Thread.currentThread().getId(), commands);
        
        sessionThreads.put(sessionId, myThreads);

        try {
            ObjectMapper mapper = new ObjectMapper();
            PrintWriter writer = response.getWriter();

            writer.write("data: " + "SSE Init" + "\n\n");
            writer.flush();

            HLCommand command = null;
            int code = HLCommandCode.UNKNOWN.getCommand();
            do {
                command = commands.poll(2, TimeUnit.SECONDS);
                if (command == null) {
                    code = HLCommandCode.UNKNOWN.getCommand();
                    continue;
                }

                code = command.getCommand();
                
                String str = mapper.writeValueAsString(command);
                writer.write("data: " + str + "\n\n");
                writer.flush();
            } while(!SESSION_STOP && code != HLCommandCode.END.getCommand());
        }
        catch (IOException e) {
            // TODO Auto-generated catch block
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        } catch (InterruptedException e) {
            // TODO Auto-generated catch block
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }
        finally {
            myThreads.remove(Thread.currentThread().getId());
            logger.info("[{}:{}:{}] subscribe: removed: thread Id={}", conn_id, connection_type, cloud_user, Thread.currentThread().getId());
        }

        return "";
    }

    /**
     * Obtaining parameter associated with current user.
     *
     * @param id The String which uniquely identifies current user.
     * @param paramName The String which identify name for wanted parameter value.
     * @return Wanted parameter value if successful, empty String if id parameter is invalid,
     *         null value if paramName is not specified for current user.
     */
    private String getParameter(String id, String name) {
        String strValue = "";
        Collection<ConnectionParameterModel> parameters = encUrlConnectionParameterMapper.select(id);
        
        for (ConnectionParameterModel parameter : parameters) {
            if (parameter.getName().equals(name)) {
                strValue = parameter.getValue();
                break;
            }
        }

        return strValue;
    }

}
