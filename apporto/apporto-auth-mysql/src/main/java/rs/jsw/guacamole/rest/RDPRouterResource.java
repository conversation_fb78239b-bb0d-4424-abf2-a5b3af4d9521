/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.X509Certificate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.commons.codec.binary.Base64;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.SendStatusData;
import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * A REST Service for RDP Router Service.
 *
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class RDPRouterResource extends BaseResource  {
    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(RDPRouterResource.class);

    /**
     * RDP Router Service api secret key
     */
    private static String SECRET_KEY;

    /**
     * RDP ROUTER api URL
     */
    private static String RDP_ROUTER_API;

    /**
     * Time interval to keep alive sesssion
     */
    private static int RDP_ROUTER_KEEP_ALIVE_INTERVAL;

    /**
     * The default max timeout for checking the RDP Router (in seconds)
     */
    private static final int DEFAULT_MAX_RDP_ROUTER_API_TIMEOUT = 5;

    /**
     * The max timeout for checking the RDP Router (in seconds)
     */
    private static int rdp_router_api_timeout = DEFAULT_MAX_RDP_ROUTER_API_TIMEOUT;

    /**
     * The default max attempted for calling RDP Router Service API
     */
    private static final int DEFAULT_MAX_ROUTER_API_ATTEMPT_COUNT = 3;

    /**
     * The max attempted for calling RDP Router Service API
     */
    private static int max_router_api_attempt_count = DEFAULT_MAX_ROUTER_API_ATTEMPT_COUNT;

    /**
     * Class for representing user action data for a session.
     */
    public static class InformationData {
        String href = "";

        String userAgent = "";

        public String getHref() {
            return href;
        }

        public void setHref(String href) {
            this.href = href;
        }

        public String getUserAgent() {
            return userAgent;
        }

        public void setUserAgent(String userAgent) {
            this.userAgent = userAgent;
        }

        public InformationData() {
            href = "";
            userAgent = "";
        }

        public InformationData(String href, String userAgent) {
            this.href = href;
            this.userAgent = userAgent;
        }
    }

    @AssistedInject
    public RDPRouterResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    static {
        StringGuacamoleProperty prop = (StringGuacamoleProperty) ApportoProperties.getProp("rdp-router-api-service");
        StringGuacamoleProperty propSecretKey = (StringGuacamoleProperty) ApportoProperties.getProp("rdp-router-api-secret-key");
        IntegerGuacamoleProperty propRouterAliveInterval = (IntegerGuacamoleProperty) ApportoProperties.getProp("rdp-router-keep-alive-interval");

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the RDP Router server from guacamole.properties
            RDP_ROUTER_API = environment.getProperty(prop);
            logger.info("rdp-router-api-service property: {}", RDP_ROUTER_API);
        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-api-service` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the RDP Router service secret key from guacamole.properties
            SECRET_KEY = environment.getProperty(propSecretKey);

            if (SECRET_KEY == null || SECRET_KEY.isEmpty()) {
                logger.error("rdp-router-api-secret-key is empty.");
            }
        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-api-secret-key` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get the time interval to keep alive session from guacamole.properties
            RDP_ROUTER_KEEP_ALIVE_INTERVAL = environment.getProperty(propRouterAliveInterval);
            logger.info("rdp-router-keep-alive-interval: {}", RDP_ROUTER_KEEP_ALIVE_INTERVAL);
        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-keep-alive-interval` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get "rdp-router-api-timeout" property from guacamole.properties
            if (environment.getProperty(ApportoProperties.RDP_ROUTER_API_TIME_OUT) != null) {
                rdp_router_api_timeout = environment.getProperty(ApportoProperties.RDP_ROUTER_API_TIME_OUT);
            }
            else {
                logger.warn("`rdp-router-api-timeout` parameter from configuration doesn't exist or is empty. " + 
                            "So, the default value {} seconds will be used.", DEFAULT_MAX_RDP_ROUTER_API_TIMEOUT);
            }

            logger.info("The value of `rdp-router-api-timeout` from configuration is {}.",
            rdp_router_api_timeout);
        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-api-timeout` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties." + 
                        "The default count {} will be used.", DEFAULT_MAX_RDP_ROUTER_API_TIMEOUT);
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get "rdp-router-api-attempt-count" property from guacamole.properties
            if (environment.getProperty(ApportoProperties.RDP_ROUTER_API_ATTEMPT_COUNT) != null) {
                max_router_api_attempt_count = environment.getProperty(ApportoProperties.RDP_ROUTER_API_ATTEMPT_COUNT);
            }
            else {
                logger.warn("`rdp-router-api-attempt-count` parameter from configuration doesn't exist or is empty. " + 
                            "So, the default value {} seconds will be used.", DEFAULT_MAX_ROUTER_API_ATTEMPT_COUNT);
            }

            logger.info("The value of `rdp-router-api-attempt-count` from configuration is {}.",
            max_router_api_attempt_count);
        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-api-attempt-count` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties." + 
                        "The default count {} will be used.", DEFAULT_MAX_ROUTER_API_ATTEMPT_COUNT);
        }
    }

    @POST
    @Path("api/start-session")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response startSession(InformationData info, @QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] startSession Information: Href: {}, UserAgent: {}",
            conn_id, connection_type, cloud_user, info.getHref(), info.getUserAgent());

        if (RDP_ROUTER_API == null || RDP_ROUTER_API.isEmpty()) {
            logger.info("[{}:{}:{}] Because the address of RDP Router Serivce is null.",
                        conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("rdp_router_api_available", false);
            error.put("status_msg", "The address of the RDP Router Serivce couldn't be found.");

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        String hostname        = getParameter(id, EncurlParameters.HOSTNAME_PARM);
        String username        = getParameter(id, EncurlParameters.USERNAME_PARM);
        String farmName        = getParameter(id, EncurlParameters.RDP_FARM_NAME_PARAM);
        String cloud_provider  = getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM);

        if (farmName == null || farmName.isEmpty()) {
            logger.info("[{}:{}:{}] Because the rdp-farm-name of RDP Router Serivce is null.",
                        conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("rdp-farm-name", false);
            error.put("status_msg", "The rdp-farm-name of the RDP Router Serivce couldn't be found.");

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject httpBody = new JSONObject();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;
        StringBuffer responseMessage = new StringBuffer();

        try {
            httpBody.put("username", username);
            httpBody.put("farm_name", farmName);
            httpBody.put("connection_id", conn_id);

            // Calculate the signature of the http body
            String message = httpBody.toString();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Create the URL based on the protocol
            URL url = new URL(RDP_ROUTER_API + "/rdp-router/api/start-session");
            logger.info("[{}:{}:{}] Starting session for {}:{} ({}) using RDP Router Service api: {}",
                         conn_id, connection_type, cloud_user, hostname, farmName, cloud_provider, url.toString());

            int connectTimeout = 5000; // Set the connect timeout to 5 seconds
            int readTimeout = 20000; // Set the read timeout to 20 seconds
            // Connect WebAPI
            HttpURLConnection con = SendStatusData.getURLConnection(url);

            con.setRequestMethod("POST");
            con.setRequestProperty("User-Agent", "Java client");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setConnectTimeout(connectTimeout);
            con.setReadTimeout(readTimeout);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());

            responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                responseMessage.append(inputLine);
            }
            in.close();

            if (responseCode / 100 == 2) {
                logger.info("[{}:{}:{}] RDP Router Start API ({}, {}) with response code ({}) for {}:{} ({})",
                conn_id, connection_type, cloud_user, url.toString(), httpBody, responseCode, hostname, farmName, cloud_provider);

                logger.info("[{}:{}:{}] ResponseMsg={}", conn_id, connection_type, cloud_user, responseMessage.toString());
            }
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: {} {}", conn_id, connection_type, cloud_user, e.toString(), e.getMessage());

            JSONObject error = new JSONObject();
            error.put("status_msg", "Exception while reading RDP Router Start API. Exception: " + e.toString());

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        return Response.ok(responseMessage.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    @POST
    @Path("api/keep-alive")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public Response keepAliveSession(InformationData info, @QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        if (RDP_ROUTER_API == null || RDP_ROUTER_API.isEmpty()) {
            logger.info("[{}:{}:{}] Because the address of RDP Router Serivce is null.",
                        conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("rdp_router_api_available", false);
            error.put("status_msg", "The address of the RDP Router Serivce couldn't be found.");

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        String hostname        = getParameter(id, EncurlParameters.HOSTNAME_PARM);
        String username        = getParameter(id, EncurlParameters.USERNAME_PARM);
        String farmName        = getParameter(id, EncurlParameters.RDP_FARM_NAME_PARAM);
        String cloud_provider  = getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM);

        if (farmName == null || farmName.isEmpty()) {
            logger.info("[{}:{}:{}] Because the rdp-farm-name of RDP Router Serivce is null.",
                        conn_id, connection_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("rdp-farm-name", false);
            error.put("status_msg", "The rdp-farm-name of the RDP Router Serivce couldn't be found.");

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject httpBody = new JSONObject();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;
        StringBuffer responseMessage = new StringBuffer();

        TrustManager[] trustAllCerts = new TrustManager[] {new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };

        try {
            httpBody.put("username", username);
            httpBody.put("farm_name", farmName);
            httpBody.put("connection_id", conn_id);

            // Calculate the signature of the http body
            String message = httpBody.toString();
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            URL url = new URL(RDP_ROUTER_API + "/rdp-router/api/keep-alive");
            logger.info("[{}:{}:{}] Starting session for {}:{} ({}) using RDP Router Service api: {}",
                         conn_id, connection_type, cloud_user, hostname, farmName, cloud_provider, url.toString());

            int connectTimeout = 5000; // Set the connect timeout to 5 seconds
            int readTimeout = 20000; // Set the read timeout to 20 seconds
            // Connect WebAPI
            HttpURLConnection con = SendStatusData.getURLConnection(url);

            logger.info("[{}:{}:{}] Keeping alive session for {}:{} ({}) using RDP Router Service api: {}",
                         conn_id, connection_type, cloud_user, hostname, farmName, cloud_provider, url.toString());

            con.setRequestMethod("POST");
            con.setRequestProperty("User-Agent", "Java client");
            con.setRequestProperty("Content-Type", "application/json");
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setConnectTimeout(connectTimeout);
            con.setReadTimeout(readTimeout);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());

            responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;

            while ((inputLine = in.readLine()) != null) {
                responseMessage.append(inputLine);
            }
            in.close();

            if (responseCode / 100 == 2) {
                logger.info("[{}:{}:{}] Keep Alive Session API ({}, {}) with response code ({}) for {}:{} ({})",
                conn_id, connection_type, cloud_user, url.toString(), httpBody, responseCode, hostname, farmName, cloud_provider);

                logger.info("[{}:{}:{}] Alive Session ResponseMsg={}", conn_id, connection_type, cloud_user, responseMessage.toString());
            }
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: {} {}", conn_id, connection_type, cloud_user, e.toString(), e.getMessage());

            JSONObject error = new JSONObject();
            error.put("status_msg", "Exception while reading Keep Alive Session API. Exception: " + e.toString());

            return Response.ok(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        return Response.ok(responseMessage.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    @Path("/router-threshold")
    @GET
    @Produces("application/json")
    public Response getRouterServerThreshold(@QueryParam("id") Integer id) {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] get the rdp router's threshold.", conn_id, connection_type, cloud_user);

        // Json Object for resoponse
        JSONObject jsonResult = new JSONObject();
        jsonResult.put("rdp_router_api_timeout", rdp_router_api_timeout);
        jsonResult.put("max_router_api_attempt_count", max_router_api_attempt_count);
        jsonResult.put("rdp_router_keep_alive_interval", RDP_ROUTER_KEEP_ALIVE_INTERVAL);

        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }
}

