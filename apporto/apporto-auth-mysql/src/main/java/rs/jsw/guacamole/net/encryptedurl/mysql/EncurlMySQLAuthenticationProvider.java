/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.mysql;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.InjectedAuthenticationProvider;
import org.apache.guacamole.net.auth.AuthenticatedUser;
import org.apache.guacamole.net.auth.Credentials;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;
import com.google.inject.Injector;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlJDBCAuthenticationProviderService;
import rs.jsw.guacamole.net.encryptedurl.rest.EncurlProviderResourceFactory;

/**
 * Guacamole authentication provider module that registers authentication
 * module.
 * 
 * Encrypted url code is taken from
 * {@link https://bitbucket.org/dmwl/encryptedurl.git}
 * 
 * <AUTHOR> Nikolić
 *
 */
public class EncurlMySQLAuthenticationProvider extends InjectedAuthenticationProvider {

    private static final Logger logger = LoggerFactory.getLogger(EncurlMySQLAuthenticationProvider.class);

    @Inject
    private EncurlProviderResourceFactory encurlProviderResourceFactory;

    /**
     * Register AuthenticationProvider service.
     */
    public EncurlMySQLAuthenticationProvider() throws GuacamoleException {
        super(new EncurlMySQLInjectorProvider(), EncurlJDBCAuthenticationProviderService.class);

        EncurlMySQLInjectorProvider provider = new EncurlMySQLInjectorProvider();
        Injector injector = provider.get();
        encurlProviderResourceFactory = injector.getInstance(EncurlProviderResourceFactory.class);
    }

    @Override
    public AuthenticatedUser updateAuthenticatedUser(AuthenticatedUser authenticatedUser, Credentials credentials)
            throws GuacamoleException {
        return authenticateUser(credentials);
    }

    @Override
    public Object getResource() throws GuacamoleException {
        logger.debug("Creating encurlProtocolResource");
        return encurlProviderResourceFactory.create();
    }

    public String getIdentifier() {
        return "encryptedurl-jdbc";
    }
}
