package rs.jsw.guacamole.rest.vm;

import org.apache.guacamole.net.auth.UserContext;

/**
 * Factory which creates resources that expose the contents of a given
 * UserContext.
 */
public interface VmBackupResourceFactory {
	 /**
     * Creates a new VmRebootResource.
     *
     * @param userContext
     *     The UserContext whose contents should be exposed.
     *
     * @return
     *     A new VmRebootResource.
     */
	VmBackupResource create(UserContext userContext);
}
