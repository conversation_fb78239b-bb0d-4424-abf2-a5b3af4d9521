/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.rest;

import java.util.Collection;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.websocket.DeploymentException;
import javax.websocket.server.ServerContainer;
import javax.websocket.server.ServerEndpointConfig;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel;
import org.codehaus.jackson.annotate.JsonProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;
import com.google.inject.Injector;

import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionParameterMapper;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.classroom.WSEndpoint;
import rs.jsw.guacamole.rest.classroom.thumbnails.ThumbnailsRetrieveWS;
import rs.jsw.guacamole.rest.classroom.thumbnails.ThumbnailsStoreWS;

/**
 * A root REST Service for all other Apporto REST resources.
 *
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class EncurlProviderResource {

    Injector injector;

    private static final String LOCALHOST_IPV6 = "0:0:0:0:0:0:0:1";

    private static final String LOCALHOST_IPV4 = "127.0.0.1";

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(EncurlProviderResource.class);

    @Inject
    private EncUrlConnectionParameterMapper encUrlConnectionParameterMapper;

    /**
     * Does not allow websocket creation more than once.
     */
    private static boolean thumbStoreWsCreated = false;
    private static boolean thumbRetrieveWsCreated = false;

    private static class WinParams {

        @JsonProperty("id")
        private int id;
        @JsonProperty("winServerName")
        private String winServerName;
        @JsonProperty("winSessionId")
        private String winSessionId;
    }

    @Path("/kill-win-session")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @POST
    public Response killWinSession(final WinParams params, @Context HttpServletRequest request) {
        // Get the connection info
        String conn_id = Integer.toString(params.id);
        String cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(conn_id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] kill-win-session: id={} server-name={} session-id={}", conn_id, connection_type,
                cloud_user, conn_id, params.winServerName, params.winSessionId);

        String client_ip = request.getHeader("x-real-ip");
        logger.info("[{}:{}:{}] x-real-ip: {}", conn_id, connection_type, cloud_user,
                client_ip != null ? client_ip : "N/A");
        String ipForwarded = request.getHeader("x-forwarded-for");
        logger.info("[{}:{}:{}] x-forwarded-for: {}", conn_id, connection_type, cloud_user,
                ipForwarded != null ? ipForwarded : "N/A");
        String remoteAddress = request.getRemoteAddr();
        logger.info("[{}:{}:{}] Remote address: {}", conn_id, connection_type, cloud_user,
                remoteAddress != null ? remoteAddress : "N/A");

        String receivedParams = String.format(
                "[%s:%s] Received kill-session parameters for session %s , windows server %s, windows session %s.",
                conn_id, cloud_user, conn_id, params.winServerName, params.winSessionId);
        logger.info(receivedParams);
        try {
            ConnectionParameterModel parModel = new ConnectionParameterModel();
            parModel.setConnectionIdentifier(String.valueOf(params.id));
            parModel.setName(EncurlParameters.WINDOWS_SERVER_NAME);
            parModel.setValue(params.winServerName);
            encUrlConnectionParameterMapper.insertOrUpdate(parModel);

            parModel.setName(EncurlParameters.WINDOWS_SESSION_ID);
            parModel.setValue(params.winSessionId);
            encUrlConnectionParameterMapper.insertOrUpdate(parModel);
            logger.info("[{}:{}::{}] Parameters for kill-session persisted.", conn_id, connection_type, cloud_user);
        } catch (Exception e) {
            String warning = String.format(
                    "[%s:%s] Requested Guacamole session %s has never been registered, please launch this session id at least once.",
                    conn_id, cloud_user, conn_id);
            logger.warn(warning);
            return Response.status(Status.NOT_FOUND).entity(warning)
                    .header("Cache-Control", "no-cache, no-store, must-revalidate").header("Pragma", "no-cache")
                    .header("Expires", "0").build();
        }

        return Response.ok(receivedParams).header("Cache-Control", "no-cache, no-store, must-revalidate")
                .header("Pragma", "no-cache").header("Expires", "0").build();
    }

    @Path("/delay")
    @POST
    public Response makeDelay(@QueryParam("id") Integer id, @QueryParam("delay") Integer delay) {
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(conn_id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Delay initialized, value = {}ms", conn_id, connection_type, cloud_user, delay);
        try {
            Thread.sleep(delay);
        } catch (InterruptedException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }
        logger.info("[{}:{}:{}] Delay finished, value = {}ms", conn_id, connection_type, cloud_user, delay);

        return Response.ok().header("Cache-Control", "no-cache, no-store, must-revalidate").header("Pragma", "no-cache")
                .header("Expires", "0").build();
    }

    private String getParameter(String id, String name) {
        String strValue = "unknown";
        Collection<ConnectionParameterModel> parameters = encUrlConnectionParameterMapper.select(id);

        for (ConnectionParameterModel parameter : parameters) {
            if (parameter.getName().equals(name)) {
                strValue = parameter.getValue();
                break;
            }
        }

        return strValue;
    }

    /**
     * This endpoint creates a new websocket endpoint.
     * 
     * Creations of the websocket endpoint is implemented inside REST API call,
     * because it is the only way to get ServletContext injected, which is necessary
     * for adding websocket endpoint.
     * 
     * If there are other ways to get ServletContext injected, this method should be
     * moved to regular initialization code.
     * 
     * Requests are filtered to only calls from localhost, to prevent unauthorized
     * calls. Also, it it not allowed to create more than one websocket endpoint.
     * 
     * @param context - ServletContext
     * @param request - HttpServletRequest
     * @return Response
     */
    @Path("/websocket-init")
    @GET
    public Response webSocketInit(@Context ServletContext context, @Context HttpServletRequest request) {
        logger.info("[init::] websocket-init");

        String client_ip = request.getHeader("x-real-ip");
        logger.debug("[init:auth:] x-real-ip: {}", client_ip != null ? client_ip : "N/A");
        String ipForwarded = request.getHeader("x-forwarded-for");
        logger.debug("[init:auth:] x-forwarded-for: {}", ipForwarded != null ? ipForwarded : "N/A");
        String remoteAddress = request.getRemoteAddr();
        logger.debug("[init:auth:] Remote address: {}", remoteAddress != null ? remoteAddress : "N/A");

        // Only allow calls from localhost
        if (!(LOCALHOST_IPV4.equals(remoteAddress) || LOCALHOST_IPV6.equals(remoteAddress))) {
            logger.error("[init:auth:] Unauthorized call to websocket-init from {}", remoteAddress);
            return Response.status(Status.UNAUTHORIZED).build();
        }

        if (!thumbStoreWsCreated) {
            createWSEndpoint(context, ThumbnailsStoreWS.class, "/thumbnails/{id}");
            thumbStoreWsCreated = true;
        } else {
            logger.warn("[init:auth:] Attempt to create more than one thumb store websocket endpoint.");
            return Response.status(Status.CONFLICT).build();
        }

        if (!thumbRetrieveWsCreated) {
            createWSEndpoint(context, ThumbnailsRetrieveWS.class, "/thumbnails/retrieve/{id}");
            thumbRetrieveWsCreated = true;
        } else {
            logger.warn("[init:auth:] Attempt to create more than one thumb retrieve websocket endpoint.");
            return Response.status(Status.CONFLICT).build();
        }

        return Response.ok().build();
    }

    /**
     * Creates a new websocket endpoint.
     * 
     * @param context    - ServletContext
     * @param wsEndpoint - Class<? extends WSEndpoint>
     * @param path       - String
     */
    private void createWSEndpoint(ServletContext context, Class<? extends WSEndpoint> wsEndpoint, String path) {
        ServerContainer container = (ServerContainer) context
                .getAttribute("javax.websocket.server.ServerContainer");

        ServerEndpointConfig config = ServerEndpointConfig.Builder
                .create(wsEndpoint, path)
                .build();
        try {
            container.addEndpoint(config);
            logger.info("[init:auth:] Websocket endpoint created.");
        } catch (DeploymentException e) {
            logger.error("[init:auth:] Error creating websocket endpoint: {}", e);
        }
    }
}
