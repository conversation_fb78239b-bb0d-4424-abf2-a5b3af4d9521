package rs.jsw.guacamole.net.encryptedurl.jdbc;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.apache.guacamole.auth.jdbc.sharing.SharedConnectionMap;
import org.apache.guacamole.auth.jdbc.sharing.connection.SharedConnectionDefinition;

public class ApportoSharedConnectionMap implements SharedConnectionMap {
	/**
     * Keeps track of the share key to SharedConnectionDefinition mapping.
     */
    private final ConcurrentMap<String, SharedConnectionDefinition> connectionMap =
            new ConcurrentHashMap<String, SharedConnectionDefinition>();

    @Override
    public SharedConnectionDefinition get(String key) {
        
        // There are no null share keys
        if (key == null)
            return null;

        // Update the last access time and return the SharedConnectionDefinition
        return connectionMap.get(key);

    }

    @Override
    public void add(SharedConnectionDefinition definition) {

        // Store definition by share key
        String shareKey = definition.getShareKey();
        connectionMap.put(shareKey, definition);

    }

    @Override
    public SharedConnectionDefinition remove(String key) {

        // There are no null share keys
        if (key == null)
            return null;

        // Attempt to retrieve only if non-null
        SharedConnectionDefinition definition = connectionMap.remove(key);
        if (definition == null)
            return null;

        // Close all associated tunnels and disallow further sharing
        definition.invalidate();
        return definition;

    }

	
	public ConcurrentMap<String, SharedConnectionDefinition> getAllConnections() {
		return connectionMap;
	}

}
