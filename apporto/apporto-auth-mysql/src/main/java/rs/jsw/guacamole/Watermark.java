/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole;

import lombok.Data;

/**
 * Defines set of licences present in Guacamole session
 * 
 * <AUTHOR>
 *
 */
@Data
public class Watermark {
    private String type = "none";
    private String text = "Unknown";
    private String size = "auto";
    private String color = "#cccccc";
    private String layout = "diagonal";
    private Boolean semiTransparent = true;
    private String picture = "";
    private String scale = "auto";
    private Boolean washout = true;

    public void setType(String type) {
        if (type != null && !type.isEmpty())
            this.type = type.toLowerCase();
    }
    public void setSize(String size) {
        if (size != null && !size.isEmpty())
            this.size = size.toLowerCase();
    }
    public void setColor(String color) {
        if (color != null && !color.isEmpty())
            this.color = color.toLowerCase();
    }
    public void setLayout(String layout) {
        if (layout != null && !layout.isEmpty())
            this.layout = layout.toLowerCase();
    }
    public void setScale(String scale) {
        if (scale != null && !scale.isEmpty())
            this.scale = scale.toLowerCase();
    }
}
