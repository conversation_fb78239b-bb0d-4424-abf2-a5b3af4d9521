/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.notification;

import org.codehaus.jackson.annotate.JsonCreator;
import org.codehaus.jackson.annotate.JsonValue;

enum MessageCommandCode {
    KILL(1), CLASSROOM(2), UNKNOWN(3),
    END(4), SYNC(5), REMOTE_JOIN(6), COLLABORATION_JOIN(7);

    private int command;

    MessageCommandCode() {
        this(3);
    }

    MessageCommandCode(int command) {
        this.command = command;
    }

    @Json<PERSON>reator
    public static MessageCommandCode fromValue(String value) {
        int code = Integer.parseInt(value);

        switch (code) {
            case 1: return MessageCommandCode.KILL;
            case 2: return MessageCommandCode.CLASSROOM;
            case 3: return MessageCommandCode.UNKNOWN;
            case 4: return MessageCommandCode.END;
            case 5: return MessageCommandCode.SYNC;
            case 6: return MessageCommandCode.REMOTE_JOIN;
            case 7: return MessageCommandCode.COLLABORATION_JOIN;
            default: return MessageCommandCode.UNKNOWN;
        }
    }

    @JsonValue
    public int toValue() {
        return ordinal();
    }

    public int getCommand() {
        return command;
    }

    public void setCommand(int command) {
        this.command = command;
    }
};
