package rs.jsw.guacamole.rest.vm;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.commons.validator.routines.InetAddressValidator;

import org.json.JSONObject;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;
import rs.jsw.guacamole.rest.CustomResult;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A REST Service for vm backup.
 *
 * <AUTHOR> Babic
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class VmBackupResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(VmBackupResource.class);

    /**
     * Commands to execute for restoring and backing up the VMs.
     * POWERSHELL_RUNNER_HOST, SCRIPT_NAME, HOSTNAME, REGION
     */
    private static final String URL = System.getProperty("user.home") + "/.guacamole/bin/runner.ps1 %s %s -vmname %s -reg %s -subscription %s";

    /**
     * Others
     */
    private String region = "";
    private String powershellRunnerHost = "";

    private String backupScript_aws = "";
    private String restoreScript_aws = "";
    private String dateTimeScript_aws = "";

    private String backupScript_azure = "";
    private String restoreScript_azure = "";
    private String dateTimeScript_azure = "";

    private static final String AWS_PROVIDER_NAME = "aws";
    private static final String AWS_BACKUP_SCRIPT = "aws_vdi_backup.ps1";
    private static final String AWS_RESTORE_SCRIPT = "aws_vdi_restore.ps1";
    private static final String AWS_DATETIME_SCRIPT = "aws_datetime_backup.ps1";

    private static final String AZURE_PROVIDER_NAME = "azure";
    private static final String AZURE_BACKUP_SCRIPT = "az_vdi_backup.ps1";
    private static final String AZURE_RESTORE_SCRIPT = "az_vdi_restore.ps1";
    private static final String AZURE_DATETIME_SCRIPT = "az_datetime_backup.ps1";

    @AssistedInject
    public VmBackupResource(@Assisted UserContext userContext) {
        super(userContext);

        Environment environment;
        try {
            environment = new LocalEnvironment();
            String propValue;

            // Indicate the aws backup script from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.VM_BACKUP_AWS);
            if (propValue != null) {
                backupScript_aws = propValue;
            }

            // Indicate the aws restore script from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.VM_RESTORE_AWS);
            if (propValue != null) {
                restoreScript_aws = propValue;
            }

            // Indicate the aws datetime script from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.VM_DATETIME_AWS);
            if (propValue != null) {
                dateTimeScript_aws = propValue;
            }

            // Indicate the azure backup script from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.VM_BACKUP_AZURE);
            if (propValue != null) {
                backupScript_azure = propValue;
            }

            // Indicate the azure restore script from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.VM_RESTORE_AZURE);
            if (propValue != null) {
                restoreScript_azure = propValue;
            }

            // Indicate the azure datetime script from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.VM_DATETIME_AZURE);
            if (propValue != null) {
                dateTimeScript_azure = propValue;
            }

            // Indicate the region from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.REGION);
            if (propValue != null) {
                region = propValue;
            }

            // Indicate the powershell-runner-host from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.POWERSHELL_RUNNER_HOST);
            if (propValue != null) {
                powershellRunnerHost = propValue;
            }

        }
        catch (GuacamoleException e) {
            logger.warn("Cannot initialize backup parameters from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    // This endpoint returns the creation datetime of the most recent backup image
    @Path("datetime")
    @GET
    @Produces("application/json")
    public Response datetime(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Datetime of backup image.", conn_id, conn_type, cloud_user);

        // Get an `InetAddressValidator`
        InetAddressValidator validator = InetAddressValidator.getInstance();
        String hostname = getParameter(id, EncurlParameters.HOSTNAME_PARM);

        if (hostname == null || hostname.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid hostname!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid hostname.");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }
        else if (!validator.isValidInet4Address(hostname)) {
            if (hostname.contains(".")) {
                hostname = hostname.substring(0, hostname.indexOf("."));
            }
        }

        // If we don't get datetime script name from guacamole.properties, set the default datetime script name.
        String runScript = "";
        String cloudProviderName = getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM);

        if (AWS_PROVIDER_NAME.equals(cloudProviderName)) {
            runScript = dateTimeScript_aws.isEmpty()? AWS_DATETIME_SCRIPT: dateTimeScript_aws;
        }
        else if (AZURE_PROVIDER_NAME.equals(cloudProviderName)) {
            runScript = dateTimeScript_azure.isEmpty()? AZURE_DATETIME_SCRIPT: dateTimeScript_azure;
        }
        else {
            JSONObject error = new JSONObject();
            error.put("Message", "Invalid cloud-provider!");
            logger.error("[{}:{}:{}] {}:{}", conn_id, conn_type, cloud_user, error.get("Message"), cloudProviderName);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        String subscription = getParameter(id, EncurlParameters.SUBSCRIPTION);
        if (subscription == null || subscription.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid subscription!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid subscription.");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        String datetime_backup = String.format(URL, powershellRunnerHost, runScript, hostname, region, subscription);
        logger.info("[{}:{}:{}] Datetime: {}", conn_id, conn_type, cloud_user, datetime_backup);
        CustomResult retVal = createProcessWithResult(datetime_backup, conn_id, conn_type, cloud_user);

        // something goes wrong
        if ((retVal.getProcessExitValue() != 0 && retVal.getProcessExitValue() != 1) || (retVal.getResultCode() != 0 && retVal.getResultCode() != 1)) {
            JSONObject error = new JSONObject();
            error.put("Message", retVal.getResultMessage() + retVal.getErrorMessage());
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        return Response.ok(retVal.getResultMessage())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @GET
    @Path("backup")
    public Response backup(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] backup.", conn_id, conn_type, cloud_user);

        // If we don't get backup script name from guacamole.properties, set the default backup script name.
        String runScript = "";
        String cloudProviderName = getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM);

        if (AWS_PROVIDER_NAME.equals(cloudProviderName)) {
            runScript = backupScript_aws.isEmpty()? AWS_BACKUP_SCRIPT: backupScript_aws;
        }
        else if (AZURE_PROVIDER_NAME.equals(cloudProviderName)) {
            runScript = backupScript_azure.isEmpty()? AZURE_BACKUP_SCRIPT: backupScript_azure;
        }
        else {
            logger.error("[{}:{}:{}] Invalid cloud-provider!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid cloud-provider!");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        // Get an `InetAddressValidator`
        InetAddressValidator validator = InetAddressValidator.getInstance();
        String hostname = getParameter(id, EncurlParameters.HOSTNAME_PARM);

        if (hostname == null || hostname.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid hostname!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid hostname.");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }
        else if (!validator.isValidInet4Address(hostname)) {
            if (hostname.contains(".")) {
                hostname = hostname.substring(0, hostname.indexOf("."));
            }
        }

        String subscription = getParameter(id, EncurlParameters.SUBSCRIPTION);
        if (subscription == null || subscription.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid subscription!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid subscription.");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        // Create a command line for backup
        String runCommand = String.format(URL, powershellRunnerHost, runScript, hostname, region, subscription);
        logger.info("[{}:{}:{}] VM backup: {}", conn_id, conn_type, cloud_user, runCommand);
        CustomResult retVal = createProcessWithResult(runCommand, conn_id, conn_type, cloud_user);

        // Something goes wrong
        if (retVal.getProcessExitValue() != 0 || retVal.getResultCode() != 0) {
            logger.error("[{}:{}:{}] Process Exit Value: {}, Result Code: {}, Result Message: {}", conn_id, conn_type, cloud_user,
                retVal.getProcessExitValue(), retVal.getResultCode(), retVal.getResultMessage());

            JSONObject error = new JSONObject();
            error.put("Message", retVal.getResultMessage() + "\n" + retVal.getErrorMessage());
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error).build();
        }

        // Print log
        logger.info("[{}:{}:{}] backup done. Result Code: {}, Result Message: {}", conn_id, conn_type, cloud_user,
            retVal.getResultCode(), retVal.getResultMessage());

        return Response.ok(retVal.getResultMessage())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    @GET
    @Path("restore")
    public Response restore(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] restore.", conn_id, conn_type, cloud_user);

        // If we don't get restore script name from guacamole.properties, set the default restore script name.
        String runScript = "";
        String cloudProviderName = getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM);

        if (AWS_PROVIDER_NAME.equals(cloudProviderName)) {
            runScript = restoreScript_aws.isEmpty()? AWS_RESTORE_SCRIPT: restoreScript_aws;
        }
        else if (AZURE_PROVIDER_NAME.equals(cloudProviderName)) {
            runScript = restoreScript_azure.isEmpty()? AZURE_RESTORE_SCRIPT: restoreScript_azure;
        }
        else {
            logger.error("[{}:{}:{}] Invalid cloud-provider!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid cloud-provider!");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        // Get an `InetAddressValidator`
        InetAddressValidator validator = InetAddressValidator.getInstance();
        String hostname = getParameter(id, EncurlParameters.HOSTNAME_PARM);

        if (hostname == null || hostname.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid hostname!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid hostname.");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }
        else if (!validator.isValidInet4Address(hostname)) {
            if (hostname.contains(".")) {
                hostname = hostname.substring(0, hostname.indexOf("."));
            }
        }

        String subscription = getParameter(id, EncurlParameters.SUBSCRIPTION);
        if (subscription == null || subscription.isEmpty()) {
            logger.error("[{}:{}:{}] Invalid subscription!", conn_id, conn_type, cloud_user);

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid subscription.");
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        // Create a command line for restore
        String runCommand = String.format(URL, powershellRunnerHost, runScript, hostname, region, subscription);
        logger.info("[{}:{}:{}] VM restore: {}", conn_id, conn_type, cloud_user, runCommand);
        CustomResult retVal = createProcessWithResult(runCommand, conn_id, conn_type, cloud_user);

        // something goes wrong
        if (retVal.getProcessExitValue() != 0 || retVal.getResultCode() != 0) {
            logger.error("[{}:{}:{}] Process Exit Value: {}, Result Code: {}, Result Message: {}", conn_id, conn_type, cloud_user,
                retVal.getProcessExitValue(), retVal.getResultCode(), retVal.getResultMessage());

            JSONObject error = new JSONObject();
            error.put("Message", retVal.getResultMessage() + "\n" + retVal.getErrorMessage());
            logger.error("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(error.toString()).build();
        }

        // Print log
        logger.info("[{}:{}:{}] restore done. Result Code: {}, Result Message: {}", conn_id, conn_type, cloud_user,
            retVal.getResultCode(), retVal.getResultMessage());

        return Response.ok(retVal.getResultMessage())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }
}
