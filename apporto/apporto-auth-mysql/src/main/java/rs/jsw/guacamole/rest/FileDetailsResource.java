package rs.jsw.guacamole.rest;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleUnsupportedException;
import org.apache.guacamole.net.auth.Connection;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * A REST Service for working with file functionality.
 *
 * <AUTHOR>
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class FileDetailsResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(FileDetailsResource.class);

    /**
     * Command templates for manipulating the files
     */
    private static final String file_details_template = System.getProperty("user.home") + "/.guacamole/bin/file_details.sh %s '%s' %s %s %s";
    private static final String file_delete_template = System.getProperty("user.home") + "/.guacamole/bin/file_delete.sh %s '%s' %s %s %s %s";

    /**
     * Creates a new FileDetailsResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public FileDetailsResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    @GET
    @Produces("application/text")
    public Response getFileDetails(@QueryParam("id") String id, @QueryParam("fileName") String fileName) throws GuacamoleException {

        if( id == null || id.isEmpty()) {
            logger.warn("Invalid request. Missing Id parameter.");
            throw new GuacamoleUnsupportedException("Invalid request. Missing Id parameter.");
        }

        // Get the connection info
        String conn_id = id;
        String cloud_user = getParam(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParam(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Get details for file: {}", conn_id, connection_type, cloud_user, fileName);

        String username = getParam(id, EncurlParameters.SFTP_USERNAME_PARM);
        String password = getParam(id, EncurlParameters.SFTP_PASSWORD_PARM);
        String hostname = getParam(id, EncurlParameters.SFTP_HOSTNAME_PARM);
        String port = getParam(id, EncurlParameters.SFTP_PORT_PARM);

        String convertedfileName = convertFileName(fileName);
        String deleteFile = String.format(file_details_template, username, password, hostname, port, convertedfileName);
        String retVal = createProcess(deleteFile, conn_id, connection_type, cloud_user);

        retVal = fileName + "#" + retVal;

        return Response.ok(retVal)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

     /**
     * Delete selected file.
     * @param id
     * @param fileName
     * @return Response
     * @throws GuacamoleException
     */
    @DELETE
    public Response delete(@QueryParam("id") String id, @QueryParam("fileName") String fileName) throws GuacamoleException {

        if( id == null || id.isEmpty()) {
            logger.warn("Invalid request. Missing Id parameter.");
            throw new GuacamoleUnsupportedException("Invalid request. Missing Id parameter.");
        }

        // Get the connection info
        String conn_id = id;
        String cloud_user = getParam(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParam(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Delete file: {}", conn_id, connection_type, cloud_user, fileName);

        String username = getParam(id, EncurlParameters.SFTP_USERNAME_PARM);
        String password = getParam(id, EncurlParameters.SFTP_PASSWORD_PARM);
        String hostname = getParam(id, EncurlParameters.SFTP_HOSTNAME_PARM);
        String port = getParam(id, EncurlParameters.SFTP_PORT_PARM);

        String[] tokens = fileName.split("/");
        int file_name_token_index = tokens.length - 1;

        String directory = "/";
        for( int i = 0; i < file_name_token_index; i++) {
            if( !tokens[i].isEmpty()) {
                directory += tokens[i] + "/";
            }
        }
        directory = convertFileName(directory);

        fileName = tokens[file_name_token_index];
        fileName = "\"" + fileName + "\"";

        String deleteFile = String.format(file_delete_template, username, password, hostname, port, directory, fileName);
        String retVal = createProcess(deleteFile, conn_id, connection_type, cloud_user);

        return Response.ok(retVal).build();
    }

    /**
     * Obtaining parameter associated with current user.
     *
     * @param id The String which uniquely identifies current user.
     * @param paramName The String which identify name for wanted parameter value.
     * @return Wanted parameter value if successful, empty String if id parameter is invalid,
     *         null value if paramName is not specified for current user.
     *
     * @throws GuacamoleException
     */
    private String getParam(String id, String paramName) throws GuacamoleException {

        logger.debug("get" + paramName + " id= " + id);

        String paramValue = "";
        GuacamoleConfiguration configuration;
        Connection connection;

        // try to get configuration object with QueryParam id. If id is valid,
        // configuration shall be obtained, otherwise value is null
        configuration = ((connection = userContext.getConnectionDirectory().get(id)) == null) ? null
                : connection.getConfiguration();

        if (configuration != null) {

            paramValue = configuration.getParameters().get(paramName);

            if (paramValue != null) {
                logger.debug(paramName + " = " + paramValue);
            }
            else {
                logger.warn("Drupal " + paramName + " not specified");
            }

        }
        else { // return value equals empty String
            logger.warn("Configuration is null. Id does not exist.");
        }

        return paramValue;
    }

    /**
     * Obtaining parameter associated with current user.
     * Convert the file name including path with some spaces to the string for sftp.
     *
     * @param fileName The String of file name to be converted.
     * @return the converted file name if successful, the unchanged file name if the parameter is empty
     *         or it isn't containing any space.
     *
     * @throws GuacamoleException
     */
    private String convertFileName(String fileName) throws GuacamoleException {

        String result;
        String separator = "/";
        String[] token = fileName.split(separator);

        for (int i = 0; i < token.length; i++) {
            String element = token[i];
            if (element.contains(" ")) {
                token[i] = "\\\"" + element + "\\\"";
            }
        }

        result = Arrays.stream(token).collect(Collectors.joining(separator));
        result = "\"" + result + "\"";

        return result;
    }
}
