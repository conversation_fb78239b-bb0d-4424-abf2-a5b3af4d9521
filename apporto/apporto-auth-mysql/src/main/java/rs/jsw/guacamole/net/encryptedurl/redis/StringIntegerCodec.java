/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.net.encryptedurl.redis;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

import io.lettuce.core.codec.RedisCodec;

public class StringIntegerCodec implements RedisCodec<String, Integer> {

    @Override
    public String decodeKey(ByteBuffer bytes) {
        // Decode ByteBuffer to String for keys
        return StandardCharsets.UTF_8.decode(bytes).toString();
    }

    @Override
    public ByteBuffer encodeKey(String key) {
        // Encode String key to ByteBuffer
        return ByteBuffer.wrap(key.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public Integer decodeValue(ByteBuffer bytes) {
        // Decode ByteBuffer to Integer for values
        return Integer.parseInt(StandardCharsets.UTF_8.decode(bytes).toString());
    }

    @Override
    public ByteBuffer encodeValue(Integer value) {
        // Encode Integer value to ByteBuffer
        return ByteBuffer.wrap(String.valueOf(value).getBytes(StandardCharsets.UTF_8));
    }
}
