package rs.jsw.guacamole.rest.classroom;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Info about one active user which will be provided to Virtual Classroom.
 *
 * <AUTHOR>
 *
 */
@Data
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
@ToString(includeFieldNames = true)
public class ClassroomData {

    @Builder.Default private String view_only_key = "";

    @Builder.Default private String full_controll_key = "";

    @EqualsAndHashCode.Include
    @Builder.Default private String session_id = "";

    @EqualsAndHashCode.Include
    @Builder.Default private String username = "";

    @Builder.Default private String windows_name = "";

    @Builder.Default private String email = "";

    @EqualsAndHashCode.Include
    @Builder.Default private String server_id = "";

    @Builder.Default private String groups = "";

    @Builder.Default private String permissionGroups = "";

    private byte[] thumbnail;

    @Builder.Default private boolean removed = false;
}
