/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom;

import com.google.inject.AbstractModule;
import com.google.inject.Scopes;

import rs.jsw.guacamole.net.encryptedurl.redis.RedisModule;
import rs.jsw.guacamole.rest.classroom.thumbnails.ThumbnailsManager;
import rs.jsw.guacamole.rest.classroom.thumbnails.ThumbnailsRetrieveWS;
import rs.jsw.guacamole.rest.classroom.thumbnails.ThumbnailsStoreWS;
import rs.jsw.guacamole.rest.classroom.thumbnails.WSSessionStore;

public class ClassroomModule extends AbstractModule {

    public static final String CLASSROOM_CONNECTION_TYPE = "classroom";

    @Override
    protected void configure() {
        bind(ThumbnailsManager.class).in(Scopes.SINGLETON);
        bind(ThumbnailsRetrieveWS.class);
        bind(ThumbnailsStoreWS.class);
        bind(WSSessionStore.class).in(Scopes.SINGLETON);

        install(new RedisModule());
    }
}
