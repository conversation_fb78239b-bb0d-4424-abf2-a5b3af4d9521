package rs.jsw.guacamole.rest;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.SendStatusData;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionParameterMapper;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * A REST Service for checking network quality.
 *
 * <AUTHOR> Babic
 */
@Produces(MediaType.TEXT_PLAIN)
@Consumes(MediaType.APPLICATION_JSON)
public class NetworkQualityResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(NetworkQualityResource.class);

    /**
     * Status messages
     */
    private static final String POOR_NETWORK_STATUS_MESSAGE = "Poor_network";

    /**
     * Mapper for manipulating connection parameters.
     */
    @Inject
    private EncUrlConnectionParameterMapper encUrlConnectionParameterMapper;

    /**
     * Creates a new NetworkQualityResource which checks network quality within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public NetworkQualityResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    @Path("/poor")
    @POST
    @Consumes("text/plain")
    public Response poorNetwork(@QueryParam("id") Integer id) {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.debug("[{}:{}:{}] poorNetwork.", conn_id, connection_type, cloud_user);

        ConnectionParameterModel parModel = new ConnectionParameterModel();
        parModel.setConnectionIdentifier(id.toString());
        parModel.setName(EncurlParameters.POOR_NETWORK);
        parModel.setValue("true");
        encUrlConnectionParameterMapper.insertOrUpdate(parModel);

        GuacamoleConfiguration configuration = getConfiguration(id);

        SendStatusData.sendNetworkStatus(configuration, POOR_NETWORK_STATUS_MESSAGE);
        logger.info("[{}:{}:{}] Sent poor network status.", conn_id, connection_type, cloud_user);

        return Response.status(Status.OK).build();
    }

}
