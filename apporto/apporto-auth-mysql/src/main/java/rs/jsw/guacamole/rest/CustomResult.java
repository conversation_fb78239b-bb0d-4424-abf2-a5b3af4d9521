package rs.jsw.guacamole.rest;

public class CustomResult {

    private int processExitValue;

    private int resultCode;

    private String resultMessage;

    private String errorMessage;

    public CustomResult() {
        processExitValue = -1;
        resultCode = -1;
        resultMessage = "";
        errorMessage = "";
    }

    public int getResultCode() {
        return resultCode;
    }

    public void setResultCode(int resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMessage() {
        return resultMessage;
    }

    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public int getProcessExitValue() {
        return processExitValue;
    }

    public void setProcessExitValue(int processExitValue) {
        this.processExitValue = processExitValue;
    }

    @Override
    public String toString() {

        return String.format("processExitValue=%s, resultCode=%s, \nresultMessage=%s, \nerrorMessage=%s",
                processExitValue, resultCode, resultMessage, errorMessage);

    }
}
