/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.listeners;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.AuthenticatedUser;
import org.apache.guacamole.net.auth.AuthenticationProvider;
import org.apache.guacamole.net.auth.Credentials;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.net.event.AuthenticationFailureEvent;
import org.apache.guacamole.net.event.AuthenticationSuccessEvent;
import org.apache.guacamole.net.event.TunnelCloseEvent;
import org.apache.guacamole.net.event.TunnelConnectEvent;
import org.apache.guacamole.net.event.listener.Listener;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.GuacamoleCommonUtility;
import com.apporto.hyperstream.core.ApportoProperties;
import com.apporto.hyperstream.core.UrlParams;

import rs.jsw.guacamole.auth.jdbc.user.UserSessionClassroom;
import rs.jsw.guacamole.net.encryptedurl.JsonDecode;
import rs.jsw.guacamole.net.encryptedurl.SecretKeyUpdater;
import rs.jsw.guacamole.net.encryptedurl.Security;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;

/**
 * A listener for events that happens in Guacamole.
 *
 * <AUTHOR> Nikolić
 */
public class EventListener implements Listener {

    private static final Logger logger = LoggerFactory.getLogger(EventListener.class);

    /**
     * URL template for get the user data from messenger. https://{subdomain
     * prefix}.{domain}/guac/messenger/user_details/{cloud_username}
     */
    private static final String check_user_data_template = "https://%s.%s/guac/messenger/user_details/%s";

    private static String DEFAULT_DOMAIN = "apporto.com";
    protected static final String USER_AGENT = "Mozilla/5.0";

    protected String HTTP_API_KEY;

    private static final String GROUP_KEY = "groups";
    private static final String PERMISSION_KEY = "permission_groups";
    private static final String SPLITTER_KEY = "=>";

    // Session info
    private String conn_id = "";
    private String conn_type = "";
    private String cloud_user = "";

    private static final String session_info_template = "%s:%s:%s";
    Map<String, Boolean> conn_status_map = new HashMap<String, Boolean>();

    public EventListener() {
        /* Load the configuration variables */
        try {
            Environment environment = new LocalEnvironment();
            String propValue;

            propValue = environment.getProperty(ApportoProperties.API_BASE_DOMAIN);
            if (propValue != null) {
                DEFAULT_DOMAIN = propValue;
            }

            SecretKeyUpdater.startWatcher();
        }
        catch (Exception e) {
            logger.error("Cannot get the proper parameters from configuration, " +
                        "please check property files. error = {}", e.getMessage());
        }
    }

    @Override
    public void handleEvent(Object event) throws GuacamoleException {
        if (event instanceof AuthenticationSuccessEvent) {
            AuthenticatedUser authenticatedUser = ((AuthenticationSuccessEvent) event).getAuthenticatedUser();
            AuthenticationProvider authProvider = authenticatedUser.getAuthenticationProvider();
            Credentials credentials = authenticatedUser.getCredentials();

            conn_id = credentials.getConnId();
            conn_type = credentials.getConnType();
            cloud_user = credentials.getCloudUserName();

            logger.info("[{}:{}:{}] successful authentication for user {}", conn_id, conn_type, cloud_user,
                        ((AuthenticationSuccessEvent) event).getCredentials().getUsername());

            if (credentials.getRequest().getParameter(EncurlParameters.SHARED_KEY_PARM) == null) {

                String message = credentials.getRequest().getAttribute(UrlParams.MESSAGE_PARAM).toString();
                List<Security> securityList = SecretKeyUpdater.getSecurityList();
                // Try the list of secret keys until decryption succeeds.
                for (Security security : securityList) {
                    String messageTmp = security.decrypt(message);
                    if (messageTmp != null) {
                        message = messageTmp;
                        break;
                    }
                }

                message = message.trim();

                JsonDecode connectionObj = new JsonDecode(message);
                final GuacamoleConfiguration config = connectionObj.getConfig();
                config.setParamsForRouter(credentials.getRequest().getParameter(EncurlParameters.HOSTNAME_PARM), 
                                          credentials.getRequest().getParameter(EncurlParameters.PORT_PARM));

                HTTP_API_KEY = config.getParameter(EncurlParameters.HTTP_API_KEY);

                final UserContext userContext = authProvider.getUserContext(authenticatedUser);
                String connId = connectionObj.getId();
                if (connId != null && !connId.isEmpty()) {
                    connId = connId.substring(2);
                    final Long id = Long.parseLong(connId);
                    String subdomain = config.getParameter(EncurlParameters.SUBDOMAIN_PARM);
                    final String real_subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);
                    
                    if (subdomain != null && userContext instanceof UserSessionClassroom) {
                        final String session_info = String.format(session_info_template, conn_id, conn_type, cloud_user);
                        if (!conn_status_map.containsKey(session_info)) {
                            conn_status_map.put(session_info, true);
                        }
                        else {
                            conn_status_map.replace(session_info, true);
                        }

                        // For debug purposes.
                        ForkJoinPool pool = ForkJoinPool.commonPool();
                        if (pool != null)
                            logger.info("[{}:{}:{}] ForkJoinPool user_details total:{} active:{} running:{} tasks:{} ",
                                    conn_id, conn_type, cloud_user, pool.getParallelism(), pool.getActiveThreadCount(), pool.getRunningThreadCount(), pool.getQueuedSubmissionCount());
                        else
                            logger.info("[{}:{}:{}] ForkJoinPool user_details is null", conn_id, conn_type, cloud_user);
                        // End of debug purposes.

                        CompletableFuture.runAsync(() -> {
                            logger.info("[{}:{}:{}] runAsync user_details, start (get data, groups and permissions)", conn_id, conn_type, cloud_user);

                            String url_str = String.format(check_user_data_template, real_subdomain, DEFAULT_DOMAIN, cloud_user);
                            String data = getData(url_str, session_info);
                            logger.info("[{}:{}:{}] runAsync, getData done", conn_id, conn_type, cloud_user);

                            if (data != null && !data.isEmpty() && conn_status_map.get(session_info)) {
                                logger.info("[{}:{}:{}] runAsync user_details, has some data to parse, data={}", conn_id, conn_type, cloud_user, data);

                                // BEFORE:
                                // {"data":{"username":"t3","windows_username":"t3_rohit","email":"<EMAIL>","group":["MessengerTesting"]}}
                                // NOW:
                                // {"data":[{"windows_username":"himanshu_rohit","firstname":"himanshu","lastname":"Messenger","email":"<EMAIL>","groups":["group118","GroupTest"],"roles":"Tech Support","status":"1"}]}
                                JSONObject json = new JSONObject(data);
                                JSONObject jsonData = json.getJSONArray("data").getJSONObject(0);
                                JSONArray groupData = jsonData.optJSONArray(GROUP_KEY);
                                JSONArray permissionGroupData = jsonData.optJSONArray(PERMISSION_KEY);

                                if (groupData != null && !groupData.isEmpty()) {
                                    for (int i = 0; i < groupData.length(); i++) {
                                        // Clean up the group data, trim all spaces between groups and subgroups
                                        String cleanedGroup = Arrays.stream(groupData.getString(i).split(SPLITTER_KEY))
                                                .map(String::trim)
                                                .collect(Collectors.joining(SPLITTER_KEY));

                                        groupData.put(i, cleanedGroup);
                                    }

                                    jsonData.put(GROUP_KEY, groupData);
                                }
                                logger.info("[{}:{}:{}] runAsync user_details, parsed groups={}", conn_id, conn_type, cloud_user, groupData);

                                if (permissionGroupData != null && !permissionGroupData.isEmpty()) {
                                    for (int i = 0; i < permissionGroupData.length(); i++) {
                                        // Clean up the group data, trim all spaces between groups and subgroups
                                        String cleanedGroup = Arrays.stream((permissionGroupData.getString(i)).split(SPLITTER_KEY))
                                                .map(String::trim)
                                                .collect(Collectors.joining(SPLITTER_KEY));


                                        permissionGroupData.put(i, cleanedGroup);
                                    }

                                    jsonData.put(PERMISSION_KEY, permissionGroupData);
                                }
                                logger.info("[{}:{}:{}] runAsync user_details, parsed permissions={}", conn_id, conn_type, cloud_user, permissionGroupData);

                                jsonData.put("appname", config.getParameter(EncurlParameters.APP_NAME_KEY));
                                ((UserSessionClassroom) userContext).getDatas().put(id, jsonData);
                                ((UserSessionClassroom) userContext).getGroups().put(id, jsonData.optJSONArray(GROUP_KEY));
                                ((UserSessionClassroom) userContext).getPermissionGroups().put(id, jsonData.optJSONArray(PERMISSION_KEY));

                                logger.info("[{}:{}:{}] runAsync user_details, done parsing", conn_id, conn_type, cloud_user);
                            }

                            logger.info("[{}:{}:{}] runAsync user_details, finished (get data, groups and permissions)", conn_id, conn_type, cloud_user);
                        });

                    }
                }
            }
        }

        if (event instanceof AuthenticationFailureEvent) {
            AuthenticationFailureEvent ev = (AuthenticationFailureEvent) event;
            String username = ev.getCredentials().getUsername();

            logger.info("[0:primary:{}] failed authentication for user {}", username,
                        ((AuthenticationFailureEvent) event).getCredentials().getUsername());
        }

        if (event instanceof TunnelConnectEvent) {
            AuthenticatedUser authenticatedUser = ((TunnelConnectEvent) event).getAuthenticatedUser();

            conn_id = authenticatedUser.getCredentials().getConnId();
            cloud_user = authenticatedUser.getCredentials().getCloudUserName();
            conn_type = authenticatedUser.getCredentials().getConnType();

            logger.info("[{}:{}:{}] tunnel connect for user {}", conn_id, conn_type, cloud_user,
                        ((TunnelConnectEvent) event).getCredentials().getUsername());
        }

        if (event instanceof TunnelCloseEvent) {
            TunnelCloseEvent ev = (TunnelCloseEvent) event;
            AuthenticatedUser authenticatedUser = ev.getAuthenticatedUser();

            conn_id = authenticatedUser.getCredentials().getConnId();
            cloud_user = authenticatedUser.getCredentials().getCloudUserName();
            conn_type = authenticatedUser.getCredentials().getConnType();

            String session_info = String.format(session_info_template, conn_id, conn_type, cloud_user);
            if (conn_status_map.containsKey(session_info)) {
                conn_status_map.replace(session_info, false);
            }

            logger.info("[{}:{}:{}] tunnel UUID={} closed for user {}", conn_id, conn_type, cloud_user,
                        ev.getTunnel().getUUID(), ev.getCredentials().getUsername());
        }
    }

    private String getData(String url_str, String session_info) {
        final String err_string = "{\"data\":\"invalid username\"}";

        try {
            URL url = new URL(url_str);
            String result = sendToDrupal(url, session_info);

            // Drupal API in some versions returns HTTP OK if user does not exists and
            // just returns error text.
            if (err_string.equalsIgnoreCase(result)) {
                result = "";
            }

            return result;

        }
        catch (MalformedURLException e) {
            logger.error("[{}] Exception log: ", session_info, e);
            return "";
        }
    }

    private String sendToDrupal(URL url, String session_info) {
        logger.debug("[{}] sendToDrupal  url= {}", session_info, url);
        StringBuffer response = new StringBuffer("");

        try {
            // Check if session has been stopped
            if (conn_status_map.get(session_info) == false) {
                conn_status_map.remove(session_info);
                return response.toString();
            }

            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            // Fix for AP-9650
            con.setConnectTimeout(5000);
            con.setReadTimeout(20000);
            if (HTTP_API_KEY != null && !HTTP_API_KEY.isEmpty()) {
                con.setRequestProperty("Http-Api-Key", HTTP_API_KEY);
            }

            if (con.getResponseCode() == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }

                in.close();
            }

            logger.info("[{}] sendToDrupal()-> user_details response = {}",
                         session_info, response.toString());
        }
        catch (MalformedURLException e) {
            logger.warn("[{}] MalformedURLException with http request, URL={}",
                        session_info, url.toString());
            logger.error("[{}] Exception log: ", session_info, e);
        }
        catch (ProtocolException e) {
            logger.warn("[{}] ProtocolException with http request, URL={}",
                        session_info, url.toString());
            logger.error("[{}] Exception log: ", session_info, e);
        }
        catch (UnknownHostException e) {
            logger.warn("[{}] UnknownHostException with user_details http request, URL={}",
                        session_info, url.toString());
            logger.error("[{}] Exception log: ", session_info, e);
        }
        catch (SocketTimeoutException e) {
            logger.warn("[{}] SocketTimeoutException with user_details http request, URL={}",
                        session_info, url.toString());
            logger.error("[{}] Exception log: ", session_info, e);
        }
        catch (IOException e) {
            // Try to reconnect when Drupal was very slow and didn't get any response
            logger.warn("[{}] Connection attempt to user_details failed with IOException, URL={}. Retrying ...",
                        session_info, url.toString());
            return sendToDrupal(url, session_info);
        }

        return response.toString();
    }
}
