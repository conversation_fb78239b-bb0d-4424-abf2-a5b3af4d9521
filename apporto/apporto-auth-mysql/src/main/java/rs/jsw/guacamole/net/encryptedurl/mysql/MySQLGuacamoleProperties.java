/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.net.encryptedurl.mysql;

import org.apache.guacamole.properties.BooleanGuacamoleProperty;
import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.properties.StringGuacamoleProperty;

/**
 * Properties used by the MySQL Authentication plugin.
 */
public class MySQLGuacamoleProperties {

    /**
     * This class should not be instantiated.
     */
    private MySQLGuacamoleProperties() {}

    /**
     * The hostname of the MySQL server hosting the Guacamole authentication 
     * tables.
     */
    public static final StringGuacamoleProperty MYSQL_HOSTNAME = new StringGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-hostname"; }

    };

    /**
     * The port number of the MySQL server hosting the Guacamole authentication 
     * tables.
     */
    public static final IntegerGuacamoleProperty MYSQL_PORT = new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-port"; }

    };

    /**
     * The name of the MySQL database containing the Guacamole authentication 
     * tables.
     */
    public static final StringGuacamoleProperty MYSQL_DATABASE = new StringGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-database"; }

    };

    /**
     * The username that should be used when authenticating with the MySQL
     * database containing the Guacamole authentication tables.
     */
    public static final StringGuacamoleProperty MYSQL_USERNAME = new StringGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-username"; }

    };

    /**
     * The password that should be used when authenticating with the MySQL
     * database containing the Guacamole authentication tables.
     */
    public static final StringGuacamoleProperty MYSQL_PASSWORD = new StringGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-password"; }

    };

    /**
     * Whether a user account within the database is required for authentication
     * to succeed, even if the user has been authenticated via another
     * authentication provider.
     */
    public static final BooleanGuacamoleProperty MYSQL_USER_REQUIRED = new BooleanGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-user-required"; }

    };

    /**
     * The maximum number of concurrent connections to allow overall. Zero
     * denotes unlimited.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_ABSOLUTE_MAX_CONNECTIONS =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-absolute-max-connections"; }

    };

    /**
     * The maximum number of concurrent connections to allow to any one
     * connection. Zero denotes unlimited.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_DEFAULT_MAX_CONNECTIONS =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-default-max-connections"; }

    };

    /**
     * The maximum number of concurrent connections to allow to any one
     * connection group. Zero denotes unlimited.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_DEFAULT_MAX_GROUP_CONNECTIONS =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-default-max-group-connections"; }

    };

    /**
     * The maximum number of concurrent connections to allow to any one
     * connection by an individual user. Zero denotes unlimited.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_DEFAULT_MAX_CONNECTIONS_PER_USER =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-default-max-connections-per-user"; }

    };

    /**
     * The maximum number of concurrent connections to allow to any one
     * connection group by an individual user. Zero denotes
     * unlimited.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_DEFAULT_MAX_GROUP_CONNECTIONS_PER_USER =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-default-max-group-connections-per-user"; }

    };

    /**
     * The maximum number of active connections that can exist at any given time.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_MAX_ACTIVE_CONNECTIONS =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-max-active-connections"; }

    };

    /**
     * The maximum number of idle connections that can exist at any given time.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_MAX_IDLE_CONNECTIONS =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-max-idle-connections"; }

    };

    /**
     * The maximum amount of time that a connection can be "checked out" of
     * the pool before it will be forcefully returned. (in milliseconds)
     * Default: 20000ms (i.e. 20 seconds)
     */
    public static final IntegerGuacamoleProperty
            MYSQL_MAX_CHECKOUT_TIME =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-max-checkout-time"; }

    };

    /**
     * This is a low level setting that gives the pool a chance to print a log status
     * and re-attempt the acquisition of a connection in the case that it’s taking
     * unusually long (to avoid failing silently forever if the pool is misconfigured).
     * Default: 20000ms (i.e. 20 seconds)
     */
    public static final IntegerGuacamoleProperty
            MYSQL_TIME_TO_WAIT =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-time-to-wait"; }

    };

    /**
     * This configures how often the poolPingQuery will be used. This can be set to match
     * the typical timeout for a database connection, to avoid unnecessary pings.
     * Default: 60000ms (60 seconds)
     * If a connection has not been used in this many milliseconds, ping the database to
     * make sure the connection is still good.
     */
    public static final IntegerGuacamoleProperty
            MYSQL_PING_CONNECTIONS_NOT_USED_FOR =
            new IntegerGuacamoleProperty() {

        @Override
        public String getName() { return "mysql-ping-connections-not-used-for"; }

    };

}
