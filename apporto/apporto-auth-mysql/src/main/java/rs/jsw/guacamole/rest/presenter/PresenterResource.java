/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.presenter;

import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.sharing.SharedConnectionMap;
import org.apache.guacamole.auth.jdbc.sharing.connection.SharedConnectionDefinition;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.auth.UserContext;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.auth.jdbc.user.UserSessionClassroom;
import rs.jsw.guacamole.net.encryptedurl.jdbc.ApportoSharedConnectionMap;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;
import rs.jsw.guacamole.rest.classroom.ClassroomData;

/**
 * A REST Service for providing users data to virtual presenter.
 *
 * <AUTHOR> Wei
 */
public class PresenterResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(PresenterResource.class);

    @Inject
    private SharedConnectionMap connectionMap;

    @AssistedInject
    public PresenterResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Retrieve groups for passed id.
     *
     * @param id
     * @param group
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/close")
    @POST
    @Consumes("text/plain")
    public Response close(@QueryParam("id") Long id, @QueryParam("group") String group,
                          @QueryParam("appname") String appname)
                          throws GuacamoleException, UnknownHostException {
        // Get the connection info
        String conn_id = Long.toString(id);
        String cloud_user = getParameter(Integer.parseInt(conn_id), EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(Integer.parseInt(conn_id), EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.debug("[{}:{}:{}] close: session_id={}, group={}",
                     conn_id, conn_type, cloud_user, conn_id, group);

        if (userContext instanceof UserSessionClassroom) {
            String key = Long.toString(id) + "=>" + group + "=>" + appname;
            ((UserSessionClassroom) userContext).getDisplayedInPresenter().remove(key);
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Retrieve active connections for one customer instance, on one server.
     *
     * @param id
     * @param group
     * @throws GuacamoleException
     * @throws UnknownHostException
     */
    @Path("/getConnections")
    @GET
    @Consumes("text/plain")
    public Response getConnections(@QueryParam("id") Integer id, @QueryParam("group") String group,
                                   @QueryParam("appname") String appname)
                                   throws GuacamoleException, UnknownHostException {
        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.debug("[{}:{}:{}] getConnections:session_id={}, group={}, appname={}",
                     conn_id, conn_type, cloud_user, conn_id, group, appname);

        String subdomain = getParameter(id, EncurlParameters.SUBDOMAIN_PARM);
        if (subdomain.contains(".")) {
            subdomain = subdomain.substring(0, subdomain.indexOf("."));
        }
        logger.debug("[{}:{}:{}] Subdomain = {}", conn_id, conn_type, cloud_user, subdomain);

        ConcurrentMap< String, SharedConnectionDefinition> mp = ((ApportoSharedConnectionMap) connectionMap)
                .getAllConnections();

        /**
         * Structure for response:
         *
         * KEY: shared_key value VALUE: ClassroomData
         */
        Map< String, ClassroomData> sharedKeys = new HashMap< String, ClassroomData>();

        /**
         * Structure for storing API data:
         *
         * KEY: sessionId VALUE: JSON data
         */
        Map< String, JSONObject> drupalData = new HashMap< String, JSONObject>();

        /**
         * Saving ids for new active users
         */
        List< Long> newKeys = new ArrayList<>();

        /**
         * The list of already displayed sessions for the user with ID Here
         * remains only ids for killed sessions
         */
        List< ClassroomData> displayed = new ArrayList<>();
        String key = Integer.toString(id) + "=>" + group + "=>" + appname;
        if (userContext instanceof UserSessionClassroom) {
            displayed = ((UserSessionClassroom) userContext).getDisplayedInPresenter(key);
        }
        List< Long> displayedId = new ArrayList<>();
        if (displayed != null) {
            for (ClassroomData cd : displayed) {
                logger.debug("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, cd.toString());
                displayedId.add(Long.parseLong(cd.getSession_id()));
            }
        }

        /**
         * The list of the shared keys in all the connections
         */
        List< String> entryKeys = new ArrayList<>();
        for (Map.Entry< String, SharedConnectionDefinition> entry : mp.entrySet()) {
            if (!entry.getKey().isEmpty()) {
                entryKeys.add(entry.getKey());
            }
        }

        for (Map.Entry< String, SharedConnectionDefinition> entry : mp.entrySet()) {

            String sessionId = entry.getValue().getSharingProfile().getModel().getParentIdentifier();

            // don't save data for current session
            if (Integer.parseInt(sessionId) == id.intValue()) {
                continue;
            }

            logger.debug("[{}:{}:{}] SESSION ID: {}, SHARING PROFILE: {}",
                         conn_id, conn_type, cloud_user, sessionId,
                         entry.getValue().getSharingProfile().getName());

            if (!newKeys.contains(Long.parseLong(sessionId))) {
                newKeys.add(Long.parseLong(sessionId));
                logger.debug("[{}:{}:{}] NEW KEYS {}", conn_id, conn_type, cloud_user, sessionId);
            }

            // don't save data for mm shared links
            if (entry.getValue().getSharingProfile().getName().equals("share-MM")
                    || entry.getValue().getSharingProfile().getName().equals("share-MM-3")) {
                continue;
            }

            String userSubdomain = "";

            userSubdomain = getParameter(Integer.parseInt(sessionId), EncurlParameters.SUBDOMAIN_PARM);
            if (userSubdomain == null) {
                logger.error("[{}:{}:{}] Parent subdomain is empty for session id {}",
                             conn_id, conn_type, cloud_user, sessionId);
                continue;
            }

            if (userSubdomain.contains(".")) {
                userSubdomain = userSubdomain.substring(0, userSubdomain.indexOf("."));
            }

            // group users by sub domain info
            if (subdomain == null || !subdomain.equals(userSubdomain)) {
                continue;
            }

            // don't save data if they are already provided
            if (displayed.size() > 0 && displayedId.contains(Long.parseLong(sessionId))) {
                int index = displayedId.indexOf(Long.parseLong(sessionId));
                String vo_key = displayed.get(index).getView_only_key();
                String fc_key = displayed.get(index).getFull_controll_key();

                if (entryKeys.contains(vo_key) && entryKeys.contains(fc_key)) {
                    logger.debug("[{}:{}:{}] The session id {} is already displayed.",
                                conn_id, conn_type, cloud_user, sessionId);
                    continue;
                }
                else {
                    displayed.remove(index);
                    displayedId.remove(index);
                    logger.error("[{}:{}:{}] Because the shared key of the session id {} doesn't exist in " +
                                 "the connection map, this session was removed.",
                                 conn_id, conn_type, cloud_user, sessionId);
                }
            }

            String username = entry.getValue().getSharingProfile().getCurrentUser().getCredentials().getUsername();
            String profileName = entry.getValue().getSharingProfile().getName();
            String identifier = sessionId + "-" + profileName;
            String sharedKey = entry.getKey();

            logger.debug("[{}:{}:{}] Username: {}, profileName: {}, identifier: {}, sharedKey: {}",
                         conn_id, conn_type, cloud_user, username, profileName, identifier, sharedKey);

            if (sharedKey.isEmpty()) {
                logger.error("[{}:{}:{}] Shared key is empty for session id {}, user {}",
                             conn_id, conn_type, cloud_user, sessionId, username);
                continue;
            }

            JSONObject jsonData = null;
            if (userContext != null && userContext instanceof UserSessionClassroom) {
                jsonData = ((UserSessionClassroom) userContext).getUserData(Long.parseLong(sessionId));
            }

            if (jsonData == null || jsonData.isEmpty()) {
                continue;
            }

            // Call of API to get first name,last name and groups for the current session
            if (!drupalData.containsKey(sessionId)) {
                drupalData.put(sessionId, jsonData);
            }
            else {
                jsonData = drupalData.get(sessionId);
            }
            logger.debug("[{}:{}:{}] {}", conn_id, conn_type, cloud_user, jsonData.toString());

            if (!appname.equals((String) jsonData.get("appname"))) {
                logger.debug("[{}:{}:{}] The session id {} is already displayed in the presenter of \"{}\" class.",
                            conn_id, conn_type, cloud_user, sessionId, appname);
                continue;
            }

            String name = (String) jsonData.get("firstname");
            String lastname = (String) jsonData.get("lastname");
            String email = (String) jsonData.get("email");

            GuacamoleTunnel tunnel = entry.getValue().getActiveConnection().getTunnel();
            if (tunnel != null && tunnel.isOpen()) {
                boolean is_VO = profileName.equals("share-VO");

                ClassroomData profile;
                if (sharedKeys.containsKey(sessionId)) {
                    profile = sharedKeys.get(sessionId);
                }
                else {
                    profile = new ClassroomData();
                }

                if (is_VO) {
                    profile.setView_only_key(sharedKey);
                }
                else {
                    profile.setFull_controll_key(sharedKey);
                }

                String fullname = "";
                if (name != null) {
                    fullname = name;
                    if (lastname != null && !lastname.isEmpty()) {
                        fullname = fullname + " " + lastname;
                    }
                }

                if (fullname.isEmpty()) {
                    fullname = email;
                }

                profile.setUsername(fullname);
                profile.setWindows_name(username);
                profile.setEmail(email);

                JSONArray groups = jsonData.optJSONArray("groups");
                if (groups != null) {
                    profile.setGroups(groups.toString());
                }

                JSONArray permissionGroups = jsonData.optJSONArray("permission-groups");
                if (permissionGroups != null) {
                    profile.setPermissionGroups(permissionGroups.toString());
                }

                Environment environment = new LocalEnvironment();
                profile.setServer_id(environment.getRequiredProperty(ApportoProperties.SERVER_ID));

                // Set the session info
                profile.setThumbnail(null);
                profile.setSession_id(sessionId);

                sharedKeys.put(sessionId, profile);

                if (!profile.getView_only_key().isEmpty() && !profile.getFull_controll_key().isEmpty()
                         && !displayed.contains(profile)) {
                    displayed.add(profile);
                    logger.debug("[{}:{}:{}] NEW KEYS {}", conn_id, conn_type, cloud_user, sessionId);
                }
            }

        }

        displayedId.removeAll(newKeys);
        if (displayedId.size() > 0) {
            Iterator< ClassroomData> iter = displayed.iterator();
            while (iter.hasNext()) {
                ClassroomData data = iter.next();
                if (displayedId.contains(Long.parseLong(data.getSession_id()))) {
                    ClassroomData forRemoving = data.toBuilder().removed(true).build();

                    sharedKeys.put(data.getSession_id(), forRemoving);
                    iter.remove();
                    ((UserSessionClassroom) userContext).removeDisplayedInPresenter(key, forRemoving);
                }
            }
        }

        return Response.ok(sharedKeys)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

}
