/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest;

import java.io.IOException;

import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.map.ObjectMapper;

class HLCommand {
    private Long contourId;
    private int command;
    private int x;
    private int y;
    private int width;
    private int height;
    private float scale;
    private boolean mmonitor;

    private static ObjectMapper mapper = new ObjectMapper();

    HLCommand() {
        this(null, HLCommandCode.UNKNOWN, 0, 0, 0, 0, 1, false);
    }

    HLCommand(Long contourId, HLCommandCode command) {
        this(contourId, command, 0, 0, 0, 0, 1, false);
    }

    HLCommand(Long contourId, HLCommandCode command, int x, int y, int width, int height, float scale, boolean mmonitor) {
        this.contourId = contourId;
        this.command = command.getCommand();
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.scale = scale;
        this.mmonitor = mmonitor;
    }
    
    HLCommand(String s) {
       try {
           HLCommand cmd = mapper.readValue(s, HLCommand.class);
           this.contourId = cmd.contourId;
//           this.command = cmd.command;
           this.x = cmd.x;
           this.y = cmd.y;
           this.width = cmd.width;
           this.height = cmd.height;
        } catch (JsonParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (JsonMappingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public Long getContourId() {
        return contourId;
    }

    public void setContourId(Long contourId) {
        this.contourId = contourId;
    }

    public int getCommand() {
        return command;
    }

    public void setCommand(int command) {
        this.command = command;
    }

    public int getX() {
        return x;
    }

    public void setX(int x) {
        this.x = x;
    }

    public int getY() {
        return y;
    }

    public void setY(int y) {
        this.y = y;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public boolean getMmonitor() {
        return mmonitor;
    }

    public void setMmonitor(boolean mmonitor) {
        this.mmonitor = mmonitor;
    }

    public float getScale() {
        return scale;
    }

    public void setScale(float scale) {
        this.scale = scale;
    }
};
