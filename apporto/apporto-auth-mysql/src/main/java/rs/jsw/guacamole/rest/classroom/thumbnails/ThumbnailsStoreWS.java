/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.classroom.thumbnails;

import java.io.IOException;

import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;
import com.google.inject.Injector;

import rs.jsw.guacamole.rest.classroom.ClassroomInjector;
import rs.jsw.guacamole.rest.classroom.ClassroomModule;
import rs.jsw.guacamole.rest.classroom.WSEndpoint;

public class ThumbnailsStoreWS implements WSEndpoint { 
    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ThumbnailsStoreWS.class);

    @Inject
    private ThumbnailsManager thumbnailsManager;

    @Inject
    private WSSessionStore sessionStore;

    public ThumbnailsStoreWS() {
        logger.info("ThumbnailsStoreWS");

        // This class is created by the 3rd party library, so we have to inject the dependencies manually.
        Injector injector = ClassroomInjector.getInjector();
        injector.injectMembers(this);
    }

    @OnOpen
    public void onOpen(Session session, @PathParam("id") Long id) throws IOException {
        logger.debug("OnOpen");
        sessionStore.addSession(id, session);
        thumbnailsManager.open();
    }

    @OnMessage
    public void onMessage(Session session,
                          byte[] thumbnail,
                          boolean last,
                          @PathParam("id") Long id
                          ) throws IOException {
        logger.info("[{}:{}:{}] ThumbnailsStoreWS OnMessage binary, last chunk: {}", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", last);

        if (id == null) {
            logger.error("[{}:{}:{}] ThumbnailsStoreWS OnMessage binary id is null", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
            return;
        }

        thumbnailsManager.storeThumb(id, thumbnail, last);
        if (last) {
            thumbnailsManager.notifySubscribers(id);
        }
        logger.debug("[{}:{}:{}] Thumbnail added", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "");
    }

    @OnClose
    public void onClose(Session session, @PathParam("id") Long id) throws IOException {
        logger.info("[{}:{}:{}] ThumbnailsStoreWS OnClose, remaining sessions {}", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", sessionStore.size());
        sessionStore.removeSession(id);

        if (sessionStore.isEmpty()) {
            thumbnailsManager.close();
        }
    }

    @OnError
    public void onError(Session session, Throwable throwable, @PathParam("id") Long id) {
        logger.error("[{}:{}:{}] ThumbnailsStoreWS OnError", id, ClassroomModule.CLASSROOM_CONNECTION_TYPE, "", throwable);
    }
}
