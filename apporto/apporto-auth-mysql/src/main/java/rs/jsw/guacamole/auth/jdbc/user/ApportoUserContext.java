/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.auth.jdbc.user;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledFuture;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.user.ModeledAuthenticatedUser;
import org.apache.guacamole.auth.jdbc.user.ModeledUserContext;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.inject.Inject;

import rs.jsw.guacamole.rest.ApportoResourceFactory;
import rs.jsw.guacamole.rest.classroom.ClassroomData;
import rs.jsw.guacamole.rest.classroom.ThumbnailData;
import rs.jsw.guacamole.rest.multimonitor.SessionDesktop;
import rs.jsw.guacamole.rest.notification.Message;
import rs.jsw.guacamole.rest.notification.dto.ResponseDTO;

/**
 * Apporto UserContext implementation which provides additional apporto specific
 * data and rest calls.
 */
public class ApportoUserContext extends ModeledUserContext
        implements UserSessionDesktop, UserSessionNotifications, UserSessionClassroom, LogoutEventManager {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ApportoUserContext.class);

    /**
     * Multimonitor parameters
     */
    @Inject
    private SessionDesktop sessionDesktop;

    @Override
    public SessionDesktop getSessionDesktop() {
        return sessionDesktop;
    }

    /**
     * Others
     */
    private static ConcurrentMap<Long, BlockingQueue<Message>> sessionNotifications = null;
    private static ConcurrentMap<Long, ResponseDTO> sessionNotificationResponses = null;

    private static Map<Long, String> raisedHands = null;
    private static Map<Long, JSONArray> permissionGroups = null;
    private static Map<Long, JSONArray> groups = null;
    private static Map<Long, JSONObject> data = null;

    private static Map<Long, ThumbnailData> thumbs = null;

    private static Map<String, List<ClassroomData>> displayedInClassroom = null;
    private static Map<String, List<ClassroomData>> displayedInPresenter = null;

    private static Map<Long, String> shareStatus = null;

    private static HashMap<String, ScheduledFuture<?>> logoutEventMap = null;

    @Override
    public BlockingQueue<Message> getSessionNotifications(long sessionId) {
        return sessionNotifications.computeIfAbsent(sessionId, k -> new LinkedBlockingQueue<>());
    }

    @Override
    public void removeSessionNotifications(long sessionId) {
        sessionNotifications.remove(sessionId);
    }

    @Override
    public ConcurrentMap<Long, ResponseDTO> getSessionNotificationResponses() {
        return sessionNotificationResponses;
    }

    /**
     * Create apporto specific resources
     */
    @Inject
    private ApportoResourceFactory apportoResourceFactory;

    @Override
    public void init(ModeledAuthenticatedUser currentUser) {
        super.init(currentUser);

        if (sessionNotifications == null)
            sessionNotifications = new ConcurrentHashMap<>();

        if (sessionNotificationResponses == null)
            sessionNotificationResponses = new ConcurrentHashMap<>();

        if (data == null)
            data = new HashMap<>();

        if (permissionGroups == null)
            permissionGroups = new HashMap<>();

        if (groups == null)
            groups = new HashMap<>();

        if (thumbs == null)
            thumbs = new HashMap<>();

        if (raisedHands == null)
            raisedHands = new HashMap<>();

        if (shareStatus == null)
            shareStatus = new HashMap<>();

        if (displayedInClassroom == null)
            displayedInClassroom = new HashMap<>();

        if (displayedInPresenter == null)
            displayedInPresenter = new HashMap<>();

        if (logoutEventMap == null)
            logoutEventMap = new HashMap<>();
    }

    @Override
    public Object getResource() throws GuacamoleException {
        logger.debug("Creating apportoResource");
        return apportoResourceFactory.create(this);
    }

    @Override
    public JSONArray getUserGroups(long sessionId) {
        if (groups == null)
            groups = new HashMap<Long, JSONArray>();

        if (!groups.containsKey(sessionId))
            groups.put(sessionId, new JSONArray());

        return groups.get(sessionId);
    }

    @Override
    public JSONObject getUserData(long sessionId) {
        if (!data.containsKey(sessionId))
            data.put(sessionId, new JSONObject());

        return data.get(sessionId);
    }

    @Override
    public Map<Long, JSONArray> getGroups() {
        return groups;
    }

    @Override
    public Map<Long, JSONObject> getDatas() {
        return data;
    }

    /**
     * Returns the map of thumbnails.
     * This method will be replaced with a redis implementation.
     * 
     * @return Map of thumbnails
     */
    public static Map<Long, ThumbnailData> getThumbs() {
        return thumbs;
    }

    @Override
    public Map<Long, String> getRaisedHands() {
        return raisedHands;
    }

    @Override
    public Map<Long, String> getShareStatus() {
        return shareStatus;
    }

    @Override
    public List<ClassroomData> getDisplayedInClassroom(String key) {
        if (!displayedInClassroom.containsKey(key))
            displayedInClassroom.put(key, new ArrayList<ClassroomData>());

        return displayedInClassroom.get(key);
    }

    @Override
    public void removeDisplayedInClassroom(String key, ClassroomData cd) {
        if (displayedInClassroom.containsKey(key)) {
            List<ClassroomData> data = displayedInClassroom.get(key);
            if (!data.isEmpty() && data.contains(cd)) {
                Iterator<ClassroomData> itr = data.iterator();
                while (itr.hasNext()) {
                    ClassroomData info = itr.next();
                    if (cd.equals(info)) {
                        itr.remove();
                        logger.info("REMOVED from " + key);
                    }
                }
                displayedInClassroom.put(key, data);
            }
            displayedInClassroom.get(key).remove(cd);

        }
    }

    @Override
    public Map<String, List<ClassroomData>> getDisplayedInClassroom() {
        return displayedInClassroom;
    }

    @Override
    public List<ClassroomData> getDisplayedInPresenter(String key) {
        if (!displayedInPresenter.containsKey(key))
            displayedInPresenter.put(key, new ArrayList<ClassroomData>());

        return displayedInPresenter.get(key);
    }

    @Override
    public Map<String, List<ClassroomData>> getDisplayedInPresenter() {
        return displayedInPresenter;
    }

    @Override
    public void removeDisplayedInPresenter(String key, ClassroomData cd) {
        if (displayedInPresenter.containsKey(key)) {
            List<ClassroomData> data = displayedInPresenter.get(key);
            if (!data.isEmpty() && data.contains(cd)) {
                Iterator<ClassroomData> itr = data.iterator();
                while (itr.hasNext()) {
                    ClassroomData info = itr.next();
                    if (cd.equals(info)) {
                        itr.remove();
                        logger.info("REMOVED from " + key);
                    }
                }
                displayedInPresenter.put(key, data);
            }
            displayedInPresenter.get(key).remove(cd);
        }
    }

    @Override
    public Map<Long, JSONArray> getPermissionGroups() {
        return permissionGroups;
    }

    @Override
    public JSONArray getUserPermissionGroups(long sessionId) {
        if (permissionGroups == null)
            permissionGroups = new HashMap<Long, JSONArray>();

        if (!permissionGroups.containsKey(sessionId))
            permissionGroups.put(sessionId, new JSONArray());

        return permissionGroups.get(sessionId);
    }

    @Override
    public HashMap<String, ScheduledFuture<?>> getLogoutEventMap() {
        return logoutEventMap;
    }

    @Override
    public ScheduledFuture<?> getLogoutEvent(String key) {
        return logoutEventMap.get(key);
    }

    @Override
    public void setLogoutEvent(String key, ScheduledFuture<?> task) {
        logoutEventMap.put(key, task);
    }

    @Override
    public void removeLogoutEvent(String key) {
        if (logoutEventMap.containsKey(key)) {
            logoutEventMap.remove(key);
        }
    }

    @Override
    public int getLogoutEventCount() {
        return logoutEventMap.size();
    }

}
