package rs.jsw.guacamole.rest;

import org.codehaus.jackson.annotate.JsonCreator;
import org.codehaus.jackson.annotate.JsonValue;

enum HLCommandCode {
    MOUSE_MOVE(1), MOUSE_LBTN_UP(2), MOUSE_LBTN_DOWN(3), END(4), UNKNOWN(5);

    private int command;

    HLCommandCode() {
        this(5);
    }

    HLCommandCode(int command) {
        this.command = command;
    }

    @JsonCreator
    public static HLCommandCode fromValue(String value) {
        int code = Integer.parseInt(value);

        switch (code) {
            case 1: return HLCommandCode.MOUSE_MOVE;
            case 2: return HLCommandCode.MOUSE_LBTN_UP;
            case 3: return HLCommandCode.MOUSE_LBTN_DOWN;
            case 4: return HLCommandCode.END;
            default: return HLCommandCode.UNKNOWN;
        }
    }

    @JsonValue
    public int toValue() {
        return ordinal();
    }

    public int getCommand() {
        return command;
    }

    public void setCommand(int command) {
        this.command = command;
    }
};
