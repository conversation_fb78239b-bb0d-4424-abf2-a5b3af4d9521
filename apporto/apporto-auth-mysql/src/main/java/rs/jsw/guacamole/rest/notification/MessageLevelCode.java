/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.notification;

import org.codehaus.jackson.annotate.JsonCreator;
import org.codehaus.jackson.annotate.JsonValue;

enum MessageLevelCode {
    NOTIFY(1), WARNING(2), SEVERE(3), NONE(4), UNKNOWN(5);

    private int level;

    MessageLevelCode() {
        this(1);
    }

    MessageLevelCode(int level) {
        this.level = level;
    }

    @JsonCreator
    public static MessageLevelCode fromValue(String value) {
        int code = Integer.parseInt(value);

        switch (code) {
            case 1: return MessageLevelCode.NOTIFY;
            case 2: return MessageLevelCode.WARNING;
            case 3: return MessageLevelCode.SEVERE;
            case 4: return MessageLevelCode.NONE;
            default: return MessageLevelCode.UNKNOWN;
        }
    }

    @JsonValue
    public int toValue() {
        return ordinal();
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }
};
