/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package rs.jsw.guacamole.rest.notification;

import java.io.IOException;

import org.codehaus.jackson.JsonParseException;
import org.codehaus.jackson.map.JsonMappingException;
import org.codehaus.jackson.map.ObjectMapper;

/**
 * Message that will be sent to the remote client.
 * 
 * It can contain both message and some command to execute.
 * 
 * <AUTHOR>
 *
 */
public class Message {
	private String title;
	private String text;
	private int level;
	private int command;
	private Long delay;
	private String data = "";
	
	private static ObjectMapper mapper = new ObjectMapper();

	Message() {
		this(null, null, MessageLevelCode.NOTIFY, MessageCommandCode.UNKNOWN, 0L);
	}

	Message(String title, String text, MessageLevelCode level, MessageCommandCode command, Long delay) {
		this.title = title;
		this.text = text;
		this.level = level.getLevel();
		this.command = command.getCommand();
		this.delay = delay;
	}

	Message(String title, String text, MessageLevelCode level, MessageCommandCode command, Long delay, String data) {
		this.title = title;
		this.text = text;
		this.level = level.getLevel();
		this.command = command.getCommand();
		this.delay = delay;
		this.data = data;
	}

    Message(String s) {
        try {
            Message msg = mapper.readValue(s, Message.class);
			this.title = msg.title;
			this.text = msg.text;
			this.level = msg.level;
			this.command = msg.command;
			this.delay = msg.delay;
         } catch (JsonParseException e) {
             // TODO Auto-generated catch block
             e.printStackTrace();
         } catch (JsonMappingException e) {
             // TODO Auto-generated catch block
             e.printStackTrace();
         } catch (IOException e) {
             // TODO Auto-generated catch block
             e.printStackTrace();
         }
    }

	public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public int getCommand() {
		return command;
	}

	public void setCommand(int command) {
		this.command = command;
	}

	public Long getDelay() {
		return delay;
	}

	public void setDelay(Long delay) {
		this.delay = delay;
	}

	public String getData() {
		return data;
	}

	public void setData(String data) {
		this.data = data;
	}
}
