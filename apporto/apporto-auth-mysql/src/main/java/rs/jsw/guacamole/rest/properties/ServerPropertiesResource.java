/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest.properties;

import javax.ws.rs.GET;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.rest.BaseResource;

/**
 * A REST Service for getting server properties.
 * 
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
public class ServerPropertiesResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(ServerPropertiesResource.class);

    /**
     * Creates a new UserContextResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public ServerPropertiesResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Returns value of the specified property.
     * 
     * @param propName - name of the property
     * 
     * @return value - value of the property.
     * 
     * @throws GuacamoleException 
     */
    @GET
    @Produces("application/json")
    public Response notifyUser(@QueryParam("name") final String propName) throws GuacamoleException {
        String propValue = "";
        StringGuacamoleProperty prop = (StringGuacamoleProperty) ApportoProperties.getProp(propName);

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get propValue from guacamole.properties
            propValue = environment.getProperty(prop);

        }
        catch (Exception e) {
            logger.error(e.getStackTrace().toString());
            logger.error(e.getMessage());
        }

        JSONObject jsonResult = new JSONObject();
        jsonResult.put(propName, propValue);

        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }
}
