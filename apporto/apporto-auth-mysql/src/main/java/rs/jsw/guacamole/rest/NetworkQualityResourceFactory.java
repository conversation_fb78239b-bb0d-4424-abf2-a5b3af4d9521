package rs.jsw.guacamole.rest;

import org.apache.guacamole.net.auth.UserContext;

public interface NetworkQualityResourceFactory {

	/**
     * Creates a new NetworkQualityResource for network quality check.
     *
     * @param userContext
     *     The UserContext whose contents should be exposed.
     *
     * @return
     *     A new NetworkQualityResource.
     */
	NetworkQualityResource create(UserContext userContext);

}
