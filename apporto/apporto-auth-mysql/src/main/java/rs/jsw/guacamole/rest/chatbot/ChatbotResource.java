package rs.jsw.guacamole.rest.chatbot;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;
import lombok.extern.slf4j.Slf4j;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.UserContext;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

import javax.inject.Inject;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Produces(MediaType.APPLICATION_JSON)
public class ChatbotResource extends BaseResource {

    @Inject
    ChatbotService service;

    /**
     * Creates a new ChatbotResource.
     *
     * @param userContext
     *     The UserContext whose contents should be exposed.
     */
    @AssistedInject
    public ChatbotResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Returns URL of the chatbot.
     *
     * @param id connection id
     * @return URL of the chatbot as JSON string in format {"url": "url"}
     */
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @Path("/url")
    public Response getUrl(@QueryParam("id") Integer id) {
        logger.debug("getChatbotUrl enter for conn id {}", id);

        String username = getParameter(id, EncurlParameters.USERNAME_PARM);

        ChatbotUrlData urlData = null;
        try {
            urlData = service.getUrl();
        } catch (GuacamoleException e) {
            logger.error("[{}:{}:{}] Cannot read chatbot configuration from guacamole.properties. Check if chatbot server or region are present.", id, username, "", e);
        } catch (Exception e) {
            logger.error("[{}:{}:{}] Cannot parse chatbot url, check if the chatbot server is working.", id, username, "", e);
        }

        return createResponse(urlData);
    }

    private Response createResponse(ChatbotUrlData urlData) {
        Map<String, String> responseMap = new HashMap<>();
        if (urlData != null && urlData.getUrl() != null) {
            responseMap.put("url", urlData.getUrl());
        } else {
            responseMap.put("url", null);
        }
        return Response.ok(responseMap).build();

    }
}
