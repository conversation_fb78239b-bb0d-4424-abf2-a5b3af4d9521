/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.jdbc.connection.ConnectionParameterModel;
import org.apache.guacamole.auth.jdbc.sharing.SharedConnectionMap;
import org.apache.guacamole.auth.jdbc.sharing.connection.SharedConnectionDefinition;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.auth.Connection;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.ApportoError;
import com.apporto.environment.ConfigurationChecker;
import com.apporto.environment.SendStatusData;
import com.apporto.hyperstream.core.ApportoProperties;
import com.google.inject.Inject;
import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import rs.jsw.guacamole.Licences;
import rs.jsw.guacamole.Watermark;
import rs.jsw.guacamole.auth.jdbc.connection.EncUrlConnectionParameterMapper;
import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.auth.jdbc.user.UserSessionDesktop;
import rs.jsw.guacamole.net.encryptedurl.Security;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.chatbot.ChatbotResourceFactory;
import rs.jsw.guacamole.rest.classroom.ClassroomResource;
import rs.jsw.guacamole.rest.classroom.ClassroomResourceFactory;
import rs.jsw.guacamole.rest.filebrowser.FileBrowserResourceFactory;
import rs.jsw.guacamole.rest.mmonitornotify.MMonitorNotificationResource;
import rs.jsw.guacamole.rest.mmonitornotify.MMonitorNotificationResourceFactory;
import rs.jsw.guacamole.rest.multimonitor.DisplayResourceFactory;
import rs.jsw.guacamole.rest.notification.NotificationResource;
import rs.jsw.guacamole.rest.notification.NotificationResourceFactory;
import rs.jsw.guacamole.rest.presenter.PresenterResource;
import rs.jsw.guacamole.rest.presenter.PresenterResourceFactory;
import rs.jsw.guacamole.rest.properties.ServerPropertiesResource;
import rs.jsw.guacamole.rest.properties.ServerPropertiesResourceFactory;
import rs.jsw.guacamole.rest.thumbnails.ThumbnailResourceFactory;
import rs.jsw.guacamole.rest.vm.VmBackupResource;
import rs.jsw.guacamole.rest.vm.VmBackupResourceFactory;
import rs.jsw.guacamole.rest.vm.VmRebootResource;
import rs.jsw.guacamole.rest.vm.VmRebootResourceFactory;
import rs.jsw.guacamole.rest.vm.VmSessionResource;
import rs.jsw.guacamole.rest.vm.VmSessionResourceFactory;

/**
 * A root REST Service for all other Apporto REST resources.
 *
 * <AUTHOR> Nikolić
 */
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ApportoResource extends BaseResource {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ApportoResource.class);

    /**
     * Names of non-Windows platforms
     */
    private static final String APPOS_MACOS = "Mac";
    private static final String APPOS_LINUX = "Linux";

    /**
     * The names of the default issuers for the LTI feature
     */
    private static final String[] DEFAULT_ISSUERS = {"canvas", "d2l", "blackboard"};

    /**
     * The default value of the USB port
     */
    private static final String DEFAULT_USB_PORT  = "4001";

    /**
     * One day expire time for the collaborate offline link
     */
    private static final Number ONEDAY_EXPIRE_TIME = 3600 * 24; // 86,400 seconds

    /**
     * One week expire time for the collaborate offline link
     */
    private static final Number ONEWEEK_EXPIRE_TIME = 3600 * 24 * 7; // 604,800 seconds

    /**
     * Others
     */
    private static boolean allow_sticky_clearing_api = true;

    /**
     * The default delay value for executing an event of the RDS session logout (in seconds)
     */
    private static int DEFAULT_RDS_LOGOUT_EVENT_DELAY = 10;

    /**
     * The delay for executing an event of the RDS session logout (in seconds)
     */
    private static int rds_logout_event_delay = DEFAULT_RDS_LOGOUT_EVENT_DELAY;

    /**
     * The default value of gRPC server
     */
    private static final String DEFAULT_GRPC_SERVER  = "";

    /**
     * The value of gRPC server
     */
    private static String grpc_server = DEFAULT_GRPC_SERVER;

    /**
     * Factory for creating instances of resources for managing snapshots.
     */
    @Inject
    private SnapshotsResourceFactory snapshotsResourceFactory;

    /**
     * Factory for creating instances of resources for handling intro functionality.
     */
    @Inject
    private LaterResourceFactory laterResourceFactory;

    /**
     * Factory for creating instances of resources for exchanging messages
     * between connected clients.
     */
    @Inject
    private MessagingResourceFactory messagingResourceFactory;

    /**
     * Factory for creating instances of resources for resolution adjusting.
     * */
    @Inject
    private FileDetailsResourceFactory fileDetailsResourceFactory;

    /**
     * Factory for creating instances of resources for network quality check.
     * */
    @Inject
    private NetworkQualityResourceFactory networkQualityResourceFactory;

    /**
     * Factory for creating instances of resources for handling notifications to the remote client.
     */
    @Inject
    private NotificationResourceFactory notificationResourceFactory;

    /**
     * Factory for creating instances of resources for checking the user action between monitors.
     */
    @Inject
    private MMonitorNotificationResourceFactory mmonitorNotificationResourceFactory;

    /**
     * Factory for creating instances of resources for getting user info for messenger security.
     */
    @Inject
    private MessengerAuthResourceFactory messengerResourceFactory;

    /**
     * Factory for creating instances of resources for getting display properties.
     */
    @Inject
    private DisplayResourceFactory displayResourceFactory;

    /**
     * Factory for creating instances of resources for managing file browser.
     */
    @Inject
    private FileBrowserResourceFactory fileBrowserResourceFactory;

    /**
     * Factory for creating instances of resources for managing chatbot
     */
    @Inject
    private ChatbotResourceFactory chatbotResourceFactory;
    /**
     * Factory for creating instances of resources for getting server properties
     */
    @Inject
    private ServerPropertiesResourceFactory serverPropertiesResourceFactory;

    /**
     * Factory for creating instances of resources for getting server properties
     */
    @Inject
    private VmRebootResourceFactory vmRebootResourceFactory;

    /**
     * Factory for creating instances of resources for vm backup
     */
    @Inject
    private VmBackupResourceFactory vmBackupResourceFactory;

    /**
     * Factory for creating instances of resources for classroom
     */
    @Inject
    private ClassroomResourceFactory classroomResourceFactory;

    /**
     * Factory for creating instances of resources for presenter
     */
    @Inject
    private PresenterResourceFactory presenterResourceFactory;

    /**
     * Factory for creating instances of resources for clearing the sticky session
     */
    @Inject
    private VmSessionResourceFactory vmSessionResourceFactory;

    /* * Factory for creating instances of resources for getting thumbnails
     */
    @Inject
    private ThumbnailResourceFactory thumbnailResourceFactory;

    /**
     * Factory for creating instances of resources for querying the server-capacity
     */
    @Inject
    private LoadBalancerResourceFactory loadBalancerResourceFactory;

    /**
     * Factory for creating instances of resources for querying the server-capacity
     */
    @Inject
    private RDPRouterResourceFactory rdpRouterResourceFactory;

    /**
     * Mapper for manipulating connection parameters.
     */
    @Inject
    private EncUrlConnectionParameterMapper encUrlConnectionParameterMapper;

    /**
     * Map of all currently-shared connections.
     */
    @Inject
    private SharedConnectionMap connectionMap;

    /**
     * Creates a new UserContextResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public ApportoResource(@Assisted UserContext userContext) {
        super(userContext);
    }

    /**
     * Load the proper configuration variable(s)
     */
    static {
        try {
            Environment environment = new LocalEnvironment();

            // if exists, get "allow-sticky-clearing-api" property from guacamole.properties
            if (environment.getProperty(ApportoProperties.ALLOW_STICKY_CLEARING_API) != null) {
                allow_sticky_clearing_api = environment.getProperty(ApportoProperties.ALLOW_STICKY_CLEARING_API);
            }
            else {
                logger.warn("`allow-sticky-clearing-api` parameter from configuration doesn't exist. " +
                            "So, the default value 'true' will be used.");
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `allow-sticky-clearing-api` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get "rds-logout-event-delay" property from guacamole.properties
            if (environment.getProperty(ApportoProperties.RDS_LOGOUT_EVENT_DELAY) != null) {
                rds_logout_event_delay = environment.getProperty(ApportoProperties.RDS_LOGOUT_EVENT_DELAY);
            }
            else {
                logger.warn("`rds-logout-event-delay` parameter from configuration doesn't exist or is empty. " +
                            "So, the default value {} seconds will be used.", DEFAULT_RDS_LOGOUT_EVENT_DELAY);
            }

            logger.info("The value of `rds-logout-event-delay` from configuration is {} seconds.",
                        rds_logout_event_delay);
        }
        catch (Exception e) {
            logger.warn("Cannot get `rds-logout-event-delay` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties." +
                        "The default delay {} seconds will be used.", DEFAULT_RDS_LOGOUT_EVENT_DELAY);
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {
            Environment environment = new LocalEnvironment();

            // if exists, get "grpc-server" property from guacamole.properties
            if (environment.getProperty(ApportoProperties.GRPC_SERVER) != null) {
                grpc_server = environment.getProperty(ApportoProperties.GRPC_SERVER);
            }
            else {
                logger.warn("`grpc-server` parameter from configuration doesn't exist or is empty. " +
                            "So, the default value {} seconds will be used.", DEFAULT_GRPC_SERVER);
            }

            logger.info("The value of `grpc-server` from configuration is {} seconds.",
                        grpc_server);
        }
        catch (Exception e) {
            logger.warn("Cannot get `grpc-server` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties." +
                        "The default delay {} seconds will be used.", DEFAULT_GRPC_SERVER);
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    /**
     * Returns Snapshots REST resource.
     *
     * @return SnapshotsResource
     */
    @Path("/snapshots")
    public BaseResource getSnapshotsResource() {
        return snapshotsResourceFactory.create(userContext);
    }

    /**
     * Returns Later REST resource.
     *
     * @return LaterResource
     */
    @Path("/later")
    public LaterResource getLaterResource() {
        return laterResourceFactory.create(userContext);
    }

    /**
     * Returns Messaging REST resource.
     *
     * @return MessagingResource
     */
    @Path("/messaging")
    public MessagingResource getMessagingResource() {
        return messagingResourceFactory.create(userContext);
    }

    /**
     * Returns FileDetailsResource REST resource.
     *
     * @return FileDetailsResource
     */
    @Path("/details")
    public FileDetailsResource getFileDetailsResource() {
        return fileDetailsResourceFactory.create(userContext);
    }

    /**
     * Returns NetworkQualityResource REST resource.
     *
     * @return NetworkQualityResource
     */
    @Path("/quality")
    public NetworkQualityResource getNetworkQualityResource() {
        return networkQualityResourceFactory.create(userContext);
    }

    /**
     * Returns Notification REST resource
     *
     * @return NotificationResource
     */
    @Path("/notify")
    public NotificationResource getNotificationResource() {
        return notificationResourceFactory.create(userContext);
    }

    /**
     * Returns MMonitor Notification REST resource
     *
     * @return MMonitorNotificationResource
     */
    @Path("/mmonitornotify")
    public MMonitorNotificationResource getMMonitorNotificationResource() {
        return mmonitorNotificationResourceFactory.create(userContext);
    }

    /**
     * Returns ServerProperties REST resource
     *
     * @return ServerProperties
     */
    @Path("/property")
    public ServerPropertiesResource getServerPropertiesResource() {
        return serverPropertiesResourceFactory.create(userContext);
    }

    /**
     * Returns VmReboot REST resource
     *
     * @return VmRebootResource
     */
    @Path("/reboot")
    public VmRebootResource getVmRebootResource() {
        return vmRebootResourceFactory.create(userContext);
    }

    /**
     * Returns VmBackup REST resource
     *
     * @return VmBackupResource
     */
    @Path("/vm")
    public VmBackupResource getVmBackupResource() {
        return vmBackupResourceFactory.create(userContext);
    }

    /**
     * Returns Classroom REST resource
     *
     * @return ClassroomResource
     */
    @Path("/classroom")
    public ClassroomResource getClassroomResource() {
        return classroomResourceFactory.create(userContext);
    }

    /**
     * Returns Presenter REST resource
     *
     * @return PresenterResource
     */
    @Path("/presenter")
    public PresenterResource getPresenterResource() {
        return presenterResourceFactory.create(userContext);
    }

    /**
     * Returns Clearing Sticky Sessions REST resource
     *
     * @return VmSessionResource
     */
    @Path("/vm-session")
    public BaseResource getVmSessionResource() {
        return vmSessionResourceFactory.create(userContext);
    }

    /**
     * Returns Thumbnail REST resource
     *
     * @return ThumbnailResource
     */
    @Path("/thumbnail")
    public BaseResource getThumbnailResource() {
        return thumbnailResourceFactory.create(userContext);
    }

    /**
     * Returns the load balancer REST resource
     *
     * @return LoadBalancerResource
     */
    @Path("/loadbalancer")
    public BaseResource getLoadBalancerResource() {
        return loadBalancerResourceFactory.create(userContext);
    }

    /**
     * Returns the load RDP Router REST resource
     *
     * @return RDPRouterResource
     */
    @Path("/rdp-router")
    public BaseResource getRDPRouterResource() {
        return rdpRouterResourceFactory.create(userContext);
    }

    /**
     * Returns available licences
     *
     * @return Licences
     */
    @Path("/licences")
    @GET
    @Produces("application/json")
    public Response getLicences(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] getLicences.", conn_id, connection_type, cloud_user);

        // Get the licenses info
        Licences lic = new Licences();

        String ltiCourseName = getParameter(id, EncurlParameters.LTI_COURSE_NAME_PARM);

        lic.setHasUpload("true".equalsIgnoreCase(getParameter(id, EncurlParameters.UPLOAD_LICENCE_PARM)));;
        lic.setHasDownload("true".equalsIgnoreCase(getParameter(id, EncurlParameters.DOWNLOAD_LICENCE_PARM)));
        lic.setHasSharing("true".equalsIgnoreCase(getParameter(id, EncurlParameters.SHARING_LICENCE_PARM)));
        lic.setHasAsyncCollaboration("true".equalsIgnoreCase(getParameter(id, EncurlParameters.COLLABORATION_LICENCE_PARM)));
        lic.setHasMessenger("true".equalsIgnoreCase(getParameter(id, EncurlParameters.MESSENGER_LICENCE_PARM)));
        lic.setHasSnapshots("true".equalsIgnoreCase(getParameter(id, EncurlParameters.SNAPSHOTS_LICENCE_PARM)));
        lic.setHasAnalytics("true".equalsIgnoreCase(getParameter(id, EncurlParameters.ANALYTICS_LICENCE_PARM)));
        lic.setHasHighlight("true".equalsIgnoreCase(getParameter(id, EncurlParameters.HIGHLIGHT_LICENCE_PARM)));
        lic.setHasMounter("true".equalsIgnoreCase(getParameter(id, EncurlParameters.MOUNTER_LICENCE_PARM)));
        lic.setHasClassroom("true".equalsIgnoreCase(getParameter(id, EncurlParameters.CLASSROOM_LICENCE_PARAM)));
        lic.setHasPresenter("true".equalsIgnoreCase(getParameter(id, EncurlParameters.PRESENTER_LICENCE_PARAM)));
        lic.setHasActivityTracker("true".equalsIgnoreCase(getParameter(id, EncurlParameters.ACTIVITY_TRACK_LICENCE_PARM)));
        lic.setHasClipboard(getParameter(id, EncurlParameters.CLIPBOARD_LICENCE_PARM) != null && !getParameter(id, EncurlParameters.CLIPBOARD_LICENCE_PARM).isEmpty() ? "true".equalsIgnoreCase(getParameter(id, EncurlParameters.CLIPBOARD_LICENCE_PARM)) : true);
        lic.setHasClipboardIn(getParameter(id, EncurlParameters.CLIPBOARDIN_LICENCE_PARM) != null && !getParameter(id, EncurlParameters.CLIPBOARDIN_LICENCE_PARM).isEmpty() ? "true".equalsIgnoreCase(getParameter(id, EncurlParameters.CLIPBOARDIN_LICENCE_PARM)) : true);
        lic.setHasClipboardOut(getParameter(id, EncurlParameters.CLIPBOARDOUT_LICENCE_PARM) != null && !getParameter(id, EncurlParameters.CLIPBOARDOUT_LICENCE_PARM).isEmpty() ? "true".equalsIgnoreCase(getParameter(id, EncurlParameters.CLIPBOARDOUT_LICENCE_PARM)) : true);
        lic.setHasReboot("true".equalsIgnoreCase(getParameter(id, EncurlParameters.REBOOT_LICENCE_PARAM)));
        lic.setHasMMonitor("true".equalsIgnoreCase(getParameter(id, EncurlParameters.MMONITOR_LICENCE_PARM)));
        lic.setHasBackup("true".equalsIgnoreCase(getParameter(id, EncurlParameters.BACKUP_LICENCE_PARAM)));
        lic.setHasMacOS(APPOS_MACOS.equalsIgnoreCase(getParameter(id, EncurlParameters.APPOS_LICENCE_PARAM)));
        lic.setHasLinux(APPOS_LINUX.equalsIgnoreCase(getParameter(id, EncurlParameters.APPOS_LICENCE_PARAM)));
        lic.setHasH264("true".equalsIgnoreCase(getParameter(id, EncurlParameters.H264_LICENCE_PARAM)));
        lic.setHasFileBrowser("true".equalsIgnoreCase(getParameter(id, EncurlParameters.FILEBROWSER_LICENCE_PARAM)));
        lic.setSupportH264("true".equalsIgnoreCase(getParameter(id, EncurlParameters.H264_SUPPORTED_PARAM)));
        lic.setHasCamera("true".equalsIgnoreCase(getParameter(id, EncurlParameters.CAMERA_LICENCE_PARAM)));
        lic.setHasStatistics("true".equalsIgnoreCase(getParameter(id, EncurlParameters.STATISTICS_LICENCE_PARM)));
        lic.setHasSftp("true".equalsIgnoreCase(getParameter(id, EncurlParameters.ENABLE_SFTP_PARM)));
        lic.setHasSftpCheckNeeded("true".equalsIgnoreCase(getParameter(id, EncurlParameters.SFTP_CHECK_NEEDED)));
        lic.setHasUSB("true".equalsIgnoreCase(getParameter(id, EncurlParameters.USB_LICENCE_PARAM)));
        lic.setIndustryType(getParameter(id, EncurlParameters.INDUSTRY_TYPE_KEY) != null && !getParameter(id, EncurlParameters.INDUSTRY_TYPE_KEY).isEmpty() ? getParameter(id, EncurlParameters.INDUSTRY_TYPE_KEY) : "");
        lic.setMonitorCount(getParameter(id, EncurlParameters.MONITOR_COUNT_PARAM) != null && !getParameter(id, EncurlParameters.MONITOR_COUNT_PARAM).isEmpty() ? Integer.parseInt(getParameter(id, EncurlParameters.MONITOR_COUNT_PARAM)) : 1);
        lic.setAppName(getParameter(id, EncurlParameters.APP_NAME_KEY) != null && !getParameter(id, EncurlParameters.APP_NAME_KEY).isEmpty() ? getParameter(id, EncurlParameters.APP_NAME_KEY) : "");
        lic.setSubdomain(getParameter(id, EncurlParameters.SUBDOMAIN_PARM) != null && !getParameter(id, EncurlParameters.SUBDOMAIN_PARM).isEmpty() ? getParameter(id, EncurlParameters.SUBDOMAIN_PARM) : "");
        lic.setIdleTimeout(getParameter(id, EncurlParameters.IDLE_TIMEOUT_KEY) != null && !getParameter(id, EncurlParameters.IDLE_TIMEOUT_KEY).isEmpty() ? getParameter(id, EncurlParameters.IDLE_TIMEOUT_KEY) : "");
        lic.setUsbPort(getParameter(id, EncurlParameters.USB_PORT_PARAM) != null && !getParameter(id, EncurlParameters.USB_PORT_PARAM).isEmpty() ? getParameter(id, EncurlParameters.USB_PORT_PARAM) : DEFAULT_USB_PORT);
        lic.setUsername(getParameter(id, EncurlParameters.USERNAME_PARM) != null && !getParameter(id, EncurlParameters.USERNAME_PARM).isEmpty() ? getParameter(id, EncurlParameters.USERNAME_PARM) : "");
        lic.setHasLTI(checkLTIParams(id));
        lic.setLtiCourseName(ltiCourseName != null && !ltiCourseName.isEmpty() ? ltiCourseName : "");
        lic.setHasWatermark(checkWatermarkParams(id));
        lic.setHasPreventCapsLock("true".equalsIgnoreCase(getParameter(id, EncurlParameters.PREVENT_CAPS_LOCK_PARM)));
        lic.setCloudProvider(getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM) != null && !getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM).isEmpty() ? getParameter(id, EncurlParameters.CLOUD_PROVIDER_PARM) : "");
        lic.setLtiIssuer(getParameter(id, EncurlParameters.LTI_ISSUER_PARM) != null && !getParameter(id, EncurlParameters.LTI_ISSUER_PARM).isEmpty() ? getParameter(id, EncurlParameters.LTI_ISSUER_PARM) : "");
        lic.setSupportMenuName(getParameter(id, EncurlParameters.SUPPORT_MENU_NAME_PARM) != null && !getParameter(id, EncurlParameters.SUPPORT_MENU_NAME_PARM).isEmpty() ? getParameter(id, EncurlParameters.SUPPORT_MENU_NAME_PARM) : "");
        lic.setSupportLink(getParameter(id, EncurlParameters.SUPPORT_LINK_PARM) != null && !getParameter(id, EncurlParameters.SUPPORT_LINK_PARM).isEmpty() ? getParameter(id, EncurlParameters.SUPPORT_LINK_PARM) : "");
        lic.setSupportEmail(getParameter(id, EncurlParameters.SUPPORT_EMAIL_PARM) != null && !getParameter(id, EncurlParameters.SUPPORT_EMAIL_PARM).isEmpty() ? getParameter(id, EncurlParameters.SUPPORT_EMAIL_PARM) : "");
        lic.setUserGuideName(getParameter(id, EncurlParameters.WEBSITE_MENU_NAME_PARM) != null && !getParameter(id, EncurlParameters.WEBSITE_MENU_NAME_PARM).isEmpty() ? getParameter(id, EncurlParameters.WEBSITE_MENU_NAME_PARM) : "");
        lic.setUserGuideLink(getParameter(id, EncurlParameters.WEBSITE_MENU_LINK_PARM) != null && !getParameter(id, EncurlParameters.WEBSITE_MENU_LINK_PARM).isEmpty() ? getParameter(id, EncurlParameters.WEBSITE_MENU_LINK_PARM) : "");
        lic.setExtendedCompute("true".equalsIgnoreCase(getParameter(id, EncurlParameters.EXTENDED_COMPUTE_PARAM)));
        lic.setRemainingTime(getParameter(id, EncurlParameters.REMAINING_TIME_PARAM) != null && !getParameter(id, EncurlParameters.REMAINING_TIME_PARAM).isEmpty() ? Integer.parseInt(getParameter(id, EncurlParameters.REMAINING_TIME_PARAM)) : 0);
        lic.setHostName(getParameter(id, EncurlParameters.HOSTNAME_PARM));
        lic.setRDPPort(getParameter(id, EncurlParameters.PORT_PARM) != null && !getParameter(id, EncurlParameters.PORT_PARM).isEmpty() ? getParameter(id, EncurlParameters.PORT_PARM) : "3389");
        lic.setPayloadType(getParameter(id, EncurlParameters.PAYLOAD_TYPE_PARAM) != null && !getParameter(id, EncurlParameters.PAYLOAD_TYPE_PARAM).isEmpty() ? getParameter(id, EncurlParameters.PAYLOAD_TYPE_PARAM) : "traditional");
        lic.setRDPFarmName(getParameter(id, EncurlParameters.RDP_FARM_NAME_PARAM) != null && !getParameter(id, EncurlParameters.RDP_FARM_NAME_PARAM).isEmpty() ? getParameter(id, EncurlParameters.RDP_FARM_NAME_PARAM) : "");
        lic.setLaunchFullScreen("true".equalsIgnoreCase(getParameter(id, EncurlParameters.LAUNCH_FULL_SCREEN_PARAM)));
        lic.setUsbDevices(getParameter(id, EncurlParameters.USB_DEVICES) != null && !getParameter(id, EncurlParameters.USB_DEVICES).isEmpty() ? getParameter(id, EncurlParameters.USB_DEVICES) : "");
        lic.setCloudUsername(getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM) != null && !getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM).isEmpty() ? getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM) : "");
        lic.setUserId(getParameter(id, EncurlParameters.USER_ID) != null && !getParameter(id, EncurlParameters.USER_ID).isEmpty() ? getParameter(id, EncurlParameters.USER_ID) : "");
        lic.setRemoteApps(getParameter(id, EncurlParameters.REMOTE_APPS) != null && !getParameter(id, EncurlParameters.REMOTE_APPS).isEmpty() ? getParameter(id, EncurlParameters.REMOTE_APPS) : "");
        lic.setRemoteAppMode(getParameter(id, EncurlParameters.REMOTE_APP_MODE) != null && !getParameter(id, EncurlParameters.REMOTE_APP_MODE).isEmpty() ? getParameter(id, EncurlParameters.REMOTE_APP_MODE) : "");
        lic.setPayloadVersion(getParameter(id, EncurlParameters.PAYLOAD_VERSION) != null && !getParameter(id, EncurlParameters.PAYLOAD_VERSION).isEmpty() ? getParameter(id, EncurlParameters.PAYLOAD_VERSION) : "");
        lic.setHasLogoff("true".equalsIgnoreCase(getParameter(id, EncurlParameters.LOGOFF_LICENCE_PARAM)));

        // If enable-h264 payload is true and there is no h264-supported payload, it indicats that the GPU is supported.
        if ("true".equalsIgnoreCase(getParameter(id, EncurlParameters.H264_LICENCE_PARAM)) &&
            getParameter(id, EncurlParameters.H264_SUPPORTED_PARAM) == null) {
            lic.setSupportH264(true);
        }

        // Set latency check values from properties file
        int latency_threashold = 0;
        int latency_check_interval = 0;
        int latency_check_count = 0;

        // Set the max resolution value from properties file
        int h264_max_resolution_property = 0;

        try {

            Environment environment = new LocalEnvironment();

            // Get "latency" properties from guacamole.properties
            if (environment.getProperty(ApportoProperties.LATENCY_THRESHOLD) != null) {
                latency_threashold = environment.getProperty(ApportoProperties.LATENCY_THRESHOLD);
            }

            if (environment.getProperty(ApportoProperties.LATENCY_CHECK_INTERVAL) != null) {
                latency_check_interval = environment.getProperty(ApportoProperties.LATENCY_CHECK_INTERVAL);
            }

            if (environment.getProperty(ApportoProperties.LATENCY_CHECK_COUNT) != null) {
                latency_check_count = environment.getProperty(ApportoProperties.LATENCY_CHECK_COUNT);
            }

            // Get "h264-max-resolution" property from guacamole.properties
            if (environment.getProperty(ApportoProperties.H264_MAX_RESOLUTION_PROPERTY) != null) {
                h264_max_resolution_property = environment.getProperty(ApportoProperties.H264_MAX_RESOLUTION_PROPERTY);
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `latency` parameters from configuration, " +
                        "please check /etc/guacamole/guacamole.properties. Use default values");
        }

        // Set default max resolution for RDP H264 in pixels.
        h264_max_resolution_property = h264_max_resolution_property != 0 ? h264_max_resolution_property : 3840 * 2160;

        lic.setLatencyThreshold(latency_threashold != 0 ? latency_threashold : 120); // ms
        lic.setLatencyCheckInterval(latency_check_interval != 0 ? latency_check_interval : 2); // second
        lic.setLatencyCheckCount(latency_check_count != 0 ? latency_check_count : 5);
        lic.setH264MaxResolution(getParameter(id, EncurlParameters.H264_MAX_RESOLUTION) != null && !getParameter(id, EncurlParameters.H264_MAX_RESOLUTION).isEmpty() ? Integer.parseInt(getParameter(id, EncurlParameters.H264_MAX_RESOLUTION)) : h264_max_resolution_property);

        // Set the rollbar access token value from properties file
        String rollbar_access_token = "";

        try {

            Environment environment = new LocalEnvironment();

            // Get "rollbar" properties from guacamole.properties
            if (environment.getProperty(ApportoProperties.ROLLBAR_ACCESS_TOKEN) != null) {
                rollbar_access_token = environment.getProperty(ApportoProperties.ROLLBAR_ACCESS_TOKEN);
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `rollbar-access-token` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties. Use default values");
        }
        
        lic.setRollbarAccessToken(rollbar_access_token != null ? rollbar_access_token : "");

        return Response.ok(lic)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Returns shared information
     *
     * @return shared information
     */
    @Path("/sharedInformation")
    @GET
    @Produces("application/json")
    public Response getSharedInformation(@QueryParam("key") String key)
        throws GuacamoleException {

        Connection connection =  userContext.getConnectionDirectory().get(key);
        GuacamoleConfiguration configuration = connection.getConfiguration();

        Map<String, String> parameters = configuration.getParameters();

        boolean isEnableH264 = "true".equalsIgnoreCase(parameters.get(EncurlParameters.H264_LICENCE_PARAM));
        boolean isMultiMonitor = "true".equalsIgnoreCase(parameters.get(EncurlParameters.MMONITOR_LICENCE_PARM));
        boolean supportH264 = "true".equalsIgnoreCase(parameters.get(EncurlParameters.H264_SUPPORTED_PARAM));

        // If enable-h264 payload is true and there is no h264-supported payload, it indicats that the GPU is supported.
        if (isEnableH264 && parameters.get(EncurlParameters.H264_SUPPORTED_PARAM) == null) {
            supportH264 = true;
        }

        JSONObject jsonResult = new JSONObject();
        jsonResult.put("isEnableH264", isEnableH264);
        jsonResult.put("isMultiMonitor", isMultiMonitor);
        jsonResult.put("supportH264", supportH264);

        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Returns the server name
     *
     * @return servername
     */
    @Path("/servername")
    @GET
    @Produces("application/json")
    public Response getServerName(@QueryParam("id") Integer id) {
        String servername = getParameter(id, EncurlParameters.WINDOWS_SERVER_NAME) != null &&
                           !getParameter(id, EncurlParameters.WINDOWS_SERVER_NAME).isEmpty() ?
                            getParameter(id, EncurlParameters.WINDOWS_SERVER_NAME) : "";
        String hostname = getParameter(id, EncurlParameters.HOSTNAME_PARM) != null &&
                         !getParameter(id, EncurlParameters.HOSTNAME_PARM).isEmpty() ?
                          getParameter(id, EncurlParameters.HOSTNAME_PARM) : "";

        JSONObject jsonResult = new JSONObject();
        jsonResult.put("servername", servername);
        jsonResult.put("hostname", hostname);
        jsonResult.put("grpc_server", grpc_server);

        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Returns Authentication Messenger REST resource
     *
     * @return MessengerAuthResource
     */
    @Path("/messenger")
    public BaseResource getMessengerAuthResource() {
        return messengerResourceFactory.create(userContext);
    }

    /**
     * Returns Multimonitor REST resource
     *
     * @return DisplayResource
     */
    @Path("/multimonitor")
    public BaseResource getMultimonitorResource() {
        return displayResourceFactory.create(userContext);
    }

    /**
     * Returns FileBrowser REST resource
     *
     * @return FileBrowserResource
     */
    @Path("/filebrowser")
    public BaseResource getFileBrowserResource() {
        return fileBrowserResourceFactory.create(userContext);
    }

    /**
     * Return Chatbot REST resource
     *
     * @return ChatbotResource
     */
    @Path("/chatbot")
    public BaseResource getChatbotResource() {
        return chatbotResourceFactory.create(userContext);
    }

    /**
     * Returns subdomain for session id passed
     *
     * @param id
     * @return username
     */
    @Path("/subdomain")
    @GET
    @Produces("text/plain")
    public Response getSubdomain(@QueryParam("id") Integer id) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] getSubdomain.", conn_id, connection_type, cloud_user);

        String subdomain = getParameter(id, EncurlParameters.SUBDOMAIN_PARM);
        if( subdomain == null || subdomain.isEmpty()) {

            JSONObject error = new JSONObject();
            error.put("Message", "Invalid id. Subdomain not found.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.UNAUTHORIZED).entity(error.toString()).build();
        }

        return Response.ok(subdomain.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Returns watermark information
     *
     * @return watermark
     */
    @Path("/watermark")
    @GET
    @Produces("application/json")
    public Response getWatermark(@QueryParam("id") Integer id, @QueryParam("key") String key)
        throws GuacamoleException {

        GuacamoleConfiguration configuration;
        Connection connection;
        if (key != null) {
            connection = userContext.getConnectionDirectory().get(key);
            configuration = connection.getConfiguration();
        }
        else {
            configuration = getConfiguration(id);
        }

        Map<String, String> parameters = configuration.getParameters();
        Watermark watermark = new Watermark();

        watermark.setType(parameters.get(EncurlParameters.WATERMARK_TYPE_PARM));
        watermark.setText(parameters.get(EncurlParameters.WATERMARK_TEXT_PARM));
        watermark.setSize(parameters.get(EncurlParameters.WATERMARK_SIZE_PARM));
        watermark.setColor(parameters.get(EncurlParameters.WATERMARK_COLOR_PARM));
        watermark.setLayout(parameters.get(EncurlParameters.WATERMARK_LAYOUT_PARM));
        watermark.setSemiTransparent("true".equalsIgnoreCase(parameters.get(EncurlParameters.WATERMARK_SEMI_TRANSPARENT_PARM)));
        watermark.setPicture(parameters.get(EncurlParameters.WATERMARK_PICTURE_PARM));
        watermark.setScale(parameters.get(EncurlParameters.WATERMARK_SCALE_PARM));
        watermark.setWashout("true".equalsIgnoreCase(parameters.get(EncurlParameters.WATERMARK_WASHOUT_PARM)));

        return Response.ok(watermark)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Notification that client browser window is closed.
     *
     * @return Response - 200 ok
     *
     * @throws GuacamoleException
     */
    @Path("/onclose")
    @POST
    @Consumes("text/plain")
    public Response notifyOnClose(@QueryParam("id") String id)
            throws GuacamoleException {

        // Get the connection info
        Integer _id = Integer.valueOf(id);
        String conn_id = id;
        String cloud_user = getParameter(_id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(_id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] notifyOnClose.", conn_id, connection_type, cloud_user);

        // Delete monitors configuration
        if ("true".equalsIgnoreCase(getParameter(_id, EncurlParameters.MMONITOR_LICENCE_PARM))) {
            deleteMonitors(_id.intValue());
        }

        // Clear the sticky session
        GuacamoleConfiguration configuration = getConfiguration(_id);

        // Check the "enable-logoff" URL parameter is TRUE
        boolean logoffEnabled = "true".equalsIgnoreCase(configuration.getParameter(EncurlParameters.LOGOFF_LICENCE_PARAM));

        // Check the "payload_version" URL parameter
        String strPayloadVersion = configuration.getParameter(EncurlParameters.PAYLOAD_VERSION);
        boolean isNGAppstore = (strPayloadVersion != null && !strPayloadVersion.isEmpty());

        // If Appstore is NG and "enable-logoff" is FALSE, we don't create logout event on browser close
        if (isNGAppstore && !logoffEnabled) {
            logger.info("[{}:{}:{}] The logout event is ignored because the Appstore is NG and the '{}' parameter is FALSE.",
                        conn_id, connection_type, cloud_user, EncurlParameters.LOGOFF_LICENCE_PARAM);
            return Response.status(Status.OK).build();
        }

        // Check if the "extend_compute" URL parameter is TRUE
        // If so, we don't call Apporto Service on browser close
        String strExtendCompute = configuration.getParameter(EncurlParameters.EXTENDED_COMPUTE_PARAM);
        if ("true".equalsIgnoreCase(strExtendCompute)) {
            logger.info("[{}:{}:{}] The logout event is ignored because the '{}' parameter is TRUE.",
                        conn_id, connection_type, cloud_user, EncurlParameters.EXTENDED_COMPUTE_PARAM);
            return Response.status(Status.OK).build();
        }

        // Create logout event
        logger.info("[{}:{}:{}] Create a logout event.", conn_id, connection_type, cloud_user);
        createLogoutEvent(conn_id, connection_type, cloud_user, configuration);

        return Response.status(Status.OK).build();
    }

    /**
     * Create logout event to terminate the server after appointed time.
     *
     * @return
     *
     */
    private void createLogoutEvent(final String conn_id, final String connection_type, final String cloud_user,
                                   final GuacamoleConfiguration configuration) {

        // Don't create an event before being determined the server's hostname
        if (configuration.getParameter(EncurlParameters.HOSTNAME_PARM) == null || 
            configuration.getParameter(EncurlParameters.HOSTNAME_PARM).isEmpty()) {
            return;
        }

        final String logoutEventIdentifier = configuration.getComparableKey();
        ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

        Runnable logoutTask = new Runnable() {
            @Override
            public void run() {
                if(Thread.interrupted()) {
                    return;
                }

                if (configuration.getParameter(EncurlParameters.PAYLOAD_TYPE_PARAM) == null || 
                    configuration.getParameter(EncurlParameters.PAYLOAD_TYPE_PARAM).equals("traditional")) {
                    // Check if the RDS is terminated successfully
                    int terminateStatusCode = 0;

                    // Terminate the non-Windows platforms
                    String appOS = configuration.getParameters().get(EncurlParameters.APPOS_LICENCE_PARAM);
                    if (APPOS_MACOS.equalsIgnoreCase(appOS) || APPOS_LINUX.equalsIgnoreCase(appOS)) {
                        terminateStatusCode = VmSessionResource.terminate(configuration);
                    }
                    // Terminate the Windows platform
                    else {
                        terminateStatusCode = SendStatusData.sendCloseWindowStatus(configuration);
                        // Remove the existing winServerName and winSessionID
                        // because the session was successfully removed in RDS side (AP-6557)
                        if (terminateStatusCode == 0) {
                            ConnectionParameterModel parModel = new ConnectionParameterModel();
                            parModel.setConnectionIdentifier(conn_id);
                            parModel.setName(EncurlParameters.WINDOWS_SERVER_NAME);
                            parModel.setValue("");
                            encUrlConnectionParameterMapper.insertOrUpdate(parModel);

                            parModel.setName(EncurlParameters.WINDOWS_SESSION_ID);
                            parModel.setValue("");
                            encUrlConnectionParameterMapper.insertOrUpdate(parModel);

                            logger.info("[{}:{}:{}] The windows session was successfully closed in RDS side. Remove the windows session info in Hyperstream side",
                                conn_id, connection_type, cloud_user);
                        }
                    }

                    // If the RDS is terminated successfully, clear the sticky session
                    if (allow_sticky_clearing_api == true && terminateStatusCode == 0) {
                        VmSessionResource.clear(configuration);
                    }
                }
                else {
                    SendStatusData.sendCloseWindowToRouter(configuration);
                }

                // Remove logout event from the logout event map
                ((ApportoUserContext) userContext).removeLogoutEvent(logoutEventIdentifier);
                logger.info("[{}:{}:{}] A logout event is executed.", conn_id, connection_type, cloud_user);
            }
        };

        ScheduledFuture<?> future = executorService.schedule(logoutTask, rds_logout_event_delay, TimeUnit.SECONDS);
        executorService.shutdown();

        // Add logout event to event map
        ((ApportoUserContext) userContext).setLogoutEvent(logoutEventIdentifier, future);
        logger.info("[{}:{}:{}] A logout event is created.", conn_id, connection_type, cloud_user);

    }

    /**
     * Invalidate all shared sessions tunnels with passed value for SHARED_KEY.
     *
     * @param id
     * @param key
     * @return Response - 200 ok
     */
    @Path("/stopSharing")
    @POST
    @Consumes("text/plain")
    public Response stopSharing(@QueryParam("id") Integer id, @QueryParam("key") String key ) {

        // Get the connection info
        String conn_id = Integer.toString(id);
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] stopSharing:{}", conn_id, connection_type, cloud_user, key);

        SharedConnectionDefinition sharedConnectionDefinition = connectionMap.get(key);
        sharedConnectionDefinition.invalidate();
        connectionMap.remove(key);
        return Response.status(Status.OK)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Check whether both LTI USER_ID, LTI COURSE_ID and LTI ISSUER are valid.
     *
     */
    public Boolean checkLTIParams(Integer id) {
        String LTI_USER_ID   = getParameter(id, EncurlParameters.LTI_USER_ID_PARM);
        String LTI_COURSE_ID = getParameter(id, EncurlParameters.LTI_COURSE_ID_PARM);
        String LTI_ISSUER    = getParameter(id, EncurlParameters.LTI_ISSUER_PARM);
        List<String> issuerList = new ArrayList<>(Arrays.asList(DEFAULT_ISSUERS));

        if (LTI_USER_ID != null && !LTI_USER_ID.isEmpty() && LTI_COURSE_ID != null && !LTI_COURSE_ID.isEmpty()
                && LTI_ISSUER != null && !LTI_ISSUER.isEmpty() && issuerList.contains(LTI_ISSUER)) {
            return true;
        }
        else {
            return false;
        }
    }

    /**
     * Check whether Watermark TYPE is valid.
     *
     */
    public Boolean checkWatermarkParams(Integer id) {

        String WATERMARK_TYPE = getParameter(id, EncurlParameters.WATERMARK_TYPE_PARM);

        if (WATERMARK_TYPE != null && !WATERMARK_TYPE.equals("none") &&
           (WATERMARK_TYPE.equals("Text") || WATERMARK_TYPE.equals("Picture"))) {
            return true;
        }
        else {
            return false;
        }

    }

    /**
     * Delete monitors configuration
     *
     * @throws GuacamoleException
     */
    public void deleteMonitors(int id) throws GuacamoleException {
        if (userContext instanceof UserSessionDesktop) {
            UserSessionDesktop cntx = (UserSessionDesktop)userContext;
            cntx.getSessionDesktop().removeMonitors(id);
        }
    }

    /**
     * Set the "enable-audio-input-opus" param.
     *
     * @param id
     * @param enableOpus
     * @return Response - 200 ok
     */
    @Path("/enable-opus")
    @POST
    @Consumes("text/plain")
    public Response setEnableOpusParam(@QueryParam("id") String id, @QueryParam("enableOpus") Boolean enableOpus) {

        // If the current connection is a shared connection
        if (id.matches("[0-9]+") == false) {
            return Response.status(Status.BAD_REQUEST)
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        // Update the URL parameter related to the opus
        ConnectionParameterModel parModel = new ConnectionParameterModel();
        parModel.setConnectionIdentifier(id);
        parModel.setName(EncurlParameters.ENABLE_AUDIO_INPUT_OPUS_PARM);
        parModel.setValue(enableOpus.toString());
        encUrlConnectionParameterMapper.insertOrUpdate(parModel);

        return Response.status(Status.OK)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

    /**
     * Check if the SFTP service is available.
     * Returns the flag indicating to the check result.
     *
     * @return servername
     */
    @Path("/check-sftp-available")
    @GET
    @Produces("application/json")
    public Response checkSftpAvailable(@QueryParam("id") String id) throws GuacamoleException {

        // Get the connection info
        String conn_id = id;
        String cloud_user = getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM);

        Connection connection = userContext.getConnectionDirectory().get(id);
        GuacamoleConfiguration configuration = connection.getConfiguration();

        if (!id.matches("[0-9]+")) { // if a current connection is a shared connection
            conn_id = configuration.getParameters().get(EncurlParameters.ID_PARM);
            cloud_user = getParameter(conn_id, EncurlParameters.CLOUD_USERNAME_PARM);
        }

        logger.info("[{}:{}:{}] checking if the SFTP service is available.", conn_id, connection_type, cloud_user);

        // If the current connection is a shared connection
        if (id.matches("[0-9]+") == false) {
            JSONObject error = new JSONObject();
            error.put("message", "The shared connection couldn't check the SFTP available.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("message"));
            return Response.status(Status.BAD_REQUEST)
                    .entity(error.toString())
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .build();
        }

        // Check the value of the "sftp-check-needed" parameter
        String sftp_check_needed = getParameter(id, EncurlParameters.SFTP_CHECK_NEEDED);
        if (sftp_check_needed.equals("false")) {
            JSONObject error = new JSONObject();
            error.put("message", "The 'sftp-check-needed' parameter is false.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("message"));
            return Response.status(Status.BAD_REQUEST)
                    .entity(error.toString())
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .build();
        }

        // Get the proper SFTP-related URL parameters
        String sftp_hostname = getParameter(id, EncurlParameters.SFTP_HOSTNAME_PARM);
        String sftp_port = getParameter(id, EncurlParameters.SFTP_PORT_PARM);
        String sftp_username = getParameter(id, EncurlParameters.SFTP_USERNAME_PARM);
        String sftp_password = getParameter(id, EncurlParameters.SFTP_PASSWORD_PARM);

        // Result
        boolean result = true;
        String message = "The SFTP service is available now.";
        ApportoError error = ApportoError.OK;

        // Check the port
        if (!ConfigurationChecker.check_sftp_port(sftp_hostname, sftp_port, conn_id, connection_type, cloud_user)) {
            result = false;
            error = ApportoError.SFTP_NA;
            message = "The SFTP port is unavailable.";
        }

        // Check the SFTP directory structure
        else if (!ConfigurationChecker.check_sftp_dir(sftp_hostname, sftp_port, sftp_username, sftp_password, conn_id, connection_type, cloud_user)) {
            result = false;
            error = ApportoError.SFTP_NOLOGIN;
            message = "The SFTP directory structure is invalid.";
        }
        else {
            configuration.setParameter(EncurlParameters.ENABLE_SFTP_PARM, "true");
            logger.info("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, message);
        }

        configuration.setParameter(EncurlParameters.CONF_STATUS_PARM, String.valueOf(error.getId()));

        JSONObject jsonResult = new JSONObject();
        jsonResult.put("sftp_available", result);
        jsonResult.put("message", message);

        return Response.ok(jsonResult.toString())
                .header("Cache-Control", "no-cache, no-store, must-revalidate")
                .header("Pragma", "no-cache")
                .header("Expires", "0")
                .build();
    }

    /**
     * Create keys for the collaborate offline links
     *
     * @param id connection id
     * @return Response encrypted keys
     *
     * An encrypted key is encrypted value of App ID + User ID + Session ID + Link Expire Time + Link Create Time
     * Two encrypted keys are created by Link Expire Time (one day or one week)
     *
     */
    @Path("/collaborate-offline-link")
    @POST
    @Consumes("text/plain")
    public Response collaborateOfflineLink(@QueryParam("id") String id) throws GuacamoleException {

        Connection connection =  userContext.getConnectionDirectory().get(id);
        GuacamoleConfiguration configuration = connection.getConfiguration();
        Map<String, String> parameters = configuration.getParameters();

        // Get the connection info
        String conn_id = id;
        String cloud_user = parameters.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String connection_type = parameters.get(EncurlParameters.CONNECTION_TYPE_PARAM);

        logger.info("[{}:{}:{}] Creating the collaborate offline links.", conn_id, connection_type, cloud_user);

        // Get http-api-key
        String httpApiKey = parameters.get(EncurlParameters.HTTP_API_KEY);
        if (httpApiKey.isEmpty() || httpApiKey == null) {
            JSONObject error = new JSONObject();
            error.put("message", "The Http Api Key param is null or empty now.");
            logger.error("[{}:{}:{}] Collaborate offline link: {}", conn_id, connection_type, cloud_user, error.get("message"));
            return Response.status(Status.BAD_REQUEST)
                           .entity(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        // Get App ID
        String appId = parameters.get(EncurlParameters.NID);
        if (appId.isEmpty() || appId == null) {
            JSONObject error = new JSONObject();
            error.put("message", "The App ID param is null or empty now.");
            logger.error("[{}:{}:{}] Collaborate offline link: {}", conn_id, connection_type, cloud_user, error.get("message"));
            return Response.status(Status.BAD_REQUEST)
                           .entity(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        // Get User ID
        String userId = parameters.get(EncurlParameters.USER_ID);
        if (userId.isEmpty() || userId == null) {
            JSONObject error = new JSONObject();
            error.put("message", "The User ID param is null or empty now.");
            logger.error("[{}:{}:{}] Collaborate offline link: {}", conn_id, connection_type, cloud_user, error.get("message"));
            return Response.status(Status.BAD_REQUEST)
                           .entity(error.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        // Create a new Security instance
        Security security = new Security(httpApiKey);

        // Json Object to create one day collaborate key
        JSONObject jsonInputOneDay = new JSONObject();
        jsonInputOneDay.put("nid", appId);
        jsonInputOneDay.put("user_id", userId);
        jsonInputOneDay.put("session_id", conn_id);
        jsonInputOneDay.put("link_expire_time", ONEDAY_EXPIRE_TIME); // one day: 86,400 seconds
        jsonInputOneDay.put("link_create_time", new Timestamp(System.currentTimeMillis()).getTime()); // timestamp for current date and time

        // Json Object to create one week collaborate key
        JSONObject jsonInputOneWeek = new JSONObject();
        jsonInputOneWeek.put("nid", appId);
        jsonInputOneWeek.put("user_id", userId);
        jsonInputOneWeek.put("session_id", conn_id);
        jsonInputOneWeek.put("link_expire_time", ONEWEEK_EXPIRE_TIME); // one week: 604,800 seconds
        jsonInputOneWeek.put("link_create_time", new Timestamp(System.currentTimeMillis()).getTime()); // timestamp for current date and time

        // Encryped keys
        String encryptedKeyOneDay = security.encrypt(jsonInputOneDay.toString());
        String encryptedKeyOneWeek = security.encrypt(jsonInputOneWeek.toString());

        // Json Object for resoponse
        JSONObject jsonResult = new JSONObject();
        jsonResult.put("keyOneDay", encryptedKeyOneDay);
        jsonResult.put("keyOneWeek", encryptedKeyOneWeek);

        logger.info("[{}:{}:{}] Created the collaborate offline links.", conn_id, connection_type, cloud_user);

        return Response.ok(jsonResult.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();

    }

}
