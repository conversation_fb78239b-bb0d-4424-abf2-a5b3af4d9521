/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package rs.jsw.guacamole.rest.mmonitornotify;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.Connection;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.codehaus.jackson.map.ObjectMapper;

import com.google.inject.assistedinject.Assisted;
import com.google.inject.assistedinject.AssistedInject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;
import lombok.extern.slf4j.Slf4j;
import rs.jsw.guacamole.auth.jdbc.user.ApportoUserContext;
import rs.jsw.guacamole.net.encryptedurl.jdbc.EncurlParameters;
import rs.jsw.guacamole.rest.BaseResource;

/**
 * A REST Service for handling message exchange.
 * 
 * <AUTHOR> Nikolić
 */
@Slf4j
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class MMonitorNotificationResource extends BaseResource {

    /**
     * The map of threads queue created by each session id.
     * 
     * The outer map is a map where key is session id and values are threads with their queues.
     * The inner map is a map where key is thread id and value is a queue of commands for each thread.
     */
    private static final Map<Long, Map<Long, BlockingQueue<MMonitorNotificationCommand>>> sessionThreads = new ConcurrentHashMap<>();

    private static final Long MMONITOR_THREAD_TIMEOUT = 30 * 60 * 1000L; // 30 minutes, kill the thread if there is no action

    private static final String SSE_INIT_MESSAGE = "data: SSE Init\n\n";
    private static final String SSE_END_MESSAGE = "data: SSE End\n\n";

    /**
     * Class for representing user action data for a session.
     */
    @Data
    @Builder
    @AllArgsConstructor
    public static class ActionData {
        Long sessionId;

        @Singular
        List<MMonitorNotificationCommand> actionCommands;
    }

    /**
     * Creates a new UserContextResource which exposes the data within the
     * given UserContext.
     *
     * @param userContext
     *     The UserContext which should be exposed through this
     *     UserContextResource.
     */
    @AssistedInject
    public MMonitorNotificationResource(@Assisted UserContext userContext) throws GuacamoleException {
        super(userContext);
    }

    /**
     * Notify that session with given ID had some action with mouse or keyboard.
     * 
     * @param sessionId - id of a session
     */
    @Path("sendUserAction")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    public synchronized void sendUserAction(@QueryParam("sessionId") String id) throws GuacamoleException {
        Map<String, String> parameters = getConnectionParameters(id);

        if (parameters == null) {
            logger.info("[{}:{}:{}] sendUserAction: received invalid session Id={}, exiting with no changes", "", "", "", id);
            return;
        }

        String conn_id = parameters.get(EncurlParameters.ID_PARM);
        String cloud_user = parameters.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = parameters.get(EncurlParameters.CONNECTION_TYPE_PARAM);

        queueCommand(ActionData.builder()
                .sessionId(Long.parseLong(conn_id))
                .actionCommand(MMonitorNotificationCommand.ACTION)
                .build());

        logger.info("[{}:{}:{}] sendUserAction done", conn_id, conn_type, cloud_user);
    }

    /**
     * Put the action data into the command queue for all threads from its session.
     * 
     * @param actionData
     * @throws GuacamoleException
     */
    private void queueCommand(ActionData actionData) throws GuacamoleException {
        Map<String, String> parameters = getConnectionParameters(actionData.getSessionId().toString());

        if (parameters == null) {
            logger.info("[{}:{}:{}] queueCommand: received invalid session Id={}, exiting with no changes", "", "", "", actionData.getSessionId());
            return;
        }

        String conn_id = parameters.get(EncurlParameters.ID_PARM);
        String cloud_user = parameters.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = parameters.get(EncurlParameters.CONNECTION_TYPE_PARAM);

        Map<Long, BlockingQueue<MMonitorNotificationCommand>> myThreads = sessionThreads.get(actionData.getSessionId());

        // Copy the threads to avoid concurrent modification exception.
        // It is possible that values from the myThreads map are removed when "end" action is executed.
        // and even the myThreads map may be removed completely, which may lead to strange effects. Therefore, we need to copy the references.
        Collection<BlockingQueue<MMonitorNotificationCommand>> myLocalThreads;
        if (myThreads != null) {
            myLocalThreads = new ArrayList<>(myThreads.values());
        }
        else {
            logger.warn("[{}:{}:{}] No threads for session", conn_id, conn_type, cloud_user);
            return;
        }

        // Send action commands to all threads
        for(MMonitorNotificationCommand command : actionData.getActionCommands()) {
            for (BlockingQueue<MMonitorNotificationCommand> commands : myLocalThreads) {
                try {
                    commands.put(command);
                } catch (InterruptedException e) {
                    logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
                }
            }
        }

        // Get the connection info
        logger.info("[{}:{}:{}] Number of commands: {}", conn_id, conn_type, cloud_user, actionData.getActionCommands().size());
    }

    @Path("end")
    @POST
    @Consumes("text/plain")
    public synchronized void endNotification(@QueryParam("sessionId") String id) throws GuacamoleException {

        Map<String, String> parameters = getConnectionParameters(id);

        if (parameters == null) {
            logger.info("[{}:{}:{}] endNotification: shared connection cannot clear its own thread, should be cleared by parent session", "", "", "");
            return;
        }

        String conn_id = parameters.get(EncurlParameters.ID_PARM);
        String cloud_user = parameters.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = parameters.get(EncurlParameters.CONNECTION_TYPE_PARAM);

        long sessionId = Long.parseLong(conn_id);

        queueCommand(ActionData.builder()
                .sessionId(sessionId)
                .actionCommand(MMonitorNotificationCommand.END)
                .build());

        logger.info("[{}:{}:{}] endNotification done", conn_id, conn_type, cloud_user);
    }

    // Check if the id is numeric
    private boolean isNumeric(String id) {
        try {
            Long.parseLong(id);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // Get parameters from the connection, depending on the type of connection, shared or primary
    private Map<String, String> getConnectionParameters(String id) throws GuacamoleException {
        Map<String, String> parameters = new HashMap<>();

        // Check if a current connection is a regular (numeric id) && ApportoUserContext,
        // or shared connection (arbitrary string as id && ApportoSharedUserContext)
        if (isNumeric(id)) {
            // If it is numeric ID, check the type of context, Primary or Shared
            if (userContext instanceof ApportoUserContext) {
                parameters.put(EncurlParameters.ID_PARM, id);
                parameters.put(EncurlParameters.CLOUD_USERNAME_PARM, getParameter(id, EncurlParameters.CLOUD_USERNAME_PARM));
                parameters.put(EncurlParameters.CONNECTION_TYPE_PARAM, getParameter(id, EncurlParameters.CONNECTION_TYPE_PARAM));
            } else {
                parameters.put(EncurlParameters.ID_PARM, id);
                parameters.put(EncurlParameters.CLOUD_USERNAME_PARM, "");
                parameters.put(EncurlParameters.CONNECTION_TYPE_PARAM, "");
            }
        } else {
            // The shared connection
            Connection connection = userContext.getConnectionDirectory().get(id);
            if (connection != null) {
                GuacamoleConfiguration configuration = connection.getConfiguration();
                parameters.put(EncurlParameters.ID_PARM, configuration.getParameters().get(EncurlParameters.ID_PARM));
                parameters.put(EncurlParameters.CLOUD_USERNAME_PARM, configuration.getParameters().get(EncurlParameters.CLOUD_USERNAME_PARM));
                parameters.put(EncurlParameters.CONNECTION_TYPE_PARAM, configuration.getParameters().get(EncurlParameters.CONNECTION_TYPE_PARAM));
            } else {
                return null;  // Shared connection is not found
            }
        }

        return parameters;
    }

    @Path("subscribe")
    @GET
    @Produces("text/event-stream")
    public synchronized String subscribe(@Context HttpServletResponse response, @QueryParam("sessionId") long sessionId) throws GuacamoleException {
        response.setContentType("text/event-stream");
        response.setHeader("Cache-Control", "no-cache");
        response.setHeader("Connection", "keep-alive");
        response.setCharacterEncoding("UTF-8");

        Map<String, String> parameters = getConnectionParameters(Long.toString(sessionId));

        if (parameters == null) {
            logger.error("[{}:{}:{}] subscribe: cannot get connection parameters, exiting without subscribing.", "", "", "");
            return "";
        }

        String conn_id = parameters.get(EncurlParameters.ID_PARM);
        String cloud_user = parameters.get(EncurlParameters.CLOUD_USERNAME_PARM);
        String conn_type = parameters.get(EncurlParameters.CONNECTION_TYPE_PARAM);
        logger.info("[{}:{}:{}] subscribe: thread Id={} Name={}", conn_id, conn_type, cloud_user, Thread.currentThread().getId(), Thread.currentThread().getName());

        // Queue of commands is stored into global hash map. In principle, queue object is created and deleted in this method,
        // so it looks like local object. However, access to this object is required from other methods as well (mouse move,
        // mouse click, key down), so it must be global.
        Map<Long, BlockingQueue<MMonitorNotificationCommand>> myThreads = sessionThreads.computeIfAbsent(sessionId, k -> new ConcurrentHashMap<>());
        BlockingQueue<MMonitorNotificationCommand> commands = new LinkedBlockingQueue<>();
        myThreads.put(Thread.currentThread().getId(), commands);

        sessionThreads.put(sessionId, myThreads);

        Long lastActionTime = System.currentTimeMillis();

        try (PrintWriter writer = response.getWriter()) {
            ObjectMapper mapper = new ObjectMapper();

            writer.write(SSE_INIT_MESSAGE);
            writer.flush();

            MMonitorNotificationCommand command = null;
            do {
                command = commands.poll(2, TimeUnit.SECONDS);

                if (command !=null ) {
                    lastActionTime = System.currentTimeMillis();
                    logger.info("[{}:{}:{}] Subscription thread Id={} Name={} received command: {}", conn_id, conn_type, cloud_user, Thread.currentThread().getId(), Thread.currentThread().getName(), command.toString());

                    String str = mapper.writeValueAsString(command);
                    writer.write("data: " + str + "\n\n");
                    writer.flush();
                } else if (System.currentTimeMillis() - lastActionTime > MMONITOR_THREAD_TIMEOUT) {
                    logger.warn("[{}:{}:{}] No action in thread Id={} Name={} for {} seconds, exiting thread", conn_id, conn_type, cloud_user, Thread.currentThread().getId(), Thread.currentThread().getName(), MMONITOR_THREAD_TIMEOUT / 1000);
                    command = MMonitorNotificationCommand.END;
                }
            } while(command == null || command != MMonitorNotificationCommand.END);

            // Send END signal to client and close the connection. This will prevent client from reconnecting.
            writer.write(SSE_END_MESSAGE);
            writer.flush();
            writer.close();
        }
        catch (IOException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("[{}:{}:{}] Interrupted Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        finally {
            myThreads.remove(Thread.currentThread().getId());
            if (myThreads.isEmpty()) {
                sessionThreads.remove(sessionId);
            }
            logger.info("[{}:{}:{}] UNsubscribe: thread Id={} Name={}", conn_id, conn_type, cloud_user, Thread.currentThread().getId(), Thread.currentThread().getName());
        }

        return "";
    }
}
