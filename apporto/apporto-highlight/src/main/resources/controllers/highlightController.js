/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing highlight drawing.
 */
angular.module('highlight').controller('highlightController', ['$scope', '$injector', '$rootScope',
    function($scope, $injector, $rootScope) {

        var ClientIdentifier         = $injector.get('ClientIdentifier');
        var $routeParams             = $injector.get('$routeParams');
        var authenticationService    = $injector.get('authenticationService');
        var $http                    = $injector.get('$http');
        var $timeout                 = $injector.get('$timeout');
        var connectionService        = $injector.get('connectionService');
        var $location                = $injector.get('$location');
        var guacClientManager        = $injector.get('guacClientManager');

        if($location.path().indexOf("classroom") > -1) {
            return;
        }

        $scope.ribbonService         = $injector.get('ribbonService');

        var mouse = null;
        var datasource = null;
        var shared_session = false;

        var sessionId = null;
        var contourId = null;

        var canvas = null,
            ctx = null,
            draw_flag = false,
            prevX = 0,
            prevY = 0,
            last_mb_state = false;
        var sender = null;

        var HLCommandCode = Object.freeze({
            MOUSE_MOVE : 1,
            MOUSE_LBTN_UP : 2,
            MOUSE_LBTN_DOWN : 3,
            END : 4,
            UNKNOWN : 5
        });
        var HLCommands = [];

        const SEND_FREQ = 500;
        const LINE_COLOR = "red";
        const SHADOW_COLOR = "#ff3333";
        const CONNECTION_SUCCESS_TEXT = "SSE Init";

        var userIdElement = document.getElementById("user-id");
        var user_last_point = {
            x: 0,
            y: 0,
        };
        var contourIds = [];

        var cl = null;
        var display = null;

        $scope.eventSource = null;

        function init() {
            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            datasource = clientIdentifier.dataSource;
            if (datasource === 'encryptedurl-jdbc') {
                sessionId = clientIdentifier.id;
                shared_session = false;
            }
            else {
                shared_session = true;
                connectionService.getConnection(clientIdentifier.dataSource, clientIdentifier.id)
                .then(function connectionRetrieved(connection) {
                    sessionId = connection.name;
                    if ($routeParams.hl && !$scope.ribbonService.highlightActivated)
                        $scope.btnHighlight();
                });
            }
        }

        $scope.$watch('ribbonService.highlightActivated', function(value) {
            if (value == true) {
                var hlCanvasContainer = document.getElementById("highlightCanvasContainer");
                var width = hlCanvasContainer.style.width;
                var height = hlCanvasContainer.style.height;

                // Wait for canvas to appear
                $timeout(function() {
                    var hlCanvas = document.getElementById("highlightCanvas");
                    if (hlCanvas != null) {
                        hlCanvas.style.width = width;
                        hlCanvas.style.height = height;
                        hlCanvas.width = width.slice(0,-2);
                        hlCanvas.height = height.slice(0,-2);
                    }
                }, 0);
            }
        });

        // start highlightController after receive sharedInformation notify.
        $rootScope.$on('sharedInformation', function(event, current, previous) {
            if ($routeParams.id) {
                init();
            }
        });

        // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
        // try to get client after 10 seconds.
        $timeout(function() {
            if (datasource == null && $routeParams.id) {
                init();
            }
        }, 10 * 1000);

        /**
         * Use this function when different between primary screen and the shared screen in size.
         * @Param recX:        received x from the other screen
         * @Param recY:        received y from the other screen
         * @Param recWidth:    received width from the other screen
         * @Param recHeight:   received height from the other screen
         * @Param recScale:    received scale from the other screen
         * @Param hasMmonitor: is multi monitor?
         *
         * @returns {Object} {x, y}
         *
         */
        function calcPosition(recX, recY, recWidth, recHeight, recScale, hasMmonitor) {

            var calcPos = {x: 0, y: 0};

            /**
             * $rootScope.width and height are set and changed whenever resizing the current screen.
             * see 'mainElementResized' function in guacClientDecorator.js.
             */
            var curScreenWidth = $rootScope.width;
            var curScreenHeight = $rootScope.height;

            // If a current connection is a primary connection
            if ($routeParams.q) {

                // recWidth: shared screen width, curScreenWidth: primary screen width
                if (recWidth < curScreenWidth) {
                    if ($scope.ribbonService.licenses.hasMMonitorLicence) {
                        calcPos.x = parseInt(recX / recScale);
                        // ssHeight: share screen height, ssY: share screen y
                        var ssHeight = parseInt(curScreenWidth * recScale * curScreenHeight / curScreenWidth);
                        var ssY = recY - parseInt((recHeight - ssHeight) / 2);
                        calcPos.y =  parseInt(curScreenHeight * ssY / ssHeight);
                    } else {
                        calcPos.x = parseInt(curScreenWidth * recX / recWidth);
                        // ssHeight: share screen height, ssY: share screen y
                        var ssHeight = parseInt(recWidth * curScreenHeight / curScreenWidth);
                        var ssY = recY - parseInt((recHeight - ssHeight) / 2);
                        calcPos.y =  parseInt(curScreenHeight * ssY / ssHeight);
                    }
                } else {
                    if ($scope.ribbonService.licenses.hasMMonitorLicence) {
                        calcPos.x = parseInt(recX / recScale);
                        // ssHeight: share screen height, ssY: share screen y
                        var ssHeight = parseInt(curScreenWidth * recScale * curScreenHeight / curScreenWidth);
                        var ssY = recY - parseInt((recHeight - ssHeight) / 2);
                        calcPos.y =  parseInt(curScreenHeight * ssY / ssHeight);
                    } else {
                        // ssWidth: share screen width, ssX: share screen x
                        var ssWidth = curScreenWidth;
                        var ssX = recX - parseInt((recWidth - ssWidth) / 2)
                        calcPos.x = parseInt(curScreenWidth * ssX / ssWidth) -
                                    parseInt((curScreenWidth / curScreenHeight) / 2);
                        calcPos.y = parseInt(curScreenHeight * recY / recHeight);
                    }
                }

            } else {

                // If a current connection is a shared connection
                if ($routeParams.key && !$routeParams.mm) {
                    // recWidth: primary screen width, curScreenWidth: shared screen width
                    if (recWidth > curScreenWidth) {
                        if (hasMmonitor) {
                            calcPos.x = parseInt(recWidth * display.getScale() * recX / recWidth);
                            calcPos.y = parseInt(recHeight * display.getScale() * recY / recHeight) +
                                        parseInt((curScreenHeight - recHeight * display.getScale()) / 2);
                        } else {
                            calcPos.x = parseInt(curScreenWidth * recX / recWidth);
                            calcPos.y = parseInt(parseInt(curScreenWidth * recHeight / recWidth) * recY / recHeight) +
                                        parseInt((curScreenHeight - parseInt(parseInt(curScreenWidth * recHeight / recWidth))) / 2);
                        }
                    } else {
                        if (hasMmonitor) {
                            calcPos.x = parseInt(recWidth * display.getScale() * recX / recWidth);
                            calcPos.y = parseInt(recHeight * display.getScale() * recY / recHeight) +
                                        parseInt((curScreenHeight - recHeight * display.getScale()) / 2);
                        } else {
                            calcPos.x = parseInt(parseInt(curScreenHeight * recWidth / recHeight) * recX / recWidth) +
                                        parseInt((curScreenWidth - parseInt(parseInt(curScreenHeight * recWidth / recHeight))) / 2);
                            calcPos.y = parseInt(recHeight * display.getScale() * recY / recHeight) +
                                        parseInt((curScreenHeight - recHeight * display.getScale()) / 2);
                        }
                    }

                } else {
                    calcPos.x = recX;
                    calcPos.y = recY;
                }
            }

            return calcPos;
        }

        $scope.getData = function getData(event) {

            // Avoid the javascript error due to the text indicating to the connection success ("SSE Init").
            if (event.data === CONNECTION_SUCCESS_TEXT)
                return;

            // Get the drawing data.
            data = JSON.parse(event.data);
            // Check if the contour was already drawn by a user.
            if (contourIds.includes(data.contourId))
                return;

            var calcPos = calcPosition(data.x, data.y, data.width, data.height, data.scale, data.mmonitor);

            var calcX = calcPos.x;
            var calcY = calcPos.y;

            switch (data.command) {
                case HLCommandCode.MOUSE_MOVE:
                    ctx.beginPath();
                    ctx.moveTo(prevX, prevY);
                    ctx.lineTo(calcX, calcY);
                    ctx.strokeStyle = LINE_COLOR;
                    ctx.shadowBlur = 1;
                    ctx.shadowColor = SHADOW_COLOR;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                    ctx.closePath();

                    userIdElement.style["left"] = calcX + "px";
                    userIdElement.style["top"] = calcY + "px";

                    prevX = calcX;
                    prevY = calcY;
                    break;

                case HLCommandCode.MOUSE_LBTN_DOWN:
                    userIdElement.style["display"] = "inline";
                    prevX = calcX;
                    prevY = calcY;
                    ctx.beginPath();
                    ctx.moveTo(prevX, prevY);
                    ctx.strokeStyle = LINE_COLOR;
                    ctx.shadowBlur = 1;
                    ctx.shadowColor = SHADOW_COLOR;
                    ctx.lineWidth = 1;
                    ctx.stroke();
                    ctx.closePath();
                    break;

                case HLCommandCode.MOUSE_LBTN_UP:
                    ctx.beginPath();
                    ctx.moveTo(prevX, prevY);
                    ctx.lineTo(calcX, calcY);
                    ctx.strokeStyle = LINE_COLOR;
                    ctx.shadowBlur = 1;
                    ctx.shadowColor = SHADOW_COLOR;
                    ctx.lineWidth = 1;
                    ctx.stroke();

                    ctx.closePath();

                    userIdElement.style["display"] = "none";
                    break;
            }
        }

        function startDrawing(state) {
            // Skip sending the same event twice. This prevent the following use case:
            // When mouse leaves the canvas, automaticaly is received MOUSE_UP event,
            // even if the mouse button is still physically pressed.
            // If mouse is returned to the canvas while still holding the button and then relesed,
            // the MOUSE_UP command will be sent twice.
            if (last_mb_state != state.left) {
                last_mb_state = state.left;

                contourId = Math.floor(Math.random() * 0x7FFFFFFF);
                contourIds.push(contourId);
                HLCommands.push({contourId: contourId, command: HLCommandCode.MOUSE_LBTN_DOWN,
                                 x:state.x, y:state.y, width: $rootScope.width,
                                 height: $rootScope.height, scale: display.getScale(),
                                 mmonitor: $scope.ribbonService.licenses.hasMMonitorLicence});

                user_last_point.x = state.x;
                user_last_point.y = state.y;

                draw_flag = true;
            }
        }

        function endDrawing(state) {
            // Skip sending the same event twice. This prevent the following use case:
            // When mouse leaves the canvas, automaticaly is received MOUSE_UP event,
            // even if the mouse button is still physically pressed.
            // If mouse is returned to the canvas while still holding the button and then relesed,
            // the MOUSE_UP command will be sent twice.
            if (last_mb_state != state.left) {
                last_mb_state = state.left;

                HLCommands.push({contourId: contourId, command: HLCommandCode.MOUSE_LBTN_UP,
                                 x:state.x, y:state.y, width: $rootScope.width,
                                 height: $rootScope.height, scale: display.getScale(),
                                 mmonitor: $scope.ribbonService.licenses.hasMMonitorLicence});

                ctx.beginPath();

                ctx.moveTo(user_last_point.x, user_last_point.y);
                ctx.lineTo(state.x, state.y);

                ctx.strokeStyle = LINE_COLOR;
                ctx.shadowBlur = 1;
                ctx.shadowColor = SHADOW_COLOR;
                ctx.lineWidth = 1;
                ctx.stroke();

                ctx.closePath();

                draw_flag = false;
            }
        }

        function mouseDrawing(state) {
            if (draw_flag) {
                ctx.beginPath();

                ctx.moveTo(user_last_point.x, user_last_point.y);
                ctx.lineTo(state.x, state.y);

                ctx.strokeStyle = LINE_COLOR;
                ctx.shadowBlur = 1;
                ctx.shadowColor = SHADOW_COLOR;
                ctx.lineWidth = 1;
                ctx.stroke();

                ctx.closePath();

                user_last_point.x = state.x;
                user_last_point.y = state.y;

                last_sent_X = state.x;
                last_sent_Y = state.y;
                HLCommands.push({contourId: contourId, command: HLCommandCode.MOUSE_MOVE,
                                 x:state.x, y:state.y, width: $rootScope.width,
                                 height: $rootScope.height, scale: display.getScale(),
                                 mmonitor: $scope.ribbonService.licenses.hasMMonitorLicence});
            }

        }

        function sendMouseData() {
            if (HLCommands.length > 0) {
                var TmpHLCommands = HLCommands.slice(0);
                $http({
                    method: 'POST',
                    url: "api/session/ext/" + datasource + "/messaging/mousedata" + "?token=" +
                         authenticationService.getCurrentToken(),
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    data: {
                        sessionId: sessionId,
                        hlCommands: TmpHLCommands
                    }
                })
                .then(function(response) {
                    console.log(response);
                })
                .catch(function(response) {
                    console.log(response);
                })
                .finally(function() {
                    HLCommands = HLCommands.slice(TmpHLCommands.length, HLCommands.length);
                    if ($scope.ribbonService.highlightActivated)
                        $timeout(sendMouseData, SEND_FREQ);
                });
            } else {
                if ($scope.ribbonService.highlightActivated)
                    $timeout(sendMouseData, SEND_FREQ);
            }
        }

        /**
         * Ends highlighting, ends receiving thread on the server.
         */
        $rootScope.endHighlight = function endHighlight() {

            if ($scope.eventSource) {
                $scope.eventSource.close();
                $scope.eventSource = null;
            }

            draw_flag = false;

            // End the event thread on server
            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            if (clientIdentifier == null) {
                console.warn("Highlight: Cannot stop the highlight service on server, " +
                             "no clientIdentitier available");
                return;
            }

            // Send the signal to the server
            if (!contourId) {
                contourId = 0;
            }

            var dataSource = encodeURIComponent(clientIdentifier.dataSource);
            var params = "?token=" + authenticationService.getCurrentToken();
            params = params + "&contourId=" + contourId + "&sessionId=" + sessionId;
            navigator.sendBeacon("api/session/ext/" + dataSource + "/messaging/end" + params);

        }

        /**
         * Stop highlighting, ends receiving thread on the server.
         */
        $rootScope.unsubscribe = function unsubscribe() {

            // End the event thread on server
            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            if (clientIdentifier == null) {
                console.warn("Highlight: Cannot stop the highlight service on server, " +
                             "no clientIdentitier available");
                return;
            }

            // Send the signal to the server
            var dataSource = encodeURIComponent(clientIdentifier.dataSource);
            var params = "?token=" + authenticationService.getCurrentToken();
            params = params + "&sessionId=" + sessionId;
            navigator.sendBeacon("api/session/ext/" + dataSource + "/messaging/stop" + params);

        }

        $scope.btnHighlight = function btnHighlight() {
            $scope.ribbonService.highlightActivated = !$scope.ribbonService.highlightActivated;

            $timeout(function() {

                if (canvas == null)
                    canvas = document.getElementById("highlightCanvas");
                if (ctx == null)
                    ctx = canvas.getContext("2d");
                if (mouse == null)
                    mouse = new Guacamole.Mouse(canvas);

                if ($scope.ribbonService.highlightActivated) {

                    cl = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);
                    display = cl.client.getDisplay();

                    $scope.eventSource = new EventSource("api/session/ext/" + datasource + "/messaging/subscribe" +
                                                         "?token=" + authenticationService.getCurrentToken() +
                                                         "&sessionId=" + sessionId);
                    $scope.eventSource.onmessage = $scope.getData;
                    mouse.onmousemove = mouseDrawing;
                    mouse.onmousedown = startDrawing;
                    mouse.onmouseup = endDrawing;
                    sender = $timeout(sendMouseData, SEND_FREQ);
                    last_mb_state = false;
                }
                else {
                    $scope.eventSource.close();
                    $scope.eventSource = null;
                    mouse.onmousemove = null;
                    mouse.onmousedown = null;
                    mouse.onmouseup = null;
                    canvas = null;
                    ctx = null;
                    mouse = null;
                    $timeout.cancel(sender);
                    if (!!contourId && !!sessionId) {
                        $rootScope.endHighlight();
                    }
                    $rootScope.stopSharing($scope.ribbonService.shareKey);
                    $rootScope.unsubscribe();
                    $scope.ribbonService.ribbonShareDialogVisible = !$scope.ribbonService.ribbonShareDialogVisible;
                    $scope.ribbonService.nextButtonClicked = false;
                    $scope.ribbonService.shareLinkCopied = !$scope.ribbonService.shareLinkCopied;
                    HLCommands = HLCommands.slice(HLCommands.length, HLCommands.length);
                }

            }, 0);
        }
    }
])
