/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays loading circle
 */
angular.module('highlight').directive('welcomeCanvas', [function welcomeCanvas() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/highlight/templates/welcome-canvas.html',
        controller: ['$scope', '$injector', '$element', function welcomeCanvasController($scope, $injector, $element) {

            $scope.ribbonService = $injector.get('ribbonService');
            var $location                = $injector.get('$location');

            if($location.path().indexOf("classroom") > -1) {
                return;
            }
            $scope.wlHide = false;

            $scope.wlClick = function wlClick(event) {
                /**
                 * The element that contains the Guacamole display element.
                 *
                 * @type Element
                 */
                var displayContainer = document.getElementsByClassName('display')[0];
                if (!!displayContainer) {
                    $scope.wlHide = true;
                    var rect = $element.find("canvas")[0].getBoundingClientRect();

                    var x = event.clientX - rect.left
                    var y = event.clientY - rect.top
                    console.debug("x: " + x + " y: " + y);

                    var moveEvent = new MouseEvent("mousemove", {
                        view: window,
                        bubbles: true,
                        cancelable: true,
                        clientX: x,
                        clientY: y
                    });
                    displayContainer.dispatchEvent(moveEvent);

                    var clickEvent = new MouseEvent("mousedown", {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    displayContainer.dispatchEvent(clickEvent);

                    var clickEvent = new MouseEvent("mouseup", {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    displayContainer.dispatchEvent(clickEvent);
                }
            }
            
        }] // end welcomeCanvas controller
    };
}]);

