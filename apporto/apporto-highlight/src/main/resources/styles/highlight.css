/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */


.drawing-pad-container {
    overflow: hidden;
    display: block;
    cursor: url(app/ext/highlight/images/circle.png) 5 5, auto;
}

.drawing-pad-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: black;
    opacity: 0.3;
}

.drawing-pad {
    position: absolute;
    height: 500px;
    width: 100%;
    top: 0;
    left: 0;
    overflow: hidden;
    display: block;
}

.drawing-pad-container .user-id {
    background-color: white;
    border: 2px solid blue;
    display: none;
    position: relative;
}

.btn-highlight {
    background-image: url(app/ext/highlight/images/highlight-black.png);
}

