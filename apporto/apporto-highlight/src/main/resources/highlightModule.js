/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
angular.module('highlight', [
    'ribbon',
    'client'
]);

angular.module('highlight').run(['ribbonService', '$injector', function(ribbonService, $injector) {
    var $location               = $injector.get('$location');

    if($location.path().indexOf("classroom") > -1) {
        return;
    }
    var drawing_pad_html ="<drawing-pad></drawing-pad>";
    var welcome_canvas_html ="<welcome-canvas style='background-color:transparent;'></welcome-canvas>";

    document.body.insertAdjacentHTML('beforeend', welcome_canvas_html);
    document.body.insertAdjacentHTML('beforeend', drawing_pad_html);

    ribbonService.highlightAvailable = true;
}]);

// Ensure the highlight module is loaded along with the rest of the app
angular.module('index').requires.push('highlight');

