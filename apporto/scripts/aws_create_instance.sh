#!/bin/bash

set -e

echo $@ > args

print_usage() {
	echo "The script requires 7 parameters:"
	echo "    1. ID of the AMI"
	echo "    2. Type of the instance"
	echo "    3. SSH key name"
	echo "    4. ID of the security group"
	echo "    5. ID of the instance subnet"
	echo "    6. Region (us-west-2)"
	echo "    7. Name of the instance"
}

if [[ $# != 7 ]]; then
	print_usage
else
	# create instance
	# $1 == ${AMI_IMAGE}
	# $2 == ${INSTANCE_TYPE}
	# $3 == ${SSH_KEY}
	# $4 == ${SECURITY_GROUP}
	# $5 == ${SUBNET}
	# $6 == ${REGION}
	# $7 == ${INSTANCE_NAME}
	INSTANCE_ID=$(aws ec2 run-instances --image $1 --count 1 --instance-type $2 --key $3 --security-group-ids $4 --subnet-id $5 --region $6 | awk '/ami-/{print $7}')
	[[ -z $INSTANCE_ID ]] && false
	# wait until finished
	#sleep 60
	aws ec2 create-tags --resources $INSTANCE_ID --tags Key=Name,Value=$7
fi
