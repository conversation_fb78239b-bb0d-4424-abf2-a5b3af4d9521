#!/bin/bash

# $1 == SFTP username
# $2 == SFTP password
# $3 == SFTP server
# Script runs every sshfs command twice to make sure it is mounted - otherwise there are connection resets.
echo $2 | sshfs -o password_stdin,uid=$(id -u),gid=$(id -g) $1@$3:SessionUploads Desktop/SessionUploads/ || echo $2 | sshfs -o password_stdin,uid=$(id -u),gid=$(id -g) $1@$3:SessionUploads Desktop/SessionUploads/ || :
echo $2 | sshfs -o password_stdin,uid=$(id -u),gid=$(id -g) $1@$3:SessionDownloads Desktop/SessionDownloads/ || echo $2 | sshfs -o password_stdin,uid=$(id -u),gid=$(id -g) $1@$3:SessionDownloads Desktop/SessionDownloads/ || :
#vncserver -geometry $4
