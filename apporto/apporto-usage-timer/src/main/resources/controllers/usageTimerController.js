/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing expiration timer.
 */
angular.module('usageTimer').controller('usageTimerController',
        [ '$scope', '$injector', '$timeout',

        function usageTimerController($scope, $injector, $timeout) {

            // Required service
            var $http                    = $injector.get('$http');
            var guacNotification         = $injector.get('guacNotification');
            var authService              = $injector.get('authenticationService');
            var $rootScope   			 = $injector.get('$rootScope');
            var $routeParams             = $injector.get('$routeParams');

            var $location                = $injector.get('$location');

            if($routeParams.hasOwnProperty("key") && $location.path().indexOf("classroom") > -1) {
                return;
            }

            var TIMER_WARNING  = 3*60*1000;  // Time after which session expiring notification is shown (convert minutes to ms)
            var TIMER_REGULAR  = 1*60*1000;  // Regular session duration
            var TIMER_EXTENDED = 5*60*1000;  // Extended time after user register

            var warningTimer;
            var sessionTimer;

            $scope.registerDialogVisible = false;
            $scope.formData = {
                    oid: "00D15000000GsjO",
                    retURL: "",
                    debug: "1",
                    debugEmail: "",
                    first_name: "",
                    last_name: "",
                    email: "",
                    phone: "",
                    company: "",
                    title: "",
                    lead_source: ""
            };

            /**
             * React on "startTimer" event and start countdown
             */
            $scope.$on('startTimer', function() {
                warningTimer = $timeout(function() {
                    $scope.notifyExpiration();
                }, TIMER_WARNING);
            });
            

            /**
             * Submitting registration form data
             */
            $scope.submitRegistration = function submitRegistration() {
               
                $http({
                    method  : 'POST',
                    url     : 'https://www.salesforce.com/servlet/servlet.WebToLead?encoding=UTF-8',
                    data    : $.param($scope.formData),  // pass in data as strings
                    headers : { 'Content-Type': 'application/x-www-form-urlencoded' }  // set the headers so angular passing info as form data (not request payload)
                   })
                    .then(function(data) {
                        console.log(data);

                        if (!data.success) {
                            // if not successful, bind errors to error variables
                            guacNotification.showStatus({
                                'title'      : "DIALOGS.DIALOG_HEADER_REGISTER_FAILED",
                                'text'       : "DIALOGS.DIALOG_TEXT_REGISTER_FAILED",
                                'actions'    : [ ACKNOWLEDGE_ACTION ]
                            });
                        } else {
                            // Extend session
                            sessionTimer = $timeout(function() {
                                authService.logout();
                            }, TIMER_EXTENDED);
                            
                            guacNotification.showStatus({
                                'title'      : "DIALOGS.DIALOG_HEADER_REGISTER_SUCCESS",
                                'text'       : "DIALOGS.DIALOG_TEXT_REGISTER_SUCCESS",
                                'actions'    : [ ACKNOWLEDGE_ACTION ]
                            });
                        }
                    });

                
                $scope.registerDialogVisible = false;
            };

            $scope.cancelRegistration = function cancelRegistration() {
              $scope.registerDialogVisible = false;
            };
            
            /**
             * An action to be provided along with the object sent to showStatus which
             * extends available time for current connection.
             */
            var REGISTER_ACTION = {
                name        : "APP.ACTION_REGISTER",
                className   : "danger",
                // Handle action
                callback    : function registerCallback() {
                	$rootScope.loginHidden = true;
                	$rootScope.ribbonVisible = false;
                	authService.logout();
                    $timeout(function trialPage() {
                    	window.open("http://www.apporto.com/trial", "_self", "", true);
                    }, 0);
                }
            };

            /**
             * An action to be provided along with the object sent to showStatus which
             * logouts the user.
             */
            var CONTINUE_ACTION = {
                name        : "APP.ACTION_CONTINUE",
                // Handle action
                callback    : function cancelCallback() {
                    guacNotification.showStatus(false);

                    sessionTimer = $timeout(function() {
                        $scope.notifyEndSession();
                    }, TIMER_REGULAR);
                }
            };

            /**
             * An action which will logout the user.
             */
            var LOGOUT_ACTION = {
                name        : "APP.ACTION_LOGOUT",
                // Handle action
                callback    : function cancelCallback() {
                    authService.logout();
                }
            };

            /**
             * Show or hides notification dialog
             */
            $scope.notifyExpiration = function notifyExpiration() {
                // Confirm deletion request
                guacNotification.showStatus({
                    'title'      : "DIALOGS.DIALOG_HEADER_CONNECTION_TIMEOUT",
                    'text'       : "DIALOGS.DIALOG_TEXT_CONNECTION_TIMEOUT",
                    'actions'    : [ REGISTER_ACTION, CONTINUE_ACTION]
                });
    
            }

            /**
             * End session
             */
            $scope.notifyEndSession = function notifyEndSession() {
                // Confirm deletion request
                guacNotification.showStatus({
                    'title'      : "DIALOGS.DIALOG_HEADER_SESSION_EXPIRED",
                    'text'       : "DIALOGS.DIALOG_TEXT_SESSION_EXPIRED",
                    'actions'    : [ REGISTER_ACTION, LOGOUT_ACTION ]
                });
            }

            $scope.startTimer = function startTimer() {
                $rootScope.$broadcast('startTimer');
            }

            $scope.$on('$routeChangeSuccess', function(event, current, previous) {

                // If the current route is available
                if (current.$$route) {

                    // Display ribbon if remote client is showing
                    if (current.$$route.bodyClassName === "client" && $routeParams.d === "1") {
                        $scope.startTimer();
                    }
                }

            });

        } ]);
