<div class="notification">

    <!-- Registration title -->
    <div class="title-bar">
        <div class="title">{{'DIALOGS.DIALOG_HEADER_REGISTRATION' | translate}}</div>
    </div>

    <div class="body">
        
        <!-- Registration text -->
        <p class="text">{{'DIALOGS.DIALOG_TEXT_SIGN_UP' | translate}}</p>
        <p class="text">{{'DIALOGS.DIALOG_TEXT_REQUIRED_FIELDS' | translate}}</p>
	
		<form ng-submit="submitRegistration()" class="form-horizontal contact-form" id="salseforceform">
	
			<input type=hidden name="oid" value="00D15000000GsjO" ng-model="formData.oid">
	
			<input type="hidden" name="debug" value=1 ng-model="formData.debug">
			<input type="hidden" name="debugEmail" value="<EMAIL>" ng-model="formData.debugEmail">
	
			<input placeholder="First Name" ng-model="formData.first_name" class="form-control form-text required" required="required"	id="first_name" maxlength="40" name="first_name" size="50" type="text" /><br>
			<input placeholder="Last Name" ng-model="formData.last_name" class="form-control form-text required" required="required" id="last_name" maxlength="80" name="last_name" size="50" type="text" /><br>
	
			<input placeholder="Email" ng-model="formData.email" class="corporate_email email form-control form-text form-email required error" required="required" id="email" maxlength="80" name="email" size="50" type="email" /><br>
			<input placeholder="Phone" ng-model="formData.phone" class="form-control form-text required" required="required" id="phone" maxlength="40" name="phone" size="50" type="text" /><br>
	
			<input placeholder="Address" ng-model="formData.company" class="form-control form-text required" required="required" id="company" maxlength="40" name="company"	size="50" type="text" /><br>
	
			<textarea Placeholder="Your Message" ng-model="formData.title" rows="3" class="form-control form-text required" required="required" id="title" maxlength="40" name="title" size="50" type="text"></textarea><br>
			<input class="form-control" ng-model="formData.lead_source" id="lead_source" name="lead_source" size="50" value="Trial form" type="hidden" /><br>
	
	        <button type="submit" class="danger">{{'APP.ACTION_REGISTER' | translate}}</button>
	        <button type="button" ng-click="cancelRegistration()">{{'APP.ACTION_CANCEL' | translate}}</button>
		</form>
	</div>
</div>