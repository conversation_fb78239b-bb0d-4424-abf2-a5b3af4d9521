/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.kafka;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import java.util.Properties;

/**
 * Kafka producer module
 */
public class ApportoKafkaProducer {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ApportoKafkaProducer.class);

    private KafkaProducer<String, String> producer;
    private String topic;

    /**
     * Constructs a new ApportoKafkaProducer with the specified configuration properties.
     *
     * @param bootstrapServers the comma-separated list of host:port pairs of Kafka brokers
     * @param topic the topic to produce messages to
     */
    public ApportoKafkaProducer(String bootstrapServers, String topic) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        this.producer = new KafkaProducer<>(props);
        this.topic = topic;
    }
    
    /**
     * Sends a message to the specified Kafka topic.
     *
     * @param message The message to send.
     */
    public void sendMessage(String message) {
        // Create a producer record with the specified topic and message
        ProducerRecord<String, String> record = new ProducerRecord<>(topic, message);

        // Send the record to the Kafka topic
        producer.send(record, (metadata, exception) -> {
            if (exception == null) {
                logger.info("Message sent successfully. Topic: {}, Partition: {}, Offset: {}",
                        metadata.topic(), metadata.partition(), metadata.offset());
            } else {
                logger.error("Error sending message. Topic: {}, Message: {}", topic, message, exception);
            }
        });
    }

}
