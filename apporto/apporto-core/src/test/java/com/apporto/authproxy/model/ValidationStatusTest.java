/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.authproxy.model;

import static org.junit.Assert.assertEquals;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import com.apporto.hyperstream.auth.model.ValidationStatus;

@RunWith(MockitoJUnitRunner.class)
public class ValidationStatusTest {

    @Test
    public void testFromString() {
        assertEquals(ValidationStatus.NEW, ValidationStatus.fromString("NEW"));
        assertEquals(ValidationStatus.VALID, ValidationStatus.fromString("VALID"));
        assertEquals(ValidationStatus.INVALID, ValidationStatus.fromString("INVALID"));
        assertEquals(ValidationStatus.INVALIDATED, ValidationStatus.fromString("INVALIDATED"));
        assertEquals(ValidationStatus.VALIDATION_IN_PROGRESS, ValidationStatus.fromString("VALIDATION_IN_PROGRESS"));
        assertEquals(ValidationStatus.INVALID, ValidationStatus.fromString("INVALID_STATUS"));
        assertEquals(ValidationStatus.INVALID, ValidationStatus.fromString(""));
        assertEquals(ValidationStatus.INVALID, ValidationStatus.fromString(null));
    }
}
