/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.authproxy.model.mapper;

import static org.junit.Assert.assertEquals;

import java.util.UUID;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import com.apporto.hyperstream.auth.model.TokenPair;
import com.apporto.hyperstream.auth.model.dto.TokenPairDto;
import com.apporto.hyperstream.auth.model.mapper.TokenPairMapper;



@RunWith(MockitoJUnitRunner.class)
public class TokenPairMapperTest {

    // add unit test for the fromDto method
    @Test
    public void testFromDto() {
        // Create UUID tokens
        UUID accessToken = UUID.randomUUID();
        UUID refreshToken = UUID.randomUUID();

        // Create a TokenPairDto object
        TokenPairDto tokenPairDto = new TokenPairDto();
        tokenPairDto.setAccessToken(accessToken.toString());
        tokenPairDto.setRefreshToken(refreshToken.toString());

        // Create a TokenPair object
        TokenPair tokenPair = TokenPairMapper.fromDto(tokenPairDto);

        // Check if the TokenPair object has the correct values
        assertEquals(accessToken, tokenPair.getAccessToken());
        assertEquals(refreshToken, tokenPair.getRefreshToken());
    }
}
