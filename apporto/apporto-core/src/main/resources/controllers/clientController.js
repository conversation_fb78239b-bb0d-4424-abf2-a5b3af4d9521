/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * The controller for the page used to connect to a connection or balancing group.
 */
angular.module('client', ['angular-intro', 'angular-web-notification']).controller('clientController', ['$scope', '$rootScope', '$routeParams', '$injector', '$timeout', '$window',
        function clientController($scope, $rootScope, $routeParams, $injector, $timeout, $window) {

    // Required types
    var ManagedClient      = $injector.get('ManagedClient');
    var ManagedClientState = $injector.get('ManagedClientState');
    var ManagedFilesystem  = $injector.get('ManagedFilesystem');
    var Protocol           = $injector.get('Protocol');
    var ScrollState        = $injector.get('ScrollState');

    // Required services
    var $location              = $injector.get('$location');
    var authenticationService  = $injector.get('authenticationService');
    var clipboardService       = $injector.get('clipboardService');
    var guacClientManager      = $injector.get('guacClientManager');
    var guacNotification       = $injector.get('guacNotification');
    var iconService            = $injector.get('iconService');
    var preferenceService      = $injector.get('preferenceService');
    var requestService         = $injector.get('requestService');
    var tunnelService          = $injector.get('tunnelService');
    var ManagedShareLink       = $injector.get('ManagedShareLink');
    var ClientIdentifier       = $injector.get('ClientIdentifier');
    var $http                  = $injector.get('$http');
    $scope.infoService         = $injector.get('infoService');
    $scope.ribbonService       = $injector.get('ribbonService');

    var $translate             = $injector.get('$translate');
    $scope.circleLoaderService = $injector.get('circleLoaderService');
    $rootScope.bHardwareAccelerated = false;

    /**
     * The minimum number of pixels a drag gesture must move to result in the
     * menu being shown or hidden.
     *
     * @type Number
     */
    var MENU_DRAG_DELTA = 64;

    /**
     * The maximum X location of the start of a drag gesture for that gesture
     * to potentially show the menu.
     *
     * @type Number
     */
    var MENU_DRAG_MARGIN = 64;

    /**
     * When showing or hiding the menu via a drag gesture, the maximum number
     * of pixels the touch can move vertically and still affect the menu.
     *
     * @type Number
     */
    var MENU_DRAG_VERTICAL_TOLERANCE = 10;

    /**
     * In order to open the guacamole menu, we need to hit ctrl-alt-shift. There are
     * several possible keysysms for each key.
     */
    var SHIFT_KEYS  = {0xFFE1 : true, 0xFFE2 : true},
        ALT_KEYS    = {0xFFE9 : true, 0xFFEA : true, 0xFE03 : true,
                       0xFFE7 : true, 0xFFE8 : true},
        CTRL_KEYS   = {0xFFE3 : true, 0xFFE4 : true},
        MENU_KEYS   = angular.extend({}, SHIFT_KEYS, ALT_KEYS, CTRL_KEYS);

    /**
     * Keysym for detecting any END key presses, for the purpose of passing through
     * the Ctrl-Alt-Del sequence to a remote system.
     */
    var END_KEYS = {0xFF57 : true, 0xFFB1 : true};

    /**
     * Keysym for sending the DELETE key when the Ctrl-Alt-End hotkey
     * combo is pressed.
     *
     * @type Number
     */
    var DEL_KEY = 0xFFFF;

    /**
     * All client error codes handled and passed off for translation. Any error
     * code not present in this list will be represented by the "DEFAULT"
     * translation.
     */
    var CLIENT_ERRORS = {
        0x0201: true,
        0x0202: true,
        0x0203: true,
        0x0207: true,
        0x0208: true,
        0x0209: true,
        0x020A: true,
        0x020B: true,
        0x0301: true,
        0x0303: true,
        0x0308: true,
        0x031D: true,
        0x0320: true
    };

    /**
     * All error codes for which automatic reconnection is appropriate when a
     * client error occurs.
     */
    var CLIENT_AUTO_RECONNECT = {
        0x0200: true,
        0x0202: true,
        0x0203: true,
        0x0207: true,
        0x0208: true,
        0x0301: true,
        0x0308: true
    };

    /**
     * All tunnel error codes handled and passed off for translation. Any error
     * code not present in this list will be represented by the "DEFAULT"
     * translation.
     */
    var TUNNEL_ERRORS = {
        0x0201: true,
        0x0202: true,
        0x0203: true,
        0x0204: true,
        0x0205: true,
        0x0207: true,
        0x0208: true,
        0x0301: true,
        0x0303: true,
        0x0308: true,
        0x031D: true
    };

    /**
     * All error codes for which automatic reconnection is appropriate when a
     * tunnel error occurs.
     */
    var TUNNEL_AUTO_RECONNECT = {
        0x0200: true,
        0x0202: true,
        0x0203: true,
        0x0207: true,
        0x0208: true,
        0x0308: true
    };

    /**
     * Save the value of the enable-h264 parameter from payload.
     *
     * @type Boolean
     */
    var isEnableH264 = false;

    /**
     * Save the value of the enable-multimonitor parameter from payload.
     *
     * @type Boolean
     */
    var isEnableMultiMonitor = false;

    /**
     * The count for checking the server capacity
     *
     * @type Number
     */
     var checkServerCapacityCount = 0;

     /**
     * The count for attempting the server capacity API call.
     *
     * @type Number
     */
     var failedAttemptCount = 0;

     /**
     * Is the seesion created with RDP Router
     *
     * @type Boolean
     */
    var isStartedRouterSession = false;

    /**
     * Number of attempts to call the RDP Router Service's Start Session API
     *
     * @type Number
     */
    var routerAPIAttemptsCnt = 0;

    const LOAD_BALANCE_SERVICE_TYPE = "traditional";
    /**
    * Indicate which load balancer service to use.
    * traditional means HAProxy Service, rdp-router means RDP Router Service
    * 
    * @type String
    */
    var lbServiceType;

    /**
     * Action which replaces the current client with a newly-connected client.
     */
    function reconnectAction() {

        // Hide the notification
        guacNotification.showStatus(false);

        // Reload translation file
        $translate.refresh();

        // When the reconnect button is clicked, clear the timeout.
        if ($scope.capacity_timer) {
            clearTimeout($scope.capacity_timer);
            $scope.capacity_timer = null;
        }

        authenticationService.updateCurrentToken($location.search())
        ['then'](function name(params) {
            if (lbServiceType == LOAD_BALANCE_SERVICE_TYPE) {
                // Check the capacity of the haproxy server
                checkServerCapacity();
            }
            else {
                checkRDPRouter();
            }

            // Reset the idle expired
            $scope.ribbonService.isIdleExpired = false;

            // Broadcast the '$fillClassroom'
            setTimeout(function() {
                $rootScope.$broadcast('$fillClassroom');
            }, 3000);
        })
        ['catch'](requestService.IGNORE);

    }

    function reconnectSession() {

        // Hide the notification
        guacNotification.showStatus(false);
    
        // Reload translation file
        $translate.refresh();
    
        // When the reconnect button is clicked, clear the timeout.
        if ($scope.capacity_timer) {
            clearTimeout($scope.capacity_timer);
            $scope.capacity_timer = null;
        }

        // Skip updating token; proceed with current session context
        Promise.resolve()
            .then(function () {
                if (lbServiceType == LOAD_BALANCE_SERVICE_TYPE) {
                    // Check the capacity of the haproxy server
                    checkServerCapacity();
                } else {
                    checkRDPRouter();
                }
    
                // Reset the idle expired
                $scope.ribbonService.isIdleExpired = false;
    
                // Broadcast the '$fillClassroom'
                setTimeout(function () {
                    $rootScope.$broadcast('$fillClassroom');
                }, 3000);
            })
            .catch(requestService.IGNORE);
    
    }
    

    // a reconnection event listener for portia chat bot
    $rootScope.$on('reconnect_hs_action', function() {
        reconnectSession();
    })

    var RECONNECT_ACTION = {
        name      : "CLIENT.ACTION_RECONNECT",
        className : "reconnect button",
        callback  : function reconnectCallback() {

            reconnectAction();

        }
    };

    var LOGIN_PAGE_ACTION = {
        name      : "CLIENT.LOGIN_PAGE",
        className : "loginPage button",
        callback  : function loginPageCallback() {

            $rootScope.goLoginPage();

        }
    };

    /**
     * Action which replaces the current client with a newly-connected client
     * when rebooting the VM.
     */
    var RECONNECT_VM_ACTION = {
        name      : "CLIENT.ACTION_RECONNECT",
        className : "reconnect button",
        callback  : function reconnectCallback() {

            // Hide the notification
            guacNotification.showStatus(false);

            // Show another message
            guacNotification.showStatus({
                title: "CLIENT.MESSAGE_TITLE",
                text: {
                    key : "CLIENT.TEXT_REBOOT_VM_WAIT"
                }
            });

            // Run the reconnect action with the delayed time
            setTimeout(function() {

                reconnectAction();

            }, 20*1000);

        }
    };

    /**
     * The reconnect countdown to display if an error or status warrants an
     * automatic, timed reconnect.
     */
    var RECONNECT_COUNTDOWN = {
        text: "CLIENT.TEXT_RECONNECT_COUNTDOWN",
        callback: RECONNECT_ACTION.callback,
        remaining: 15
    };

    /**
     * The reconnect countdown to display if an error or status warrants an
     * automatic, timed reconnect when rebooting the VM.
     */
     var RECONNECT_VM_COUNTDOWN = {
        text: "CLIENT.TEXT_RECONNECT_COUNTDOWN",
        callback: RECONNECT_VM_ACTION.callback,
        remaining: 15
    };

    /**
     * Menu-specific properties.
     */
    $scope.menu = {

        /**
         * Whether the menu is currently shown.
         *
         * @type Boolean
         */
        shown : false,

        /**
         * Whether the Guacamole display should be scaled to fit the browser
         * window.
         *
         * @type Boolean
         */
        autoFit : true,

        /**
         * The currently selected input method. This may be any of the values
         * defined within preferenceService.inputMethods.
         *
         * @type String
         */
        inputMethod : preferenceService.preferences.inputMethod,

        /**
         * The current scroll state of the menu.
         *
         * @type ScrollState
         */
        scrollState : new ScrollState(),

        /**
         * The current desired values of all editable connection parameters as
         * a set of name/value pairs, including any changes made by the user.
         *
         * @type {Object.<String, String>}
         */
        connectionParameters : {}

    };

    // Convenience method for closing the menu
    $scope.closeMenu = function closeMenu() {
        $scope.menu.shown = false;
    };

    /**
     * Applies any changes to connection parameters made by the user within the
     * Guacamole menu.
     */
    $scope.applyParameterChanges = function applyParameterChanges() {
        angular.forEach($scope.menu.connectionParameters, function sendArgv(value, name) {
            ManagedClient.setArgument($scope.client, name, value);
        });
    };

    /**
     * The client which should be attached to the client UI.
     *
     * @type ManagedClient
     */
    $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

    /**
     * The username
     *
     * @type String
     */
    $scope.username = authenticationService.getCurrentUsername();

    /**
     * Map of all available sharing profiles for the current connection by
     * their identifiers. If this information is not yet available, or no such
     * sharing profiles exist, this will be an empty object.
     *
     * @type Object.<String, SharingProfile>
     */
    $scope.sharingProfiles = {};

    /**
     * Map of all currently pressed keys by keysym. If a particular key is
     * currently pressed, the value stored under that key's keysym within this
     * map will be true. All keys not currently pressed will not have entries
     * within this map.
     *
     * @type Object.<Number, Boolean>
     */
    var keysCurrentlyPressed = {};

    /**
     * Map of all substituted key presses.  If one key is pressed in place of another
     * the value of the substituted key is stored in an object with the keysym of
     * the original key.
     *
     * @type Object.<Number, Number>
     */
    var substituteKeysPressed = {};

    /**
     * Map of all currently pressed keys (by keysym) to the clipboard contents
     * received from the remote desktop while those keys were pressed. All keys
     * not currently pressed will not have entries within this map.
     *
     * @type Object.<Number, ClipboardData>
     */
    var clipboardDataFromKey = {};

    /**
     * flag to enable or disable to show open desktop page
     */
    var show_desktop_page = false;

    // Hide menu when the user swipes from the right
    $scope.menuDrag = function menuDrag(inProgress, startX, startY, currentX, currentY, deltaX, deltaY) {

        // Hide menu if swipe gesture is detected
        if (Math.abs(currentY - startY)  <  MENU_DRAG_VERTICAL_TOLERANCE
                  && startX   - currentX >= MENU_DRAG_DELTA)
            $scope.menu.shown = false;

        // Scroll menu by default
        else {
            $scope.menu.scrollState.left -= deltaX;
            $scope.menu.scrollState.top -= deltaY;
        }

        return false;

    };

    // Update menu or client based on dragging gestures
    $scope.clientDrag = function clientDrag(inProgress, startX, startY, currentX, currentY, deltaX, deltaY) {

        // Scroll display if absolute mouse is in use
        if ($scope.client.clientProperties.emulateAbsoluteMouse && startX > MENU_DRAG_MARGIN) {
            $scope.client.clientProperties.scrollLeft -= deltaX;
            $scope.client.clientProperties.scrollTop -= deltaY;
        }

        return false;

    };

    /**
     * If a pinch gesture is in progress, the scale of the client display when
     * the pinch gesture began.
     *
     * @type Number
     */
    var initialScale = null;

    /**
     * If a pinch gesture is in progress, the X coordinate of the point on the
     * client display that was centered within the pinch at the time the
     * gesture began.
     *
     * @type Number
     */
    var initialCenterX = 0;

    /**
     * If a pinch gesture is in progress, the Y coordinate of the point on the
     * client display that was centered within the pinch at the time the
     * gesture began.
     *
     * @type Number
     */
    var initialCenterY = 0;

    // Zoom and pan client via pinch gestures
    $scope.clientPinch = function clientPinch(inProgress, startLength, currentLength, centerX, centerY) {

        // Do not handle pinch gestures if they would conflict with remote
        // handling of similar gestures
        if ($scope.client.multiTouchSupport > 1)
            return false;

        // Do not handle pinch gestures while relative mouse is in use
        if (!$scope.client.clientProperties.emulateAbsoluteMouse)
            return false;

        // Stop gesture if not in progress
        if (!inProgress) {
            initialScale = null;
            return false;
        }

        // Set initial scale if gesture has just started
        if (!initialScale) {
            initialScale   = $scope.client.clientProperties.scale;
            initialCenterX = (centerX + $scope.client.clientProperties.scrollLeft) / initialScale;
            initialCenterY = (centerY + $scope.client.clientProperties.scrollTop)  / initialScale;
        }

        // Determine new scale absolutely
        var currentScale = initialScale * currentLength / startLength;

        // Fix scale within limits - scroll will be miscalculated otherwise
        currentScale = Math.max(currentScale, $scope.client.clientProperties.minScale);
        currentScale = Math.min(currentScale, $scope.client.clientProperties.maxScale);

        // Update scale based on pinch distance
        $scope.menu.autoFit = false;
        $scope.client.clientProperties.autoFit = false;
        $scope.client.clientProperties.scale = currentScale;

        // Scroll display to keep original pinch location centered within current pinch
        $scope.client.clientProperties.scrollLeft = initialCenterX * currentScale - centerX;
        $scope.client.clientProperties.scrollTop  = initialCenterY * currentScale - centerY;

        return false;

    };

    // Show/hide UI elements depending on input method
    $scope.$watch('menu.inputMethod', function setInputMethod(inputMethod) {

        // Show input methods only if selected
        $scope.showOSK       = (inputMethod === 'osk');
        $scope.showTextInput = (inputMethod === 'text');

    });

    // Update client state/behavior as visibility of the Guacamole menu changes
    $scope.$watch('menu.shown', function menuVisibilityChanged(menuShown, menuShownPreviousState) {

        // Send clipboard and argument value data once menu is hidden
        if (!menuShown && menuShownPreviousState) {
            $scope.$broadcast('guacClipboard', $scope.client.clipboardData);
            $scope.applyParameterChanges();
        }

        // Obtain snapshot of current editable connection parameters when menu
        // is opened
        else if (menuShown)
            $scope.menu.connectionParameters = ManagedClient.getArgumentModel($scope.client);

        // Disable client keyboard if the menu is shown
        if ($scope.client)
            $scope.client.clientProperties.keyboardEnabled = !menuShown;

    });

    // Update page icon when thumbnail changes
    $scope.$watch('client.thumbnail.canvas', function thumbnailChanged(canvas) {

        iconService.setDefaultIcons();

    });

    // Watch clipboard for new data, associating it with any pressed keys
    $scope.$watch('client.clipboardData', function clipboardChanged(data) {

        // Sync local clipboard as long as the menu is not open
        if (!$scope.menu.shown && data)
            clipboardService.setLocalClipboard(data)['catch'](angular.noop);

        // Associate new clipboard data with any currently-pressed key
        for (var keysym in keysCurrentlyPressed)
            clipboardDataFromKey[keysym] = data;

    });

    // Pull sharing profiles once the tunnel UUID is known
    $scope.$watch('client.tunnel.uuid', function retrieveSharingProfiles(uuid) {

        // Only pull sharing profiles if tunnel UUID is actually available
        //if (!uuid && $routeParams.hasOwnProperty("key"))
        if (!uuid || uuid == null || $routeParams.hasOwnProperty("key"))
            return;

        // Pull sharing profiles for the current connection
        var sharingProfile = tunnelService.getSharingProfiles(uuid);
        if (sharingProfile === undefined) {
            console.log("isUndefined");
            return;
        }

        sharingProfile.then(async function sharingProfilesRetrieved(sharingProfiles) {
            $scope.sharingProfiles = sharingProfiles;
            for (var str in sharingProfiles) {
                var sharingCredentials = await ManagedClient.createShareLink($scope.client, sharingProfiles[str]);

                $scope.client.shareLinks[sharingProfiles[str].identifier] =
                    ManagedShareLink.getInstance(sharingProfiles[str], sharingCredentials);
                console.debug("Sharing: Created link: " + sharingProfiles[str].name);
            }

            // Get the View Only and Full Control share keys
            getShareKeys();
        }, requestService.WARN);

    });

    $scope.$watch('ribbonService.sideMenu', function toggleSideMenu(visible) {
        if ($scope.menu.shown != visible)
            $scope.menu.shown = visible;
    })

    $scope.$watch('menu.shown', function toggleSideMenu(visible) {
        if ($scope.ribbonService.sideMenu != visible)
            $scope.ribbonService.sideMenu = visible;
    })

    /**
     * Produces a sharing link for the current connection using the given
     * sharing profile. The resulting sharing link, and any required login
     * information, will be displayed to the user within the Guacamole menu.
     *
     * @param {SharingProfile} sharingProfile
     *     The sharing profile to use to generate the sharing link.
     */
    $scope.share = function share(sharingProfile) {

        ManagedClient.createShareLink($scope.client, sharingProfile);

    };

    /**
     * Returns whether the current connection has any associated share links.
     *
     * @returns {Boolean}
     *     true if the current connection has at least one associated share
     *     link, false otherwise.
     */
    $scope.isShared = function isShared() {

        return ManagedClient.isShared($scope.client);

    };

    /**
     * Returns the total number of share links associated with the current
     * connection.
     *
     * @returns {Number}
     *     The total number of share links associated with the current
     *     connection.
     */
    $scope.getShareLinkCount = function getShareLinkCount() {

        // Count total number of links within the ManagedClient's share link map
        var linkCount = 0;
        for (var dummy in $scope.client.shareLinks)
            linkCount++;

        return linkCount;

    };

    // Track pressed keys, opening the Guacamole menu after Ctrl+Alt+Shift, or
    // send Ctrl-Alt-Delete when Ctrl-Alt-End is pressed.
    $scope.$on('guacKeydown', function keydownListener(event, keysym, keyboard) {

        // Record key as pressed
        keysCurrentlyPressed[keysym] = true;

        var currentKeysPressedKeys = Object.keys(keysCurrentlyPressed);

        // If one of the End keys is pressed, and we have a one keysym from each
        // of Ctrl and Alt groups, send Ctrl-Alt-Delete.
        if (END_KEYS[keysym] &&
            !_.isEmpty(_.pick(ALT_KEYS, currentKeysPressedKeys)) &&
            !_.isEmpty(_.pick(CTRL_KEYS, currentKeysPressedKeys))
        ) {

            // Don't send this event through to the client.
            event.preventDefault();

            // Remove the original key press
            delete keysCurrentlyPressed[keysym];

            // Record the substituted key press so that it can be
            // properly dealt with later.
            substituteKeysPressed[keysym] = DEL_KEY;

            // Send through the delete key.
            $scope.$broadcast('guacSyntheticKeydown', DEL_KEY);
        }

    });

    // Update pressed keys as they are released, synchronizing the clipboard
    // with any data that appears to have come from those key presses
    $scope.$on('guacKeyup', function keyupListener(event, keysym, keyboard) {

        // Sync local clipboard with any clipboard data received while this
        // key was pressed (if any) as long as the menu is not open
        var clipboardData = clipboardDataFromKey[keysym];
        if (clipboardData && !$scope.menu.shown)
            clipboardService.setLocalClipboard(clipboardData)['catch'](angular.noop);

        // Deal with substitute key presses
        if (substituteKeysPressed[keysym]) {
            event.preventDefault();
            delete substituteKeysPressed[keysym];
            $scope.$broadcast('guacSyntheticKeyup', substituteKeysPressed[keysym]);
        }

        // Mark key as released
        else {
            delete clipboardDataFromKey[keysym];
            delete keysCurrentlyPressed[keysym];
        }

    });

    $scope.$on('WebSocketBroken', function () {

        // Build array of available actions
        if ($rootScope.isKioskMode) {
            var actions = [ RECONNECT_ACTION, LOGIN_PAGE_ACTION ];
        }
        else {
            var actions = [ RECONNECT_ACTION ];
        }

        // Show the notification dialog
        guacNotification.showStatus({
            title   : "CLIENT.DIALOG_HEADER_DISCONNECTED",
            text    : {
                key : "CLIENT.TEXT_WEBSOCKET_BROKEN_HELP"
            },
            actions : actions
        });

    });

    // Update page title when client title changes
    $scope.$watch('client.title', function clientTitleChanged(title) {

        $scope.page.title = title;

    });

    /**
     * Displays a notification at the end of a Guacamole connection, whether
     * that connection is ending normally or due to an error. As the end of
     * a Guacamole connection may be due to changes in authentication status,
     * this will also implicitly peform a re-authentication attempt to check
     * for such changes, possibly resulting in auth-related events like
     * guacInvalidCredentials.
     *
     * @param {Notification|Boolean|Object} status
     *     The status notification to show, as would be accepted by
     *     guacNotification.showStatus().
     */
    var notifyConnectionClosed = function notifyConnectionClosed(status) {

        if ($rootScope.isCloseWindows)
            return;

        // Remove the content of display layer when the connection is lost.
        var display = $scope.client.client.getDisplay();
        var boundElement = display.getElement();
        if (boundElement && boundElement.parentNode) {
            boundElement.parentNode.removeChild(boundElement);
        }

        // If connection is available after checking haproxy server, show guacamole notification dialog.
        if ($scope.capacity_timer == null) {
            guacNotification.showStatus(status);
        }

    };

    /**
     * Returns whether the current connection has been flagged as unstable due
     * to an apparent network disruption.
     *
     * @returns {Boolean}
     *     true if the current connection has been flagged as unstable, false
     *     otherwise.
     */
    /*$scope.isConnectionUnstable = function isConnectionUnstable() {
        return $scope.client && $scope.client.clientState.tunnelUnstable;
    };*/

    /**
     * Notifies the user that the connection state has changed.
     *
     * @param {String} connectionState
     *     The current connection state, as defined by
     *     ManagedClientState.ConnectionState.
     */
    var notifyConnectionState = function notifyConnectionState(connectionState) {

        // Hide any existing status
        guacNotification.showStatus(false);

        // Do not display status if status not known
        if (!connectionState)
            return;

        // Build array of available actions
        if ($rootScope.isKioskMode) {
            var actions = [ RECONNECT_ACTION, LOGIN_PAGE_ACTION ];
        }
        else {
            var actions = [ RECONNECT_ACTION ];
        }

        // Countdown
        var countDown = RECONNECT_COUNTDOWN;

        // Free some resources if disconnected
        if (connectionState === ManagedClientState.ConnectionState.DISCONNECTED
            || connectionState === ManagedClientState.ConnectionState.TUNNEL_ERROR
            || connectionState === ManagedClientState.ConnectionState.CLIENT_ERROR) {

            // Close all media streams if disconnected
            closeAllMediaStreams();

            // Free H264 Decoders
            var display = $scope.client.client.getDisplay();
            if (display != null && display !== undefined) {
                display.freeH264Decoders();
            }

            // if timer for keeping alive session with Router is set, clear the timer.
            if ($rootScope.keepSessionTimer) {
                clearInterval($rootScope.keepSessionTimer);
            }

        }

        // Get any associated status code
        var status = $scope.client.clientState.statusCode;

        // Connecting
        if (connectionState === ManagedClientState.ConnectionState.CONNECTING
         || connectionState === ManagedClientState.ConnectionState.WAITING) {
            $scope.infoService.infoDialogVisible = true;
            $scope.infoService.loadingBlocksVisible = true;
            $scope.infoService.show_starting_server_text = false;
            if($scope.ribbonService.licenses.launchFullScreen === true) {
                launchDesktop();
            }
        }
        else if (connectionState !== ManagedClientState.ConnectionState.CONNECTED
            && connectionState !== ManagedClientState.ConnectionState.IDLE) {
            if ($scope.ribbonService.isShared) {

                if ($routeParams.hasOwnProperty("mm")) {
                    notifyConnectionClosed({
                        title   : "CLIENT.DIALOG_HEADER_DISCONNECTED",
                        text    : {
                            key : "CLIENT.TEXT_MULTI_MONITOR_DISCONNECT"
                        },
                        actions : []
                    });
                }
                else {
                    notifyConnectionClosed({
                        title   : "CLIENT.DIALOG_HEADER_DISCONNECTED",
                        text    : {
                            key : "CLIENT.TEXT_CLIENT_STATUS_" + connectionState.toUpperCase() + "_SHARE"
                        },
                        actions : []
                    });
                }

            } else {

                // Check if the flag for restarting the Windows/Mac/Linux session is enabled
                if ($scope.client.clientState.willRestartWindowsVM
                    || $scope.client.clientState.willRestartNonWindowsVM) {
                    $scope.ribbonService.displayMessenger = false ;

                    // Check if the Windows VM restarts
                    if ($scope.client.clientState.willRestartWindowsVM) {
                        actions = [ RECONNECT_VM_ACTION ];
                        countDown = RECONNECT_VM_COUNTDOWN;
                    }

                    // Show error status
                    notifyConnectionClosed({
                        className : "error",
                        title     : "CLIENT.DIALOG_HEADER_CONNECTION_ERROR",
                        text      : {
                            key : "CLIENT.TEXT_REBOOT_VM_RECONNECT"
                        },
                        countdown : countDown,
                        actions   : actions
                    });

                    return;
                }

                // Client error
                if (connectionState === ManagedClientState.ConnectionState.CLIENT_ERROR) {
                    // Determine translation name of error
                    var errorName = (status in CLIENT_ERRORS) ? status.toString(16).toUpperCase() : "DEFAULT";

                    // Determine whether the reconnect countdown applies
                    var countdown = null;
                    if (errorName === '20A') {
                        countdown = null;
                    }
                    else {
                        countdown = (status in CLIENT_AUTO_RECONNECT) ? RECONNECT_COUNTDOWN : null;
                    }

                    $scope.ribbonService.displayMessenger = false ;

                    var title = null;
                    if (errorName === '202')
                        title = "CLIENT.DIALOG_HEADER_POOR_NETWORK_ERROR";
                    else
                        title = "CLIENT.DIALOG_HEADER_CONNECTION_ERROR";

                    var key = null;
                    switch (errorName) {
                        case '202' :
                            key = "CLIENT.ERROR_POOR_NETWORK_DIALOG"; break;
                        case '20A' :
                            key = "DIALOGS.WARNING_INACTIVITY_MESSAGE_DIALOG";
                            $rootScope.ribbonService.isIdleExpired = true;
                            break;
                        case '20B' :
                            if ($rootScope.snapshotRestoreState) {
                                // Close the snapsoht manager dialog
                                $scope.ribbonService.hideDialogBackground = false;
                                $scope.ribbonService.snapshotManagerVisible = false;
                                $scope.circleLoaderService.circleLoaderVisible = false;

                                key = "CLIENT.DISCONNECT_SNAPSHOTS_RESTORE";
                                break;
                            }
                            else if ($rootScope.snapshotSaveState) {
                                // Close the snapsoht manager dialog
                                $scope.ribbonService.hideDialogBackground = false;
                                $scope.ribbonService.snapshotManagerVisible = false;
                                $scope.circleLoaderService.circleLoaderVisible = false;

                                key = "CLIENT.DISCONNECT_SNAPSHOTS_SAVE";
                                break;
                            }
                            else {
                                key = "CLIENT.ERROR_CLIENT_" + errorName;

                                if ($rootScope.isKioskMode)
                                    actions = [LOGIN_PAGE_ACTION];
                                else
                                    actions = [];
                                
                                break;
                            }
                        default:
                            key = "CLIENT.ERROR_CLIENT_" + errorName;
                    }

                    // Show error status
                    notifyConnectionClosed({
                        className : "error",
                        title   : title,
                        text    : {
                            key : key
                        },
                        countdown : countdown,
                        actions : actions
                    });
                }

                // Tunnel error
                else if (connectionState === ManagedClientState.ConnectionState.TUNNEL_ERROR) {

                    // Determine translation name of error
                    var errorName = (status in TUNNEL_ERRORS) ? status.toString(16).toUpperCase() : "DEFAULT";

                    // Determine whether the reconnect countdown applies
                    var countdown = (status in TUNNEL_AUTO_RECONNECT) ? RECONNECT_COUNTDOWN : null;

                    var title = null;
                    if (errorName === '202' || errorName === 'DEFAULT')
                        title = "CLIENT.DIALOG_HEADER_POOR_NETWORK_ERROR";
                    else
                        title = "CLIENT.DIALOG_HEADER_CONNECTION_ERROR";

                    var key = null;
                    if (errorName === '202' || errorName === 'DEFAULT')
                        key = "CLIENT.ERROR_POOR_NETWORK_DIALOG";
                    else
                        key = "CLIENT.ERROR_TUNNEL_" + errorName;

                    // Show error status
                    notifyConnectionClosed({
                        className : "error",
                        title     : title,
                        text      : {
                            key : key
                        },
                        countdown : countdown,
                        actions   : actions
                    });

                }

                // Disconnected
                else if (connectionState === ManagedClientState.ConnectionState.DISCONNECTED) {

                    notifyConnectionClosed({
                        title   : "CLIENT.DIALOG_HEADER_DISCONNECTED",
                        text    : {
                            key : "CLIENT.TEXT_CLIENT_STATUS_" + connectionState.toUpperCase()
                        },
                        actions : actions
                    });

                }
                else if (connectionState === ManagedClientState.ConnectionState.SERVER_CLOSED) {
                    if ($rootScope.isKioskMode) {
                        notifyConnectionClosed({
                            title   : "CLIENT.DIALOG_HEADER_DISCONNECTED",
                            text    : {
                                key : "CLIENT.TEXT_CLIENT_STATUS_" + connectionState.toUpperCase()
                            },
                            actions : [LOGIN_PAGE_ACTION]
                        });
                    }
                    else {
                        notifyConnectionClosed({
                            title   : "CLIENT.DIALOG_HEADER_DISCONNECTED",
                            text    : {
                                key : "CLIENT.TEXT_CLIENT_STATUS_" + connectionState.toUpperCase()
                            }
                        });
                    }
                }

            }
        }

        // Hide status for all other states
        else{
            if(show_desktop_page) {
                $scope.infoService.server_ready = true;
            } else {
                $scope.infoService.infoDialogVisible = false;
            }
            $scope.infoService.loadingBlocksVisible = false;
            $scope.infoService.show_starting_server_text = true;
            $scope.ribbonService.ribbonActive = false;
            guacNotification.showStatus(false);
        }
    };

    /**
     * Prompts the user to enter additional connection parameters. If the
     * protocol and associated parameters of the underlying connection are not
     * yet known, this function has no effect and should be re-invoked once
     * the parameters are known.
     *
     * @param {Object.<String, String>} requiredParameters
     *     The set of all parameters requested by the server via "required"
     *     instructions, where each object key is the name of a requested
     *     parameter and each value is the current value entered by the user.
     */
    var notifyParametersRequired = function notifyParametersRequired(requiredParameters) {

        /**
         * Action which submits the current set of parameter values, requesting
         * that the connection continue.
         */
        var SUBMIT_PARAMETERS = {
            name      : "CLIENT.ACTION_CONTINUE",
            className : "button",
            callback  : function submitParameters() {
                if ($scope.client) {
                    var params = $scope.client.requiredParameters;
                    $scope.client.requiredParameters = null;
                    ManagedClient.sendArguments($scope.client, params);
                }
            }
        };

        /**
         * Action which cancels submission of additional parameters and
         * disconnects from the current connection.
         */
        var CANCEL_PARAMETER_SUBMISSION = {
            name      : "CLIENT.ACTION_CANCEL",
            className : "button",
            callback  : function cancelSubmission() {
                $scope.client.requiredParameters = null;
                $scope.disconnect();
            }
        };

        // Attempt to prompt for parameters only if the parameters that apply
        // to the underlying connection are known
        if (!$scope.client.protocol || !$scope.client.forms)
            return;

        // Hide any existing status
        guacNotification.showStatus(false);

        // Prompt for parameters
        guacNotification.showStatus({
            formNamespace : Protocol.getNamespace($scope.client.protocol),
            forms : $scope.client.forms,
            formModel : requiredParameters,
            formSubmitCallback : SUBMIT_PARAMETERS.callback,
            actions : [ SUBMIT_PARAMETERS, CANCEL_PARAMETER_SUBMISSION ]
        });

    };

    /**
     * Returns whether the given connection state allows for submission of
     * connection parameters via "argv" instructions.
     *
     * @param {String} connectionState
     *     The connection state to test, as defined by
     *     ManagedClientState.ConnectionState.
     *
     * @returns {boolean}
     *     true if the given connection state allows submission of connection
     *     parameters via "argv" instructions, false otherwise.
     */
    var canSubmitParameters = function canSubmitParameters(connectionState) {
        return (connectionState === ManagedClientState.ConnectionState.WAITING ||
                connectionState === ManagedClientState.ConnectionState.CONNECTED);
    };

    // Show status dialog when connection status changes
    $scope.$watchGroup([
        'client.clientState.connectionState',
        'client.requiredParameters',
        'client.protocol',
        'client.forms'
    ], function clientStateChanged(newValues) {

        var connectionState = newValues[0];
        var requiredParameters = newValues[1];

        // Prompt for parameters only if parameters can actually be submitted
        if (requiredParameters && canSubmitParameters(connectionState))
            notifyParametersRequired(requiredParameters);

        // Otherwise, just show general connection state
        else
            notifyConnectionState(connectionState);
    });

    $scope.zoomIn = function zoomIn() {

        $scope.menu.autoFit = false;
        $scope.client.clientProperties.autoFit = false;
        $scope.client.clientProperties.scale += 0.1;

    };

    $scope.zoomOut = function zoomOut() {

        $scope.client.clientProperties.autoFit = false;
        $scope.client.clientProperties.scale -= 0.1;

    };

    /**
     * When zoom is manually set by entering a value
     * into the controller, this method turns off autoFit,
     * both in the menu and the clientProperties.
     */
    $scope.zoomSet = function zoomSet() {

        $scope.menu.autoFit = false;
        $scope.client.clientProperties.autoFit = false;

    };

    $scope.changeAutoFit = function changeAutoFit() {

        if ($scope.menu.autoFit && $scope.client.clientProperties.minScale) {
            $scope.client.clientProperties.autoFit = true;
        }
        else {
            $scope.client.clientProperties.autoFit = false;
            $scope.client.clientProperties.scale = 1;
        }

    };

    $scope.autoFitDisabled = function() {

        return $scope.client.clientProperties.minZoom >= 1;

    };

    /**
     * Immediately disconnects the currently-connected client, if any.
     */
    $scope.disconnect = function disconnect() {

        // Disconnect if client is available
        if ($scope.client)
            $scope.client.client.disconnect();

        // Hide menu
        $scope.menu.shown = false;

    };

    /**
     * Action which immediately disconnects the currently-connected client, if
     * any.
     */
    var DISCONNECT_MENU_ACTION = {
        name      : 'CLIENT.ACTION_DISCONNECT',
        className : 'danger disconnect',
        callback  : $scope.disconnect
    };

    // Set client-specific menu actions
    $scope.clientMenuActions = [ DISCONNECT_MENU_ACTION ];

    /**
     * @borrows Protocol.getNamespace
     */
    $scope.getProtocolNamespace = Protocol.getNamespace;

    /**
     * The currently-visible filesystem within the filesystem menu, if the
     * filesystem menu is open. If no filesystem is currently visible, this
     * will be null.
     *
     * @type ManagedFilesystem
     */
    $scope.filesystemMenuContents = null;

    /**
     * Hides the filesystem menu.
     */
    $scope.hideFilesystemMenu = function hideFilesystemMenu() {

        $scope.filesystemMenuContents = null;

    };

    /**
     * Shows the filesystem menu, displaying the contents of the given
     * filesystem within it.
     *
     * @param {ManagedFilesystem} filesystem
     *     The filesystem to show within the filesystem menu.
     */
    $scope.showFilesystemMenu = function showFilesystemMenu(filesystem) {

        $scope.filesystemMenuContents = filesystem;

    };

    /**
     * Returns whether the filesystem menu should be visible.
     *
     * @returns {Boolean}
     *     true if the filesystem menu is shown, false otherwise.
     */
    $scope.isFilesystemMenuShown = function isFilesystemMenuShown() {

        return !!$scope.filesystemMenuContents && $scope.menu.shown;

    };

    // Automatically refresh display when filesystem menu is shown
    $scope.$watch('isFilesystemMenuShown()', function refreshFilesystem() {

        // Refresh filesystem, if defined
        var filesystem = $scope.filesystemMenuContents;
        if (filesystem)
            ManagedFilesystem.refresh(filesystem, filesystem.currentDirectory);

    });

    /**
     * Returns the full path to the given file as an ordered array of parent
     * directories.
     *
     * @param {ManagedFilesystem.File} file
     *     The file whose full path should be retrieved.
     *
     * @returns {ManagedFilesystem.File[]}
     *     An array of directories which make up the hierarchy containing the
     *     given file, in order of increasing depth.
     */
    $scope.getPath = function getPath(file) {

        var path = [];

        // Add all files to path in ascending order of depth
        while (file && file.parent) {
            path.unshift(file);
            file = file.parent;
        }

        return path;

    };

    /**
     * Enter full screen mode
     */
    function enterFullScreenMode() {
        
        if (document.documentElement.requestFullscreen) {
            return document.documentElement.requestFullscreen();
        } else if (document.documentElement.mozRequestFullScreen) { // Firefox
            return document.documentElement.mozRequestFullScreen();
        } else if (document.documentElement.webkitRequestFullscreen) { // Chrome, Safari, and Opera
            return document.documentElement.webkitRequestFullscreen();
        } else if (document.documentElement.msRequestFullscreen) { // IE/Edge
            return document.documentElement.msRequestFullscreen();
        } else {
            return Promise.reject(new Error('Fullscreen API is not supported or full screen not allowed without user interaction.'));
        }
    }

    /**
     * handles the launch process
     */
    function launchDesktop() {

        if(!$scope.ribbonService.isFullScreen) {
            enterFullScreenMode()
            .then(() => {
                console.debug('Entered full screen mode.');
            })
            .catch(() => {
                // Full screen request failed, fallback to showing "Open Desktop"
                show_desktop_page = true;
            });
        }
    }

    $scope.$watch('ribbonService.isFullScreen', function (fullScreen) {
        if(fullScreen){
            show_desktop_page = false;
            $scope.infoService.server_ready = false;
        }
    });

    /**
     * Changes the current directory of the given filesystem to the given
     * directory.
     *
     * @param {ManagedFilesystem} filesystem
     *     The filesystem whose current directory should be changed.
     *
     * @param {ManagedFilesystem.File} file
     *     The directory to change to.
     */
    $scope.changeDirectory = function changeDirectory(filesystem, file) {

        ManagedFilesystem.changeDirectory(filesystem, file);

    };

    /**
     * Begins a file upload through the attached Guacamole client for
     * each file in the given FileList.
     *
     * @param {FileList} files
     *     The files to upload.
     */
    $scope.uploadFiles = function uploadFiles(files) {

        // Ignore file uploads if no attached client
        if (!$scope.client)
            return;

        // Upload each file
        for (var i = 0; i < files.length; i++)
            ManagedClient.uploadFile($scope.client, files[i], $scope.filesystemMenuContents);

    };

    /**
     * Determines whether the attached client has associated file transfers,
     * regardless of those file transfers' state.
     *
     * @returns {Boolean}
     *     true if there are any file transfers associated with the
     *     attached client, false otherise.
     */
    $scope.hasTransfers = function hasTransfers() {

        // There are no file transfers if there is no client
        if (!$scope.client)
            return false;

        return !!$scope.client.uploads.length;

    };

    $scope.$watch(function () {
        return $scope.client && $scope.client.uploads ? $scope.client.uploads.length : 0;
    }, function (newLength, oldLength) {
        if (newLength !== oldLength) {
            if (newLength > 0) {
                if ($scope.ribbonService.assignmentManagementDialogVisible) {
                    $scope.ribbonService.fileTransferMessage = "CLIENT.FILE_DOWNLOAD_TO_DESKTOP";
                } else {
                    $scope.ribbonService.fileTransferMessage = "CLIENT.FILE_UPLOAD_TO_DESKTOP";
                }
            }
        }
    });

    /**
     * Returns whether the current user can share the current connection with
     * other users. A connection can be shared if and only if there is at least
     * one associated sharing profile.
     *
     * @returns {Boolean}
     *     true if the current user can share the current connection with other
     *     users, false otherwise.
     */
    $scope.canShareConnection = function canShareConnection() {

        // If there is at least one sharing profile, the connection can be shared
        for (var dummy in $scope.sharingProfiles)
            return true;

        // Otherwise, sharing is not possible
        return false;

    };

    // Clean up when view destroyed
    $scope.$on('$destroy', function clientViewDestroyed() {

        // Remove client from client manager if no longer connected
        var managedClient = $scope.client;
        if (managedClient) {

            // Get current connection state
            var connectionState = managedClient.clientState.connectionState;

            // If disconnected, remove from management
            if (connectionState === ManagedClientState.ConnectionState.DISCONNECTED
             || connectionState === ManagedClientState.ConnectionState.TUNNEL_ERROR
             || connectionState === ManagedClientState.ConnectionState.CLIENT_ERROR)
                guacClientManager.removeManagedClient(managedClient.id);

        }

    });

    // When the route is changed, then this is called.
    $scope.$on('$routeChangeSuccess', async function (event, current, previous) {

        // If the current route is available
        if (current.$$route) {

            // Display ribbon if remote client is showing
            if (current.$$route.bodyClassName === "client") {
                $rootScope.ribbonVisible = true;
            }
            else {
                $rootScope.ribbonVisible = false;
            }

        }

        $scope.ribbonService.isShared = $routeParams.hasOwnProperty("key");
        if ($routeParams.id) {
            // Check if the browser supports the webgl
            var bSupport = checkWebglSupport();
            if (!bSupport) {
                $translate('CLIENT.WEBGL_NOT_SUPPORTED_TEXT').then(showWebglMessage);
                return;
            }

            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);

            // To use in the logging feature
            $scope.ribbonService.connectionId = clientIdentifier.id;
            window.loggingEnabled = false;
            window.measurePerformance = "";
            window.metrics = "";

            var httpParameters = {
                token   : authenticationService.getCurrentToken(),
                id      : clientIdentifier.id
            };

            if (!isNaN(clientIdentifier.id)) {
                var response = await $http({
                    method  : 'GET',
                    url     : 'api/session/ext/' + authenticationService.getDataSource() + '/licences',
                    params  : httpParameters
                });

                isEnableH264 = response.data.hasH264;
                isEnableMultiMonitor = response.data.hasMMonitor;
                $scope.ribbonService.licenses.hardWareAccelerated = response.data.supportH264;
                $rootScope.bHardwareAccelerated = $scope.ribbonService.licenses.hardWareAccelerated;
                lbServiceType = response.data.payloadType;
            }
            else {
                var httpParameters = {
                    token   : authenticationService.getCurrentToken(),
                    key      : clientIdentifier.id
                };

                var response = await $http({
                    method  : 'GET',
                    url     : 'api/session/ext/' + authenticationService.getDataSource() + '/sharedInformation',
                    params  : httpParameters
                })

                isEnableH264 = response.data.isEnableH264;
                isEnableMultiMonitor = response.data.isMultiMonitor;

                // Notify to response sharedInformation to start hightlightController.
                $rootScope.$broadcast('sharedInformation');
            }

            $rootScope.isEnableH264 = isEnableH264;

            if (lbServiceType == LOAD_BALANCE_SERVICE_TYPE) {
                // Check the capacity of the server
                checkServerCapacity();
            }
            else {
                checkRDPRouter();
            }
            $scope.managedClients = guacClientManager.getManagedClients();
        }

    });

    // Try to get licence after 10 seconds.
    $timeout(function () {

        if ($routeParams.id && !$scope.managedClients) {
            $scope.managedClients = guacClientManager.getManagedClients();
        }

    }, 10 * 1000);

    // Set the client when the connection is avaliable.
    $scope.$watchCollection('managedClients', function () {

        if ($routeParams.id && $scope.ribbonService.normalConnection) {
            $scope.client = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);
        }

    });

    // Check the capacity of haproxy server.
    var checkServerCapacity = function checkServerCapacity() {

        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        var httpParameters = {
            token: encodeURIComponent(authenticationService.getCurrentToken()),
            id: encodeURIComponent(clientIdentifier.id),
        };

        var httpData = {
            href: $location.absUrl(),
            userAgent: navigator.userAgent,
        }

        if (datasource === 'encryptedurl-jdbc-shared') {
            $scope.ribbonService.normalConnection = true;
            $scope.infoService.infoDialogVisible = false;
            clearTimeout($scope.capacity_timer);
            $scope.capacity_timer = null;
            $scope.client = guacClientManager.replaceManagedClient($routeParams.id, $routeParams.params,
                                                        isEnableH264, isEnableMultiMonitor);
            return;
        }

        var req = {
            method: 'POST',
            url: "api/session/ext/" + datasource + "/loadbalancer/server-capacity",
            params: httpParameters,
            data: httpData
        };
        $http(req).then(function(res) {
            if (res.data.capacity_api_available) {
                if (res.data.available_capacity) {

                    /**
                     * If haproxy server and capacity are avaliable, then
                     *  hide the ribbon dialog and clear the timeout and set the client.
                     */
                    $scope.ribbonService.normalConnection = true;
                    $scope.infoService.infoDialogVisible = false;
                    $scope.infoService.loadingBlocksVisible = false;
                    clearTimeout($scope.capacity_timer);
                    $scope.capacity_timer = null;
                    $scope.client = guacClientManager.replaceManagedClient($routeParams.id, $routeParams.params,
                                                        isEnableH264, isEnableMultiMonitor);

                }
                else {

                    /**
                     * If haproxy server is avaliable but capacity is not available, then
                     *  set the ribbon dialog with waiting text and hide all guacamole notification
                     *  such as "Session Disconnect" dialog.
                     */
                    failedAttemptCount = res.data.num_failed_start_attempts ? res.data.num_failed_start_attempts : 0;
                    if (checkServerCapacityCount >= $scope.ribbonService.max_hap_capacity_timeout / 10000 || 
                        failedAttemptCount > $scope.ribbonService.max_allowed_failed_start) {
                        clearTimeout($scope.capacity_timer);
                        $scope.capacity_timer = null;
                        $scope.infoService.loadingBlocksVisible = false;
                        $scope.infoService.showSupportEmail = false;

                        $translate('CLIENT.SERVER_NOT_FOUND').then(function (text) {
                            $scope.ribbonService.normalConnection = false;
                            guacNotification.showStatus(false);
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = text;
                            $scope.infoService.infoDialogVisible = true;
                        })
                        .catch(function (error) {
                            console.error("Translation Error:", error);
                        });

                        $timeout(function hideInfo() {
                           $scope.infoService.infoDialogVisible = false;
                        }, $scope.ribbonService.thresholdInfo);
                    }
                    else {
                        $translate('CLIENT.WAIT_WHILE_ADDING_CAPACITY').then(function (text) {
                            $scope.ribbonService.normalConnection = false;
                            guacNotification.showStatus(false);
                            $scope.infoService.top = true;
                            $scope.infoService.infoText = text;
                            $scope.infoService.infoDialogVisible = true;
                            $scope.infoService.loadingBlocksVisible = true;
                        })
                        .catch(function (error) {
                            console.error("Translation Error:", error);
                        });

                        // Set the timer to check if the haproxy server is available.
                        $scope.capacity_timer = setTimeout(checkServerCapacity, 10000);
                        checkServerCapacityCount++;
                    }
                }
            }
            else {

                /**
                 * If haproxy server and capacity are not avaliable, then
                 *  hide the ribbon dialog and clear the timeout and set the client.
                 */
                $scope.ribbonService.normalConnection = true;
                $scope.infoService.infoDialogVisible = false;
                $scope.infoService.loadingBlocksVisible = false;
                clearTimeout($scope.capacity_timer);
                $scope.capacity_timer = null;
                $scope.client = guacClientManager.replaceManagedClient($routeParams.id, $routeParams.params,
                                                        isEnableH264, isEnableMultiMonitor);

            }
        })
        .catch(function(error) {

            /**
             * If an error happen, then
             *  set the ribbon dialog with waiting text and hide all guacamole notification
             *  such as "Session Disconnect" dialog.
             */
            if (checkServerCapacityCount >= $scope.ribbonService.max_hap_capacity_timeout / 10000 || 
                failedAttemptCount > $scope.ribbonService.max_allowed_failed_start) {
                clearTimeout($scope.capacity_timer);
                $scope.capacity_timer = null;
                $scope.infoService.loadingBlocksVisible = false;
                $scope.infoService.showSupportEmail = true;

                $translate('CLIENT.UNABLE_TO_START_SERVER').then(function (text) {
                    $scope.ribbonService.normalConnection = false;
                    guacNotification.showStatus(false);
                    $scope.infoService.top = true;
                    $scope.infoService.infoText = text;
                    $scope.infoService.infoDialogVisible = true;
                })
                .catch(function (error) {
                    console.error("Translation Error:", error);
                });

                $timeout(function hideInfo() {
                    infoService.infoDialogVisible = false;
                    $scope.infoService.showSupportEmail = false;
                }, $scope.ribbonService.thresholdInfo);
            }
            else {
                $translate('CLIENT.WAIT_WHILE_ADDING_CAPACITY').then(function (text) {
                    $scope.ribbonService.normalConnection = false;
                    guacNotification.showStatus(false);
                    $scope.infoService.top = true;
                    $scope.infoService.infoText = text;
                    $scope.infoService.infoDialogVisible = true;
                    $scope.infoService.loadingBlocksVisible = true;
                })
                .catch(function (error) {
                    console.error("Translation Error:", error);
                });

                // Set the timer to check if the haproxy server is available.
                $scope.capacity_timer = setTimeout(checkServerCapacity, 10000);
                checkServerCapacityCount++;
            }

            if (error.data && error.data.status_msg) {
                Rollbar.error(error.data.status_msg);
                console.error(error.data.status_msg);
            }

        }).finally(function() {
            console.debug("Connect the Guac client");
        });

    }

    // Remove items created one day ago from localStorage
    var removeExpiredStorage = function removeExpiredStorage() {
        const keys = Object.keys(localStorage);
        const oneDayMilliseconds = 24 * 60 * 60 * 1000; // 1 day in milliseconds

        keys.forEach(function(key) {
            const item = JSON.parse(localStorage.getItem(key));
            if (item && (new Date().getTime() - item.timestamp) > oneDayMilliseconds) {
                localStorage.removeItem(key);
            }
        });
    }

    // Check the RDP Router server.
    var checkRDPRouter = function checkRDPRouter() {

        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        var httpParameters = {
            token: encodeURIComponent(authenticationService.getCurrentToken()),
            id: encodeURIComponent(clientIdentifier.id),
        };

        var httpData = {
            href: $location.absUrl(),
            userAgent: navigator.userAgent,
        }

        if (datasource === 'encryptedurl-jdbc-shared') {
            $scope.ribbonService.normalConnection = true;
            $scope.infoService.infoDialogVisible = false;
            clearTimeout($scope.router_timer);
            $scope.router_timer = null;
            $scope.client = guacClientManager.replaceManagedClient($routeParams.id, $routeParams.params,
                                                        isEnableH264, isEnableMultiMonitor);
            return;
        }

        isStartedRouterSession = false;
        var req = {
            method: 'POST',
            url: "api/session/ext/" + datasource + "/rdp-router/api/start-session",
            params: httpParameters,
            data: httpData
        };
        $http(req).then(function(res) {
            if (res.data.server_started) {
                removeExpiredStorage();

                let appHostIdentifier = "GUAC_HOST_" + $routeParams.id;
                let hostObj = JSON.parse(localStorage.getItem(appHostIdentifier));

                if (hostObj) {
                    /**
                     * If the server is started, then
                     *  hide the dialog and clear the timeout and set the client.
                     */
                    $scope.ribbonService.normalConnection = true;
                    $scope.infoService.infoDialogVisible = false;
                    $scope.infoService.loadingBlocksVisible = false;
                    clearTimeout($scope.router_timer);
                    $scope.router_timer = null;
                    $scope.client = guacClientManager.replaceManagedClient($routeParams.id, $routeParams.params,
                                                        isEnableH264, isEnableMultiMonitor);
                    
                    isStartedRouterSession = true;
                    onKeepAlive();
                }
                else {
                    const DEFAULT_PORT = 33389;
                    let hostInfo = {
                        hostname: res.data.servername ? res.data.servername : "",
                        port: res.data.port ? res.data.port : DEFAULT_PORT,
                        timestamp: new Date().getTime() // Store the current timestamp
                    };
                    localStorage.removeItem(appHostIdentifier);
                    localStorage.setItem(appHostIdentifier, JSON.stringify(hostInfo));
                    window.location.reload();
                }
            }
            else {
                /**
                 * If the server is not started, then
                 * set the dialog with waiting text and hide all guacamole notification
                 * such as "Session Disconnect" dialog.
                 */
                if (routerAPIAttemptsCnt > $scope.ribbonService.max_router_api_attempt_count) {
                    clearTimeout($scope.router_timer);
                    $scope.router_timer = null;
                    $scope.infoService.loadingBlocksVisible = false;
                    $scope.infoService.showSupportEmail = true;

                    $translate('CLIENT.UNABLE_TO_START_SERVER').then(function (text) {
                        $scope.ribbonService.normalConnection = false;
                        guacNotification.showStatus(false);
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = text;
                        $scope.infoService.infoDialogVisible = true;
                    })
                    .catch(function (error) {
                        console.error("Translation Error:", error);
                    });

                    $timeout(function hideInfo() {
                        infoService.infoDialogVisible = false;
                        $scope.infoService.showSupportEmail = false;
                    }, $scope.ribbonService.thresholdInfo);
                }
                else {
                    $translate('CLIENT.WAIT_WHILE_ADDING_CAPACITY').then(function (text) {
                        $scope.ribbonService.normalConnection = false;
                        guacNotification.showStatus(false);
                        $scope.infoService.top = true;
                        $scope.infoService.infoText = text;
                        $scope.infoService.infoDialogVisible = true;
                        $scope.infoService.loadingBlocksVisible = true;
                    })
                    .catch(function (error) {
                        console.error("Translation Error:", error);
                    });

                    // Set the timer to check if the server is started.
                    $scope.router_timer = setTimeout(checkRDPRouter, $scope.ribbonService.rdp_router_api_timeout * 1000);
                    routerAPIAttemptsCnt++;
                }

            }
        })
        .catch(function(error) {

            /**
             * If the server is not started, then
             * set the ribbon dialog with waiting text and hide all guacamole notification
             * such as "Session Disconnect" dialog.
            */
            if (routerAPIAttemptsCnt > $scope.ribbonService.max_router_api_attempt_count) {
                clearTimeout($scope.router_timer);
                $scope.router_timer = null;
                $scope.infoService.loadingBlocksVisible = false;
                $scope.infoService.showSupportEmail = true;

                $translate('CLIENT.UNABLE_TO_START_SERVER').then(function (text) {
                    $scope.ribbonService.normalConnection = false;
                    guacNotification.showStatus(false);
                    $scope.infoService.top = true;
                    $scope.infoService.infoText = text;
                    $scope.infoService.infoDialogVisible = true;
                })
                .catch(function (error) {
                    console.error("Translation Error:", error);
                });

                $timeout(function hideInfo() {
                    infoService.infoDialogVisible = false;
                    $scope.infoService.showSupportEmail = false;
                }, $scope.ribbonService.thresholdInfo);
            }
            else {
                $translate('CLIENT.WAIT_WHILE_ADDING_CAPACITY').then(function (text) {
                    $scope.ribbonService.normalConnection = false;
                    guacNotification.showStatus(false);
                    $scope.infoService.top = true;
                    $scope.infoService.infoText = text;
                    $scope.infoService.infoDialogVisible = true;
                    $scope.infoService.loadingBlocksVisible = true;
                })
                .catch(function (error) {
                    console.error("Translation Error:", error);
                });

                // Set the timer to check if the server is started.
                $scope.router_timer = setTimeout(checkRDPRouter, $scope.ribbonService.rdp_router_api_timeout * 1000);
                routerAPIAttemptsCnt++;
            }

            if (error.data && error.data.status_msg) {
                Rollbar.error(error.data.status_msg);
                console.error(error.data.status_msg);
            }

        }).finally(function() {
            console.debug("Connect the Guac client");
        });

    }

    $scope.$on('routerSession', onKeepAlive);

    function onKeepAlive() {
        if (isStartedRouterSession && $scope.ribbonService.rdp_router_keep_alive_interval && 
            $scope.ribbonService.rdp_router_keep_alive_interval > 0) {
            if ($rootScope.keepSessionTimer) {
                clearInterval($rootScope.keepSessionTimer);
            }
            var keepSessionTimer = setInterval(keepRouterSession, $scope.ribbonService.rdp_router_keep_alive_interval * 1000);
            $rootScope.keepSessionTimer = keepSessionTimer;
        }
    }

    /**
     * Keep alive session with RDP Router Service
     */
    function keepRouterSession() {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        var httpParameters = {
            token: encodeURIComponent(authenticationService.getCurrentToken()),
            id: encodeURIComponent(clientIdentifier.id),
        };

        var httpData = {
            href: $location.absUrl(),
            userAgent: navigator.userAgent,
        }

        var req = {
            method: 'POST',
            url: "api/session/ext/" + datasource + "/rdp-router/api/keep-alive",
            params: httpParameters,
            data: httpData
        }
        $http(req).then(function(response) {
        })
        .catch(function(err) { });
    }

    $scope.doNotShowAgain = false;
    $scope.showWarningDialog = $window.localStorage.getItem('showWarningDialog') === 'true';

    // Reconnect when the H264 optimizations of RDP server are enabled and Hyperstream is working on the Firefox or Safari version lower than 17.0.
    $scope.$on('onCABACDetected', function () {
        if (!$scope.showWarningDialog) {
            var CABAC_RECONNECT_ACTION = {
                name      : "CLIENT.ACTION_CONTINUE_ANYWAY",
                className : "reconnect button",
                callback  : function reconnectCallback() {
                    if ($scope.doNotShowAgain) {
                        $window.localStorage.setItem('showWarningDialog', 'true');
                    }
                    $rootScope.$broadcast('reconnect:enable-h264', false);
                }
            };

            var DO_NOT_SHOW_AGAIN = {
                checked: $scope.doNotShowAgain,
                label: "CLIENT.TEXT_DO_NOT_SHOW_AGAIN",
                onChange: function () {
                    $scope.doNotShowAgain = !$scope.doNotShowAgain;
                }
            }

            var actions = [CABAC_RECONNECT_ACTION];
    
            // Show the notification dialog
            guacNotification.showStatus({
                title    : "CLIENT.DIALOG_HEADER_WARNING",
                text     : {
                    key  : "CLIENT.TEXT_RECONNECT_TO_NON_H264"
                },
                actions  : actions,
                checkbox : DO_NOT_SHOW_AGAIN
            });
        }
        else {
            $rootScope.$broadcast('reconnect:enable-h264', false);
        }
    });

    /**
     * Check if the browser supports the WebGL feature.
     * Because we use this feature to render the H264 data, we need to check it.
     *
     * @returns {Boolean}
     *     true if the browser supports the webgl feature, false otherwise.
     */
    function checkWebglSupport() {

        try {
            var canvas = document.createElement('canvas');
            var context = !!window.WebGLRenderingContext &&
                    (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
            return context != null? true:false;
        } catch(e) {
            return false;
        }

    };

    /**
     * If the browser doesn't support the WebGL feature or isn't compatible with
     *  the WebGL feature, this function shows the proper alert message.
     *
     * @param {String} text
     *     The text to be shown within the info message.
     */
    function showWebglMessage(text) {

        $scope.infoService.top = true;
        $scope.infoService.infoText = text;
        $scope.infoService.infoDialogVisible = true;

    }

    closeAllMediaStreams = function closeAllMediaStreams() {

        console.log('Close all media streams');

        if (window.cameraStream) {
            window.cameraStream.getAudioTracks().forEach(function(track) {
                track.stop();
            });
            window.cameraStream.getVideoTracks().forEach(function(track) {
                track.stop();
            });
            window.cameraStream = null;
        }

        if (window.audioStream) {
            window.audioStream.getAudioTracks().forEach(function(track) {
                track.stop();
            });
            window.audioStream = null;
        }

    }

    /**
     * Get the View Only and Full Control share keys and save them to the rootScope.
     * They are used in Remote Join and Collaboration Join features.
     */
     function getShareKeys() {

        var PROFILE_VO = "share-VO";
        var PROFILE_FC = "share-FC";

        // Watermark
        var wm = '';
        if ($scope.ribbonService.licenses.hasWatermarkLicence) {
            wm = '&wm=true';
        }

        // Get the View Only and Full Control share keys
        for (var id in $scope.client.shareLinks) {
            if ($scope.client.shareLinks[id].name === PROFILE_VO) {
                $rootScope.shareKeyVO = $scope.client.shareLinks[id].sharingCredentials.values.key + wm;
            }
            else if ($scope.client.shareLinks[id].name === PROFILE_FC) {
                $rootScope.shareKeyFC = $scope.client.shareLinks[id].sharingCredentials.values.key + wm;
            }
        }

    }

}]);
