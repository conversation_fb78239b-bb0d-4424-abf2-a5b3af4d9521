/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays informational messages
 */
angular.module('core').directive('infoMessage', [function infoMessage() {

    return {
        restrict: 'E',
        scope: {
            variant: '@'
        },

        templateUrl: 'app/ext/core/templates/info-message.html',
        controller: ['$scope', '$injector', function infoMessageController($scope, $injector) {

            $scope.infoService = $injector.get('infoService');
            $scope.ribbonService = $injector.get('ribbonService');
            $scope.hide = function hide() {
            	$scope.infoService.infoDialogVisible = false;
                $scope.infoService.loadingBlocksVisible = false;
            }

            $scope.openDesktop = function openDesktop() {
                var elem = document.documentElement;
                if (elem.requestFullscreen) {
                    elem.requestFullscreen();
                } else if (elem.mozRequestFullScreen) { // For Firefox
                    elem.mozRequestFullScreen();
                } else if (elem.webkitRequestFullscreen) { // For Chrome, Safari, and Opera
                    elem.webkitRequestFullscreen();
                } else if (elem.msRequestFullscreen) { // For IE/Edge
                    elem.msRequestFullscreen();
                }
                
                $scope.infoService.infoDialogVisible = false;
                $scope.infoService.server_ready = false;
            }
       }] // end info message controller

    };
}]);
