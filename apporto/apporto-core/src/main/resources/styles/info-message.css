/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#StartMezSpan{
  color:#1A1A1A;
  font-family: Lato;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.24px;
  margin: 8px 0px 12px 0px;
}

#StartMezSpanVC{
  color:#FFFFFF;
  font-family: Lato;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: 0.24px;
  margin: 8px 0px 12px 0px;
}

#loadingAnimation{
  width: 440px;
  height: 82px;
}

#waitingMezSpan{
  width: 442px;
  text-align: center;
  text-wrap: wrap;
  font-family: Lato;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: #1A1A1A;
  line-height: 34px;
}

#waitingMezSpanVC{
  width: 442px;
  text-align: center;
  text-wrap: wrap;
  font-family: Lato;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 34px;
}

#waitingMessage{
  width: 100vw !important;
  height: 100vh !important;
  position: fixed;
  left: 0px !important;
  top: 0px !important;
  background-color: #f7f8ff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;       
}
 
#waitingVC{
  width: 100vw !important;
  height: 100vh !important;
  position: fixed;
  left: 0px !important;
  top: 0px !important;
  background-color: #10161E;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;       
}

#readyMessage{
  width: 100vw !important;
  height: 100vh !important;
  position: fixed;
  left: 0px !important;
  top: 0px !important;
  background-color: #f7f8ff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;       
}
 
#readyVC{
  width: 100vw !important;
  height: 100vh !important;
  position: fixed;
  left: 0px !important;
  top: 0px !important;
  background-color: #10161E;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;       
}

.info-dialog {
  display: flex;
  max-width: 100%;
  max-height: 3in;
  padding: 0px 8px;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  border: 1px solid var(--Accent-Blue, #CED3D9);
  background: #FFF;
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 20;
}

.info-message {
  margin-bottom: 10px;
  margin-left: 10px;
  margin-top: 10px;
  margin-right: 10px;
  text-align: center;
}

.info-message .info-message-content{
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-message .info-message-content .ads_click {
  width: 24px;
  height: 24px;
  background-image: url('app/ext/ribbon/images/ads_click.png');
}

.info-message .info-message-content .message {
  white-space: pre-wrap;
  color: #1A1A1A;
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: Lato;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.info-message .bottom-message-content{
  display: flex;
  align-items: center;
  gap: 8px;
}

.bottom-message-content .groups_black {
  width: 24px;
  height: 24px;
  background-image: url('app/ext/ribbon/images/groups_black.png');
}

.bottom-message-content .close-message {
  width: 24px;
  height: 24px;
  background-image: url('app/ext/ribbon/images/share_close.svg');
}

.bottom-message-content .bottom-message {
  white-space: pre-wrap;
  color: #1A1A1A;
  font-feature-settings: 'clig' off, 'liga' off;
  font-family: Lato;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.info-message:focus-visible {
  outline: none;
}

.waiting-message {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.ready-message {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
}

.info-text {
  white-space: pre-wrap;
}

.info-message .info-message-content .info-email {
  color: rgba(0,123,255,1);
}

.info-dialog-top {
  background-color: rgb(224, 232, 243);
  border: 3px white;
  border-style: groove;
  max-width: 100%;
  max-height: 3in;
  position: fixed;
  right: 0;
  width: 5in;
  z-index: 20;
  top: 4%;
  left: calc((100vw - 5in) / 2);
}

#load {
  position:relative;
  width:480px;
  height:82px;
  overflow:visible;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  cursor:default;
  overflow: hidden;
  background: #F6FAFF;
}

#loadVC {
  position:relative;
  width:480px;
  height:82px;
  overflow:visible;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  cursor:default;
  overflow: hidden;
  background: #10161E;
}
  
#load div {
  position:absolute;
  top: 22px;
  width:40px;
  height:40px;
  animation: move_block 14s linear infinite forwards;
  color:#35C4F0;
  opacity: 0;
}

#loadVC div {
  position:absolute;
  top: 22px;
  width:40px;
  height:40px;
  animation: move_block 14s linear infinite forwards;
  color:#35C4F0;
  opacity: 0;
}

.running_block {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  min-width: 40px;
  animation-delay: calc(var(--i) * -1.4s) !important;
}
 
#left_block{
  position: absolute;
  left: 0px;
  top: 0px;
  height: 82px;
  width: 179px;
  background: linear-gradient(270deg, rgba(246, 250, 255, 0.00) 0%, #F6FAFF 93.85%);
  z-index: 2;
}

#left_block_VC{
  position: absolute;
  left: 0px;
  top: 0px;
  height: 82px;
  width: 179px;
  background: linear-gradient(270deg, rgba(246, 250, 255, 0.00) 0%, #10161E 93.85%);
  z-index: 2;
}

#right_block{
  position: absolute;
  right: 0px;
  top: 0px;
  height: 82px;
  width: 179px;
  background: linear-gradient(-270deg, rgba(246, 250, 255, 0.00) 0%, #F6FAFF 93.85%);
  z-index: 2;
}

#right_block_VC{
  position: absolute;
  right: 0px;
  top: 0px;
  height: 82px;
  width: 179px;
  background: linear-gradient(-270deg, rgba(246, 250, 255, 0.00) 0%, #10161E 93.85%);
  z-index: 2;
}

#serverLoaded {
  height: 38px;
  width: 59px;
}

.server-ready {
  height: 38px;
  width: 59px;
  background-image: url(app/ext/ribbon/images/server_ready.png);
}

.open-desktop-btn {
  height: 44px;
  padding: 12px;
  border-radius: 8px;
  opacity: 1;
  background: #22538F;
  color: white;
  border: none;
  cursor: pointer;
}

.open-desktop-btn:hover {
  border-radius: 8px !important;
  background-color: #1a3d6a !important;
}

.open-desktop-btn img.btn-icon {
  width: 24px;
  height: 24px;
  margin-left: 8px;
  display: inline-block;
}

@keyframes move_block {
  0% {
    left:110%;
    opacity:0;
  }
  2%{
      left: 110%;
      opacity:0.7;
  }
  50% {
    opacity:1;
    left: 50%;
  }
  98% {
      opacity:0.7;
      left:-10%; 
  }
  100%{
      opacity: 0;
      left: -10%;
  }
}
