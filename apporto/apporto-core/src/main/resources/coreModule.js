/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/************************************************************/
/* This modulre should contain some core functionalities of */
/* the Apporto product. At this moment, it doesn't contain  */
/* anything.                                                */
/************************************************************/

angular.module('core').
config(function() {

        /**
         * Removed in Guacamole 1.1. A new feature has been added in Base, inserting thumbnail icon that show 
         * miniaturized desktop. This can be changes / dissabled in ManagedClientDecorator.
         *
         *
         * document.head.removeChild(document.head.children[5]);
         * document.head.removeChild(document.head.children[5]);
         * document.head.removeChild(document.head.children[5]);
         **/

        var head_html =
            "<link rel=\"apple-touch-icon\" sizes=\"57x57\" href=\"app/ext/core/images/favicons/apple-icon-57x57.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"60x60\" href=\"app/ext/core/images/favicons/apple-icon-60x60.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"72x72\" href=\"app/ext/core/images/favicons/apple-icon-72x72.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"76x76\" href=\"app/ext/core/images/favicons/apple-icon-76x76.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"114x114\" href=\"app/ext/core/images/favicons/apple-icon-114x114.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"120x120\" href=\"app/ext/core/images/favicons/apple-icon-120x120.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"144x144\" href=\"app/ext/core/images/favicons/apple-icon-144x144.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"152x152\" href=\"app/ext/core/images/favicons/apple-icon-152x152.png\">" + "\n" +
            "<link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"app/ext/core/images/favicons/apple-icon-180x180.png\">" + "\n" +
            "<link rel=\"shortcut icon\" type=\"image/png\" sizes=\"192x192\" href=\"app/ext/core/images/favicons/android-icon-192x192.png\">" + "\n" +
            "<link rel=\"shortcut icon\" type=\"image/png\" sizes=\"32x32\" href=\"app/ext/core/images/favicons/favicon-32x32.png\">" + "\n" +
            "<link rel=\"shortcut icon\" type=\"image/png\" sizes=\"96x96\" href=\"app/ext/core/images/favicons/favicon-96x96.png\">" + "\n" +
            "<link rel=\"shortcut icon\" type=\"image/png\" sizes=\"16x16\" href=\"app/ext/core/images/favicons/favicon-16x16.png\">" + "\n" +
            "<link rel=\"manifest\" href=\"app/ext/core/images/favicons/manifest.json\">" + "\n" +
            "<meta name=\"msapplication-TileColor\" content=\"#ffffff\">" + "\n" +
            "<meta name=\"msapplication-TileImage\" content=\"app/ext/core/images/favicons/ms-icon-144x144.png\">" + "\n" +
            "<meta name=\"theme-color\" content=\"#ffffff\">" + "\n";
        document.head.insertAdjacentHTML('beforeend', head_html);

}).
run(function() {
});

// Ensure the core module is loaded along with the rest of the app
angular.module('index').requires.push('core');

