/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * The utilityService.js file is a good place to put utility functions that are used throughout the application.
 */
angular.module('core').factory('utilityService',
    [ function utilityService() {

    var utilityService = {};


    /**
     * Counts the number of valid tokens in an array, excluding "_" values.
     *
     * @param {Array} array - The array to process. If null, undefined, or not an array, returns 0.
     * @returns {number} The count of elements that are not equal to "_".
     */
    utilityService.countTokens = function countTokens(array) {
        if (array == null || !Array.isArray(array)) {
            console.warn("utilityService.countTokens: Expected an array but received:", array);
            return 0; // Return 0 for non-array inputs to prevent runtime errors.
        }
        return array.filter(item => item !== "_").length;
    }

    return utilityService;
} ]);
