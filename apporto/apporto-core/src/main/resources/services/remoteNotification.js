/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for remote notification
 */
angular.module('core').factory('remoteNotificationService',
    [ '$injector', '$timeout', '$rootScope', 'webNotification',
            function remoteNotificationService($injector, $timeout, $rootScope, webNotification) {

    var authenticationService = $injector.get('authenticationService');
    var ClientIdentifier      = $injector.get('ClientIdentifier');
    var connectionService     = $injector.get('connectionService');
    var $routeParams          = $injector.get('$routeParams');
    var $http                 = $injector.get('$http');
    var $q                    = $injector.get('$q');
    var infoService           = $injector.get('infoService');
    var guacNotification      = $injector.get('guacNotification');
    var ribbonService         = $injector.get('ribbonService');
    $rootScope.ribbonService  = $injector.get('ribbonService');
    var $location             = $injector.get('$location');
    var $translate            = $injector.get('$translate');

    var notificationEventSource = null;
    var reconnectFrequency = 1;
    var timeoutReconnect = null;
    $rootScope.isClassroom = false;

    var MessageLevelCode = Object.freeze({
        NOTIFY  : 1,
        WARNING : 2,
        SEVERE  : 3,
        NONE    : 4,
        UNKNOWN : 5
    });

    var MessageCommandCode = Object.freeze({
        KILL               : 1,
        CLASSROOM          : 2,
        UNKNOWN            : 3,
        END                : 4,
        SYNC               : 5,
        REMOTE_JOIN        : 6,
        COLLABORATION_JOIN : 7
    });

    var delayTimeout = false;
    var service = {}

    function getClientId(datasource, clientIdentifier) {
        var deffered = $q.defer();

        if (datasource === "encryptedurl-jdbc") {
            deffered.resolve(clientIdentifier.id);
        }
        else if (datasource && datasource !== "undefined" || !!clientIdentifier.dataSource) {
            connectionService.getConnection(datasource, clientIdentifier.id)
            .then(function connectionRetrieved(connection) {
                deffered.resolve(connection.name);
            });
        }

        return deffered.promise;
    };

    /**
     * Cancel existing reconnections if any and try to start service.
     */
    service.start = function start() {
        if (timeoutReconnect != null) {
            $timeout.cancel(timeoutReconnect);
            timeoutReconnect = null;
        }

        startNotificationService();
    }

    /**
     * Subscribe to remote notification via SSE.
     */
    function startNotificationService() {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        if (clientIdentifier == null) {
            console.warn("Notifications: Cannot start notification service, " +
                            "no clientIdentitier available");
            return;
        }

        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        if (notificationEventSource == null && !$routeParams.hasOwnProperty("key")) {
            if ($location.path().indexOf("classroom") > -1) {
                console.warn("Notifications: Cannot start notification service in classroom");
                return;
            }

            getClientId(datasource, clientIdentifier)
            .then(function (clientId) {
                notificationEventSource = new EventSource("api/session/ext/" +
                                                datasource + "/notify/subscribe" + "?token=" +
                                                authenticationService.getCurrentToken() + "&id=" +
                                                clientId);
                notificationEventSource.onopen = onOpen;
                notificationEventSource.onmessage = onNotification;
                notificationEventSource.onerror = onError;
            })
            .catch(function (error) {
                console.debug(error);
            });
        }
    }

    /**
     * Cancel existing reconnections if any and try to stop service.
     */
    service.stop = function stop() {
        if (timeoutReconnect != null) {
            $timeout.cancel(timeoutReconnect);
            timeoutReconnect = null;
        }

        stopNotificationService();
    }

    /**
     * Unsubscribe from remote notification
     */
    function stopNotificationService() {
        if (notificationEventSource != null) {
            // Unsubscribe
            notificationEventSource.close();
            notificationEventSource = null;

            // Stop event thread on server
            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            if (clientIdentifier == null) {
                console.warn("Notifications: Cannot stop notification service on server, " +
                                "no clientIdentitier available");
                return;
            }

            var datasource = encodeURIComponent(clientIdentifier.dataSource);
            var params = "?token=" + authenticationService.getCurrentToken();

            params = params + "&id=" + clientIdentifier.id;
            navigator.sendBeacon("api/session/ext/" + datasource + "/notify/unsubscribe" + params);

            console.debug("Notifications: Remote notifications disabled");
        }
    }

    /**
     * When event source is opened, set reconnect frequency to 1 second.
     * This will ensure that further connection errors will try to reconnect immediately.
     */
    function onOpen(event) {
        reconnectFrequency = 1;
    }

    /**
     * Parse the incomming notification and handle it.
     */
    function onNotification(event) {
        data = JSON.parse(event.data);
        if (data.hasOwnProperty("initMessage")) {
            console.log("Notifications: " + data.initMessage);
        }

        handleRemoteNotification(data);
    }

    /**
     * In case of an error, stop the event source and try to create a new one
     * after reconnectFrequency seconds.
     *
     * If there are consecutive errors with connection, like the EventSource cannot be created at all,
     * onError function will be called each time. onOpen function is called only when EventSource is
     * successfully created.
     */
    function onError(event) {
        stopNotificationService();
        timeoutReconnect = $timeout(reconnect, reconnectFrequency * 1000);
    }

    /**
     * Tries to reconnect to the EventSource.
     * reconnectFrequency is doubled each time, to keep server from overloading in case of some
     * long-term error.
     *
     * When the EventSource is successfully opened, reconnectFrequency will be reset to 1s.
     */
    function reconnect() {
        startNotificationService();

        reconnectFrequency = reconnectFrequency * 2;
        if (reconnectFrequency >= 64) {
            reconnectFrequency = 64;
        }
    }

    /**
     * Handles remote notifications.
     *
     * There are several cases, in general, there should be notification to the user that
     * creates info dialogs, or the commands that should be executed on the client.
     *
     * Due to the historic reasons, those are mixed at the moment.
     *
     * General approach should be:
     * - use MessageLevelCode NOTIFY or WARNING for displaying info messages or dialogs.
     * - use MessageLevelCode NONE and MessageCommandCode for executing commands.
     */
    function handleRemoteNotification(data) {
        switch (data.level) {
            case MessageLevelCode.NOTIFY: {
                // Classroom title-like handling on level NOTIFY is probably obsolete and not
                // used anymore, but is kept here until confirmed that it is not needed anymore.
                if (data.title === 'Classroom display') {
                    data.delay = 20000;
                    $rootScope.ribbonService.cursor_name = "";

                    if (data.data) {
                        var params = JSON.parse(atob(decodeURIComponent(data.data)));
                        var uuid = params.uuid;
                        var index = $rootScope.ribbonService.openedClassroom.indexOf(uuid);
                        if (index == -1) {
                            $rootScope.ribbonService.openedClassroom.push(uuid);
                        }
                    }

                    if ($rootScope.ribbonService.openedClassroom.length > 0) {
                        $rootScope.visibleClassroomView = true;
                        $rootScope.ribbonService.activeClass = true;
                        $rootScope.$broadcast('classroom:start', params);

                        // This code belongs here.
                        infoService.infoText = data.text;
                        infoService.infoDialogVisible = true;
                        infoService.top = true;
                        $timeout(function hideInfo() {
                            infoService.infoDialogVisible = false;
                        }, ribbonService.thresholdInfo);
                    }

                    return;
                }
                else if (data.title === 'Classroom group') {
                    $rootScope.selectedGroup = data.text;
                    $timeout(function () { $rootScope.$broadcast('$changedGroup'); });
                    return;
                }
                else if (data.title === 'closedClassroom') {
                    var uuid = data.data;
                    var index = $rootScope.ribbonService.openedClassroom.indexOf(uuid);
                    if (index > -1) {
                        $rootScope.ribbonService.openedClassroom.splice(index, 1);
                    }

                    if ($rootScope.ribbonService.openedClassroom.length === 0) {
                        $rootScope.visibleClassroomView = false;
                        $rootScope.ribbonService.activeClass = false;
                        // remove hand icon from the ribbon bar
                        $rootScope.handRaised = false;
                        $rootScope.$broadcast('classroom:stop');
                    }

                    $rootScope.displayClassroomView = false;
                    $rootScope.isClassroom = false;
                    // return previously opened group to the group picker list
                    $rootScope.closedGroups.push(data.text);
                    $rootScope.$broadcast("updateClosedGroupList");
                    console.log("Notifications: " + data.text);
                    $rootScope.ribbonService.cursor_name = "";

                    return;
                }
                else if (data.title === 'Presenter Mode') {
                    $rootScope.$broadcast("presenterMode:start", data);
                    return;
                }
                else if (data.title === 'Stop Presenter Mode') {
                    $rootScope.$broadcast("presenterMode:stop", data);
                    return;
                }
                else if (data.title === 'Stop New Tab') {
                    $rootScope.$broadcast("newtab:stop");
                    return;
                } 
                else if (data.title === 'Classroom Start') {
                    $rootScope.isClassroom = true;
                    $rootScope.displayClassroomView=true;

                    if($rootScope.openedGroups.indexOf(data.text) < 0){
                        $rootScope.openedGroups.push(data.text);
                    }

                    $rootScope.$broadcast("updateOpenedGroupList");

                    if (data.data) {
                        var params = JSON.parse(atob(decodeURIComponent(data.data)));
                        var uuid = params.uuid;
                        var index = $rootScope.ribbonService.openedClassroom.indexOf(uuid);
                        if (index == -1) {
                            $rootScope.ribbonService.openedClassroom.push(uuid);
                        }
                    }
                    return;
                }
                else if (data.title === 'Classroom Stop') {
                    $rootScope.isClassroom = false;
                    $rootScope.displayClassroomView = false;
                    if (data.data) {
                        var params = JSON.parse(atob(decodeURIComponent(data.data)));
                        var uuid = params.uuid;
                        var index = $rootScope.ribbonService.openedClassroom.indexOf(uuid);
                        if (index > -1) {
                            $rootScope.ribbonService.openedClassroom.splice(index, 1);
                        }
                    }
                    return;
                }
                else if (data.title === 'Start ViewScreen') {

                    $translate('CLASSROOM.NOTIFY_START_VIEW_SCREEN').then(function (message) {
                        infoService.infoText = message;
                        infoService.infoDialogVisible = true;
                        infoService.top = false;
                        infoService.closeButton = true;
                        $rootScope.closeMessage = function closeMessage() {
                            infoService.infoDialogVisible = false;
                            infoService.closeButton = false;
                        };
                    });
                    return;
                }
                else if (data.title === 'End ViewScreen') {

                    $translate('CLASSROOM.NOTIFY_END_VIEW_SCREEN').then(function (message) {
                        infoService.infoText = message;
                        infoService.infoDialogVisible = true;
                        infoService.top = false;
                        infoService.closeButton = true;
                        $rootScope.closeMessage = function closeMessage() {
                            infoService.infoDialogVisible = false;
                            infoService.closeButton = false;
                        };
                    });
                    return;
                }
                else {
                    // When data.title is like "Application Available for a Limited Time"
                    // or "Session is Ending"
                    var delay = ribbonService.thresholdInfo;
                    if (data.delay != 0) {
                        delay = data.delay;
                    }

                    infoService.infoText = data.text;
                    infoService.infoDialogVisible = true;
                    infoService.top = true;
                    $timeout(function hideInfo() {
                        infoService.infoDialogVisible = false;
                    }, delay);
                }

                break;
            }
            case MessageLevelCode.WARNING: {
                switch (data.command) {
                    case MessageCommandCode.REMOTE_JOIN:
                        // View Only share key
                        askUserPermission($rootScope.shareKeyVO);
                        break;
                    case MessageCommandCode.COLLABORATION_JOIN:
                        // Full Control share key
                        askUserPermission($rootScope.shareKeyFC);
                        break;
                    case MessageCommandCode.CLASSROOM:
                        askUserPermission("");
                        break;
                }
                break;
            }
            case MessageLevelCode.NONE: {
                switch (data.command) {
                    case MessageCommandCode.SYNC:
                        break;
                }
                break;
            }
        }
    }

    function sendResponse(response, options) {
        var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
        var datasource = encodeURIComponent(clientIdentifier.dataSource);
        var httpParameters = {
            token: encodeURIComponent(authenticationService.getCurrentToken()),
            id: encodeURIComponent(clientIdentifier.id)
        };
        var req = {
            method: 'POST',
            url: "api/session/ext/" + datasource + "/notify/setresponse",
            params: httpParameters,
            data: $.param({
                response: response,
                options: options
            }),
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        };

        $http(req).then(function () {
        }).catch(function () {
        }).finally(function () {
            console.debug("Notifications: Response sent");
            guacNotification.showStatus(false);
        });
    }

    function askUserPermission(options) {
        var text = [];
        text = data.text.split("//");
        if (text[1] === undefined || text[1] === "")
            $rootScope.ribbonService.cursor_name = "";
        else
            $rootScope.ribbonService.cursor_name = text[1] + " (Teacher)";
        var DECLINE_ACTION = {
            name: 'DIALOGS.BUTTON_DECLINE',
            callback: function close() {
                if (delayTimeout) {
                    $timeout.cancel(delayTimeout);
                    delayTimeout = null;
                }
                sendResponse('Decline', "");
            }
        };

        var ACCEPT_ACTION = {
            name: 'DIALOGS.BUTTON_ACCEPT',
            callback: function close() {
                if (delayTimeout) {
                    $timeout.cancel(delayTimeout);
                    delayTimeout = null;
                }
                sendResponse('Accept', options);
                $translate('CLASSROOM.NOTIFY_SHARE_FULL_SCREEN').then(
                    function acceptFC(text) {
                        infoService.infoText = text;
                        infoService.top = false;
                        infoService.infoDialogVisible = true;
                        infoService.closeButton = false;
                        infoService.className = "ads_click";
                        $timeout(function hideInfo() {
                            infoService.infoDialogVisible = false;
                            infoService.className = "";
                        }, ribbonService.thresholdInfo);
                    }
                );
            }
        };

        guacNotification.showStatus({
            title: data.title,
            text: {
                key: text[0]
            },
            actions: [DECLINE_ACTION, ACCEPT_ACTION]
        });

        if (data.delay != 0) {
            if (data.command == MessageCommandCode.COLLABORATION_JOIN) {
                // To notify faculty about the timeout status
                delayTimeout = $timeout(function hideInfo() {
                    sendResponse('NoResponse', "");
                    guacNotification.showStatus(false);
                }, data.delay);
            }
            else {
                delayTimeout = $timeout(function hideInfo() {
                    sendResponse('Decline', "");
                    guacNotification.showStatus(false);
                }, data.delay);
            }
        }

        var notificationTitle;
        if (options) {
            notificationTitle = "Remote Join";
        }
        else {
            notificationTitle = "Virtual Classroom";
        }

        webNotification && webNotification.showNotification(notificationTitle, {
            body: text[0],
            requireInteraction: true,
            onClick: function onNotificationClicked() {
                console.log(notificationTitle + ' notification.');
                try {
                    window.focus();
                    this.cancel();
                }
                catch (ex) {
                }
            },
            icon: 'app/ext/ribbon/images/favicons/apple-icon-60x60.png'
        }, function onShow(error, hide) {
            if (error) {
                console.log('Unable to show ' + notificationTitle + ' notification: ' + error.message);
            }
            else {
                console.log(notificationTitle + ' notification Shown.');
            }
        });
    }

    return service;
}]);
