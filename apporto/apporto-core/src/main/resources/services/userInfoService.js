/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for user information
 */
angular.module('core').factory('userInfoService',
    ['$injector', '$routeParams',
        function userInfoService($injector, $routeParams) {

    var $http                 = $injector.get('$http');

    var authenticationService = $injector.get('authenticationService');
    var ribbonService         = $injector.get('ribbonService');
    var ClientIdentifier      = $injector.get('ClientIdentifier');



    var service = {}

    /**
     * Fetch user information
     */
    service.getUserInfo = function getUserInfo(windowsName, selectedSubGroup, parentToken, clientIdentifier) {

        clientIdentifier = clientIdentifier || ClientIdentifier.fromString($routeParams.id).id;

        var dataSource = authenticationService.getDataSource() ? authenticationService.getDataSource() : 'encryptedurl-jdbc';
        if (dataSource === 'encryptedurl-jdbc-shared') {
            return;
        }

        var httpParameters = {
            token: parentToken ? parentToken : authenticationService.getCurrentToken(),
            id: clientIdentifier,
            windows_name: windowsName ? windowsName : ''
        };

        var req = {
            method: 'GET',
            url: "api/session/ext/" + dataSource + "/messenger/getUserInfo",
            params: httpParameters
        };

        return $http(req)
        .then(function (response) {
            var data = response.data;
            var fullName = service.getFullName(data['firstname'], data['lastname']);
            fullName = fullName ? fullName : data['email'];
            var initials = service.getInitials(fullName);
            initials = initials ? initials : service.getInitials(data['email']);

            // Handle subgroups if provided
            var windowsUserName = data['email'] + (selectedSubGroup ? '/vclassroom/' + selectedSubGroup : '');

            ribbonService.userinfo = {
                adu: data['windows_username'],
                windows_username: windowsUserName,
                email: data['email'],
                groups: data['groups'],
                roles: data['roles'],
                status: data['status'],
                name: fullName,
                initials: initials,
                sha256_username: sha256(data['windows_username']),
                state: ribbonService.userinfo.state
            }

            if (ribbonService.userinfo.roles === 'Faculty Admin') {
                ribbonService.presenterVisible = true;
            }

            // Optionally return response data if needed
            // Some calls to this service do not use response data, but some do.
            return response;

        }).catch(function (response) {
            console.error("Chatting error group fetch: ", response.message);
        }).finally(function () { })
    }

    /**
     * Get full name when given first name and last name
     */
    service.getFullName = function getFullName(firstname, lastname) {
        if (!firstname && !lastname)
            return '';

        if (!firstname)
            firstname = '';

        if (!lastname)
            lastname = '';

        return lastname == '' ? firstname : (firstname + ' ' + lastname);
    }

    /**
     * Get initials from the first letter of the first name and the first letter of the last name
     */
    service.getInitials = function getInitials(string) {
        if (!string) string = '';
        var names = string.split(' ');

        if (names && names.length > 0) {
            var initials = names[0].substring(0, 1).toUpperCase();

            if (names.length > 1) {
                initials += names[names.length - 1].substring(0, 1).toUpperCase();
            }
            return initials;
        }

        return '';
    };

    return service;
}]);
