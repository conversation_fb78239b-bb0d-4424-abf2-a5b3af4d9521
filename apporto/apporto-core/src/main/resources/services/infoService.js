/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for displaying informational messages
 */
angular.module('core').factory('infoService',
[ function infoService() {

    var infoService = {
        /**
         * Information text to display in dialog
         *
         * @type String
         */
        infoText: "No information",

        /**
         * Show information dialog
         *
         * @type Boolean
         */
        infoDialogVisible: false,

        /**
         * Show running dog
         *
         * @type Boolean
         */
        loadingBlocksVisible: false,

         /**
         * Show starting server text with laoding blocks
         *
         * @type Boolean
         */
         show_starting_server_text: true,

        /**
         * Display dialog on the top
         *
         * @type Boolean
         */
        top: false,

        /**
         * Class name to display in dialog
         *
         * @type String
         */
        className: "",

        /**
         * close button in dialog
         *
         * @type Booleam
         */
        closeButton: false,

        /**
         * check for server is ready
         * 
         * @type Boolean
         */
        server_ready: false
    };

    return infoService;
} ]);
