<div class="info-message" tabindex="0">
    <div ng-if="infoService.server_ready" id="{{variant === 'VC' ? 'readyVC' : 'readyMessage'}}" class="ready-message">
        <div id="serverLoaded">
            <div class="server-ready"></div>
        </div>
        <span ng-if="infoService.show_starting_server_text" id="{{variant === 'VC' ? 'StartMezSpanVC' : 'StartMezSpan'}}" class="info-text">{{'CLIENT.SERVER_READY' | translate}}</span>
        <span ng-if="infoService.server_ready" id="{{variant === 'VC' ? 'waitingMezSpanVC' : 'waitingMezSpan'}}" class="info-text">{{'CLIENT.TEXT_CLIENT_STATUS_READY' | translate}}</span>
        <button class='open-desktop-btn' ng-if="infoService.server_ready" ng-click='openDesktop()' role="button" aria-label="Open Desktop">
            {{'CLIENT.OPEN_DESKTOP' | translate}}
            <img src='app/ext/ribbon/images/arrow_right_alt.png' alt='Forward' class='btn-icon' />
        </button>
    </div>
    <div ng-if="infoService.loadingBlocksVisible" id="{{variant === 'VC' ? 'waitingVC' : 'waitingMessage'}}" class="waiting-message">
        <div id="loadingAnimation">
            <div id="{{variant === 'VC' ? 'loadVC' : 'load'}}">
                <span id="{{variant === 'VC' ? 'left_block_VC' : 'left_block'}}"></span>
                <span id="{{variant === 'VC' ? 'right_block_VC' : 'right_block'}}"></span>
                <div class="running_block" style="background: #F6E472; --i: 1;"></div>
                <div class="running_block" style="background: #EC6D72; --i: 2;"></div>
                <div class="running_block" style="background: #69AECD; --i: 3;"></div>
                <div class="running_block" style="background: #F6E472; --i: 4;"></div>
                <div class="running_block" style="background: #85cd98; --i: 5;"></div>
                <div class="running_block" style="background: #69AECD; --i: 6;"></div>
                <div class="running_block" style="background: #F6E472; --i: 7;"></div>
                <div class="running_block" style="background: #EC6D72; --i: 8;"></div>
                <div class="running_block" style="background: #69AECD; --i: 9;"></div>
                <div class="running_block" style="background: #F6E472; --i: 10;"></div>
                <div class="running_block" style="background: #85cd98; --i: 11;"></div>
            </div>
        </div>
        <span ng-if="infoService.show_starting_server_text" id="{{variant === 'VC' ? 'StartMezSpanVC' : 'StartMezSpan'}}" class="info-text">{{variant === 'VC' ? ('CLIENT.LAUNCHING_VC' | translate) : ('CLIENT.START_SERVER' | translate)}}</span>
        <span ng-if="infoService.show_starting_server_text" id="{{variant === 'VC' ? 'waitingMezSpanVC' : 'waitingMezSpan'}}" class="info-text">{{variant === 'VC' ? ('CLIENT.WAIT_WHILE_LAUNCHING_VC' | translate) : (infoService.infoText)}}</span>
        <span id="waitingMezSpan" ng-if="!infoService.show_starting_server_text">{{'CLIENT.TEXT_CLIENT_STATUS_WAITING' | translate}}</span>
    </div>
    <div ng-if="!infoService.loadingBlocksVisible && !infoService.closeButton" class="info-message-content" ng-click="hide()">
        <div ng-if="infoService.className.length > 0" class={{infoService.className}}></div>
        <span class="message">{{infoService.infoText}}</span>
        <div ng-if="ribbonService.licenses.supportEmail && infoService.showSupportEmail" class="info-email">
            {{ribbonService.licenses.supportEmail}}
        </div>
    </div>
    <div ng-if="!infoService.loadingBlocksVisible && infoService.closeButton" class="bottom-message-content">
        <div class="groups_black"></div>
        <span class="bottom-message">{{infoService.infoText}}</span>
        <div class="close-message" ng-click="$root.closeMessage()"></div>
    </div>
</div>
