<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                        http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.apporto</groupId>
    <artifactId>apporto-core</artifactId>
    <packaging>jar</packaging>
    <version>1.2.0</version>
    <name>apporto-core</name>
    <url>http://apporto.com/</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <profiles>
        <profile>
            <activation>
                <property>
                    <name>!prod</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.samaxes.maven</groupId>
                        <artifactId>minify-maven-plugin</artifactId>
                        <version>1.7.6</version>
                        <executions>
                            <execution>
                                <id>default-cli</id>
                                <configuration>
                                    <closureCompilationLevel>SIMPLE_OPTIMIZATIONS</closureCompilationLevel>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>org.codehaus.plexus</groupId>
                                <artifactId>plexus-utils</artifactId>
                                <version>3.5.1</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>
            <!-- Written for Java 1.8 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <release>8</release>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgs>
                        <arg>-Xlint:all,-processing</arg>
                        <arg>-Werror</arg>
                    </compilerArgs>
                    <fork>true</fork>
                </configuration>
            </plugin>

            <!-- JS/CSS Minification Plugin -->
            <plugin>
                <groupId>com.samaxes.maven</groupId>
                <artifactId>minify-maven-plugin</artifactId>
                <version>1.7.6</version>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <configuration>
                            <charset>UTF-8</charset>
                            
                            <webappSourceDir>${basedir}/src/main/resources</webappSourceDir>
                            <webappTargetDir>${project.build.directory}/classes</webappTargetDir>

                            <cssSourceDir>/</cssSourceDir>
                            <cssTargetDir>/</cssTargetDir>
                            <cssFinalFile>apporto-core.css</cssFinalFile>

                            <cssSourceFiles>
                                <cssSourceFile>license.txt</cssSourceFile>
                            </cssSourceFiles>

                            <cssSourceIncludes>
                                <cssSourceInclude>**/*.css</cssSourceInclude>
                            </cssSourceIncludes>

                            <jsSourceDir>/</jsSourceDir>
                            <jsTargetDir>/</jsTargetDir>
                            <jsFinalFile>apporto-core.js</jsFinalFile>

                            <jsSourceFiles>
                                <jsSourceFile>license.txt</jsSourceFile>
                            </jsSourceFiles>

                            <jsSourceIncludes>
                                <jsSourceInclude>**/*.js</jsSourceInclude>
                            </jsSourceIncludes>

                            <!-- Do not minify and include tests -->
                            <jsSourceExcludes>
                                <jsSourceExclude>**/*.test.js</jsSourceExclude>
                            </jsSourceExcludes>
                            <jsEngine>CLOSURE</jsEngine>

                            <!-- Disable warnings for JSDoc annotations -->
                            <closureWarningLevels>
                                <misplacedTypeAnnotation>OFF</misplacedTypeAnnotation>
                                <nonStandardJsDocs>OFF</nonStandardJsDocs>
                            </closureWarningLevels>

                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-utils</artifactId>
                        <version>3.5.1</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- Assembly plugin - for easy distribution -->
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <finalName>${project.artifactId}-${project.version}</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/main/assembly/dist.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-dist-archive</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Copy dependencies prior to packaging -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>unpack-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeScope>runtime</includeScope>
                            <outputDirectory>${project.build.directory}/classes</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Verify format using Apache RAT -->
            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <version>0.12</version>

                <configuration>
                    <excludesFile>${basedir}/.ratignore</excludesFile>
                    <excludes>
                        <exclude>.ratignore</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>src/licenses/**/*</exclude>
                        <exclude>src/main/resources/generated/**/*</exclude>
                        <exclude>src/main/resources/templates/*.html</exclude>
                        <exclude>src/main/resources/images/**/*</exclude>
                        <exclude>src/main/java/lombok.config</exclude>
                    </excludes>
                </configuration>

                <!-- Bind RAT to validate phase -->
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>

        </plugins>
    </build>

    <dependencies>
        <!-- Guacamole Extension API -->
        <dependency>
            <groupId>org.apache.guacamole</groupId>
            <artifactId>guacamole-ext</artifactId>
            <version>1.1.0</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>


        <!-- JUnit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.mockito/mockito-core -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.11.0</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
