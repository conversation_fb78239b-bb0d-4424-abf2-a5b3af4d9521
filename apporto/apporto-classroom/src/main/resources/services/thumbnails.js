/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for generating and sending thumbnails to the server
 */
angular.module('classroomModule').factory('thumbnailService',
    ['$injector', function thumbnailService($injector) {
        var $location             = $injector.get('$location');
        var $interval             = $injector.get('$interval');
        var ClientIdentifier      = $injector.get('ClientIdentifier');
        var $routeParams          = $injector.get('$routeParams');
        var authenticationService = $injector.get('authenticationService');
        var $http                 = $injector.get('$http');
        var ribbonService         = $injector.get('ribbonService');
        var guacClientManager     = $injector.get('guacClientManager');
        var $timeout              = $injector.get('$timeout');
        var $rootScope            = $injector.get('$rootScope');

        var clientInterval = null;
        var thumbInterval = null;

        /**
         * To upload thumbnail only when classroom is active
         */
        var availableThumb = false;

        var client = null;

        var service = {}

        var thumbWorker;

        const THUMB_INTERVAL = 1 * 1000;

        /**
         * Function is called periodicaly to check if the client is setup. Once client is setup,
         * the classrom thumbnails can be generated and send (availableThumb == true).
         * 
         * Also, the thumbWorker thread is initialized with parameters needed for sending thumbnails
         * to the server.
         */
        function getClient() {
            if ($routeParams.id) {
                var cl = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);

                if (cl == null || cl.tunnel.uuid == null) {
                    console.debug("Thumbnails: Client or tunnel not yet available.");
                    return;
                }

                client = cl;

                // when user login after classroom already launched
                if ($rootScope.ribbonService.activeClass) {
                    availableThumb = true;
                    uploadThumbnail();
                }

                $interval.cancel(clientInterval);
            }
        };

        function initWorker() {
            thumbWorker = new Worker('app/ext/classroom/workers/thumbWorker.js');
            
            // Initialize thumbWorker with parameters needed for POST request
            thumbWorker.postMessage({
                command: "init",
                baseUrl: location.origin + location.pathname,
                clientId: ClientIdentifier.fromString($routeParams.id).id,
                dataSource: ClientIdentifier.fromString($routeParams.id).dataSource,
                token: authenticationService.getCurrentToken()
            });
        }

        $rootScope.$on("classroom:start", function () {
            const startClassroom = function() {
                if (client) {
                    initWorker();
                    availableThumb = true;

                    thumbWorker.postMessage({
                        command: "connect"
                    });
                    $timeout(uploadThumbnail, 1000);
                } else {
                    $timeout(startClassroom, 1000);
                }
            }

            startClassroom();
        });

        $rootScope.$on("classroom:stop", function () {
            if (thumbInterval) {
                $timeout.cancel(thumbInterval);
            }
            availableThumb = false;
            thumbWorker.postMessage({
                command: "disconnect"
            });
        });

        service.start = function start() {
            if($location.path().indexOf("key") > -1) {
                console.log("Thumbnails: disabled for sharing mode");
                return;
            }

            if($location.path().indexOf("classroom") > -1) {
                console.log("Thumbnails: disabled for classroom view");
                return;
            }

            // Repeat until client is connected and ready.
            clientInterval = $interval(getClient, 3000);
        }

        service.stop = function stop() {
            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            var datasource = encodeURIComponent(clientIdentifier.dataSource);

            var httpParameters = {
                token: encodeURIComponent(authenticationService.getCurrentToken()),
                id: encodeURIComponent(clientIdentifier.id)
            };

            var req = {
                    method: 'POST',
                    url: "api/session/ext/" + datasource + "/thumbnail/unsubscribe",
                    params: httpParameters
            };

            $http(req)
            .then(function(response) {
                console.debug("Thumbnails: unsubscribed.");
            })
            .catch(function(response) {
                console.error("Thumbnails: Error unsubscribing: ", response.message);
            })
            .finally( function() { 
                client = null;
            });
        }

        var uploadThumbnail = function uploadThumbnail() {
            if (!client)
                return;

            if (ribbonService.licenses.hasH264Licence) {
                client.client.getDisplay().getImageBitmap()
                    .then(function(data) {
                        sendToWorker(data);
                        console.debug("Thumbnails: Image bitmap received from GL");
                    });
            }
            else {
                var canvas = client.client.getDisplay().getDefaultLayer().getCanvas();
                imageData = canvas.toBlob((blob) => {
                        blob.arrayBuffer().then((data)=> {
                            sendToWorker(data);
                            console.debug("Thumbnails: Image bitmap received from 2D");
                        })
                    },
                    "image/webp");
            }
        }

        var sendToWorker = function sendToWorker(imageBitmap) {
            if (imageBitmap != null) {
                thumbWorker.postMessage({
                    command: "send",
                    originalImage: imageBitmap
                }, [imageBitmap]);
                imageBitmap = null;
            }

            if (thumbInterval)
                $timeout.cancel(thumbInterval);

            if (availableThumb)
                thumbInterval = $timeout(uploadThumbnail, THUMB_INTERVAL);
        }

        return service;
}])

