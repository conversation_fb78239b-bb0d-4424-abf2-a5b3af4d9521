/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A service for managing thumbnails in the Virtual Classroom
 * professor view.
 * 
 * This service is responsible for initializing a web worker for receiving
 * thumbnails from the servers and updating the UI with the received thumbnails.
 */
angular.module('classroomModule').factory('thumbManager', ['$injector',
    function thumbService($injector) {

        // Required services
        var $routeParams             = $injector.get('$routeParams');
        var pagerService             = $injector.get('pagerService');
        var $rootScope               = $injector.get('$rootScope');
        var $timeout                 = $injector.get('$timeout');

        var clientIdentifier         = $routeParams.id;

        var serverCount              = 0;
        var updateThumbnailsCallback = null;

        var service = {}

        service.init = function(config) {
            initWorker(config);
            connectWorkerToServers();
        }

        service.destroy = function() {
            disconnectWorkerFromServers();
        }

        var thumbReceiver;

        /**
         * Initializes worker thread for receiving thumbnails.
         */
        function initWorker(config) {
            thumbReceiver = new Worker('app/ext/classroom/workers/thumbReceiver.js');

            thumbReceiver.postMessage({
                command: 'init',
                baseUrl: location.origin + location.pathname,
                clientId: clientIdentifier,
                token: config.parentToken
            })

            thumbReceiver.onmessage = getServerData;

            serverCount = config.serverCount;
            updateThumbnailsCallback = config.updateThumbnailsCallback;
        }

        /**
         * Callback function for receiving thumbnails from the server.
         * 
         * When the server sends a message with the thumbnail data, this function
         * deserializes the data and calls the updateThumbnailsCallback function
         * to update the UI with the received thumbnail.
         */
        function getServerData(message) {
            let {thumbId, thumbData} = deserialize(message.data.buffer);

            if (thumbData.length === 0) {
                return;
            }

            let thumbData_b64 = toBase64(thumbData);

            updateThumbnailsCallback(thumbId, message.data.srv_id, thumbData_b64);
        }

        /**
         * Deserializes the thumbnail data received from the server.
         * 
         * Thumbnails data is sent as an ArrayBuffer with the following format:
         * - srv_id_length (4 bytes)
         * - srv_id (srv_id_length bytes)
         * - thumbId (8 bytes)
         * - thumbData (remaining bytes)
         * 
         * @param {ArrayBuffer} data - The thumbnail data received from the server.
         * @returns {Object} - An object containing the thumbnail id, server id, and thumbnail data.
         */
        function deserialize(data) {
            // Create a DataView to read bytes as integers of specific byte lengths
            var dataView = new DataView(data);

            thumbId = dataView.getBigInt64(0);

            thumbData = new Uint8Array(data.slice(8));

            return {thumbId, thumbData};
        }

        /**
         * Converts an Uint8Array to a base64 string.
         * 
         * @param {Uint8Array} buffer - The ArrayBuffer to convert to base64.
         * @returns {string} - The base64 string representation of the ArrayBuffer.
         */
        function toBase64(buffer) {
            let binary_b64 = '';
            buffer.forEach(function(byte) {
                binary_b64 += String.fromCharCode(byte);
            });

            return window.btoa(binary_b64);
        }

        /**
         * Connect web worker to all the servers.
         * 
         * Initializes the connection between the web worker and the servers. The web worker
         * establishes a WebSocket connection with each server to receive thumbnail data.
         */
        function connectWorkerToServers() {
            thumbReceiver.postMessage({
                command: 'connect',
                numServers: serverCount
            });
        }

        function disconnectWorkerFromServers() {
            thumbReceiver.postMessage({
                command: 'disconnect'
            });
        }

        /**
         * Retrieves the users on the current page and stores them in an array.
         */
        function getUsersOnPage(page) {
            var pageSize = pagerService.pageSize;
            var start = (page - 1) * pageSize;
            var end = start + pageSize;
            return $rootScope.visibleUsers.slice(start, end);
        }

        /**
         * Subscribe to thumbnail data.
         * 
         * Each thumbnail will be sent only when received.
         */
        service.subscribe = function subscribe() {
            let pageIds = getUsersForUpdate();

            Object.keys(pageIds).forEach(server_id => {
                thumbReceiver.postMessage({
                    command: 'subscribe',
                    thumbList: pageIds[server_id],
                    srv_id: server_id
                })
            });
        }

        /**
         * Requests thumbnails for all the users on the current page.
         * 
         * This function is called periodically to request thumbnails for all the users
         * on the current page. It creates a list of user ids for each server and sends a
         * request to the server to retrieve the thumbnails.
         * 
         * The server immediately sends the thumbnail data back to the web worker, which
         * then return data to this service for updating the UI.
         */
        service.retrieveThumbnails = function retrieveThumbnails() {
            let pageIds = getUsersForUpdate();

            Object.keys(pageIds).forEach(server_id => {
                thumbReceiver.postMessage({
                    command: 'request',
                    thumbList: pageIds[server_id],
                    srv_id: server_id
                })
            });

            $timeout(service.retrieveThumbnails, 1500);
        }

        /**
         * Retrieves IDs of the users whose thumbnails should be updated:
         * - visible users on the current page
         * - non-visible users without thumbnails
         * 
         * Returns: array of server IDs of array of user IDs
         * 
         * e.g. pageIds[srv-1]=[1234,334,565]
         */
        function getUsersForUpdate() {
            let pageIds = {};
            let currentPageData = getUsersOnPage(pagerService.currentPage);

            /**
             * Distribute all visible users on page over their server ids.
             * 
             * e.g. pageIds[srv-1]=[1234,334,565]
             * 
             */
            currentPageData.forEach(user => {
                const { info } = user;
                if (info) {
                    if (!pageIds[info.server_id])
                        pageIds[info.server_id] = [];

                    pageIds[info.server_id].push(info.id);
                }
            });

            /**
             * If some users on non-current page do not have a thumbnail,
             * request it via its server.
             */
            const usersData = $rootScope.usersData;
            usersData.forEach(user => {
                const { server_id, id, thumbnail } = user;
                if (!pageIds[server_id])
                    pageIds[server_id] = [];

                if (!thumbnail && !pageIds[server_id].includes(id)) {
                    pageIds[server_id].push(id);
                }
            });

            return pageIds;
        }

        return service;
}]);

