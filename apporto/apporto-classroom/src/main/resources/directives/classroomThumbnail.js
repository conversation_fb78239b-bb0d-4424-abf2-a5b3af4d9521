/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays loading circle
 */
angular.module('classroomModule').directive('classroomThumbnail', [function classroomThumbnail() {

    return {
        restrict: 'E',
        scope: {
        },
        templateUrl: 'app/ext/classroom/templates/classroom-thumbnail.html',
        controller: ['$scope', '$injector', '$rootScope', function classroomThumbnailController($scope, $injector, $rootScope) {
            console.log("Mouseover directive");
            // Required services
            $scope.ribbonService =      $injector.get('ribbonService');
            $scope.ServiceCall   =      $injector.get('ServiceCall');
            $scope.dragOptions = {
                container: 'classroom-thumbnail',
                handle: '.chat-head.controlbox-head',
            }

            $scope.hide = function hide(info) {
            	$rootScope.selectedItem.waiting = false;	
            }

            $scope.roomStyle = function roomStyle($index) {
                var right = 10 + 245 * $index;
                return {
                    right: right
                }
            }

        }] // end classroom thumbnail controller
    };
}]);

