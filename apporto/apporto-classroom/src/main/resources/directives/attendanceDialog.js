/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays attendance dialog
 */
angular.module('classroomModule').directive('attendanceDialog', [ function attendanceDialogDirective() {

    return {
        restrict: 'E',
        scope: {},

        templateUrl: 'app/ext/classroom/templates/attendance-dialog.html',
        controller: ['$scope', '$injector', '$rootScope',

            function attendanceDialogController($scope,  $injector, $rootScope) {

                $scope.ribbonService         = $injector.get('ribbonService');
                $scope.ServiceParticipant    = $injector.get('ServiceParticipant');

                FOCUS_SORT_NAME              = 0;
                FOCUS_SORT_STATUS            = 1;

                $scope.userSortTypes = ["Alphabetically", "By Sign-In"];

                $scope.showSortingOptions  = false;
                $scope.sortType = $scope.userSortTypes[0];

                /**
                 * Function to sort the array by 'name' key of the array.
                 */
                $scope.sortByName = function sortByName() {
                    $rootScope.manualSort = true;
                    $rootScope.sortByName = !$rootScope.sortByName;
                    $rootScope.focusSortKey = FOCUS_SORT_NAME;
                    $scope.sortType = $scope.userSortTypes[0];

                    if ($rootScope.sortByName) {
                        $rootScope.classroomData.sort(function(a, b){
                            let x = a.name.toUpperCase();
                            let y = b.name.toUpperCase();
                            if (x < y) {return 1;}
                            if (x > y) {return -1;}
                            return 0;
                        });
                    }
                    else {
                        $rootScope.classroomData.sort(function(a, b){
                            let x = a.name.toUpperCase();
                            let y = b.name.toUpperCase();
                            if (x > y) {return 1;}
                            if (x < y) {return -1;}
                            return 0;
                        });
                    }

                    if (!$scope.ribbonService.showAbsent) {
                        $rootScope.visibleUsers = $rootScope.classroomData.filter((user) => user.status === "Present");
                    }
                }

                $scope.sortBySignIn = function sortBySignIn() {
                    $scope.sortType = $scope.userSortTypes[1];
                    $rootScope.focusSortKey = 2;
                    $rootScope.classroomData.sort(function(a, b) {
                        return a.order - b.order;
                    });
                }

                $scope.openOptions = function(index) {
                    for (var i = 0; i < $rootScope.classroomDataWithOptions.length; i++) {
                        if (i === index) {
                            $rootScope.classroomDataWithOptions[i].options = !$rootScope.classroomDataWithOptions[i].options;
                        } else {
                            $rootScope.classroomDataWithOptions[i].options = false;
                        }
                    }
                };
                
                /**
                 * Function to sort the array by 'status' key of the array.
                 */
                $scope.sortByStatus = function sortByStatus() {
                    $rootScope.manualSort = true;
                    $rootScope.sortByStatus = !$rootScope.sortByStatus;
                    $rootScope.focusSortKey = FOCUS_SORT_STATUS;

                    if ($rootScope.sortByStatus) {
                        $rootScope.classroomData.sort(function(a, b){
                            let x = a.order;
                            let y = b.order;
                            if (x < y) {return 1;}
                            if (x > y) {return -1;}
                            return 0;
                        });
                    }
                    else {
                        $rootScope.classroomData.sort(function(a, b){
                            let x = a.order;
                            let y = b.order;
                            if (x > y) {return 1;}
                            if (x < y) {return -1;}
                            return 0;
                        });
                    }

                    if (!$scope.ribbonService.showAbsent) {
                        $rootScope.visibleUsers = $rootScope.classroomData.filter((user) => user.status === "Present");
                        $rootScope.visibleUsers.sort(function(a, b){
                            let x = a.order;
                            let y = b.order;
                            if (x > y) {return 1;}
                            if (x < y) {return -1;}
                            return 0;
                        });
                    }
                }

            }
        ] // end attendance dialog controller
    };
}]);

