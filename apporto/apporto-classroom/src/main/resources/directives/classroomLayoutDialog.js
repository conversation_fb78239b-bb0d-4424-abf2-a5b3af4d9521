/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays classroom layout dialog
 */
angular.module('classroomModule').directive('classroomLayoutDialog', [function classroomLayoutDialog() {

    return {
        restrict: 'E',
        scope: {

        },

        templateUrl: 'app/ext/classroom/templates/classroom-layout-dialog.html',
        controller: ['$scope', '$injector', '$rootScope', '$location',
            function classroomLayoutDialogController($scope, $injector, $rootScope, $location) {

            var $rootScope            = $injector.get('$rootScope');

            $rootScope.layoutSettingUpdated = false;
            $scope.showLayoutOptions = false;
            $scope.layoutOptions = [
                "Large (2x2)",
                "Medium (3x3)",
                "Small (4x4)",
                "Very Small (5x5)"
            ];
            $rootScope.currentLayoutOption = 0;
            // Layout type - Auto (0), Layout value (1)
            $rootScope.currentLayoutType = 0;

            $scope.showSortOptions = false;
            $scope.sortOptions = [
                "By Sign-In",
                "Alphabetically",
            ];
            $rootScope.currentSortOption = 0;

            $scope.closeDialog = function closeDialog() {
                // Close dialog
                $rootScope.layoutDialogVisible = false;
            }

            $scope.btnSave = function btnSave() {
                // Close dialog
                $rootScope.layoutDialogVisible = false;
                $rootScope.layoutSettingUpdated = true;
            }

        }] // end classroom layout dialog controller
    };
}]);
