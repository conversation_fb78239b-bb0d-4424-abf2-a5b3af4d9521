/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays classroom
 */
angular.module('classroomModule').directive('classroomDialog', [ function classroomDirective() {

    return {
        restrict: 'E',
        scope: {},

        templateUrl: 'app/ext/classroom/templates/classroom-dialog.html',
        controller: ['$scope', '$injector', '$rootScope', function classroomDialogController($scope, $injector, $rootScope) {

            $scope.ribbonService = $injector.get('ribbonService');

        	/**
             * Close the dialog.
             */
            $rootScope.closeClassroomDialog = function closeClassroomDialog() {
                $scope.ribbonService.classroomDialogVisible = false;
            };

            /**
             * launch the classroom display.
             */
            $scope.launchClassroom = function launchClassroom() {
                $rootScope.openedGroups.push($rootScope.selectedGroup);
                $scope.ribbonService.classroomDialogVisible = false;
            };

            const customSelectClassroom = document.querySelector(".custom-select-classroom");
            const selectBtnClassroom = document.querySelector(".select-button-classroom");

            $scope.closeDropDown = function closeDropDown() {
                customSelectClassroom.classList.remove("active");
            };
            if (selectBtnClassroom != null) {
                // add click event to select button
                selectBtnClassroom.addEventListener("click", (event) => {
                    event.stopPropagation();
                    customSelectClassroom.classList.toggle("active");
                });
                selectBtnClassroom.addEventListener("blur", () => {
                    customSelectClassroom.classList.remove("active");
                });
            }

            $scope.ribbonService.selectClassroomOption = function selectClassroomOption(val){
                customSelectClassroom.classList.remove("active");
                $rootScope.selectedGroup = val;
            }

        }] // end classroom dialog controller
    };
}]);

