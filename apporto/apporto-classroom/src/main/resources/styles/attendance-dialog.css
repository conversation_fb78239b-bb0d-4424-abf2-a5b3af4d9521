/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

 #dlg-attendance>* {
  margin: unset;
}

.short_name_class{
  background: #10161e;
  border-radius: 50%;
  width: 34px;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  top: -8px;
  color: white;
}

/* The Close Button */
.attendance-dialog-content span.close {
  color: #808080;
  cursor: default;
  float: right;
  font-weight: bold;
  font-size: 28px;
  line-height: 25px;
  position: relative;
  right: 12px;
  text-align: center;
  text-decoration: none;
  top: 10px;
  width: 24px;
}

.attendance-dialog-content {
  width: 450px;
  background-color: #f8f9fa !important;
  color: #000000;
  overflow: hidden;
  border-radius: 6px;
}

.attendance-dialog-content::before {
  content: "";
  background: linear-gradient(to right,#00dcff,#00f);
  position: absolute;
  height: 5px;
  top: -5px;
  left: 4px;
  right: 4px;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
}

.attendance-dialog-content .title {
  background-color: #ffffff;
  text-align: left;
  padding: 0px;
  font-family: Lato;
  font-size: 24px;
  font-weight: 600;
  line-height: 28.8px;
  letter-spacing: 0.01em;
  text-align: left;
  color: #1A1A1A;
}

.attendance_status {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: #ffffff;
  margin: 15px 0;
  margin-left: -9px;
}

.status_title {
  display: flex;
  flex-direction: column-reverse;
  align-items: flex-start;
  font-size: 18px;
  font-weight: bold;
  padding: 8px 12px 8px 12px;
  border: 1px solid #EDEBEB;
  border-radius: 8px;
  width: 140px;
  height: 64px;
}

.status_class{
  font-family: Lato;
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
    letter-spacing: 0.01em;
    text-align: left;
    color: #777777;
}

.count {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #1A1A1A;
  font-family: Lato;
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  letter-spacing: 0.01em;
  text-align: right;

}

.attendance_content {
  background: rgb(255,255,255);
}

.content_cell .avatar {
  position: relative;
  display: inline-block;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: visible;
  align-items: stretch;
  width: 56px !important;
  height: 56px !important;
  justify-content: center;
  border: none !important;
  border-radius: 18px !important;
  padding: 10px;
}

.content_cell .avatar .avatar-text {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: hidden;
  align-items: center;
  background: #f0f6ff;
  width: 36px;
  height: 36px;
  border-radius: 18px;
  justify-content: center;
}

.content_cell .avatar .avatar-text .avatar-short-text {
  position: relative;
  display: inline;
  flex-grow: 0;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 15px;
  color: black;
  font-weight: 400;
  background-color: rgba(0, 0, 0, 0);
  cursor: inherit;
}

.username {
  width: 140px;
  text-align: left;
  font-weight: bold; 
  font-size: 18px;
}

.content_cell {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  width: 225px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.status_cell {
  justify-content: flex-end;
  padding-right: 32px;
}

.table_head {
  display: flex;
  align-items: center;
  height: 50px;
  border-bottom: 1px solid #e9ecef;
  margin-left: 8px;
  margin-right: 8px;
}

.table_row {
  display: flex;
  align-items: center;
}

.table_row:hover {
  background: #d7e6fc;
  cursor: pointer;
  border-radius: 8px;
}

.table_cell {
  display: flex;
  align-items: center;
}

.table_body {
  height: 50vh;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-gutter: stable;
}

.table_body::-webkit-scrollbar {
  width: 8px;
  height: 0;
  border: 10px solid transparent;
}

.table_body::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 0 10px;
  border-radius: 10px;
  cursor: pointer;
  color: #c1b7b7;
}

.sort_icon {
  background-image: url(app/ext/ribbon/images/sort.png);
  width: 30px;
  height: 30px;
  margin-left: 4px;
  cursor: pointer;
}

.name_text {
  max-width: 164px;
  text-overflow: ellipsis;
  overflow: hidden;
  font-family: Lato;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
}

.present_header{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.sorting_button{
  padding-inline: 8px;
  border-radius: 8px;
  color: #777777;
}

.sorting_button:hover{
  background-color: #F0F6FF !important;
  cursor: pointer;
}

.sorting_option{
  position: absolute;
  bottom: -80px;
  right: -8px;
  width: 127px;
  height: 88px;
  padding: 12px 8px 12px 8px;
  border: 1px solid #CED3D9;
  border-radius: 8px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 8px;
  box-shadow: 0px 2px 12px 0px #00000014;
  z-index: 122;
}

.sorting_option p{
  margin: 0px;
    padding: 4px 8px;
    font-family: Lato;
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    text-align: left;
    cursor: pointer;
    border-radius: 8px;
}

.sorting_option p:hover{
  background-color: #F0F6FF !important;
}

#extraOptions{
  position: absolute;
  bottom: -102px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 184px;
  height: 96px;
  padding: 12px 8px 12px 8px;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid #CED3D9;  
  background-color: white;
  box-shadow: 0px 2px 12px 0px #00000014;
  z-index: 10;
  font-family: Lato;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  padding: 12px 8px;
}

.extraOptionsElement{
  display: flex;
  flex-direction: row;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 8px;
}

.extraOptionsElement:hover{
  background-color: #f0f6ff !important;
  cursor: pointer;
}

.viewScreenOption{
  background-image: url(app/ext/ribbon/images/fullscreen.svg);
}

.requestControlOption{
  background-image: url(app/ext/ribbon/images/requestControl.svg);
}

.optionImage{
  background-size: 24px;
  padding: 4px 2px !important;
  width: min-content;
  min-width: 40px !important;
  margin: 0px !important;
  background-repeat: no-repeat;
}

.active_options{
  background: #f0f6ff !important;
}

@keyframes slideDown {
  from {
    top: 0px;
  }
  to {
    top: 17px;
  }
}

#control_request_notitification {
  position: absolute;
  z-index: 3;
  left: 50%;
  transform: translateX(-50%);
  background: white;
  color: black;
  transition: all 0.5s ease;
  top: 17px; /* Final position */
  padding: 2px 8px;
  border-radius: 4px;
  font-family: Lato;
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  text-align: left;
  height: 32px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  box-shadow: 0px 2px 12px 0px #00000014;
  animation: slideDown 0.5s ease forwards;
}