/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.container {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.fullScreen {
    width: 80vw;
    height: 80vh;
    position: absolute;
    top: 0;
    left: 0;
}

.right-fullControll {
    text-decoration: none;
    Z-INDEX: 1;
}

iframe {
    min-height: 15vw;
    position: relative;
    min-width: 100vw;
}

.i_display {
    width: 30vw;
    float: left;
    height: 17.5vw;
    overflow: visible;
    position: relative;
    display: inline;
}

.i_display_4 {
    width: calc((100vw - 152px) / 2);
    height: calc((100vh - 206px) / 2);
}

.i_display_9 {
    width: calc((100vw - 176px) / 3);
    height: calc((100vh - 206px) / 3);
}

.i_display_16 {
    width: calc((100vw - 200px) / 4);
    height: calc((100vh - 206px) / 4);
}

.i_display_25 {
    width: calc((100vw - 224px) / 5);
    height: calc((100vh - 206px) / 5);
}

.classroomUsername {
    background: #c6d6e8;
    height: 30px;
    width: 45vw;
    position: relative;
    top: -18.6em;
    left: -23em;
}

.left-username {
    background: white;
    display: inherit;
    position: absolute;
    text-shadow: 0 0 black;
    align-items: center;
    top: 5px;
    overflow: overlay;
    white-space: pre-wrap;
    left: 0;
    font-weight: 550;
    font-size: 14pt;
    padding-left: 10pt;
    margin: 1px;
    margin-top: 0;
    margin-right: 1px;
    margin-bottom: 1px;
    margin-left: 1px;
    margin-left: 0;
}

.loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1000;
}

.loader {
    display: block;
    position: relative;
    left: 50%;
    top: 50%;
    width: 150px;
    height: 150px;
    margin: -75px 0 0 -75px;
    border-radius: 50%;
    border: 7px solid transparent;
    border-top-color: #3498db;
    -webkit-animation: spin 2s linear infinite;
    /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 2s linear infinite;
    /* Chrome, Firefox 16+, IE 10+, Opera */
}

.loader:before {
    content: "";
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    border: 7px solid transparent;
    border-top-color: #e74c3c;
    -webkit-animation: spin 3s linear infinite;
    /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 3s linear infinite;
    /* Chrome, Firefox 16+, IE 10+, Opera */
}

.loader:after {
    content: "";
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-radius: 50%;
    border: 7px solid transparent;
    border-top-color: #f9c922;
    -webkit-animation: spin 1.5s linear infinite;
    /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 1.5s linear infinite;
    /* Chrome, Firefox 16+, IE 10+, Opera */
}

.mm-dialog-outer {
    display: table;
    height: 100%;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.5);
    visibility: visible;
    opacity: 1;
    transition: opacity, visibility;
    transition-duration: .25s;
    z-index: 100;
}

div.a {
    width: 150px;
    height: 80px;
    background-color: yellow;
    -ms-transform: rotate(20deg);
    /* IE 9 */
    -webkit-transform: rotate(20deg);
    /* Safari 3-8 */
    transform: rotate(20deg);
}

div#classroomGroups {
    text-align: center;
    padding-top: 1em;
}

.pagination {
    display: flex;
    padding-left: 0;
    margin: 16px 0;
    border-radius: 4px;
    cursor: pointer;
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    text-decoration: none;
    background-color: #fff;
}

.pagination>.disabled>a,
.pagination>.disabled>a:focus,
.pagination>.disabled>a:hover,
.pagination>.disabled>span,
.pagination>.disabled>span:focus,
.pagination>.disabled>span:hover {
    color: #777;
    cursor: not-allowed;
    background-color: #fff;
}

.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
    z-index: 3;
}

ul {
    display: block;
    list-style-type: none;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    padding-inline-start: 40px;
}

.classroom-footer {
    margin: auto auto 0;
}

.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
    color: #d3d3d3;
    cursor: default;
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    text-decoration: none;
    background-color: #fff;
}

.cl_display_4 {
    min-height: 15%;
    cursor: pointer;
    position: absolute;
    max-width: calc(100% - 24px);
    max-height: calc((100vh - 206px) / 2 - 66px);
}

.cl_display_9 {
    min-height: 15%;
    cursor: pointer;
    position: absolute;
    max-width: calc(100% - 24px);
    max-height: calc((100vh - 206px) / 3 - 66px);
}

.cl_display_16 {
    min-height: 15%;
    cursor: pointer;
    position: absolute;
    max-width: calc(100% - 24px);
    max-height: calc((100vh - 206px) / 4 - 66px);
}

.cl_display_25 {
    min-height: 15%;
    cursor: pointer;
    position: absolute;
    max-width: calc(100% - 24px);
    max-height: calc((100vh - 206px) / 5 - 66px);
}

.cl-display {
    min-height: 15%;
    cursor: pointer;
    width: 100%;
    position: relative;
    min-width: 100%;
}

.cl-display.absent {
    background: #c9C9c9;
    border: 2px solid black;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: unset;
}

.cl-display.absent>label {
    font-size: 45px;
    font-weight: bold;
    color: white;
    cursor: pointer;
}

.animate-show-hide {
    display: flex;
    padding: 4px;
    align-items: center;
    gap: 8px;
    border-radius: 48px;
    background: var(--Accent, #156CD5);
}

.animate-show-hide.ng-hide {
    width: 16px;
    height: 16px;
    background-image: url("app/ext/ribbon/images/back_hand_white.svg");
    opacity: 0;
}

.animate-show-hide.ng-hide-add,
.animate-show-hide.ng-hide-remove {
    transition: all linear 0.5s;
}

.item-dialog-outer {
    width: 100vw;
    border: 1px solid black;
    margin-bottom: 0;
    height: calc(100vh - 80px);
    left: 0px;
    top: -42px;
    padding: 50px 60px;
    background: #10161e
}

.dialog-left-username {
    min-width: 30vw;
    display: inherit;
    position: absolute;
    text-shadow: 0 0 black;
    align-items: center;
    top: 0;
    overflow: overlay;
    font-weight: 550;
    font-size: large;
    color: white;
    right: 27vw;
    background-color: green;
    border-bottom-left-radius: .2em;
    position: fixed;
    padding-left: 15px;
    padding-right: 15px;
}

.full {
    background-color: #ffce44 !important;
    color: black;
    font-weight: 100;
}

.classroom-close {
    cursor: pointer;
    position: fixed;
    right: 6.4vw;
    top: 4.5vh;
    z-index: 10;
    font-size: 20pt;
    color: grey;
    text-align: right;
    background-color: transparent;
    padding: 0px;
    margin: 0px;
    min-width: 25px;
    box-shadow: none;
}

button.classroom-close:hover:enabled {
    background-color: transparent;
    box-shadow: none;
}

button.classroom-close:active {
    background-color: transparent;
    box-shadow: none;
}

.dialog-middle img.cl-display {
    margin-top: 0.1vh;
    cursor: default;
}

#classroom-content {
    background: #10161e;
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: calc(100vh - 80px);
    padding-left: 52px;
    padding-right: 52px;
    padding-top: 8px;
    border-bottom: 1px solid #1B2533;
}

.action-dialog {
    display: inherit;
    top: -0.1vh;
    padding-left: 10pt;
    width: 150px;
    left: 52vw;
}

.dropbtn {
    background-color: #393c39;
    color: white;
    font-size: 16px;
    border: 0;
    height: 1.51em;
    width: 150px;
    margin-top: 0;
    top: 0;
    margin: 0;
    border-bottom-right-radius: .2em;
    padding: 4px 10px 4px 10px;
}

.dropdown {
    display: inline-block;
    top: 0;
}

.dropdown-content {
    min-width: 150px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.dropdown-content a {
    color: white;
    padding: 12px 5.5px;
    text-decoration: none;
    font-size: 14px;
    background: #156CD5;
    border-radius: 98px;
    width: 127px;
    height: 24px;
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
}

.dropdown-content a:hover {
    background: #0d6ee6;
}

.dropdown:hover .dropbtn {
    background-color: #646564;
    margin: 0;
    padding: 0;
}


.dropbtn:after {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: .255em;
    vertical-align: .255em;
    content: "";
    border-top: .3em solid;
    border-right: .3em solid transparent;
    border-bottom: 0;
    border-left: .3em solid transparent;
}

.dropdown-content a:hover[disabled] {
    pointer-events: none;
}

.blink_me {
    animation: blinker 1s linear infinite;
    background-color: #337ab7 !important;
    border-color: #337ab7;
}

@keyframes blinker {
    50% {
        opacity: 0;
    }
}

.thumbnail-loader {
    display: block;
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 7px solid transparent;
    border-top-color: #3498db;
    -webkit-animation: spin 2s linear infinite;
    /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 2s linear infinite;
    /* Chrome, Firefox 16+, IE 10+, Opera */
}

.thumbnail-loader:before {
    content: "";
    position: absolute;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-radius: 50%;
    border: 7px solid transparent;
    border-top-color: #e74c3c;
    -webkit-animation: spin 3s linear infinite;
    /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 3s linear infinite;
    /* Chrome, Firefox 16+, IE 10+, Opera */
}

.thumbnail-loader:after {
    content: "";
    position: absolute;
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-radius: 50%;
    border: 7px solid transparent;
    border-top-color: #f9c922;
    -webkit-animation: spin 1.5s linear infinite;
    /* Chrome, Opera 15+, Safari 5+ */
    animation: spin 1.5s linear infinite;
    /* Chrome, Firefox 16+, IE 10+, Opera */
}

.btn-attendance {
    height: 40px;
    width: 40px;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    background-image: url(app/ext/ribbon/images/attendance.png);
    margin: 0 10px;
}

.classroom-group {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    align-items: center;
    background-color: #10161E !important;
    color: white;
    position: absolute;
    bottom: 0px;
    width: 100%;
    padding-inline: 64px;
    height: 80px;
}

.classroom-group .label-show-absent {
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-align: center;
    text-overflow: ellipsis;
    margin: 0 10px;
}

.undock-attendance {
    z-index: 1;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    position: relative;
}

.attendance-dialog-content{
    position: absolute;
    right: 0px;
    width: 360px;
    height: 100vh;
    padding: 32px;
    background-color: white !important;
}

.attendance-hidden {
    background: unset;
    width: auto;
    height: auto;
}

.classroom-screen-count {
    outline: none;
    width: 60px;
    height: 27px;
    margin: 0 10px;
}

#attendance_option{
    background-image: url(app/ext/ribbon/images/person.svg);
    width: 24px;
    background-size: 20px;
    display: block;
    background-repeat: no-repeat;
}

#attendance_box{
    display: flex;
    flex-direction: row;
    gap: 8px;
    border: 1px solid #1B2533;
    padding: 8px 16px 8px 16px;
    border-radius: 40px;
    cursor: pointer;
}

.end-session-btn {
    display: flex;
    flex-direction: row;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    background-color: #D53737;
}

#messenger_icon{
    background-image: url(app/ext/ribbon/images/chat.svg);
    width: 24px;
    background-size: 20px;
    display: block;
    background-repeat: no-repeat;
}

#chat_box{
    display: flex;
    flex-direction: row;
    gap: 8px;
    border: 1px solid #1B2533;
    padding: 8px 16px 8px 16px;
    border-radius: 40px;
    cursor: pointer;
}

.classroom-header{
    font-family: Lato;
    font-size: 20px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.01em;
    text-align: left;

}

.custom-select-layout {
    position: relative;
    display: inline-block;
    color: black;
  }
  /* Style for the icon */
  .custom-select-layout .custom-select-icon {
    position: absolute;
    top: -20px;
    width: 30px;
    height: 100%; 
    background-color: #f9f9f9;
    border-radius: 0 4px 4px 0;
    text-align: center;
    cursor: pointer;
    z-index: 1;
    border: 1px solid #1B2533;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    background: #1B2533;
    padding: 8px;
  }
  /* Style for the arrow icon */
  .custom-select-layout .custom-select-icon::after {
    content: url(app/ext/ribbon/images/layout.svg);
    position: absolute;
    top: 53%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  /* Style for the dropdown options */
  .custom-select-layout .custom-select-options {
    display: none;
    position: absolute;
    top: -216px;
    left: -40px;
    width: 100px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    border-radius: 0 0 4px 4px;
    z-index: 35;
  }
  /* Style for individual options */
  .custom-select-layout .custom-select-option {
    padding: 10px;
    cursor: pointer;
  }

  .custom-select-layout .custom-select-option:hover{
    background-color: #c6d6e8 !important;
  }

  .tooltip {
    position: absolute;
    background-color: #323C47;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    top: -70px;
    border-radius: 8px;
    color: white;
    z-index: 21;
}

#close_toogle_screen{
    left: 64px !important;
    width: max-content;
    gap: 8px;
    top: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    padding: 4px 8px;
}

#close_toogle_screen:hover{
    background: #1b2533 !important;
}

#close_text{
    font-family: Lato;
    font-size: 14px;
    font-weight: 700;
    line-height: 16.8px;
    letter-spacing: 0.01em;
    text-align: left;
    color: white;
}

.text_style{
    font-family: Lato;
    font-size: 14px;
    font-weight: 700;
    line-height: 16.8px;
    letter-spacing: 0.01em;
    text-align: left;
}

.class-ui {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #10161E;
    z-index: 20;
}

.class-ui .class-module > div {
    margin-bottom: 20px;
}

.class-ui .class-module > div:last-child {
    margin-bottom: 0;
}

.class-ui .class-module .logo {
    display: block;
    margin: 25px auto;
    width: 62px;
    height: 28px;
    background-image: url("app/ext/ribbon/images/groups.png");
}

.class-ui .class-module .empty-class {
    font-family: Lato;
    font-size: 24px;
    font-weight: 600;
    line-height: 28.8px;
    letter-spacing: 0.01em;
    text-align: center;
    color: #FFFFFF;
}

.class-ui .class-module .empty-class-log {
    font-family: Lato;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
    color: #FFFFFF;
}

#stopControl{
    background-color: #D53737;
    color: white;
    border-radius: 8px;
    width: max-content;
    min-width: 106px;
    padding: 2px 12px;
    font-family: Lato;
    font-size: 14px;
    font-weight: 700;
    line-height: 16.8px;
    letter-spacing: 0.01em;
    text-align: left;
    height: 24px;
    cursor: pointer;
    margin-inline: 15px;
}

.threeDots{
    background-image: url(app/ext/ribbon/images/threeDots.svg);
    width: 28px;
    height: 28px;
    background-repeat: no-repeat;
    background-size: 5px;
    position: relative;
    cursor: pointer;
    background-position: center;
    border-radius: 50%;
}

.threeDots:hover{
    background-color: #171829;
}

.raiseNotifications {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: 331px;
    display: flex;
    flex-direction: column-reverse;
    z-index: 1000;
}

.raiseNotification {
    background: #fff;
    border: 1px solid #ddd;
    padding: 10px;
    margin-top: 8px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.raiseNotification .back_hand_black {
    width: 21px;
    height: 23px;
    background-image: url('app/ext/ribbon/images/back_hand_black.svg');
}

.raiseNotification .bottom-message {
    color: #1A1A1A;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.raiseNotification .view-button {
    color: var(--Primary, #22538F);
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
    text-decoration-line: underline;
}

.raiseNotification .close-message {
    width: 24px;
    height: 24px;
    background-image: url('app/ext/ribbon/images/share_close.svg');
}

div.endSession-ui {
    height: 100%;
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    display: table;
    background: white;
    z-index: 20;
}

.endSession-ui .logo {
    display: block;
    margin: 0.5em auto;
    width: 48px;
    height: 48px;
    background-image: url("app/ext/ribbon/images/session-expire.png");
}

.endSession-ui .session-ended {
    font-family: Lato;
    font-size: 24px;
    font-weight: 600;
    line-height: 28.8px;
    letter-spacing: 0.01em;
    text-align: center;
    color: #1A1A1A;
}


.endSession-ui div.endSession-dialog-middle {
    width: 100%;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
}

.endSession-ui .close-tab {
    font-family: Lato;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
    color: #1A1A1A;
}