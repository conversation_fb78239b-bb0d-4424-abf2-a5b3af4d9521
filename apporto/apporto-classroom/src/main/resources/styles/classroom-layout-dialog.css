/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.classroom-layout-dialog {
    display: flex;
    width: 558px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    border-radius: 8px;
}

.classroom-layout-dialog .content {
    display: flex;
    padding: 32px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    border: 1px solid #CDCDCD;
    background: #FFF;
}

.classroom-layout-dialog .wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
}

.classroom-layout-dialog .title {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    position: relative;
}

.classroom-layout-dialog .options {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 24px;
    align-self: stretch;
}

.options .thumb-options {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
}

.options .thumb-sort {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
}

.thumb-sort .title {
    color: var(--Primary-Dark, #1A1A1A);
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
}

.options button {
    display: flex;
    height: 40px;
    padding: 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border-radius: 8px;
    background: #22538F;
}

.thumb-options .layout-auto {
    display: flex;
    padding: 24px;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: 8px;
    border: 2px solid #22538F;
    background: #EAF1FA;
}

.layout-auto .layout-auto-content {
    display: flex;
    align-items: flex-start;
    gap: 96px;
    flex: 1 0 0;
}

.content .auto-title {
    width: 66px;
    color: #1A1A1A;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
}

.content .auto-content {
    flex: 1 0 0;
    color: #22538F;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
}

.thumb-options .layout-fixed {
    display: flex;
    padding: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid #C7C7C7;
    background: #FFF;
}

.layout-fixed .description {
    display: flex;
    align-items: flex-start;
    gap: 127px;
    align-self: stretch;
}

.description .description-title {
    color: #1A1A1A;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
}

.description .description-content {
    flex: 1 0 0;
    color: #777;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 142.857% */
}

#dlg-vc-layout>* {
    margin: initial;
}

/* The Close Button */
.classroom-layout-dialog .enable-close {
    width: 24px;
    height: 24px;
    position: absolute;
    left: 486px;
    top: -16px;
    background-image: url(app/ext/ribbon/images/share_close.svg) !important;
}

.disable-close {
    cursor: not-allowed!important;
}

.classroom-layout-dialog .content button:hover {
    background-color: rgba(74, 144, 226, 0.8);
    cursor: pointer !important;
    border-radius: 8px;
}

.subtitle-layout {
    color: #1A1A1A;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px; /* 125% */
}

.title-layout {
    padding: 0px;
    color: #000;
    font-family: Lato;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.24px;
}

.classroom-layout-dialog 
div.footer {
    padding: 10px;
    padding-top: 20px;
}

.dialog .footer {
    text-align: right;
}

div.line {
    border-top: 1px solid rgba(0,0,0,0.125);
    margin: 0 1em;
}

.layout-fixed-menu {
    display: flex;
    margin-left: 161px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
}

.layout-fixed-menu .layouts_options {
    display: flex;
    height: 40px;
    padding: 0px 8px 0px 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid var(--Accent-Blue, #CED3D9);
    background: #FFF;
    position: relative;
}

.layout-fixed-menu .layouts_options .option_values {
    position: absolute;
    top: 39px;
    right: 20px;
    width: 250px;
    height: 150px;
    padding: 12px 8px 12px 8px;
    border: 1px solid #ced3d9;
    border-radius: 8px;
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 8px;
    box-shadow: 0 2px 12px 0 #00000014;
    z-index: 31;
}

.layout-fixed-menu .layouts_options p {
    color: var(--Secondary-Text, #777);
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin: 0px;
    padding: 4px 8px;
    font-family: Lato;
    text-align: left;
    cursor: pointer;
    border-radius: 8px;
}

.layout-fixed-menu .layouts_options .option_values p:hover {
    background-color: #d1d7e1 !important;
}

.sort-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
}

.sort-list .sort_options {
    display: flex;
    height: 40px;
    padding: 0px 8px 0px 8px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 8px;
    border: 1px solid var(--Accent-Blue, #CED3D9);
    background: #FFF;
    position: relative;
}

.sort-list .sort_options .option_values {
    position: absolute;
    top: 39px;
    left: 5px;
    width: 475px;
    height: 81px;
    padding: 12px 8px 12px 8px;
    border: 1px solid #ced3d9;
    border-radius: 8px;
    background: #fff;
    display: flex;
    flex-direction: column;
    gap: 8px;
    box-shadow: 0 2px 12px 0 #00000014;
}

.sort-list .sort_options p {
    color: var(--Secondary-Text, #777);
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin: 0px;
    padding: 4px 8px;
    font-family: Lato;
    text-align: left;
    cursor: pointer;
    border-radius: 8px;
}

.sort-list .sort_options .option_values p:hover {
    background-color: #d1d7e1 !important;
}

.show_bg_in_btn{
    background-image: none !important;
    padding-left: 12px !important;
}

.warning_mez_text{
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    color: #777;
}

.border-enabled {
    border: 2px solid #22538F !important;
    background: #EAF1FA !important;
}

.border-disabled {
    border: 1px solid #C7C7C7 !important;
    background: #FFF !important;
}

.span-enabled {
    color: #22538F !important;
}

.span-disabled {
    color: #777 !important;
}

.disable_text .disable_url{
    background: #FFF !important;
}

.disable_url span{
    color: #C7C7C7 !important;
}

.disable_text span{
    color: #C7C7C7 !important;
}

.classroom-layout-dialog .footer .stop{
    background-image: none;
    padding: 12px;
}