/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
/**
 * Module for managing classroom display.
 */
angular.module('classroomModule').controller('classroomController',
    ['$scope', '$injector', '$timeout', '$routeParams', '$rootScope',

    function classroomController($scope, $injector, $timeout, $routeParams, $rootScope) {

        // Required services
        var $http                    = $injector.get('$http');
        var $interval                = $injector.get('$interval');
        var $window                  = $injector.get('$window');
        var $q                       = $injector.get('$q');
        var remoteNotifications      = $injector.get('remoteNotificationService');
        var ClientIdentifier         = $injector.get('ClientIdentifier');
        var authenticationService    = $injector.get('authenticationService');
        var $translate               = $injector.get('$translate');
        var ua                       = navigator.userAgent;
        var guacNotification         = $injector.get('guacNotification');
        var thumbManager             = $injector.get('thumbManager');
        var userInfoService          = $injector.get('userInfoService');
        var utils                    = $injector.get('utilityService');

        $scope.ribbonService         = $injector.get('ribbonService');
        $scope.pagerService          = $injector.get('pagerService');
        $scope.circleLoaderService   = $injector.get('circleLoaderService');
        $scope.infoService           = $injector.get('infoService');
        $scope.circleLoaderService   = $injector.get('circleLoaderService');
        $scope.ServiceCall           = $injector.get('ServiceCall');
        $scope.dragOptions = {
            container: 'status-outer',
            handle: '.chat-head.controlbox-head',
        }
        $scope.dragOptionsCall = {
            container: 'status-outer',
            handle: '#call-confirm-dialog',
        }

        // Array for storing data about users retrieved from the server
        $scope.data                  = [];
        $scope.showTooltipForlayout  = false;
        $rootScope.showAccessControl = true;
        // Array with data ready for displaying
        $rootScope.usersData         = [];
        $rootScope.raisedHands       = [];
        $rootScope.items             = [];

        $rootScope.layoutDialogVisible   = false;

        // Array for storing classroom data with attendace
        $rootScope.classroomData     = [];
        $rootScope.classroomDataWithOptions = [];

        // Array for storing visiable users
        $rootScope.visibleUsers      = [];

        // Count of the present students in the classroom
        $rootScope.presentCount      = 0;

        // Count of the absent students in the classroom
        $rootScope.absentCount       = 0;

        // Order number for the present students
        $rootScope.orderIndex        = 0;
        ABSENT_ORDER_NUMBER          = 9999;

        // When trying to sort by students name or attendance status on attendance dialog
        $rootScope.manualSort        = false;
        $rootScope.sortByName        = false;
        $rootScope.sortByStatus      = false;

        // To distinguish which sorting button is pressed on attendance dialog
        FOCUS_SORT_NAME              = 0;
        FOCUS_SORT_STATUS            = 1;
        $rootScope.focusSortKey      = FOCUS_SORT_NAME;

        // Array for storing keys for each server in the group
        var authRequests             = [];
        $scope.selectedGroup         = $window.atob($routeParams.selectedGroup);
        $scope.appname               = $window.atob($routeParams.appname);
        $scope.clientIdentifier      = $routeParams.id;
        $scope.authToken             = $routeParams.at;

        $rootScope.showControlNotification = false;
        $rootScope.notificationValue = "";
        $rootScope.notficationInterval;
        $scope.raiseNotifications = [];

        /**
         * The access tokens for all backed servers are passed in the VC URL. The format of the VC URL is the following:
         *
         * https://<something>.apporto.com/hyperstream/#/classroom/<session-id>/<group-name>/<app-name>/<id-of-professor-server>/<srv-1-token>/<srv-2-token>/<srv-3-token>/<srv-4-token>
         *
         * If some of the tokens are missing, symbol '_' is used as a placeholder. This means that this server is not available.
         */
        authRequests.push($routeParams.srv1);
        authRequests.push($routeParams.srv2);
        authRequests.push($routeParams.srv3);
        authRequests.push($routeParams.srv4);

        // Flag for displaying classroom thumbnail
        $rootScope.classroomThumbnailVisible = false;
        // If noActiveMsg is set, the message "No active students" appears in the classroom.
        $scope.noActiveMsg = true;
        // Default page size for dropdown
        $scope.screensPerPage = 9;
        // Counter for managing initial loader
        $rootScope.cnt = 0;
        // Item selected for classroom thumbnail displaying
        $rootScope.selectedItem;
        // By default classroom session not ended
        $rootScope.classroomSessionEnd = false;

        var closeClassroomGroup = "";
        var parentToken;
        var parentName;

        closeClassroomGroup = '?text=' + $scope.selectedGroup + '&token=' + authRequests[$scope.authToken-1] +
                              '&delay=0&level=notify&title=closedClassroom&data=' + $scope.ribbonService.uuid;
        parentToken = authRequests[$scope.authToken-1];

        console.log("closeClassroomGroup:" + closeClassroomGroup);

        function initController() {
            $scope.init = true;

            thumbManager.init({
                parentToken: parentToken,
                serverCount: authRequests.filter(element => element !== '_').length,
                updateThumbnailsCallback: updateThumbnail
             });

            $scope.setPage(1);

            var parameters = {
                token: parentToken,
                id: $scope.clientIdentifier
            };
            var req = {
                method: 'GET',
                url: "api/session/ext/encryptedurl-jdbc/classroom/getFullName",
                params: parameters
            };

            $http(req)
            .then(function (response) {
                parentName = response.data.full_name;
            })
            .catch(function (response) {
                console.debug("Error getting full name: ", response.message);
            });
        }

        $scope.setPage = function setPage(page) {

            if (page < 1 || (page > $scope.pagerService.totalPages && $scope.pagerService.totalPages != 0)) {
                console.warn("setPage: page number out of range: " + page);
                return;
            }

            if ($scope.pagerService.handPages.indexOf(page) > -1) {
                var index = $scope.pagerService.handPages.indexOf(page);
                $scope.pagerService.handPages.splice(index, 1);

                var pages = document.getElementsByClassName('pagination')[0].getElementsByTagName("a");
                for (var i = 0; i < pages.length; i++) {
                    if (!isNaN(pages[i].text) && Number(pages[i].text) == page) {
                        pages[i].classList.remove("blink_me");
                    }
                }
            }

            var usersData = $rootScope.visibleUsers;
            $scope.currentPageThumbnailCounts = $scope.pagerService.pageSize;

            if (page == $scope.pagerService.currentPage) {
                $scope.pagerService.GetPager($rootScope.visibleUsers.length, page, getPageSize());
                $rootScope.items = usersData.slice($scope.pagerService.startIndex, $scope.pagerService.endIndex + 1);
            }
            else {
                // get pager object from service
                $scope.pagerService.GetPager($rootScope.visibleUsers.length, page, getPageSize());
                if ($scope.infoService.loadingBlocksVisible) {
                    $scope.infoService.loadingBlocksVisible = false;
                }
                $rootScope.items = usersData.slice($scope.pagerService.startIndex, $scope.pagerService.endIndex + 1);

                // add loader only on the start
                for (var i = 0; i < $rootScope.items.length && $scope.init; i++) {
                    $rootScope.items[i].load = true;
                }

                //maybe don't need this
                if ($scope.init) {
                    console.log("Before remote notification");
                    remoteNotifications.start();
                    $scope.ribbonService.isVirtualClassroomThumbnail = false;
                }

                $scope.init = false;
                setTimeout(function hideInfo() {
                    for (var i = 0; i < $rootScope.items.length; i++) {
                        $rootScope.items[i].load = false;
                    }
                    retrieveRaisedHands();
                }, 3000);
            }

            thumbManager.subscribe();
        }

        $scope.pagerService.GetPager = function GetPager(totalItems, currentPage, pageSize) {
            // default to first page
            currentPage = currentPage || $scope.pagerService.currentPage;

            // default page size is 4
            pageSize = pageSize || $scope.pagerService.pageSize;

            // calculate total pages
            var totalPages = Math.ceil(totalItems / pageSize);

            var startPage, endPage;
            if (totalPages <= 10) {
                // less than 10 total pages so show all
                startPage = $scope.pagerService.startPage;
                endPage = totalPages;
            }
            else {
                // more than 10 total pages so calculate start and end pages
                if (currentPage <= 6) {
                    startPage = 1;
                    endPage = 10;
                }
                else if (currentPage + 4 >= totalPages) {
                    startPage = totalPages - 9;
                    endPage = totalPages;
                }
                else {
                    startPage = currentPage - 5;
                    endPage = currentPage + 4;
                }
            }

            // calculate start and end item indexes
            var startIndex = (currentPage - 1) * pageSize;
            var endIndex = Math.min(startIndex + pageSize - 1, totalItems - 1);

            // create an array of pages to ng-repeat in the pager control
            var pages = _.range(startPage, endPage + 1);

            $scope.pagerService.totalItems = totalItems;
            $scope.pagerService.currentPage = currentPage;
            $scope.pagerService.pageSize = pageSize;
            $scope.pagerService.totalPages = totalPages;
            $scope.pagerService.startPage = startPage;
            $scope.pagerService.endPage = endPage;
            $scope.pagerService.startIndex = startIndex;
            $scope.pagerService.endIndex = endIndex;
            $scope.pagerService.pages = pages;
        }

        $window.onbeforeunload = function () {
            $scope.onCloseWindow();
        }

        $scope.onCloseWindow = function () {
            handleWindowClose();
        };

        function handleWindowClose() {
            var data = JSON.stringify({
                uuid: $scope.ribbonService.uuid,
                selectedGroup: $scope.selectedGroup,
            });

            var CLASSROOM_STOP = "Classroom Stop";
            var param = '?token=' + parentToken + '&delay=0&level=notify&title=' +
                        CLASSROOM_STOP + '&data=' + encodeURIComponent(btoa(data));

            navigator.sendBeacon('/hyperstream/api/session/ext/encryptedurl-jdbc/notify/' +
                                  $scope.clientIdentifier + param, false);

            if ($rootScope.usersData && $rootScope.usersData.length > 0) {
                for (var i = 0; i < $rootScope.usersData.length; i++) {
                    var info = $rootScope.usersData[i];
                    var param = '?text=closed.&token=' + info.token + '&delay=0&level=notify&title=closedClassroom&data=' +
                                $scope.ribbonService.uuid;
                    navigator.sendBeacon(info.location + 'api/session/ext/encryptedurl-jdbc/notify/' + info.id + param, false);
                }
            }

            if ($scope.clientIdentifier == null) {
                $scope.clientIdentifier = ClientIdentifier.fromString($routeParams.id);
            }

            if (parentToken == null) {
                parentToken = authenticationService.getCurrentToken();
            }

            // return opened group in the list of groups in the group picker
            navigator.sendBeacon('api/session/ext/encryptedurl-jdbc/notify/' + $scope.clientIdentifier + closeClassroomGroup);

            // reset delta state
            for (var i = 0; i < authRequests.length; i++) {
                if (authRequests[i] === '_')
                    continue;

                var params = "?token=" + authRequests[i];
                params = params + "&id=" + $scope.clientIdentifier + "&group=" + $scope.selectedGroup + "&appname=" + $scope.appname;
                if (utils.countTokens(authRequests) > 1) {
                    navigator.sendBeacon('/srv-' + (i+1) + '/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/close' + params);
                }
                else {
                    navigator.sendBeacon('api/session/ext/encryptedurl-jdbc/classroom/close' + params);
                }
            }

            remoteNotifications.stop();
            authRequests = [];

            thumbManager.destroy();

            $interval.cancel($scope.checkRetrieveRaisedHands);
        }

        $rootScope.retrieveAllSharedLinks = function retrieveAllSharedLinks() {
            $scope.data = [];
            if ($routeParams.hasOwnProperty("key") || utils.countTokens(authRequests) === 0) {
                console.debug("Invalid route: share link in the URL or auth requests are not present.");
                return;
            }

            const promises = [];

            const generateHttpRequest = (token, index) => {
                const httpParameters = {
                    token,
                    id: $scope.clientIdentifier,
                    group: $scope.selectedGroup,
                    appname: $scope.appname
                };
                const url = (index === undefined) ?
                    "/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getConnections" :
                    `/srv-${index + 1}/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getConnections`;

                return {
                    method: 'GET',
                    url,
                    params: httpParameters
                };
            };

            const handleHttpResponse = (response) => {
                const result = Object.entries(response.data);
                parseData(result);
                addNewUsers();
            };

            if ($window.location.protocol == "http:") {
                const req = generateHttpRequest(authRequests[0]);
                promises.push(
                    $http(req)
                    .then(handleHttpResponse)
                    .catch(error => console.debug("getConnections error ", error.message))
                );
            } else {
                authRequests.forEach((authToken, index) => {
                    if (authToken !== '_') {
                        const req = generateHttpRequest(authToken, index);
                        promises.push(
                             $http(req)
                             .then(handleHttpResponse)
                             .catch(error => console.debug("getConnections error ", error.message))
                        );
                    }
                });
            }


            $q.all(promises).then(function () {
                $rootScope.cnt++;

                if ($scope.data.length > 0) {
                    var toRemoveData = $scope.data.filter(item => item.removed).map(item => ({ id: item.id, server_id: item.server_id }));

                    /**
                     * remove old sessions
                     *
                     * Filter userData and remove objects that are in the toRemoveData array. Filtered result
                     * assign back to userData, so objects are effectively removed from userData.
                     */
                    if ($rootScope.usersData && $rootScope.usersData.length > 0) {
                        $rootScope.usersData = $rootScope.usersData.filter(el => !toRemoveData.some(item => item.id === el.id && item.server_id === el.server_id));
                    }

                    /**
                     * Same as above, just for the items.
                     */
                    if ($rootScope.items && $rootScope.items.length > 0) {
                        $rootScope.items = $rootScope.items.filter(el => !toRemoveData.some(item => item.id === el.id && item.server_id === el.server_id));
                    }

                    if ($rootScope.usersData.length === 0) {
                        $scope.pagerService.GetPager($rootScope.visibleUsers.length, 0);
                    }
                }

                let usersData = $rootScope.usersData.filter(el => el.thumbnail);

                $scope.noActiveMsg = usersData.length <= 0;

                let totalPages = Math.ceil($rootScope.visibleUsers.length / $scope.pagerService.pageSize);

                if (usersData.length == $scope.pagerService.totalItems) {
                    $scope.pagerService.totalPages = totalPages;
                    $scope.setPage($scope.pagerService.currentPage);
                }
                else if (totalPages >= $scope.pagerService.totalPages && usersData.length > $scope.pagerService.totalItems) {
                    $scope.pagerService.totalPages = totalPages;
                }

                if (usersData.length >= $scope.pagerService.totalItems) {
                    $scope.setPage($scope.pagerService.currentPage);
                }
                else if ($scope.pagerService.currentPage <= totalPages) {
                    $scope.setPage($scope.pagerService.currentPage);
                }
                else if ($scope.pagerService.currentPage > totalPages) {
                    $scope.setPage(totalPages);
                }
            }).finally(function () {
                $timeout( $rootScope.retrieveAllSharedLinks, 5000);
            });
        };

        var parseData = function parseData(filteredResults) {

            filteredResults.forEach(function (rData) {

                if (!rData || rData.length === 0 || !rData[1] || !rData[1].groups) {
                    return;
                }

                const sessionID = rData[0];
                const profileData = rData[1];
                const groups = JSON.parse(profileData.groups.replace(/\\/g, ''));   // Get all groups where user is member
                const VO = profileData.view_only_key;
                const FC = profileData.full_controll_key;
                const username = profileData.username;
                const windows_name = profileData.windows_name;
                const srv = profileData.server_id;
                const thumbnail = profileData.thumbnail;
                const removed = profileData.removed;
                let decodedThumbnail;

                // Don't parse data if the user is not a member of the selected group or view only link is missing
                if (!groups.includes($scope.selectedGroup) || !VO) {
                    return;
                }

                if (thumbnail != null) {
                    decodedThumbnail = atob(thumbnail);

                    $rootScope.usersData.forEach(function(userData) {
                        const id = Number(userData.id);
                        if (id === sessionID) {
                            userData.thumbnail = decodedThumbnail;
                        }
                    });
                }

                $scope.linkOrigin = $window.location.origin || ($window.location.protocol + '//' + $window.location.hostname + ($window.location.port ? (':' + $window.location.port) : ''));

                // Build base link
                if (!$window.location.pathname.includes('srv') && $window.location.protocol != "http:") {
                    $scope.link = $scope.linkOrigin + "/" + srv + $window.location.pathname + '#/';
                }
                else if ($window.location.protocol == "http:") {
                    $scope.link = $scope.linkOrigin + '/hyperstream/#/';
                }
                else {
                    // remove original server mark (/srv-x)
                    var pathname = $window.location.pathname.substring(6);
                    $scope.link = $scope.linkOrigin + "/" + srv + pathname + '#/';
                }

                const vo_href = VO ? ($scope.link + '?key=' + VO + '&connection_type=classroom') : "";
                const fc_href = FC ? ($scope.link + '?key=' + FC + '&connection_type=classroom') : "";


                var nnIndex = username.lastIndexOf('@');
                const notificationName = nnIndex > -1 ? username.substring(0, nnIndex) : username;

                // Create info object from response for rendering html template
                var index = $window.location.protocol != "http:" ? srv.split("-")[1].trim() - 1 : 0;
                var info = {
                    id: sessionID,
                    full: fc_href,
                    fc_key: FC,
                    view: vo_href,
                    vo_key: VO,
                    username: username,
                    windows_name: windows_name,
                    location: $scope.link.split('#')[0],
                    token: authRequests[index],
                    groups: groups,
                    waiting: false,
                    load: false,
                    thumbnail: decodedThumbnail,
                    raisedHand: false,
                    hoverTimer: null,
                    removed: removed,
                    permission: false,
                    notificationName: notificationName,
                    expanded: false,
                    ViewOnlyFrame: false,
                    fullControlLicence: false, //probabbly redudant with permission; earlier used to memorize licence duging one VC session
                    server_id: profileData.server_id
                }

                /**
                 * Find in userData (displayed students) the one with id equals to newly received student.
                 * Check if it is on the same server, if so, remove it. Otherwise just set removed to true
                 * and it will be removed when another server is queried.
                 *
                 * This use case is probably when student hits reload. It may end up on another server (srv-xxx)
                 * and his sharing credentials are different.
                 */
                if ($rootScope.usersData && $rootScope.usersData.length > 0) {
                    var index = $rootScope.usersData.findIndex(user => user.id === info.id);

                    // If found, remove if on the same server. Otherwise, just set it to removed.
                    // It will be removed when all servers are queried.
                    if (index > -1) {
                        if ($rootScope.usersData[index].server_id == info.server_id) {
                            $rootScope.usersData.splice(index, 1);
                        }
                        else {
                            $rootScope.usersData[index].removed = true;
                        }
                    }
                }

                // Reset the attendance status
                $scope.updateClassroomData();

                $scope.data.push(info);
                if ($scope.noActiveMsg && groups.includes($scope.selectedGroup)) {
                    $scope.noActiveMsg = false;
                }

                // Update the shareKeys when open thumbnail
                if ($rootScope.classroomThumbnailVisible && $rootScope.selectedItem.id === info.id) {
                    const updateThumbnail = (elementId, source) => {
                        const element = $("#" + elementId);
                        if (element.length) {
                            element.attr('src', source + "&name=" + info.notificationName).on('load', function() {
                                if (this.contentWindow) {
                                    this.contentWindow.location.reload();
                                    checkIframeLoaded(this);
                                }
                            });
                        }
                    };

                    if ($rootScope.selectedItem.vo_key !== info.vo_key) {
                        updateThumbnail('view_el', info.view);
                    }

                    if ($rootScope.selectedItem.fc_key !== info.fc_key) {
                        updateThumbnail('full_el', info.full);
                    }
                }
            })
        };

        /**
         * Filter out result of the getConnection call and keep only items that are not currently displayed
         * (not currently displayed == not present in the usersData).
         * Add new items to the end of the usersData.
         */
        function addNewUsers() {
            if ($rootScope.cnt >= 2 && $scope.infoService.loadingBlocksVisible) {
                $scope.infoService.loadingBlocksVisible = false;
            }

            // Get students that are not displayed in the classroom
            let newData = $scope.data.filter(el => {
                return $rootScope.usersData.findIndex(user => user.id === el.id && user.server_id === el.server_id) < 0;
            });

            // Add new students to the displayed users
            newData.forEach(item => {
                if (!item.removed) {
                    $rootScope.usersData.push(item);
                    $rootScope.viewOnlyNotification(item);
                }
            });

            $scope.noActiveMsg = $rootScope.usersData.length > 0;

            $scope.updateClassroomData();
        }

        $rootScope.viewOnlyNotification = function viewOnlyNotification(info) {
            var notify = new XMLHttpRequest();
            var data = JSON.stringify({
                uuid: $scope.ribbonService.uuid,
                selectedGroup: $scope.selectedGroup,
                selectedUser: $scope.ribbonService.userinfo.email
            });

            $translate('CLASSROOM.NOTIFY_SHARE_VIEW_ONLY').then(function (message) {
                var param = '?text=' + message + '&token=' + info.token +
                            '&delay=0&level=notify&title=Classroom display&data=' + encodeURIComponent(btoa(data));
                notify.open('POST', info.location + 'api/session/ext/encryptedurl-jdbc/notify/' + info.id + param, false);
                notify.send();
            });
        }

        $rootScope.fullControllNotification = function fullControllNotification(info) {
            if (info.fullControlLicence) {
                info.permission = true;
                $translate('CLASSROOM.CAN_CONTROL_USER_SCREEN', {USERNAME: $rootScope.selectedItem.notificationName}).then(function (message) {
                    $(".dialog-left-username").addClass("full");
                    $('.dialog-left-username').text(message);
                    $(".dropdown-content a").attr('disabled', 'disabled');
                    $rootScope.showAccessControl = false;
                });
                return;
            }

            $translate('CLASSROOM.CONTROL_REQUEST_SENT').then(function (text) {
                $rootScope.showControlNotification = true;
                clearTimeout($rootScope.notficationInterval);
                $rootScope.notificationValue = text + " "+ $rootScope.selectedItem.notificationName;
                $rootScope.notficationInterval = setTimeout(()=>{
                    $rootScope.showControlNotification = false;
                },3000)
            });

            var name = parentName ? parentName : "Faculty";
            $translate('CLASSROOM.REQUEST_REMOTE_CONTROL', {USERNAME: name}).then(function (message) {
                var httpParameters = {
                    text: message + '//' + name,
                    token: info.token,
                    delay: 180000,
                    level: 'WARNING',
                    title: 'Control Request',
                    command: 'CLASSROOM'
                };
                var req = {
                    method: 'POST',
                    url: info.location + 'api/session/ext/encryptedurl-jdbc/notify/' + info.id,
                    params: httpParameters
                };

                $http(req)
                .then(function (response) {
                    console.log("NOTIFY RESPONSE");
                    var notify = new XMLHttpRequest();

                    var waitResponse = $interval(function () {
                        var responseRequest = '?id=' + info.id + "&token=" + info.token;
                        notify.open('DELETE', info.location + 'api/session/ext/encryptedurl-jdbc/notify/getresponse' + responseRequest);
                        notify.send();
                        notify.onreadystatechange = function () {
                            if (notify.readyState === 4) { // DONE
                                if (!notify.response) {
                                    console.error("notify.response is empty or null");
                                    return;
                                }

                                console.log("notify.response=", notify.response);

                                var result = JSON.parse(notify.response);
                                if (result.status === 'Accept') {
                                    info.permission = true;
                                    info.waiting = false;
                                    if ($rootScope.classroomThumbnailVisible && info.expanded) {
                                        $translate('CLASSROOM.CAN_CONTROL_USER_SCREEN', {USERNAME: $rootScope.selectedItem.notificationName}).then(function (message) {
                                            $(".dialog-left-username").addClass("full");
                                            $('.dialog-left-username').text(message);
                                            $(".dropdown-content a").attr('disabled', 'disabled');
                                        });
                                        $translate('CLASSROOM.ACCEPTED_CONTROL_REQUEST', {USERNAME: $rootScope.selectedItem.notificationName}).then(function (message) {
                                            $rootScope.showAccessControl = false;
                                            clearTimeout($rootScope.notficationInterval);
                                            $rootScope.showControlNotification = true;
                                            $rootScope.notificationValue = $rootScope.selectedItem.notificationName+" "+ message;
                                            $rootScope.notficationInterval = setTimeout(()=>{
                                                $rootScope.showControlNotification = false;
                                            },3000)
                                        });
                                    }

                                    info.fullControlLicence = true;
                                    if (!info.raisedHand) {
                                        document.getElementById(info.id).style.border = "6px solid green";
                                    }
                                    $interval.cancel(waitResponse);
                                }
                                else if (result.status === 'Decline') {
                                    $translate('CLASSROOM.DECLINED_REMOTE_CONTROL', {USERNAME: info.username}).then(function (text) {
                                        $rootScope.showControlNotification = true;
                                        $rootScope.notificationValue = text;
                                        clearTimeout($rootScope.notficationInterval);
                                        $rootScope.notficationInterval = setTimeout(()=>{
                                            $rootScope.showControlNotification = false;
                                        },3000)

                                        $timeout(function hideInfo() {
                                            $interval.cancel(waitResponse);
                                            info.waiting = false;
                                            $scope.infoService.infoDialogVisible = false;
                                        }, $scope.ribbonService.thresholdInfo);
                                    });
                                }
                            }
                        }
                    }, 3000);
                })
                .catch(function (response) {
                    console.debug("Error getting full request response: ", response.message);
                });
            });
        }

        $rootScope.stopContol = function stopContol(){
            $translate('CLASSROOM.VIEWING_USER_SCREEN', {USERNAME: $rootScope.selectedItem.notificationName}).then(function (message) {
                $('.dialog-left-username').text(message);
                $(".dialog-left-username").removeClass("full");
                $(".dropdown-content a").removeAttr('disabled');
                $rootScope.showAccessControl = true;
                $rootScope.selectedItem.permission = false;
            });
        }

        $rootScope.requestControlScreen = function requestControlScreen(info){
            $scope.toggleViewOnly(info,true);
            $scope.ribbonService.attendanceVisible = false;
            for (var i = 0; i < $rootScope.classroomDataWithOptions.length; i++) {
                    $rootScope.classroomDataWithOptions[i].options = false;
            }
        }

        $scope.hide = function hide(info) {
            $scope.infoService.infoDialogVisible = false;
            info.waiting = false;
        }

        $scope.$on('$routeChangeSuccess', function (event, current, previous) {
            console.log("routeChangeSuccess classroomController");

            $scope.infoService.loadingBlocksVisible = true;
            // to use guacNotification
            $rootScope.$broadcast('guacLogin');

            $interval(function () {
                if (!navigator.onLine) { // to check online/offline status
                    var OK_ACTION = {
                        name      : "APP.ACTION_ACKNOWLEDGE",
                        callback  : function cancelCallback() {
                            guacNotification.showStatus(false);
                            window.close();
                        }
                    };

                    var actions = [OK_ACTION];

                    guacNotification.showStatus({
                        title   : "CLASSROOM.DISCONNECT_TITLE",
                        text    : {
                            key : "CLASSROOM.DISCONNECT_MESSAGE"
                        },
                        actions : actions
                    });
                }
            }, 1500);

            // If the current route is available
            if (current.$$route) {
                // Display classroom
                if (current.$$route.bodyClassName === "classroom") {
                    event.preventDefault();
                    if ($scope.ribbonService.toggleClassroom)
                        $scope.ribbonService.toggleClassroom();
                    $scope.ribbonService.isVirtualClassroom = true;

                    var subgroups = $scope.selectedGroup.split("=>");
                    var windowsName = '';
                    var selectedSubGroup = subgroups[subgroups.length - 1];

                    $q.all([
                        userInfoService.getUserInfo(windowsName, selectedSubGroup, parentToken, $scope.clientIdentifier)
                    ]).then(function() {
                        $q.all([
                            $scope.getClassroomGroupsData($scope.ribbonService.userinfo['adu'])
                        ]).then(function() {
                            startMessenger();
                            $rootScope.retrieveAllSharedLinks();
                            initController();

                            $scope.checkRetrieveRaisedHands = $interval(function () {
                                retrieveRaisedHands();
                            }, 1000);
                        });

                    });

                    // Notification for classroom start
                    var CLASSROOM_START = "Classroom Start";
                    var notify = new XMLHttpRequest();
                    var data = JSON.stringify({
                        uuid: $scope.ribbonService.uuid,
                        selectedGroup: $scope.selectedGroup,
                    });

                    var param = '?token=' + parentToken + '&delay=0&level=notify&title=' + CLASSROOM_START + '&data=' +
                                encodeURIComponent(btoa(data))+'&text=' + $scope.selectedGroup;
                    notify.open('POST',
                                '/hyperstream/api/session/ext/encryptedurl-jdbc/notify/' + $scope.clientIdentifier + param,
                                false);
                    notify.send();

                    //Add the title of Virtual Classroom
                    var title = current.$$route.title;
                    if (title) {
                        $translate(title).then(function setTitle(text) {
                            $scope.page.title = text + ' (' + $scope.selectedGroup + ')';
                        });
                    }

                    for (var i = 0; i < authRequests.length; i++) {
                        if (authRequests[i] === '_')
                            continue;

                        var httpParameters = {
                            token: authRequests[i],
                            id: $scope.clientIdentifier
                        };
                        var req = {
                            method: 'GET',
                            url: '/srv-' + (i + 1) + '/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getVersion',
                            params: httpParameters
                        };

                        $http(req).then(function (response) {
                            var info = response.data;
                            var app_js_load = "<link rel=\"preload\" as=\"script\" href=\"/srv-" + (i + 1) + "/hyperstream/app_" + info.version + ".js\" />";
                            var app_css_load = "<link rel=\"preload\" as=\"style\" href=\"/srv-" + (i + 1) + "/hyperstream/app_" + info.version + ".css\" />";
                            var angular_min_js_load = "<link rel=\"preload\" as=\"script\" href=\"/srv-" + (i + 1) + "/hyperstream/webjars/angular/1.8.2/angular.min.js\" />";
                            var jquery_min_js_load = "<link rel=\"preload\" as=\"script\" href=\"/srv-" + (i + 1) + "/hyperstream/webjars/jquery/3.3.1/dist/jquery.min.js\" />";
                            document.head.insertAdjacentHTML('afterbegin', app_js_load);
                            document.head.insertAdjacentHTML('afterbegin', app_css_load);
                            document.head.insertAdjacentHTML('afterbegin', angular_min_js_load);
                            document.head.insertAdjacentHTML('afterbegin', jquery_min_js_load);
                        })
                    }
                }
            }
        });

        $scope.endSession = function endSession() {
            var CANCEL_BTN = {
                name        : "DIALOGS.BUTTON_CANCEL",
                // Handle action
                callback    : function acknowledgeCallback() {
                    guacNotification.showStatus(false);
                },
                className   : 'cancelBtnClass'
            };

            var CLOSE_CLASSROOM_ACTION = {
                name    : "CLASSROOM.ACTION_CLOSE_CLASSROOM",
                callback: function closeClassroomCallback() {
                    // addition
                    $scope.onCloseWindow();
                    guacNotification.showStatus(false);
                    $rootScope.classroomSessionEnd = true;
                }
            }

            var actions = [CANCEL_BTN, CLOSE_CLASSROOM_ACTION];

            guacNotification.showStatus({
                title   : "CLASSROOM.END_SESSION_TITLE",
                actions : actions
            });
        }

        var retrieveRaisedHands = function retrieveRaisedHands() {
            if ($routeParams.hasOwnProperty("key") || utils.countTokens(authRequests) == 0) {
                return;
            }

            var handPromises = [];

            if ($window.location.hostname == 'localhost') {
                var httpParameters = {
                    token: authRequests[0],
                    id: $scope.clientIdentifier,
                };
                var req = {
                    method: 'GET',
                    url: "api/session/ext/encryptedurl-jdbc/classroom/getraisedhands",
                    params: httpParameters
                };

                handPromises.push(
                    $http(req).then(function (response) {
                        toggleHandRaise(response.data);
                        return response.data;
                    })
                    .catch(function (response) {
                        console.debug("Error getting raised hands: ", response.message);
                    })
                );
            }
            else {
                for (var i = 0; i < authRequests.length; i++) {
                    if (authRequests[i] === '_')
                        continue;

                    var authToken = authRequests[i];
                    var httpParameters = {
                        token: authToken,
                        id: $scope.clientIdentifier,
                    };

                    if (utils.countTokens(authRequests) == 1) {
                        var req = {
                            method: 'GET',
                            url: "/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getraisedhands",
                            params: httpParameters
                        };
                    }
                    else {
                        var req = {
                            method: 'GET',
                            url: "/srv-" + (i + 1) + "/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getraisedhands",
                            params: httpParameters
                        };
                    }

                    handPromises.push(
                        $http(req).then(function (response) {
                            toggleHandRaise(response.data);
                            return response.data;
                        })
                        .catch(function (response) {
                            console.debug("Error getting raised hands: ", response.message);
                        })
                    );
                }
            }

            $q.all(handPromises).then(function (results) {
                var data = results.reduce((p, c) => p + c);
                if (data == null || data.length == 0) {
                    for (var i = 0; i < $rootScope.classroomData.length; i++) {
                        if ($rootScope.classroomData[i].info.raisedHand) {
                            $rootScope.classroomData[i].info.raisedHand = false;

                            var element = document.getElementById($rootScope.classroomData[i].info.id);
                            if (!element) continue;

                            if ($rootScope.classroomData[i].info.fullControlLicence) {
                                element.style.border = "6px solid green";
                            }
                            else {
                                element.style.removeProperty("border");
                            }
                        }
                    }
                }
                else {
                    for (var i = 0; i < $rootScope.classroomData.length; i++) {
                        var id = Number($rootScope.classroomData[i].info.id);
                        if (data.indexOf(id) <= -1 && $rootScope.classroomData[i].info.raisedHand) {
                            $rootScope.classroomData[i].info.raisedHand = false;

                            var element = document.getElementById($rootScope.classroomData[i].info.id);
                            if (!element) continue;

                            if ($rootScope.classroomData[i].info.fullControlLicence) {
                                element.style.border = "6px solid green";
                            }
                            else {
                                element.style.removeProperty("border");
                            }
                        }
                    }
                }
            })
        };

        var toggleHandRaise = function toggleHandRaise(data) {
            if (data == null || data.length == 0) {
                return;
            }

            for (var i = 0; i < $rootScope.classroomData.length; i++) {
                var id = Number($rootScope.classroomData[i].info.id);

                if (!id) continue;

                if (data.indexOf(id) > -1 && !$rootScope.classroomData[i].info.raisedHand) {
                    $rootScope.classroomData[i].info.raisedHand = true;

                    var handPage = Math.ceil((i + 1) / $scope.pagerService.pageSize);
                    if (handPage != $scope.pagerService.currentPage && $scope.pagerService.handPages.indexOf(handPage) == -1) {
                        var pages = document.getElementsByClassName('pagination')[0].getElementsByTagName("a");
                        for (var i = 0; i < pages.length; i++) {
                            if (!isNaN(pages[i].text) && Number(pages[i].text) == handPage) {
                                pages[i].classList.add("blink_me");
                            }
                        }
                        $scope.pagerService.handPages.push(handPage);
                    }

                }

                if (data.indexOf(id) > -1 && $rootScope.classroomData[i].info.raisedHand) {
                    var element = document.getElementById($rootScope.classroomData[i].info.id);
                    var styleborder = "3px solid var(--Accent, #156CD5)";
                    if (element) {
                        if (element.style.border != styleborder) {
                            $scope.raiseNotifications.push({
                                id: id,
                                message: $rootScope.classroomData[i].info.username,
                                userinfo: $rootScope.classroomData[i].info,
                            });
                        }
                        element.style.border = styleborder;
                    }
                }

            }
        }

        // Function to remove notification
        $scope.removeNotification = function(index) {
            $scope.raiseNotifications.splice(index, 1);
        }

        // Expose the method outside
        $rootScope.toggleHandRaise = toggleHandRaise;

        $scope.removeHand = function removeHand(info) {
            var httpParameters = {
                token: info.token,
                id: info.id
            };

            var req = {
                method: 'DELETE',
                url: info.location + "api/session/ext/encryptedurl-jdbc/classroom/removehand",
                params: httpParameters
            };

            $http(req).then(function () {
                info.raisedHand = false;
                $scope.raiseNotifications = $scope.raiseNotifications.filter(notification => notification.message !== info.username);
                if (info.fullControlLicence) {
                    document.getElementById(info.id).style.border = "6px solid green";
                }
                else {
                    document.getElementById(info.id).style.removeProperty("border");
                }

                var usersIds = [];
                for (var i = 0; i < $rootScope.usersData.length; i++) {
                    usersIds[i] = $rootScope.usersData[i].id;
                }

                var userIndex = userIds.indexOf(info.id);
                var handPage = Math.ceil((userIndex + 1) / pageSize);
                if (handPage != $scope.pagerService.currentPage) {
                    var index = $scope.pagerService.handPages.indexOf(handPage);
                    if (index > -1) {
                        $scope.pagerService.handPages.splice(index, 1);
                    }

                    var pages = document.getElementsByClassName('pagination')[0].getElementsByTagName("a");
                    for (var i = 0; i < pages.length; i++) {
                        if (!isNaN(pages[i].text) && Number(pages[i].text) == page) {
                            pages[i].classList.remove("blink_me");
                        }
                    }
                }

            }).catch(function () {
            }).finally(function () {
                console.debug("Removed raised hand");
            });
        }

        $scope.toggleViewOnly = function toggleViewOnly(info,requestControlVal=false) {
            var index = info.server_id.split("-")[1].trim() - 1;
            if (authRequests[index] === '_')
                return;

            var authToken = authRequests[index];
            var httpParameters = {
                token: authToken,
                id: $scope.clientIdentifier,
                group: $scope.selectedGroup,
                appname : $scope.appname
            };

            if (utils.countTokens(authRequests) == 1) {
                var req = {
                    method: 'GET',
                    url: "/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getConnections/" + info.id,
                    params: httpParameters
                };
            }
            else {
                var req = {
                    method: 'GET',
                    url: "/srv-" + (index + 1) + "/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/getConnections/" + info.id,
                    params: httpParameters
                };
            }

            $scope.ribbonService.thumbnailLoad = true;
            $http(req).then(function (response) {
                var shareKeys = response.data;
                $rootScope.selectedItem = info;

                if (!shareKeys.view_only_key || !shareKeys.full_controll_key) {
                    var index = $rootScope.usersData.findIndex(function (user) {
                        return user.id == $rootScope.selectedItem.id;
                    });

                    if (index) {
                        $rootScope.usersData.splice(index, 1);
                    }

                    index = $rootScope.items.findIndex(function (user) {
                        return user.id == $rootScope.selectedItem.id;
                    });

                    if (index) {
                        $rootScope.items.splice(index, 1);
                    }

                    return;
                }

                var linkOrigin = '';
                var link = '';
                if (!$window.location.origin) {
                    linkOrigin = $window.location.protocol + '//' + $window.location.hostname +
                                ($window.location.port ? (':' + $window.location.port) : '');
                }
                else {
                    linkOrigin = $window.location.origin;
                }

                // Build base link
                if (!$window.location.pathname.includes('srv') && $window.location.protocol != "http:") {
                    link = linkOrigin + "/" + info.server_id + $window.location.pathname + '#/';
                }
                else if ($window.location.protocol == "http:") {
                    link = linkOrigin + '/hyperstream/#/';
                }
                else {
                    // remove original server mark (/srv-x)
                    var pathname = $window.location.pathname.substring(6);
                    link = linkOrigin + "/" + info.server_id + pathname + '#/';
                }

                if (shareKeys.view_only_key) {
                    info.vo_key = shareKeys.view_only_key;
                    if (info.vo_key != "") {
                        info.view = link + '?key=' + info.vo_key + '&connection_type=classroom';
                    }
                }
                else if (shareKeys.full_controll_key) {
                    info.fc_key = shareKeys.full_controll_key;
                    if (info.fc_key != "") {
                        info.full = link + '?key=' + info.fc_key + '&connection_type=classroom';
                    }
                }

                if (info.raisedHand) {
                    $scope.removeHand(info);
                }
                $('#view_el').attr('src', info.view + "&name=" + info.notificationName);
                checkIframeLoaded($("#view_el")[0]);

                $rootScope.classroomThumbnailVisible = true;

                var notify = new XMLHttpRequest();

                var param = '?text=start.&token=' + info.token + '&delay=0&level=notify&title=Start ViewScreen';
                notify.open('POST', info.location + 'api/session/ext/encryptedurl-jdbc/notify/' + info.id + param, false);
                notify.send();

                info.expanded = true;
                info.ViewOnlyFrame = true;
                $rootScope.selectedItem = info;
                if(info.username){
                    let nameArray = info.username.split(" ")
                    if (nameArray[1]) {
                        $rootScope.selectedItem.short_name = getShortName(nameArray[0],nameArray[1]);
                    }
                    else {
                        $rootScope.selectedItem.short_name = getShortName(nameArray[0],'');
                    }
                }

                if (info.permission) {
                    $('#full_el').attr('src', info.full + "&name=" + $rootScope.selectedItem.notificationName);
                    checkIframeLoaded($("#full_el")[0]);

                    $rootScope.fullControllNotification(info);
                    $rootScope.selectedItem.ViewOnlyFrame = false;
                    return;
                }

                $translate('CLASSROOM.VIEWING_USER_SCREEN', {USERNAME: $rootScope.selectedItem.notificationName}).then(function (message) {
                    $('.dialog-left-username').text(message);
                });

                $timeout(function () {
                    if ($rootScope.selectedItem !== null && $rootScope.selectedItem !== undefined)
                        $rootScope.selectedItem.ViewOnlyFrame = false;
                }, 6000);

                $('#full_el').attr('src', $rootScope.selectedItem.full + "&name=" + $rootScope.selectedItem.notificationName);

                var group = $scope.selectedSubGroup;
                $scope.ribbonService.licenses.hasChattingLicence = false;
                $scope.ribbonService.chattingVisible = false;

                var windowsName = info.windows_name;

                userInfoService.getUserInfo(windowsName).then(function(res) {
                    var data = res.data;

                    index = $scope.ribbonService.userlist[group].findIndex(function (user) {
                        return user.windows_name == data['email']
                    });
                    if (index > -1) {
                        $scope.ribbonService.activeMembers = [];
                        $scope.ribbonService.activeMembers.push($scope.ribbonService.userlist[group][index]);
                        $scope.ribbonService.licenses.hasChattingLicence = false;
                        $scope.ribbonService.chattingVisible = true;
                        $scope.ribbonService.isVirtualClassroomThumbnail = true;
                    }
                })
                .catch(function (res) {
                    console.debug("Chatting error userinfo fetch: ", res.message);
                })
                .finally(function () {
                    if(requestControlVal){
                        $rootScope.fullControllNotification(info);
                    }
                })
            })
            .catch(function (response) {
                $scope.infoService.loadingBlocksVisible = false;
                console.debug("getConnections error ", response.message);
            });
        }

        $rootScope.toggleViewOnlyScreen = function toggleViewOnlyScreen(info){
            $scope.toggleViewOnly(info);
            $scope.ribbonService.attendanceVisible = false;
            for (var i = 0; i < $rootScope.classroomDataWithOptions.length; i++) {
                $rootScope.classroomDataWithOptions[i].options = false;
            }
        }

        var updateThumbnail = function updateThumbnail(thumbId, thumb_server_id, thumbnail_b64) {
            if ($rootScope.classroomData) {
                for (var i = 0; i < $rootScope.classroomData.length; i++) {
                    var id = Number($rootScope.classroomData[i].info.id);
                    var server_id = $rootScope.classroomData[i].info.server_id;
                    if (id == thumbId && server_id == thumb_server_id) {
                        $rootScope.classroomData[i].info.thumbnail = "data:image/webp;base64,"+thumbnail_b64;
                    }
                }
            }

            if ($rootScope.usersData) {
                for (i = 0; i < $rootScope.usersData.length; i++) {
                    id = Number($rootScope.usersData[i].id);
                    var server_id = $rootScope.usersData[i].server_id;
                    if (id == thumbId && server_id == thumb_server_id) {
                        $rootScope.usersData[i].thumbnail = "data:image/webp;base64,"+thumbnail_b64;
                    }
                }
            }
        }

        function getMessengerHostname() {
            var httpParameters = {
                token: parentToken,
                id: $scope.clientIdentifier,
                name: 'messenger-server'
            };

            return $http({
                    method: 'GET',
                    url: 'api/session/ext/encryptedurl-jdbc/property',
                    params: httpParameters
                })
                .then(function (response) {
                    console.debug("Virtual Classroom: Messenger server hostname = " + response.data["messenger-server"]);
                    $scope.ribbonService.kurentoHostName = response.data["messenger-server"] || "";

                    if (!$scope.ribbonService.kurentoHostName) {
                        $scope.ribbonService.licenses.hasChattingLicence = false;
                        $scope.ribbonService.chattingVisible = false;
                        $scope.ribbonService.groups = [];
                    }
                })
                .catch(function (response) {
                    console.debug("Error getting messenger hostname info.");
                    $scope.ribbonService.licenses.hasChattingLicence = false;
                    $scope.ribbonService.chattingVisible = false;
                    $scope.ribbonService.groups = [];
                });
        }

        function getSubDomain() {
            var httpParameters = {
                token: parentToken,
                id: $scope.clientIdentifier,
            };
            var req = {
                method: 'GET',
                url: "api/session/ext/encryptedurl-jdbc/subdomain",
                params: httpParameters
            };

            return $http(req).then(function (response) {
                console.log('subdomain: ', response.data);
                $scope.ribbonService.GLOBAL_ROOM_NAME = response.data ? 'apporto/' + response.data : 'apporto';
            }).catch(function (error) {
                console.log('subdomain: ', error);
                $scope.ribbonService.GLOBAL_ROOM_NAME = 'apporto';
            })
        }

        /**
         * Fetch userlist
         */
        var getUserList = function getUserList() {
            var httpParameters = {
                token: parentToken,
                id: $scope.clientIdentifier,
            };
            var req = {
                method: 'GET',
                url: "api/session/ext/encryptedurl-jdbc/messenger/getUserList",
                params: httpParameters
            };

            $http(req)
            .then(function (response) {
                var data = response.data;
                var groups = [];
                $scope.ribbonService.userlist = data;

                $translate('MESSENGER.FACULTY').then(function setTitle(text) {
                    var subgroups = $scope.selectedGroup.split("=>");
                    $scope.selectedSubGroup = subgroups[subgroups.length - 1];
                    if ($scope.selectedSubGroup in data) {
                        group = $scope.selectedSubGroup;
                        var index = data[group].findIndex(function (user) {
                            return user['email'] == $scope.ribbonService.userinfo['email'];
                        });

                        if (index > -1) {
                            data[group].splice(index, 1);
                        }

                        groups.push(group);
                        for (var i = 0; i < data[group].length; i++) {
                            if ($scope.ribbonService.userinfo['email'].includes(data[group][i]['email'])) {
                                data[group].splice(i, 1);
                                i--;
                                continue;
                            }

                            var fullName = userInfoService.getFullName(data[group][i]['first name'], data[group][i]['last name']);
                            fullName = fullName ? fullName : data[group][i]['email'];

                            var initials = userInfoService.getInitials(fullName);
                            initials = initials ? initials : userInfoService.getInitials(data[group][i]['email']);

                            $scope.ribbonService.userlist[group][i]['name'] = fullName;
                            $scope.ribbonService.userlist[group][i]['initials'] = initials;
                            $scope.ribbonService.userlist[group][i]['windows_name'] = data[group][i]['email'];
                            $scope.ribbonService.userlist[group][i].sha256_username = sha256(data[group][i]['windows_name']);
                            $scope.ribbonService.userlist[group][i].state = '3';

                            if ($scope.ribbonService.userlist[group][i].Role == 'Faculty Admin') {
                                $scope.ribbonService.userlist[group][i].name += " (" + text + ")";
                            }

                            var index = $scope.ribbonService.apportoMembers.findIndex(function (member) {
                                return member['windows_name'] == $scope.ribbonService.userlist[group][i]['windows_name'];
                            });

                            if (index == -1) {
                                $scope.ribbonService.apportoMembers.push($scope.ribbonService.userlist[group][i]);
                            }
                        }
                    }

                    if (!$scope.ribbonService.userlist || $scope.ribbonService.userlist.length == 0) {
                        $scope.ribbonService.licenses.hasChattingLicence = false;
                        $scope.ribbonService.chattingVisible = false;
                        $scope.ribbonService.groups = [];
                    }
                    else {
                        $scope.ribbonService.groups = groups;
                        $scope.ribbonService.groups.sort();
                        $scope.ribbonService.licenses.hasChattingLicence = true;
                        $scope.ribbonService.chattingVisible = true;
                        $scope.ribbonService.chattingMinimized = true;
                    }
                });
            })
            .catch(function (response) {
                console.debug("Chatting error group fetch: ", response);
                $scope.ribbonService.userlist = [];
                $scope.ribbonService.licenses.hasChattingLicence = false;
            })
            .finally(function () { })
        }

        /**
         * Function that retrieves licence information from server
         */
        var startMessenger = function startMessenger() {

            var httpParameters = {
                token: parentToken,
                id: $scope.clientIdentifier
            };

            $http({
                method: 'GET',
                url: 'api/session/ext/encryptedurl-jdbc/licences',
                params: httpParameters
            })
            .then(function (response) {
                if (/Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini|Mobile|mobile|CriOS/i.test(ua)) {
                    $scope.ribbonService.licenses.hasMessengerLicence = false;
                }
                else {
                    $scope.ribbonService.licenses.hasMessengerLicence = response.data.hasMessenger;
                    if ($scope.ribbonService.licenses.hasMessengerLicence) {
                        $q.all([
                            getMessengerHostname(),
                            getSubDomain()
                        ])
                        .then(function () {
                            $q.all([
                                getUserList(),
                            ])
                            .then(function () {
                                $scope.ribbonService.register();
                            });
                        });
                    }
                }
            })
            .catch(function(response) {
                console.log("Error getting licence info:", response.message);
            });

        }

        $scope.roomStyle = function roomStyle($index) {
            var right = ($scope.ribbonService.chattingMinimized ? 110 : 380) + 380 * $index;
            return {
                right: right
            }
        }

        $rootScope.$watch('selectedItem.permission', function changePermission(visible) {
            if (visible) {
                $scope.ribbonService.thumbnailLoad = true;
                setTimeout(function () {
                    $('#full_el').attr('src', $rootScope.selectedItem.full + "&name=" + $rootScope.selectedItem.notificationName);
                    checkIframeLoaded($('#full_el')[0]);
                }, 1000);
            }
        });

        $scope.showLayoutDialog = function showLayoutDialog() {
            $rootScope.layoutDialogVisible = true;
        }

        $scope.showAttendanceDialog = function showAttendanceDialog() {
            $scope.ribbonService.attendanceVisible = !$scope.ribbonService.attendanceVisible;
        }

        /**
         * Update the classroom data for attendance
         */
        $scope.updateClassroomData = function updateClassroomData() {
            let usersDataCopy = angular.copy($rootScope.usersData);

            // Update classroomData based on user status
            if ($rootScope.classroomData && $rootScope.classroomData.length > 0) {
                $rootScope.classroomData.forEach((cdata, i) => {
                    let index = usersDataCopy.findIndex(udata => udata['windows_name'] === cdata['windows_name']);

                    if (index > -1) {
                        if (!usersDataCopy[index]['removed']) {
                            $rootScope.classroomData[i]['status'] = "Present";
                            if (!$rootScope.classroomData[i]['order'] || $rootScope.classroomData[i]['order'] === ABSENT_ORDER_NUMBER) {
                                $rootScope.classroomData[i]['order'] = $rootScope.orderIndex;
                                $rootScope.orderIndex++;
                            }
                        }
                        else {
                            $rootScope.classroomData[i]['status'] = "Absent";
                            $rootScope.classroomData[i]['order'] = ABSENT_ORDER_NUMBER;
                        }

                        $rootScope.classroomData[i]['info'] = usersDataCopy[index];
                        usersDataCopy.splice(index, 1);
                    }
                    else {
                        $rootScope.classroomData[i]['status'] = "Absent";
                        $rootScope.classroomData[i]['order'] = ABSENT_ORDER_NUMBER;
                        $rootScope.classroomData[i]['info'] = {};
                    }
                });
            }

            // Sort classroomData based on order, name, or status
            if (!$rootScope.manualSort) {
                $rootScope.classroomData.sort((a, b) => a.order - b.order);
            }
            else {
                if ($rootScope.focusSortKey === FOCUS_SORT_NAME) {
                    const sortFunc = ($rootScope.sortByName) ? ((a, b) => b.name.localeCompare(a.name)) : ((a, b) => a.name.localeCompare(b.name));
                    $rootScope.classroomData.sort(sortFunc);
                }
                else {
                    const sortFunc = ($rootScope.sortByStatus) ? ((a, b) => b.order - a.order) : ((a, b) => a.order - b.order);
                    $rootScope.classroomData.sort(sortFunc);
                }
            }

            // Filter and sort visibleUsers based on status
            if (!$scope.ribbonService.showAbsent) {
                $rootScope.visibleUsers = $rootScope.classroomData.filter(user => user.status === "Present");
                if ($rootScope.focusSortKey === FOCUS_SORT_STATUS) {
                    $rootScope.visibleUsers.sort((a, b) => a.order - b.order);
                }
                $rootScope.classroomDataWithOptions = $rootScope.visibleUsers;
            }
            else {
                $rootScope.visibleUsers = $rootScope.classroomData;
            }

            $rootScope.presentCount = $rootScope.absentCount = 0;
            $rootScope.classroomData.forEach((user) => {
                user.status == "Present" ? $rootScope.presentCount++ : $rootScope.absentCount++;
            });
        }

        //toggle options on thumbanil
        $rootScope.toggleOptionFunc = function toggleOptionFunc(windows_name) {
            console.log(windows_name);
            $rootScope.visibleUsers = $rootScope.visibleUsers.map((user) => {
                if (user.windows_name===windows_name) {
                    return Object.assign({}, user, {toggleOptions: !user.toggleOptions});
                }
                else {
                    return Object.assign({}, user, {toggleOptions: false});
                }
            });

            $rootScope.classroomData = $rootScope.classroomData.map((user) => {
                if (user.windows_name===windows_name) {
                    return Object.assign({}, user, {toggleOptions: !user.toggleOptions});
                }
                else {
                    return Object.assign({}, user, {toggleOptions: false});
                }
            });
        }

        /**
         * Get the short name of the user
         *
         * @param {String} firstname
         * @param {String} lastname
         * @returns {String}
         */
        function getShortName(firstname, lastname) {
            if (!firstname && !lastname)
                return '';

            if (!firstname)
                firstname = '';

            if (!lastname)
                lastname = '';

            return lastname == '' ? firstname.substring(0, 1) : (firstname.substring(0, 1) + lastname.substring(0, 1));
        }

        /**
         * Fetch the classroom data
         *
         * @param {String} cloudUsername
         */
        $scope.getClassroomGroupsData = function getClassroomGroupsData(cloudUsername) {
            // if sub group exist, use sub group name
            var subgroups = $scope.selectedGroup.split("=>");
            var subgroup = subgroups[subgroups.length - 1];
            var parameters = {
                token: parentToken,
                id: $scope.clientIdentifier,
                cloud_username: cloudUsername,
                group_name: subgroup
            };

            var req = {
                method: 'GET',
                url: "api/session/ext/encryptedurl-jdbc/classroom/getClassroomGroupsData",
                params: parameters
            };

            return $http(req)
            .then(function (response) {
                if (response.data.data && response.data.data.length > 0) {
                    response.data.data.map((data) => {
                        if (data['windows_username'] == cloudUsername)
                            return;

                        var fullName = userInfoService.getFullName(data['firstname'], data['lastname']);
                        fullName = fullName ? fullName : data['email'];
                        var user_data = {
                            name: fullName,
                            username: fullName,
                            short_name: getShortName(data['firstname'], data['lastname']),
                            windows_name: data['windows_username'],
                            status: "Absent",
                            toggleOptions: false
                        }
                        $rootScope.classroomData.push(user_data);
                    });

                    // Sort the data by name
                    $rootScope.classroomData.sort(function(a, b){
                        let x = a.name.toUpperCase();
                        let y = b.name.toUpperCase();
                        if (x < y) {return -1;}
                        if (x > y) {return 1;}
                        return 0;
                    });

                    // Reset the attendance status
                    $scope.updateClassroomData();
                }
            })
            .catch(function (response) {
                console.debug("Error getting classroom groups data: ", response.message);
            });
        }

        /**
         * Change the size of thumbnails per page
         */
        $scope.ChangedThumbnailCountsPerPage = function ChangedThumbnailCountsPerPage() {
            $scope.pagerService.pageSize = getPageSize();

            $scope.pagerService.GetPager($rootScope.visibleUsers.length, 0, $scope.pagerService.pageSize);
            $scope.setPage(1);
        }

        /**
         * Toggle shown/hidden absent students
         */
        $scope.toggleShowAbsent = function toggleShowAbsent() {
            $scope.ribbonService.showAbsent = !$scope.ribbonService.showAbsent;
            $scope.updateClassroomData();
            $scope.ChangedThumbnailCountsPerPage();
        }

        /**
         * Get the size of thumbnails per page
         *
         * @returns pageSize
         */
        var getPageSize = function getPageSize() {
            if ($scope.thumbnailCountsPerPage == -1) {
                if ($rootScope.visibleUsers.length <= 4) {
                    return 4;
                }
                else if ($rootScope.visibleUsers.length <= 9) {
                    return 9;
                }
                else if ($rootScope.visibleUsers.length <= 16) {
                    return 16;
                }
                else {
                    return 25;
                }
            }
            else {
                return $scope.thumbnailCountsPerPage;
            }
        }

        let optionsDefined = true;
        $scope.toggleOptions = function toggleOptions() {
            var options = document.getElementById("customOptions");
            if (optionsDefined) {
                let layoutBtn = document.getElementsByClassName("custom-select-layout")[0];
                layoutBtn.addEventListener("blur", () => {
                    options.style.display = "none";
                });
                optionsDefined = false;
            }
            if (options.style.display === "block") {
                options.style.display = "none";
            }
            else {
                options.style.display = "block";
            }
        }

        // Function to select an option for layout
        $scope.selectOption = function selectOption(value) {
            $scope.thumbnailCountsPerPage = value;
            $scope.ChangedThumbnailCountsPerPage();
        };

        $rootScope.$watch('layoutSettingUpdated', function layoutSettingUpdated(updated) {
            if (updated) {
                // Layout
                if ($rootScope.currentLayoutType == 0) // Auto
                    $scope.selectOption('-1');
                else {
                    if ($rootScope.currentLayoutOption == 0) // 2x2
                        $scope.selectOption('4');
                    else if ($rootScope.currentLayoutOption == 1) // 3x3
                        $scope.selectOption('9');
                    else if ($rootScope.currentLayoutOption == 2) // 4x4
                        $scope.selectOption('16');
                    else // 5x5
                        $scope.selectOption('25');
                }

                // Sort
                if ($rootScope.currentSortOption == 0) { // By Sign-In
                    // Manual sort means the faculty's sort like the alphabetic
                    // Not manual sort means the auto (sign-in) sort
                    $rootScope.manualSort = false;
                }
                else { // Alphabetically
                    $rootScope.manualSort = true;
                    $rootScope.focusSortKey = FOCUS_SORT_NAME;
                }

                $scope.updateClassroomData();
            }
            $rootScope.layoutSettingUpdated = false;
        });

        $rootScope.closeViewDialog = function closeViewDialog() {

            $rootScope.classroomThumbnailVisible = false;
            $rootScope.showAccessControl = true;

            closeLightbox($rootScope.selectedItem);

            var notify = new XMLHttpRequest();
            var param = '?text=end.&token=' + $rootScope.selectedItem.token +'&delay=0&level=notify&title=End ViewScreen';
            notify.open('POST', $rootScope.selectedItem.location + 'api/session/ext/encryptedurl-jdbc/notify/'
                        + $rootScope.selectedItem.id + param, false);
            notify.send();

            $('.dialog-left-username').text("You are viewing " + $rootScope.selectedItem.notificationName + "'s screen");
            $(".dialog-left-username").removeClass("full");
            $(".dropdown-content a").removeAttr("disabled");

            $('#full_el').attr('src', '');
            $('#view_el').attr('src', '');

            $rootScope.selectedItem = null;

            $scope.ribbonService.chattingBoxMinimized = true;
            $scope.ribbonService.chattingMinimized = true;
            $scope.ribbonService.isVirtualClassroomThumbnail = false;
            $scope.ribbonService.licenses.hasChattingLicence = true;
            $scope.ribbonService.activeMembers = [];
        }

        var closeLightbox = function closeLightbox(item){
            if (item === null || item === undefined)
                return;
            item.expanded = false;
            item.fullControlLicence = false;
            item.permission = false;
            item.ViewOnlyFrame = false;
            var elThumbnail = document.getElementById(item.id);
            if (elThumbnail !== null && elThumbnail !== undefined)
                elThumbnail.style.removeProperty("border");
        }

        var checkIframeLoaded = function checkIframeLoaded(element) {
            var iframeDoc = element.contentDocument || element.contentWindow.document;

            var checkIframeLoadedInterval = $interval(function () {

                if (iframeDoc.readyState == 'complete') {

                    $timeout(function () { $scope.ribbonService.thumbnailLoad = false; }, 5000);
                    $interval.cancel(checkIframeLoadedInterval);

                }

            }, 50);
        }
    }
]); // end classroom controller
