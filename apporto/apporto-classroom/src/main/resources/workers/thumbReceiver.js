/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A worker that sends thumbnails to the server using the
 * WebSocket connection.
 * 
 * <AUTHOR> mark<PERSON>@apporto.com
 */

/**
 * The worker is used to receive thumbnail data from the server.
*/

var wsUri = null;

var ws_protocol = {
    "http:":  "ws:",
    "https:": "wss:"
};

var baseUrl = null;
var clientId = null;
var token = null;

var websockets = {};

// If the worker is running locally, we need to set the flag to true and not use server identification in the url,
var isRunningLocally = false;

self.onmessage = function (e) {
    if (e.data.command === null) {
        console.warn('thumbReceiver worker message command is null');
        return;
    }

    console.debug('thumbReceiver worker message command: '+e.data.command);

    switch (e.data.command) {
        case 'init':
            init(e.data);
            break;
        case 'connect':
            connect(e.data);
            break;
        case 'disconnect':
            disconnect();
            break;
        case 'subscribe':       // Subscribe to thumbs from given session IDs
            thumbSubscribe(e.data.srv_id, e.data.thumbList);
            break;
        case 'unsubscribe':
            break;
        case 'request':         // Request thumbs for given session IDs
            thumbRequest(e.data.srv_id, e.data.thumbList);
            break;
        default:
            console.warn('thumbReceiver worker message command is not recognized: ' + e.data.command);
    }
};

self.init = function init(data) {
    baseUrl = new URL(data.baseUrl);
    wsUri = ws_protocol[baseUrl.protocol] + '//' + baseUrl.host;

    isRunningLocally = baseUrl.hostname === 'localhost';

    clientId = encodeURIComponent(data.clientId);
    token = encodeURIComponent(data.token);
};

self.connect = function connect(data) {
    for (let i = 0; i < data.numServers; i++) {
        let key = "srv-"+(i+1);

        self.connectServer(key);
    }
};

self.connectServer = function connectServer(key) {
    if (isRunningLocally) {
        websockets[key] = new WebSocket(wsUri + "/hyperstream/thumbnails/retrieve/" + clientId);
    } else {
        websockets[key] = new WebSocket(wsUri + "/" + key + "/hyperstream/thumbnails/retrieve/" + clientId);
    }

    websockets[key].onopen = function(evt) {
        console.log('WebSocket opened');
    };
    websockets[key].onclose = function(evt) {
        console.log('WebSocket closed');
    };
    websockets[key].onmessage = function(evt) {
        console.log('WebSocket message');
        evt.data.arrayBuffer().then(function(buffer) {
            let key = extractServerKey(evt.target.url);
            self.postMessage({srv_id:key, buffer}, [buffer]);
        });
    };
    websockets[key].onerror = function(evt) {
        console.log('WebSocket error');
    }; 

    function extractServerKey(url) {
        if (isRunningLocally) {
            return 'srv-1';
        }

        const regex = /\/(srv-\d)\//;
        let match = url.match(regex);
        return match[1];
    }
};

self.disconnect = function disconnect() {
    for (let key in websockets) {
        websockets[key].close();
    }
    self.close();
};


self.thumbRequest = function thumbRequest(srv_id, thumbList) {
    console.debug('Requesting thumbs for session IDs: '+thumbList+" from server "+srv_id);

    if (websockets[srv_id].readyState === WebSocket.OPEN) {
        websockets[srv_id]
            .send(JSON.stringify({
                                    command: "request",
                                    thumbList: thumbList
                                }));
    } else {
        console.warn('WebSocket is not open');

        if (websockets[srv_id].readyState === WebSocket.CLOSED) {
            console.log('Reconnecting to WebSocket');
            connectServer(srv_id);
        }
    }
};


self.thumbSubscribe = function thumbSubscribe(srv_id, thumbList) {
    console.debug('Subscribing thumbs for session IDs: '+thumbList+" from server "+srv_id);

    if (websockets[srv_id].readyState === WebSocket.OPEN) {
        websockets[srv_id]
            .send(JSON.stringify({
                                    command: "subscribe",
                                    thumbList: thumbList
                                }));
    } else {
        console.warn('WebSocket is not open');

        if (websockets[srv_id].readyState === WebSocket.CLOSED) {
            console.log('Reconnecting to WebSocket');
            connectServer(srv_id);
        }
    }
};

