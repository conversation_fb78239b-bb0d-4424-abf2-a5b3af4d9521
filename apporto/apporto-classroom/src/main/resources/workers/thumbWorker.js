/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * A worker that sends thumbnails to the server using the
 * WebSocket connection.
 * 
 * <AUTHOR> mark<PERSON>@apporto.com
 */

/**
 * The worker is using the pica library to resize the thumbnails.
 * It is not possible to use the pica library directly in the worker,
 * because the worker does not have access to the DOM, and to the window object.
 * 
 * This is why the pica library is not included in the js array in the
 * guac-manifest.json file, but inteaed it is loaded as the resource (in the same
 * way as the worker itself is loaded).
*/

var wsUri = null;

var ws_protocol = {
    "http:":  "ws:",
    "https:": "wss:"
};

var baseUrl = null;
var clientId = null;
var dataSource = null;
var token = null;

var pica = null;

// boolean to check if browser is Firefox
var isFirefox = false;

const THUMB_WIDTH = 1000;
const UPLOAD_TIMEOUT = 2 * 1000; // set the timeout of the api to upload thumbnail less than 3 sec

self.onmessage = function (e) {
    if (e.data.command === null) {
        console.warn('Classroom worker message command is null');
        return;
    }

    console.debug('Classroom worker message command: '+e.data.command);

    switch (e.data.command) {
        case 'init':
            init(e.data);
            break;
        case 'connect':
            connect();
            break;
        case 'disconnect':
            disconnect();
            break;
        case 'send':
            send(e.data.originalImage);
            break;
        default:
            console.warn('Classroom worker message command is not recognized: ' + e.data.command);
    }
};

self.init = function init(data) {
    importScripts('pica.min.js');

    baseUrl = new URL(data.baseUrl);
    wsUri = ws_protocol[baseUrl.protocol] + '//' + baseUrl.host + '/hyperstream/thumbnails/';

    clientId = encodeURIComponent(data.clientId);
    dataSource = encodeURIComponent(data.dataSource);
    token = encodeURIComponent(data.token);

    pica = new pica({createCanvas: self.createCanvas});

    isFirefox =  navigator.userAgent.indexOf("Firefox") > -1;
};

self.createCanvas = function createCanvas(width, height) {
    var canvas = new OffscreenCanvas(width, height);
    return canvas;
};

self.connect = function connect() {
    websocket = new WebSocket(wsUri+clientId);

    websocket.onopen = function(evt) {
        console.log('WebSocket opened');
    };
    websocket.onclose = function(evt) {
        console.log('WebSocket closed');
    };
    websocket.onmessage = function(evt) {
        console.log('WebSocket message');
    };
    websocket.onerror = function(evt) {
        console.log('WebSocket error');
    };
};

self.disconnect = function disconnect() {
    websocket.close();
    self.close();
};

self.send = function send(originalImage) {
    if (websocket.readyState === WebSocket.OPEN) {
        createImageBitmap(new Blob([originalImage], {type: 'image/webp'}))
            .then(function(imageBitmap) {
                if (imageBitmap.width === 0) {
                    throw new Error("Image bitmap width is zero, cannot resize.");
                }
                return pica.resize(from = imageBitmap, to = new OffscreenCanvas(THUMB_WIDTH, imageBitmap.height * THUMB_WIDTH / imageBitmap.width));
            })
            .then(function(result) {
                if(isFirefox) {
                    return result.convertToBlob({type: 'image/webp', quality: 0.25});
                }
                return pica.toBlob(result, 'image/webp', 0.25);
            })
            .then(function(result) {
                result.arrayBuffer().then(function(arrayBuffer) {
                    websocket.send(arrayBuffer);
                });
            })
            .catch(function(err) {
                console.error("Error in promises:", err);
             });
    } else {
        console.warn('WebSocket is not open');

        if (websocket.readyState === WebSocket.CLOSED) {
            console.log('Reconnecting to WebSocket');
            connect();
        }
    }
};