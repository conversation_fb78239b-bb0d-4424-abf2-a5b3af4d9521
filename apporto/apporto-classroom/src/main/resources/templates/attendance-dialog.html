<div class="dialog-middle"> 
    <div class="attendance-dialog-content dialog" id="dlg-attendance">
        <div id="move-attendance-dialog">
            <button class="close classroom" ng-click="ribbonService.attendanceVisible = false" tabindex="0" aria-label="Close" style="position: absolute;">&times;</button>
            <div class="title">
                {{'CLASSROOM.ATTENDANCE' | translate}}
            </div>
            <div class="attendance_status">
                <div class="status_title">
                    <span class="count">{{$root.presentCount}}</span>
                    <span class="status_class" style="font-size: 18px;">{{'CLASSROOM.PRESENT' | translate}}</span>
                </div>
                <div class="status_title">
                    <span class="count">{{$root.absentCount}}</span>
                    <span class="status_class" style="font-size: 18px;">{{'CLASSROOM.ABSENT' | translate}}</span>
                </div>
            </div>
        </div>
        <div class="attendance_content">
            <div class="present_header" style="position: relative;" tabindex="10" ng-blur="showSortingOptions=false">
                <div>{{'CLASSROOM.PRESENT' | translate}} ({{$root.presentCount}})</div>
                <div class="present_header sorting_button" style="gap: 8px;" ng-click="showSortingOptions = !showSortingOptions">
                    <p>
                        Sort: {{sortType}}
                    </p> 
                    <span>
                       <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                           <path d="M5.99953 7.0377L0.345703 1.3839L1.39953 0.330078L5.99953 4.93008L10.5995 0.330078L11.6534 1.3839L5.99953 7.0377Z" fill="#777777"/>
                           </svg>
                    </span> 
                </div>
                <div class="sorting_option" ng-show="showSortingOptions">
                     <p ng-click="sortByName(); showSortingOptions=false">{{userSortTypes[0]}}</p>
                     <p ng-click="sortBySignIn(); showSortingOptions=false">{{userSortTypes[1]}}</p>
                </div>
            </div>
           

            <div class="table_body" style="height: 25vh; border-bottom: 1px solid #EDEBEB; margin-bottom: 24px;">
                <div ng-repeat="x in $root.visibleUsers" class="table_row" ng-if="x.status === 'Present'">
                    <div class="content_cell">
                        <div class="avatar">
                            <div role="none" class="avatar-text">
                                <div class="avatar-short-text">
                                    {{x.short_name}}
                                </div>
                            </div>
                        </div> 
                        <div role="none" class="username" title="{{x.name}}">
                            <p class="name_text">
                                {{x.name}}
                            </p>
                        </div>
                    </div>
                    <div class="status_cell content_cell" style="position: relative;">
                        <div style="transform: rotate(90deg); padding: 0px 4px; padding-bottom: 11px; border-radius: 8px; user-select: none;"  ng-click="openOptions($index)"      ng-class="{'active_options': $root.classroomDataWithOptions[$index].options}">...</div>
                        <div id="extraOptions" ng-show="$root.classroomDataWithOptions[$index].options" ng-click="$event.stopPropagation()">
                                <div ng-click="$root.toggleViewOnlyScreen(x.info)" class="extraOptionsElement">
                                    <div class="viewScreenOption optionImage"></div>
                                    <div>View Screen</div>
                                </div>
                                <div ng-click="$root.requestControlScreen(x.info)"  class="extraOptionsElement">
                                    <div style="background-size: 20px;" class="requestControlOption optionImage"></div>
                                    <div>Request Control</div>
                                </div>
                        </div>
                    </div>
                </div>                
            </div>
        </div>
        <div style="text-align: left;">{{'CLASSROOM.ABSENT' | translate}} ({{$root.absentCount}})</div>
        <div class="table_body" style="height: 40vh;">
            <div ng-repeat="x in $root.classroomData" class="table_row" ng-if="x.status === 'Absent'">
                <div class="content_cell">
                    <div class="avatar">
                        <div role="none" class="avatar-text">
                            <div class="avatar-short-text">
                                {{x.short_name}}
                            </div>
                        </div>
                    </div> 
                    <div role="none" class="username" title="{{x.name}}">
                        <p class="name_text" style="color: #777777;">
                            {{x.name}}
                        </p>
                    </div>
                </div>
                <div class="status_cell content_cell">
                </div>
            </div>                
        </div>
    </div>
</div>
