<div class="dialog-middle" ng-show="$root.layoutDialogVisible" style="text-align: left;">
    <div class="classroom-layout-dialog dialog" id="dlg-vc-layout">
        <div class="content">
            <div class="wrapper">
                <div class="title">
                    <button class="enable-close" tabindex="0" aria-label="Close" style="position: absolute;"
                        ng-click="closeDialog()">
                        </button>
                    <h2 class="title-layout">{{'CLASSROOM.LAYOUT_TITLE' | translate}}</h2>
                    <span class="subtitle-layout">{{'CLASSROOM.LAYOUT_SUBTITLE_1' | translate}}</span>
                </div>

                <div class="options">
                    <div class="thumb-options">
                        <div class="layout-auto {{$root.currentLayoutType==0?'border-enabled':'border-disabled'}}" ng-click="$root.currentLayoutType=0">
                            <div class="layout-auto-content">
                                <span class="auto-title">{{'CLASSROOM.LAYOUT_AUTO' | translate}}</span>
                                <span class="auto-content {{$root.currentLayoutType==0?'span-enabled':'span-disabled'}}">{{'CLASSROOM.LAYOUT_AUTO_DESCRIPTION' | translate}}</span>
                            </div>
                        </div>

                        <div class="layout-fixed {{$root.currentLayoutType==0?'border-disabled':'border-enabled'}}" ng-click="$root.currentLayoutType=1">
                            <div class="description">
                                <span class="description-title">{{'CLASSROOM.LAYOUT_FIXED' | translate}}</span>
                                <span class="description-content {{$root.currentLayoutType==0?'span-disabled':'span-enabled'}}">{{'CLASSROOM.LAYOUT_FIXED_DESCRIPTION' | translate}}</span>
                            </div>

                            <div class="layout-fixed-menu" ng-click="showLayoutOptions = !showLayoutOptions">
                                <div class="layouts_options">
                                    <p class="option_selected">{{layoutOptions[$root.currentLayoutOption]}}</p>
                                    <div class="option_values" ng-show="showLayoutOptions">
                                        <p ng-click="$root.currentLayoutOption=0;">{{layoutOptions[0]}}</p>
                                        <p ng-click="$root.currentLayoutOption=1;">{{layoutOptions[1]}}</p>
                                        <p ng-click="$root.currentLayoutOption=2;">{{layoutOptions[2]}}</p>
                                        <p ng-click="$root.currentLayoutOption=3;">{{layoutOptions[3]}}</p>
                                    </div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <mask id="mask0_7172_2985" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                          <rect width="24" height="24" fill="#D9D9D9"/>
                                        </mask>
                                        <g mask="url(#mask0_7172_2985)">
                                          <path d="M11.9995 14.6538L7.5957 10.25H16.4033L11.9995 14.6538Z" fill="#1A1A1A"/>
                                        </g>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="thumb-sort">
                        <span class="title">
                            {{'CLASSROOM.LAYOUT_SUBTITLE_2' | translate}}
                        </span>
                        <div class="sort-list" ng-click="showSortOptions = !showSortOptions">
                            <div class="sort_options">
                                <p class="option_selected">{{sortOptions[$root.currentSortOption]}}</p>
                                <div class="option_values" ng-show="showSortOptions">
                                    <p ng-click="$root.currentSortOption=0;">{{sortOptions[0]}}</p>
                                    <p ng-click="$root.currentSortOption=1;">{{sortOptions[1]}}</p>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <mask id="mask0_7172_2985" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
                                      <rect width="24" height="24" fill="#D9D9D9"/>
                                    </mask>
                                    <g mask="url(#mask0_7172_2985)">
                                      <path d="M11.9995 14.6538L7.5957 10.25H16.4033L11.9995 14.6538Z" fill="#1A1A1A"/>
                                    </g>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <button ng-click="btnSave()" tabindex="0">{{'CLASSROOM.LAYOUT_SAVE' | translate}}</button>
                </div>

            </div>
        </div>
    </div>
</div>