<body ng-app="classroomModule" ng-controller="classroomController" class="classroom-container">
    <div ng-show="!infoService.loadingBlocksVisible">
        <div class="classroom-group">
            <h1 class="classroom-header">{{selectedGroup}}</h1>
            <div class="custom-select-layout" ng-mouseenter="showTooltipForlayout = true" ng-mouseleave="showTooltipForlayout = false" tabindex="10">
                <classroom-layout-dialog ng-class="{shown: $root.layoutDialogVisible}" class="dialog-outer" style="z-index: 30;">
                </classroom-layout-dialog>
                <div style="display: flex; justify-content: center; align-items: center;">
                    <div class="custom-select-icon" ng-click="showLayoutDialog()"></div>
                    <div class="tooltip" ng-show="showTooltipForlayout">Layout</div>
                </div>
            </div>

            <div class="raiseNotifications">
                <div ng-repeat="raiseNotification in raiseNotifications" class="raiseNotification">
                    <div style="display: flex; gap: 8px;">
                        <div class="back_hand_black"></div>
                        <span class="bottom-message">{{raiseNotification.message}}</span>
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <div class="view-button" style="cursor: pointer;" ng-click="$root.toggleViewOnlyScreen(raiseNotification.userinfo)">view</div>
                        <div class="close-message" ng-click="removeNotification($index)"></div>
                    </div>
                </div>
            </div>
                
            <div style="display: flex; gap: 8px; justify-content: flex-end;">
                <div  ng-if="ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence && !$root.isKioskMode"
                ng-class="{shown: ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence}">
                    <div class="kurento-chatboxes" ng-class="{'hidden': !ribbonService.chattingMinimized}" id="chat_box" style="position: static;" ng-click="ribbonService.expandChatting()">
                        <span id="messenger_icon"></span>
                        <a id="toggle-controlbox" class="toggle-controlbox" tabindex="0">
                            <span class="toggle-feedback">{{'RIBBON.CHAT' | translate}}</span>
                        </a>
                    </div>
                </div>
                <div id="attendance_box" ng-click="showAttendanceDialog()">
                    <span id="attendance_option"></span>
                    <span>{{$root.presentCount}}/{{$root.absentCount + $root.presentCount}}</span>
                </div>
                <div class="end-session-btn" ng-click="endSession()" tabindex="0">
                    <span>{{'CLASSROOM.END_SESSION' | translate}}</span>
                </div>
            </div>
        </div>
        <div id="classroom-content">
            <div class="container">
                <div id="{{data.info.id}}" style="background: #1B2533; padding: 24px 12px 66px 12px; border-radius: 8px; display: flex; flex-direction: column; gap: 8px; margin-top: 24px; margin-right: 12px; margin-left: 12px; align-items: center; place-content: center;"
                    ng-repeat="data in $root.visibleUsers.slice(pagerService.currentPage * pagerService.pageSize - pagerService.pageSize, pagerService.currentPage * pagerService.pageSize)"
                    class="i_display item"
                    ng-class="{'i_display_4': (currentPageThumbnailCounts <= 4), 'i_display_9': (currentPageThumbnailCounts > 4 && currentPageThumbnailCounts <= 9), 'i_display_16': (currentPageThumbnailCounts > 9 && currentPageThumbnailCounts <= 16), 'i_display_25': (currentPageThumbnailCounts > 16 )}">
                    <div style="margin-left: 35%;margin-top: 15%;z-index: 10;position: absolute;" ng-show="data.info.load">
                        <div class="thumbnail-loader"></div>
                    </div>
                    <img ng-class="{'cl_display_4': (currentPageThumbnailCounts <= 4), 'cl_display_9': (currentPageThumbnailCounts > 4 && currentPageThumbnailCounts <= 9), 'cl_display_16': (currentPageThumbnailCounts > 9 && currentPageThumbnailCounts <= 16), 'cl_display_25': (currentPageThumbnailCounts > 16 )}"
                        ng-if="data.status=='Present' && data.info.thumbnail" ng-src="{{data.info.thumbnail}}"
                        ng-click="toggleViewOnly(data.info)" tabindex="0">
                    <div class="cl-display" style="display: flex; justify-content: center; align-items: center;" ng-if="!data.info.thumbnail">
                        <div style="width: 50px; height: 50px;" class="short_name_class">{{data.short_name}}</div>
                    </div>
                    <div style="width: calc(100% - 24px); display: flex; flex-direction: row; justify-content: space-between; position: absolute; bottom: 8px;">
                   <div style="display: flex;">
                    <div style="top: 0px;" class="short_name_class">{{data.short_name}}</div>
                    <label class="left-username" data-username="{{data.username}}" style="position: static; background-color: transparent; color: white;">{{data.username}}</label>
                   </div>
                   <div  style="display: flex;">
                        <a class="right-fullControll animate-show-hide" data-sessionId="{{data.info.id}}"
                            ng-show="data.info.raisedHand" data-location="{{data.info.location}}"
                            data-token="{{data.info.token}}" ng-click="removeHand(data.info)" tabindex="0">
                            <img class="animate-show-hide" src="app/ext/ribbon/images/back_hand_white.svg" ng-show="data.info.raisedHand" title="{{'CLASSROOM.VIEW_ONLY' | translate}}">
                        </a>
                        <div class="threeDots" ng-click="$root.toggleOptionFunc(data.windows_name)">
                            <div style="left: -164px;" ng-show="data.toggleOptions" id="extraOptions"  ng-click="$event.stopPropagation()">
                                <div ng-click="$root.toggleViewOnlyScreen(data.info)" class="extraOptionsElement">
                                    <div class="viewScreenOption optionImage"></div>
                                    <div>View Screen</div>
                                </div>
                                <div ng-click="$root.requestControlScreen(data.info)"  class="extraOptionsElement">
                                    <div style="background-size: 20px;" class="requestControlOption optionImage"></div>
                                    <div>Request Control</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    </div>
                    <div class="cl-display absent" ng-if="data.status=='Absent'" tabindex="0">
                        <label>{{'CLASSROOM.ABSENT' | translate}}</label>
                    </div>
                </div>
            </div>
    
            <classroom-thumbnail ng-class="{shown: $root.classroomThumbnailVisible}"
                class="dialog-outer classroom-thumbnail">
            </classroom-thumbnail>
    
            <div ng-class="{shown: guacNotification.getStatus()}" class="status-outer">
                <div class="status-middle">
                    <guac-notification notification="guacNotification.getStatus()"></guac-notification>
                </div>
            </div>
    
            <div ng-show="($root.visibleUsers.length==0 && $root.usersData.length==0) && !$root.classroomThumbnailVisible && noActiveMsg" class="class-ui">
                <div class="class-module">
                    <div class="logo"></div>
                    <div class="empty-class">
                        Your virtual classroom is ready
                    </div>
                    <div class="empty-class-log">
                        Students’ screens will appear as they log in
                    </div>
                </div>
            </div>
    
            <!-- pagerService -->
            <div class="classroom-footer">
                <ul ng-if="pagerService.pages.length" class="pagination">
                    <li ng-class="{disabled:pagerService.currentPage === 1}" ng-hide="pagerService.totalPages === 1">
                        <a ng-click="setPage(pagerService.currentPage - 1)" tabindex="0 ">Previous</a>
                    </li>
                    <li ng-repeat="page in pagerService.pages" ng-class="{active:pagerService.currentPage === page}">
                        <a ng-click=" setPage(page)" tabindex="0">{{page}}</a>
                    </li>
                    <li ng-class="{disabled:pagerService.currentPage === pagerService.totalPages}">
                        <a ng-click="setPage(pagerService.currentPage + 1)" tabindex="0">Next</a>
                    </li>
                </ul>
            </div>
        </div>
        </div>
    
        <video id="videoOutput" autoplay="" width="320px" height="240px" class="dialog-outer" style="
            top: 36px;
            width: 320px;
            height: 240px;
            opacity: 0;
            visibility: hidden;
        "></video>
    
        <video id="videoInput" autoplay="" width="240px" height="180px" class="dialog-outer" style="
            top: 36px;
            left: 330px;
            width: 240px;
            height: 180px;
            opacity: 0;
            visibility: hidden;
        "></video>
        <div id="kurento"
            ng-if="ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence"
            ng-class="{shown: ribbonService.chattingVisible && ribbonService.licenses.hasChattingLicence && ribbonService.licenses.hasMessengerLicence}">
            <div class="dialog-outer chatting-dialog hidden" ng-class="{'hidden': ribbonService.chattingMinimized}"
                ng-draggable='dragOptions'>
                <chatting-dialog>
                </chatting-dialog>
            </div>
            <div class="dialog-outer chatting-room" ng-repeat="member in ribbonService.activeMembers track by $index"
                ng-style="roomStyle($index)" ng-draggable='dragOptions'>
                <chatting-room roominfo="member" id="chatting-room-{{member.uid}}"></chatting-room>
            </div>
        </div>
        <div class="dialog-outer call-dialog" ng-if="ServiceCall.callDialogVisible || ServiceCall.callConfirmDialogVisible"
        ng-class="{'shown': ServiceCall.callDialogVisible || ServiceCall.callConfirmDialogVisible}"
        ng-draggable='dragOptionsCall'>
        <call-dialog></call-dialog>
    </div>
    <div ng-class="{shown: ribbonService.attendanceVisible, 'undock-attendance': ribbonService.attendanceVisible, 'attendance-hidden': !ribbonService.attendanceVisible}"
        class="dialog-outer-attendance" style="z-index: 30;">
        <attendance-dialog></attendance-dialog>
    </div>
    </div>

    <div class='endSession-ui' ng-show='classroomSessionEnd'>
        <div class='endSession-dialog-middle'>
        <div class='logo'></div>
        <div class='session-ended'>{{'CLASSROOM.SESSION_ENDED' | translate}}</div>
        <div class='close-tab'>{{'CLASSROOM.CLOSE_TAB' | translate}}</div>
        <img src='app/ext/ribbon/images/logo_footer.png' alt='Apporto Logo' class='apporto-logo' />
        </div>
    </div>
</body>

<info-message variant="VC" ng-class="{'info-dialog': !infoService.top, 'info-dialog-top': infoService.top}" role="alert"
		ng-show="infoService.loadingBlocksVisible" tabindex="0"></info-message>
