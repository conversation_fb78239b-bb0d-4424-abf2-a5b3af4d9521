<div class="dialog-middle">

	<div class="classroom-close" id="close_toogle_screen" ng-click="$root.closeViewDialog()" tabindex="0" aria-label="Close">
		<div style="display: flex; justify-content: center; align-items: center;">
			<svg width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M5 10L0 5L5 0L5.8875 0.8875L1.775 5L5.8875 9.1125L5 10Z" fill="white"/>
				</svg>				
		</div>
		<div id="close_text">Back</div>
	</div>
	<info-message ng-class="{'info-dialog': !infoService.top, 'info-dialog-top': infoService.top}" role="alert"
		ng-show="$root.selectedItem.waiting" ng-click="hide($root.selectedItem)" tabindex="0"></info-message>

	<mmonitor ng-show="ribbonService.thumbnailLoad" class="mm-dialog-outer"></mmonitor>

	<div id="{{$index}}" class="i_display item item-dialog-outer menu-content">

	<div style="padding: 16px 16px 0px 16px; background-color: #1b2533; border: none; border-radius: 8px; height: 100%; display: flex; flex-direction: column; gap: 8px; position: relative;">
		<div id="control_request_notitification" ng-show="$root.showControlNotification">
			<div class="requestControlOption" style="background-size: 24px; width: 24px; height: 24px;"></div>
			<div>{{$root.notificationValue}}</div>
	 </div>
		<img class="cl-display" ng-src="{{$root.selectedItem.thumbnail}}"
			style="height: calc(100% - 44px);"
			ng-show="!$root.selectedItem.permission && $root.selectedItem.ViewOnlyFrame">
		<iframe id="view_el" class="cl-display-view-el"
		ng-show="!$root.selectedItem.permission && !$root.selectedItem.ViewOnlyFrame"
		style="min-width: 100%; cursor: pointer; margin-top: .1vh; padding: 0; height: calc(100% - 44px); border-radius: 8px;" />
		<iframe id="full_el" class="cl-display-full-el" ng-if="$root.selectedItem.permission"
		style="min-width: 100%; cursor: pointer; margin-top: .1vh; padding: 0; height: calc(100% - 44px); border-radius: 8px; border: 2px solid #FFDC00;" />
		<div style="height: 40px; padding: 8px 0px; display: flex; flex-direction: row; justify-content: space-between;">
			<div style="display: flex; flex-direction: row; gap: 8px;">
			  <div class="short_name_class">{{$root.selectedItem.short_name}}</div>
			  <div style="color: white;">{{$root.selectedItem.username}}</div>
			</div>
		</div>
	</div>

	<div style="display: flex; flex-direction: row; gap: 8px; position: absolute; top: 10px; left: 50%; width: 455px; overflow: hidden; transform: translateX(-50%);">
		<label class="dialog-left-username text_style" data-username="{{$root.selectedItem.username}}" style="background: #777777; border-radius: 8px; position: static; min-width: max-content;">You are viewing
			{{$root.selectedItem.notificationName}}'s screen</label>

		<div class="action-dialog" ng-show="$root.showAccessControl">
			<div class="dropdown">
				<div class="dropdown-content">
					<a target="_blank" href="" data-full="{{$root.selectedItem.full}}"
						data-sessionId="{{$root.selectedItem.id}}" data-location="{{$root.selectedItem.location}}"
						data-token="{{$root.selectedItem.token}}"
						ng-click="$root.fullControllNotification($root.selectedItem)" tabindex="0" class="text_style" style="width: max-content;">Request control</a>
				</div>
			</div>
		</div>

		<div ng-show="!$root.showAccessControl" id="stopControl" ng-click="$root.stopContol()">
			Stop control
		</div>
	</div>

	</div>
	<div id="kurento"
		ng-if="ribbonService.chattingVisible && $root.classroomThumbnailVisible && ribbonService.licenses.hasMessengerLicence"
		ng-class="{shown: ribbonService.chattingVisible && $root.classroomThumbnailVisible && ribbonService.licenses.hasMessengerLicence}">
		<div class="kurento-chatboxes" ng-class="{'hidden': !ribbonService.chattingBoxMinimized}">
			<a ng-click="ribbonService.chattingBoxMinimized=false" id="toggle-controlbox" class="toggle-controlbox"
				tabindex="0">
				<span class="toggle-feedback">{{'RIBBON.MESSENGER' | translate}}</span>
			</a>
		</div>
		<div class="dialog-outer hidden">
			<chatting-dialog>
			</chatting-dialog>
		</div>
		<div class="dialog-outer chatting-room hidden" ng-repeat="member in ribbonService.activeMembers"
			ng-class="{'hidden': ribbonService.chattingBoxMinimized}"
			style="right: unset; bottom: unset; left: calc(100% - 360px); top: calc(100% - 380px);"
			ng-draggable='dragOptions'>
			<chatting-room roominfo="member"></chatting-room>
		</div>
	</div>
</div>