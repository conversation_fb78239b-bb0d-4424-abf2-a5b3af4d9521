<div class="dialog-middle"> 
    <div class="classroom-dialog dialog" id="dlg-classroom" style="width: 558px;height: 275px;min-width: 450px;min-height: 190px; padding: 24px;">
        <button class="close classroom" ng-click="$root.closeClassroomDialog()" tabindex="0" aria-label="Close" style="position: absolute;">&times;</button>
        <div style="display: flex; flex-direction: column; gap: 16px;">
            <h3 style="text-align: left; padding-left: 5px; margin-top: 0px;" class="title-classroom">{{'CLASSROOM.CLASSROOM_TITLE' | translate}}</h3>
            <h4 class="subtitle-classroom" style="margin-bottom: 0; text-align: left; padding-left: 5px; margin-top: 0px;">Select a group and click Launch</h4>

            <div id="classroomGroups" style="display: flex; flex-direction: column; gap: 8px; padding-top: 0px;">
                <div style="text-align: left; padding-left: 5px;">{{'PRESENTER.GROUP' | translate}}</div>
                <div class="custom-select-classroom" tabindex="10">
                    <button
                      class="select-button-classroom"
                      role="combobox"
                      aria-labelledby="select button"
                      aria-haspopup="listbox"
                      aria-expanded="false"
                      aria-controls="select-dropdown"
                    >
                      <span class="selected-value">{{$root.selectedGroup}}</span>
                      <span class="arrow"></span>
                    </button>
                    <ul class="select-dropdown" role="listbox" id="select-dropdown">
                        <li ng-repeat="group in ribbonService.classroomGroups" role="option" ng-click="ribbonService.selectClassroomOption(group)">
                            <input type="radio" ng-attr-id="{{group}}" ng-attr-name="{{group}}" />
                            <label ng-style="{'background-color': group === $root.selectedGroup ? '#d1ddec' : '#fff'}"
                              ><i></i>{{group}}</label
                            >
                          </li>
                    </ul>
                  </div>
            </div>
        <div class="footer" style="text-align: left !important; padding: 0px;">
            <button id="presenterCloseBtn" class="launch button" style="border-radius: 8px;  width: 67px; height: 40px; border: 1px solid #CED3D9; background: #ffffff; color: black;"
            ng-click="$root.closeClassroomDialog()" tabindex="0">
            Close
        </button>
            <button class="launch button" style="border-radius: 8px;  width: 8em; height: 40px; cursor: pointer;" ng-click="$root.openClassroom()" tabindex="0">
                {{'DIALOGS.BUTTON_LAUNCH' | translate}}
            </button>       

    </div>
</div>
</div>
