/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.auth.ott;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.time.Duration;

import javax.servlet.http.HttpServletRequest;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.auth.ott.user.AuthenticatedUser;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.net.auth.Credentials;
import org.apache.guacamole.net.auth.credentials.CredentialsInfo;
import org.apache.guacamole.net.auth.credentials.GuacamoleInvalidCredentialsException;
import org.codehaus.jackson.JsonProcessingException;
import org.codehaus.jackson.map.ObjectMapper;

import com.apporto.hyperstream.auth.model.ValidatedTokenPair;
import com.apporto.hyperstream.auth.model.ValidationStatus;
import com.apporto.hyperstream.auth.model.dto.TokenPairDto;
import com.apporto.hyperstream.core.ApportoProperties;
import com.apporto.hyperstream.core.UrlParams;
import com.google.inject.Inject;

import lombok.extern.slf4j.Slf4j;

/**
 * Service providing convenience functions for the OTT
 * AuthenticationProvider implementation.
 */
@Slf4j
public class AuthenticationProviderService {

    private final Environment environment;

    @Inject
    public AuthenticationProviderService(Environment environment) {
        this.environment = environment;
    }

    /**
     * Returns an AuthenticatedUser representing the user authenticated by the
     * given credentials.
     *
     * @param credentials
     *     The credentials to use for authentication.
     *
     * @return
     *     An AuthenticatedUser representing the user authenticated by the
     *     given credentials.
     *
     * @throws GuacamoleException
     *     If an error occurs while authenticating the user, or if access is
     *     denied.
     */
    public AuthenticatedUser authenticateUser(Credentials credentials) throws GuacamoleException, GuacamoleInvalidCredentialsException {

        HttpServletRequest req = credentials.getRequest();
        String messageParam = "";
        if (req.getParameter(UrlParams.MESSAGE_PARAM) == null && req.getParameter(UrlParams.OTT_PARAM) != null) {
            ValidatedTokenPair validatedTokenPair = validateTokenPair(req.getParameter(UrlParams.OTT_PARAM), null);

            if (validatedTokenPair != null && validatedTokenPair.getStatus() == ValidationStatus.VALID) {
                logger.info("[{}:{}:{}] OTT Valid, forwarding parameters to EncryptedURL authentication.", "", "", "");
                messageParam = validatedTokenPair.getEncryptedUrl();
            }
            else
                throw new GuacamoleInvalidCredentialsException("Invalid login.", CredentialsInfo.USERNAME_PASSWORD);
        }
        else if (req.getParameter(UrlParams.MESSAGE_PARAM) != null) {
            logger.info("[{}:{}:{}] Found EncUrl authentication paramater, forwarding to EncryptedURL authentication", "", "", "");
            messageParam = req.getParameter(UrlParams.MESSAGE_PARAM);
        }

        req.setAttribute(UrlParams.MESSAGE_PARAM, messageParam);

        // Authentication success, but we must proceed with EncryptedURL
        return null;
    }

    /**
     * Validates the provided token pair by sending a request to the auth proxy.
     * 
     * @param accessToken  the access token to be validated
     * @param refreshToken the refresh token to be validated
     * @return the validated token pair, or null if validation fails
     */
    public ValidatedTokenPair validateTokenPair(String accessToken, String refreshToken) {
        try {
            TokenPairDto tokenPairDto = new TokenPairDto(accessToken, refreshToken);
            return requestValidation(tokenPairDto);
        } catch (Exception e) {
            logger.error("[{}:{}:{}] Failed to execute validation for access token: {} and refresh token {}", "", "", "", accessToken, refreshToken, e);
            return null;
        }
    }

    /**
     * Sends a POST request to the token validation service to validate the provided token pair
     *
     * @param tokenPairDto
     * @return ValidatedTokenPair
     * 
     * @throws IOException
     * @throws JsonProcessingException
     * @throws InterruptedException
     * @throws GuacamoleException 
     */
    private ValidatedTokenPair requestValidation(TokenPairDto tokenPairDto) throws JsonProcessingException, IOException, InterruptedException, GuacamoleException {

        // Serialize DTO to JSON string
        ObjectMapper objectMapper = new ObjectMapper();
        String requestBody = objectMapper.writeValueAsString(tokenPairDto);

        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(environment.getProperty(ApportoProperties.OTT_URL)))
                .timeout(Duration.ofSeconds(environment.getProperty(ApportoProperties.OTT_VALIDATION_TIMEOUT)))
                .header("Content-Type", "application/json")
                .POST(BodyPublishers.ofString(requestBody))
                .build();

        HttpResponse<String> response = client.send(request, BodyHandlers.ofString());

        if (response.statusCode() == 200) {
            logger.info("[{}:{}:{}] Token validation successful for token pair: {}", "", "", "", tokenPairDto);
            return objectMapper.readValue(response.body(), ValidatedTokenPair.class);
        } else {
            logger.error("[{}:{}:{}] Failed to validate token pair {}. Status code: {}", "", "", "", tokenPairDto, response.statusCode());
            return null;
        }
    }
}
