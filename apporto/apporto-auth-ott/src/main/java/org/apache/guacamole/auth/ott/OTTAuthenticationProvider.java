/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.auth.ott;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.AbstractAuthenticationProvider;
import org.apache.guacamole.net.auth.AuthenticatedUser;
import org.apache.guacamole.net.auth.Credentials;

import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;

import lombok.extern.slf4j.Slf4j;

/**
 * Guacamole authentication backend which authenticates users using an
 * arbitrary external OTT. No storage for connections is
 * provided - only authentication. Storage must be provided by some other
 * extension.
 */
@Slf4j
public class OTTAuthenticationProvider extends AbstractAuthenticationProvider {

    @Inject
    private AuthenticationProviderService authenticationProviderService;

    public OTTAuthenticationProvider() {
        logger.info("[{}:{}:{}] OTTAuthenticationProvider created", "", "", "");
        Injector injector = Guice.createInjector(new OTTAuthenticationProviderModule());
        injector.injectMembers(this);
    }

    @Override
    public String getIdentifier() {
        return "ott";
    }

    /**
     * Attempts to authenticate a user using the One Time Token (OTT) provided.
     * 
     * The requirement for this authentication is that if the OTT is not provided, the old
     * Encrypted URL is used. If the OTT is provided, the OTT is used for authentication.
     * 
     * The OTT essentially retrieves the EncryptedURL token and passes it to the EncryptedURL
     * authentication provider. There are too much dependencies on the EcryptedUrl provider, so it
     * must always be the one that is finishing authentication.
     * 
     * Because it is required to continue authentication with EncryptedUrl, OTT provider must fail even
     * for the valid OTT - but for valid OTT it will record the EncryptedUrl token in the HTTP request.
     * 
     * The authentication will continue with EncryptedUrl provider.
     * 
     * 
     * The algorithm is as follows:
     * 1. If the OTT is provided,
     * 1.1 try to get/create EncryptedUrl for the given OTT
     * 1.2 set the EncryptedUrl token in the HTTP request
     * 1.3 throw exception to proceed to EncryptedUrl authentication
     * 2. If the OTT is not provided,
     * 2.1 check if the EncryptedUrl token is provided
     * 2.2 if the EncryptedUrl token is provided, throw exception and proceed with EncryptedUrl authentication
     * 
     * Exception is actually thrown in the AuthenticationProviderService.authenticateUser method.
     * 
     */
    @Override
    public AuthenticatedUser authenticateUser(Credentials credentials) throws GuacamoleException {

        logger.info("[{}:{}:{}] Trying OTT authentication...", "", "", "");

        // Pass credentials to authentication service.
        return authenticationProviderService.authenticateUser(credentials);
    }

}
