/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
angular.module('statsCollect', [
    'index'
]);

// Check if it is possible to receive event when connection is established and removed,
// and create/destroy counters accordingly.
angular.module('statsCollect').controller('statsController',
        ['$scope', '$injector', '$interval',
            function($scope, $injector, $interval) {

                $scope.stats = $injector.get('statsCollectService');

                var waitForDisplay = $interval(function() {
                    // Check if element with ".display" class is created; this element
                    // will normally receive mouse event. Once element is created, attach
                    // Mouse object to ".display" and react on mouse event.
                    // Display element is created only when connection is established,
                    // so Mouse cannot be attached before.
                    var el = document.getElementsByClassName("display")[0];
                    if (el != null) {
                        $scope.stats.setElement(el);
                        $scope.$on('guacKeyup', function keyupListener(event, keysym, keyboard) {
                            $scope.stats.collectStatistics();
                        });
                        $interval.cancel(waitForDisplay);
                    }
                }, 1000);
            }
])

// Change HTML generation in this function with HTML template and/or directive.
angular.module('statsCollect').run(['$injector', '$interval', function($injector, $interval) {
    var $location                = $injector.get('$location');

    if($location.path().indexOf("classroom") > -1) {
       return;
    }
    var content_el=document.getElementById("content");
    var newNode = document.createElement('div');
    newNode.setAttribute("ng-controller", "statsController");
    content_el.appendChild(newNode);
}]);

// Ensure the statsCollect module is loaded along with the rest of the app
angular.module('index').requires.push('statsCollect');
