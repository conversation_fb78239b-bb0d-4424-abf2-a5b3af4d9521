/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Module for managing expiration timer.
 */
angular.module('statsCollect').factory('statsCollectService',
        [ '$injector', '$rootScope', '$interval',

        function ($injector, $rootScope, $interval) {

            // Required types
            var ClientIdentifier         = $injector.get('ClientIdentifier')
            var ManagedClientState       = $injector.get('ManagedClientState');
            
            // Required services
            var $window                  = $injector.get('$window');
            var authenticationService    = $injector.get('authenticationService');
            var $routeParams             = $injector.get('$routeParams');
            var $location                = $injector.get('$location');

            if($routeParams.hasOwnProperty("key") && $location.path().indexOf("classroom") > -1) {
                return;
            }
            
            var service = {
                longEventCounter: 0,  // number of events on 5 minutes
                shortEventCounter: 0, // number of events on 5 seconds
                counterUpdateHandler: null,

                long_event_timer_handler: null,
                short_event_timer_handler: null,
                
                mouse: null,
                last_mouse_event: new Date().valueOf(),
                
                oldOnbeforeunload: null
            }
            
            var MOUSE_RESOLUTION = 100;

            // Just count events received.
            service.increaseEventCounter = function increaseEventCounter() {
                service.longEventCounter++;
                service.shortEventCounter++;
            }

            // Allow mouse event only with 0,5 second resolution
            service.collectMouseStatistics = function collectMouseStatistics() {
            	current_ts = new Date().valueOf();
            	
            	if (current_ts - service.last_mouse_event > MOUSE_RESOLUTION) {
            		service.increaseEventCounter();
            		service.last_mouse_event = current_ts;
            	}
            }
            
            // Call functions for statistics collection.
            // Different statistics can be collected on each event.
            service.collectStatistics = function collectStatistics() {
                service.increaseEventCounter();
            }
            
            // Attach mouse to the given element and start listening for the keyboard
            // events. Keyboard events must be collected from client 'guacKeyup' event
            // on the scope.
            service.setElement = function setElement(el) {
                if (service.long_event_timer_handler != null)
                    $interval.cancel(service.long_event_timer_handler);
                if (service.short_event_timer_handler != null)
                    $interval.cancel(service.short_event_timer_handler);
                
                service.long_event_timer_handler = $interval(service.periodicEventSend, 5*60*1000);
                service.short_event_timer_handler = $interval(service.resetShortCounter, 5*1000);
                
                service.mouse = new Guacamole.Mouse(el);
                service.mouse.onmousemove = 
                  service.mouse.onmouseup = service.collectMouseStatistics;
            }
            
            service.resetShortCounter = function() {
                if (service.counterUpdateHandler != null)
                    service.counterUpdateHandler(service.shortEventCounter);
                service.shortEventCounter = 0;
            }

            service.sendEventStatistics = function(isAsync) {
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);                
                var params = "?token=" + authenticationService.getCurrentToken();

                params = params + "&id=" + clientIdentifier.id;
                params = params + "&datasource=" + clientIdentifier.dataSource;
                params = params + "&events=" + service.longEventCounter;
                
                if (!$routeParams.hasOwnProperty("key") && !location.pathname.includes("classroom")) {
	                var xmlhttp = new XMLHttpRequest();
                    xmlhttp.onreadystatechange = function() {
                        if (this.readyState == 4 && this.status == 200) {
                            var response = this.responseText;
                            if (JSON.parse(response).remaining_time && JSON.parse(response).remaining_time > 0 && JSON.parse(response).extended_compute) {
                                $rootScope.$broadcast('remainingTime', JSON.parse(response).remaining_time);
                            }
                        }
                    };
	                xmlhttp.open("GET", "api/stats/events" + params, isAsync);
	                xmlhttp.send();
                }
            }

            service.periodicEventSend = function() {
                if (ManagedClientState.isDisconnectState() == true) {
                    return;
                }

                service.sendEventStatistics(true);
                service.longEventCounter = 0;
            }

            $rootScope.$on('checkStartableRemaining', function() {
                service.periodicEventSend();
            });

            /**
             * Keep old onbeforeunload handler, if it exists, and call it after our work.
             */
            service.oldOnbeforeunload = $window.onbeforeunload;
            
            /**
             * When the window is closed, send close event to server side; call REST
             * method:
             * 
             * http://localhost:8080/guacamole/api/onclose?token=<token>&id=<session-id>&datasource=<data-source>
             * 
             */
            $window.onbeforeunload = function() {
                if(service.oldOnbeforeunload) return service.oldOnbeforeunload();
            }

            return service;
        }
]);
