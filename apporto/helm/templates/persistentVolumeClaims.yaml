{{- range $pvcName, $pvcDict := .Values.persistentVolumeClaims }}
---
# Source: .../persistentVolumeClaims.yaml : .Values.persistentVolumeClaims.{{$pvcName}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ template "hyperstream.fullname" $ }}.{{$pvcName}}
  labels:
    heritage: {{ $.Release.Service }}
    release: {{ $.Release.Name }}
    chart: {{ $.Chart.Name }}-{{ $.Chart.Version }}
    app: {{ template "hyperstream.name" $ }}
  namespace: {{ $.Release.Namespace }}
spec: 
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: {{ $pvcDict.claimSize }}
{{- end}}
