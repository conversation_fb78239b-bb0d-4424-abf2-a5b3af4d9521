apiVersion: v1
kind: Service
metadata:
  name: {{ template "hyperstream.fullname" . }}
  labels:
    app: {{ template "hyperstream.name" . }}
    chart: {{ template "hyperstream.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  namespace: {{ .Release.Namespace }}
spec:
  type: {{ .Values.hyperstream.service.type }}
  ports:
    - port: {{ .Values.hyperstream.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ template "hyperstream.name" . }}
    release: {{ .Release.Name }}
{{- $replicaCount := (int .Values.replicaCount) -}}
{{- $directPathCounts := untilStep 0 $replicaCount 1 -}}    
{{- range $index, $val := $directPathCounts }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "hyperstream.fullname" $ }}-svc{{$index}}
  labels:
    app: {{ template "hyperstream.name" $ }}
    chart: {{ template "hyperstream.chart" $ }}
    release: {{ $.Release.Name }}
    heritage: {{ $.Release.Service }}
  namespace: {{ $.Release.Namespace }}
spec:
  type: {{ $.Values.hyperstream.service.type }}
  ports:
    - port: {{ $.Values.hyperstream.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    statefulset.kubernetes.io/pod-name: {{ template "hyperstream.fullname" $ }}-{{$index}}
    app: {{ template "hyperstream.name" $ }}
    release: {{ $.Release.Name }}

{{- end}}
