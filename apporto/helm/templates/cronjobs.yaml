{{- $global := . }}
{{- $loop_first_pass := "yes" }}
{{- range $i, $cronjob := .Values.cronjobs }}
{{- if ne $loop_first_pass "yes" }}
---
{{- end}}
{{- $loop_first_pass = "no" }}
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: {{ template "hyperstream.fullname" $global }}-cron-{{ $i }}
  labels:
    app: {{ template "hyperstream.name" $global }}-cron-{{ $i }}
    chart: {{ template "hyperstream.chart" $global }}
    release: {{ $global.Release.Name }}-cron-{{ $i }}
    heritage: {{ $global.Release.Service }}
  namespace: {{ $global.Release.Namespace }}
spec:
  schedule: {{ $cronjob.schedule | quote }}
  jobTemplate:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmaps.yaml") $global | sha256sum }}
        checksum/secrets: {{ include (print $.Template.BasePath "/secrets.yaml") $global | sha256sum }}
      labels:
        app: {{ template "hyperstream.name" $global }}-cron-{{ $i }}
        release: {{ $global.Release.Name }}-cron-{{ $i }}
    spec:
      template:
        metadata:
          annotations:
{{- if $global.Values.deployment.annotations }}
{{ toYaml $global.Values.deployment.annotations | indent 12 }}
{{- end  }}
        spec:
          containers:
          - name: {{ $global.Release.Name }}-cron-{{ $i }}
            image: "{{ $global.Values.image.repository }}:{{ $global.Values.image.tag }}"
            imagePullPolicy: {{ $global.Values.image.pullPolicy }}
{{- if $cronjob.command }}
            command: {{ toJson $cronjob.command | replace "," ", " }}
{{- end }}
{{- if $cronjob.args }}
            args: {{ toJson $cronjob.args  | replace "," ", "}}
{{- end }}
{{- if $global.Values.envVars }}
            env:
{{- range $global.Values.envVars }}
            - name: {{ .name | quote }}
              value: {{ tpl .value $ | quote }}
{{- end }}
{{- end }}
            volumeMounts:
{{- range $cmName, $cmDict := $global.Values.configMaps }}
            - name: {{ $cmName | quote }}
              mountPath: {{ print $cmDict.hostDir "/" $cmDict.filename | quote }}
              subPath: {{ $cmDict.filename | quote }}
              readOnly: true
{{- end }}
{{- range $sName, $sDict := $global.Values.k8sSecretMounts }}
            - name: {{ $sName | quote }}
              mountPath: {{ print $sDict.hostDir "/" $sDict.filename | quote }}
              subPath: {{ $sDict.filename | quote }}
              readOnly: true
{{- end }}
          volumes:
{{- range $cmName, $cmDict := $global.Values.configMaps }}
          - name: {{$cmName | quote}}
            configMap:
              name: "{{ template "hyperstream.fullname" $global }}"
              items:
              - key: {{ $cmName | quote }}
                path: {{ $cmDict.filename | quote }}
{{- end }}
{{- range $secretName, $sDict := $global.Values.k8sSecretMounts }}
          - name: {{$secretName | quote}}
            secret:
              secretName: "{{ template "hyperstream.fullname" $global }}.{{$secretName}}"
{{- end }}
          restartPolicy: {{ $cronjob.restartPolicy | default "Never" }}
{{- end }}
