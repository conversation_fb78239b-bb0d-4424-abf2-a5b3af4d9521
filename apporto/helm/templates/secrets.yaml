{{- range $secretName, $sDict := .Values.k8sSecretMounts }}
---
# Source: .../secrets.yaml : .Values.k8sSecretMounts.{{$secretName}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "hyperstream.fullname" $ }}.{{$secretName}}
  labels:
    heritage: {{ $.Release.Service }}
    release: {{ $.Release.Name }}
    chart: {{ $.Chart.Name }}-{{ $.Chart.Version }}
    app: {{ template "hyperstream.name" $ }}
  namespace: {{ $.Release.Namespace }}
data: 
  {{ $sDict.filename }}: {{ tpl $sDict.data $ }}
{{- end}}
