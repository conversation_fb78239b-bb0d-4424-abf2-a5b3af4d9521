{{- if .Values.ingress.enabled -}}
{{- $fullName := include "hyperstream.fullname" . -}}
{{- $servicePort := .Values.hyperstream.service.port -}}
{{- $ingressPath := .Values.ingress.path -}}
{{- $ingressHostname := .Values.ingress.hostname -}}
{{- $ingressExtraPaths := .Values.ingress.extra_paths -}}
{{- $tlsSecretName := .Values.ingress.tlsSecretName -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    app: {{ template "hyperstream.name" . }}
    chart: {{ template "hyperstream.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  namespace: {{ .Release.Namespace }}
{{- with .Values.ingress.annotations }}
  annotations:
{{ toYaml . | indent 4 }}
{{- end }}
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  tls:
    - hosts:
        - {{ $ingressHostname }}
      secretName: {{ $tlsSecretName }}
  rules:
    - host: {{ $ingressHostname }}
      http:
        paths:
          - path: {{ $ingressPath }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  name: http
  {{- range $ingressExtraPaths -}}
  {{- $extraPath := . }}
    - host: {{ $ingressHostname }}
      http:
        paths:
          - path: {{ $extraPath }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  name: http
  {{- end }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}-directpath
  labels:
    app: {{ template "hyperstream.name" . }}
    chart: {{ template "hyperstream.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  namespace: {{ .Release.Namespace }}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: {{ .Values.ingress.path }}/$1
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  tls:
    - hosts:
        - {{ $ingressHostname }}
      secretName: {{ $tlsSecretName }}
  rules:
  {{- $replicaCountPlusOne := (int (toString (int .Values.replicaCount) | add1)) }}
  {{- $directPathCounts := untilStep 1 $replicaCountPlusOne 1 -}}
  {{- range $index, $val := $directPathCounts }}
    - host: {{ $ingressHostname }}
      http:
        paths:
        - path: /srv-{{$val}}{{ $ingressPath }}/(.*)
          pathType: ImplementationSpecific
          backend:
            service:
              name: {{ $fullName }}-svc{{$index}}
              port:
                name: http
  {{- end }}
{{- end }}
