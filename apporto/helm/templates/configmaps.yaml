apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "hyperstream.fullname" . }}
  labels:
    heritage: {{ .Release.Service }}
    release: {{ .Release.Name }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version }}
    app: {{ template "hyperstream.name" . }}
  namespace: {{ .Release.Namespace }}
data:
  # When the config map is mounted as a volume, these will be created as files.
  guacamole.properties: |

    # Hostname and port of guacamole proxy
    guacd-hostname: localhost
    guacd-port: 4822

    # Location to read extra .jar's from
    lib-directory: CATALINA_BASE/webapps/hyperstream/WEB-INF/classes

    auth-provider: rs.jsw.guacamole.net.encryptedurl.mysql.EncurlMySQLAuthenticationProvider
    auth-provider: rs.jsw.guacamole.net.encryptedurl.mysql.EncurlMySQLSharedAuthenticationProvider

    # MySQL properties
    # Note: use IP to force network connection.
    mysql-hostname: 127.0.0.1
    mysql-port: 3306
    mysql-database: {{ .Values.db.env.MYSQL_DATABASE }}
    mysql-username: {{ .Values.db.env.MYSQL_USER }}
    mysql-password: {{ .Values.db.env.MYSQL_PASSWORD }}
    mysql-max-active-connections: {{ .Values.db.poolCfgs.MYSQL_MAX_ACTIVE_CONNECTIONS }}
    mysql-max-idle-connections: {{ .Values.db.poolCfgs.MYSQL_MAX_IDLE_CONNECTIONS }}
    mysql-max-checkout-time: {{ .Values.db.poolCfgs.MYSQL_MAX_CHECKOUT_TIME }}
    mysql-time-to-wait: {{ .Values.db.poolCfgs.MYSQL_TIME_TO_WAIT }}
    mysql-ping-connections-not-used-for: {{ .Values.db.poolCfgs.MYSQL_PING_CONNECTIONS_NOT_USED_FOR }}

    timestamp-age-limit: {{ .Values.apporto_config.timestamp_age_limit }}
    messenger-server: {{ .Values.apporto_config.messenger_server }}

    dc-server-id: {{ .Values.apporto_config.dc_server_id }}
    snapshot-username: {{ .Values.apporto_config.snapshot_username }}
    port: {{ .Values.apporto_config.snapshot_port }}
    region: {{ .Values.apporto_config.region }}

    server-id: srv-SERVER_ID
    server-group-count: {{ .Values.replicaCount }}

    prevent-caps-lock: {{ .Values.apporto_config.prevent_caps_lock }}

    {{- if .Values.apporto_config.snapshot_scripts }}
    snapshot-enumerate: {{ .Values.apporto_config.snapshot_scripts.enumerate }}
    snapshot-restore: {{ .Values.apporto_config.snapshot_scripts.restore }}
    snapshot-backup: {{ .Values.apporto_config.snapshot_scripts.backup }}
    {{- end }}

    {{ if .Values.apporto_config.vm_scripts -}}
    vm-cloud.aws.vm-backup: {{ .Values.apporto_config.vm_scripts.aws_backup }}
    vm-cloud.aws.vm-restore: {{ .Values.apporto_config.vm_scripts.aws_restore }}
    vm-cloud.aws.vm-datetime: {{ .Values.apporto_config.vm_scripts.aws_datetime }}
    vm-cloud.azure.vm-backup: {{ .Values.apporto_config.vm_scripts.az_backup }}
    vm-cloud.azure.vm-restore: {{ .Values.apporto_config.vm_scripts.az_restore }}
    vm-cloud.azure.vm-datetime: {{ .Values.apporto_config.vm_scripts.az_datetime }}
    {{- end }}

    {{ if .Values.apporto_config.haproxy_server -}}
    haproxy-server: {{ .Values.apporto_config.haproxy_server }}
    {{- end }}

    {{ if .Values.apporto_config.otu_settings -}}
    default-otu-check: {{ .Values.apporto_config.otu_settings.default_otu_check }}
    {{- else }}
    default-otu-check: false
    {{- end }}
      
    redis-url: {{ .Values.apporto_config.redis_url }}
    redis-thumb-db: {{ .Values.apporto_config.redis_thumb_db }}

    api-base-domain: apporto.com
    powershell-runner-host: {{ .Values.apporto_config.powershell_runner_host }}
    local-dns-zone: apporto.com

    allow-sticky-clearing-api: true

    {{ if .Values.apporto_config.enable_custom_translation_file -}}
    custom-translation-file: customer-lang.json
    {{- end }}
    
    check-sftp-dir-timeout: 15

    allow-h264-frame-drop: {{ .Values.apporto_config.allow_h264_frame_drop }}

    {{ if .Values.apporto_config.hap_capacity_api -}}
    hap-capacity-api-service: {{ .Values.apporto_config.hap_capacity_api.url }}
    hap-capacity-api-secret-key: {{ .Values.apporto_config.hap_capacity_api.api_secret_key }}
    hap-capacity-api-service-reboot: {{ .Values.apporto_config.hap_capacity_api.reboot_url }}
    max-hap-capacity-timeout: {{ .Values.apporto_config.hap_capacity_api.max_hap_capacity_timeout }}
    max-allowed-failed-start: {{ .Values.apporto_config.hap_capacity_api.max_allowed_failed_start }}
    {{- end }}

    # API default ports
    apporto-service-port: 50018
    session-mgmt-api-port: 5000

    rollbar-access-token: {{ .Values.apporto_config.rollbar_access_token }}

    rds-logout-event-delay: {{ .Values.apporto_config.rds_logout_event_delay }}

    {{ if .Values.apporto_config.otel_api -}}
    otel-metrics-interval: {{ .Values.apporto_config.otel_api.metrics_interval }}
    otel-collector-endpoint: {{ .Values.apporto_config.otel_api.collector_endpoint }}
    {{- end }}

    {{ if .Values.apporto_config.rdp_router_api -}}
    rdp-router-api-service: {{ .Values.apporto_config.rdp_router_api.service_url }}
    rdp-router-api-secret-key: {{ .Values.apporto_config.rdp_router_api.secret_key }}
    rdp-router-api-timeout: {{ .Values.apporto_config.rdp_router_api.timeout }}
    rdp-router-api-attempt-count: {{ .Values.apporto_config.rdp_router_api.attempt_count }}
    rdp-router-keep-alive-interval: {{ .Values.apporto_config.rdp_router_api.keep_alive_interval }}
    {{- end }}

    filebrowser-api-key: {{ .Values.apporto_config.filebrowser.api_key }}

    filebrowser-server: {{ .Values.apporto_config.filebrowser.server }}

    grpc-server: {{ .Values.apporto_config.virtual_usb.grpc_server }}

    sso-cert-gen-server: {{ .Values.apporto_config.sso_cert_gen_server }}

  secretkey.properties: |

    # The secret key used to encrypt and decrypt the url
    secret-key: {{ .Values.apporto_config.guac_encrypt_secret }}


  guacd.conf: |
    [sso]
    windows_domain_certificate=FILE:/etc/win_domain_cert.pem

