apiVersion: {{ default "apps/v1" .Values.k8s_cluster.deploymentApiVersion }}
kind: StatefulSet
metadata:
  name: {{ template "hyperstream.fullname" . }}
  labels:
    app: {{ template "hyperstream.name" . }}
    chart: {{ template "hyperstream.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  namespace: {{ .Release.Namespace }}
spec:
  serviceName: {{ .Release.Name }}
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ template "hyperstream.name" . }}
      release: {{ .Release.Name }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmaps.yaml") . | sha256sum }}
        checksum/secrets: {{ include (print $.Template.BasePath "/secrets.yaml") . | sha256sum }}
{{- if .Values.deployment.annotations }}
{{ toYaml .Values.deployment.annotations | indent 8 }}
{{- end  }}
      labels:
        app: {{ template "hyperstream.name" . }}
        release: {{ .Release.Name }}
    spec:
      containers:
      - name: tomcat
        image: "{{ .Values.hyperstream.image.repository }}:{{ .Values.hyperstream.image.tag }}"
        imagePullPolicy: {{ .Values.hyperstream.image.pullPolicy }}
{{- if .Values.hyperstream.image.command }}
        command: {{ toJson .Values.hyperstream.image.command | replace "," ", " }}
{{- end }}
{{- if .Values.hyperstream.image.args }}
        args: {{ toJson .Values.hyperstream.image.args | replace "," ", " }}
{{- end }}
        env:
          - name: "MAX_THREADS"
            value: {{ .Values.hyperstream.limits.max_threads | quote }}
          - name: "CATALINA_OPTS"
            value: "-Xmx{{ .Values.hyperstream.limits.Xmx_size }}m -XX:MaxMetaspaceSize={{ .Values.hyperstream.limits.metaspace_size }}m -Xshare:off"
{{- if .Values.hyperstream.env }}
{{- range $key, $value := .Values.hyperstream.env }}
          - name: {{ $key }}
            value: {{ quote $value }}
{{- end }}
{{- end }}
        readinessProbe:
        livenessProbe:
        ports:
        - name: http
          containerPort: {{ .Values.hyperstream.service.port }}
          protocol: TCP
        volumeMounts:
        - mountPath: /srv/hyperstream
          name: "hyperstream-configs"
        - mountPath: /logs/
          name: "app-logs"
        - mountPath: /srv/win-root-cert
          name: "win-cert"
        resources:
          requests:
            memory: {{ .Values.hyperstream.limits.max_memory }}Mi
      - name: db
        image: "{{ .Values.db.image.repository }}:{{ .Values.db.image.tag }}"
        imagePullPolicy: {{ .Values.db.image.pullPolicy }}
{{- if .Values.db.image.command }}
        command: {{ toJson .Values.db.image.command | replace "," ", " }}
{{- end }}
{{- if .Values.db.image.args }}
        args: {{ toJson .Values.db.image.args | replace "," ", " }}
{{- end }}
        env:
{{- if .Values.db.env }}
{{- range $key, $value := .Values.db.env }}
          - name: {{ $key }}
            value: {{ quote $value }}
{{- end }}
{{- end }}
        readinessProbe:
        livenessProbe:
        ports:
        volumeMounts:
        - mountPath: /var/lib/mysql
          name: "db-data"
        - mountPath: /logs/
          name: "app-logs"
        resources:
          requests:
            memory: {{ .Values.db.limits.max_memory }}Mi
      - name: rdp-client
        image: "{{ .Values.guacd.image.repository }}:{{ .Values.guacd.image.tag }}"
        imagePullPolicy: {{ .Values.guacd.image.pullPolicy }}
{{- if .Values.guacd.image.command }}
        command: {{ toJson .Values.guacd.image.command | replace "," ", " }}
{{- end }}
{{- if .Values.guacd.image.args }}
        args: {{ toJson .Values.guacd.image.args | replace "," ", " }}
{{- end }}
        env:
{{- if .Values.guacd.env.WIN_DOMAIN }}
          - name: WIN_DOMAIN
            value: "{{ .Values.guacd.env.WIN_DOMAIN }}"
          - name: WIN_DOMAIN_PDC
            value: "{{ .Values.guacd.env.WIN_DOMAIN_PDC }}"
          - name: WIN_DOMAIN_ROOT_CERT
            value: "{{ .Values.guacd.env.WIN_DOMAIN_ROOT_CERT }}"
{{- end }}
        readinessProbe:
        livenessProbe:
        ports:
        volumeMounts:
        - mountPath: /srv/hyperstream
          name: "hyperstream-configs"
        - mountPath: /logs/
          name: "app-logs"
        - mountPath: /srv/win-root-cert
          name: "win-cert"
        resources:
          requests:
            memory: {{ .Values.guacd.limits.max_memory }}Mi
{{- if .Values.sidecarLogging.enabled }}
      - name: sidecar-logger
        image: {{ .Values.sidecarLogging.image | default "splunk/universalforwarder:6.6.1" }}
        env:
        - name: SPLUNK_START_ARGS
          value: "--accept-license --answer-yes"
        - name: SPLUNK_FORWARD_SERVER
          value: "{{ .Values.sidecarLogging.splunkServer }}"
        - name: SPLUNK_USER
          value: root
        - name: SPLUNK_ADD
          value: "monitor {{ .Values.sidecarLogging.logLocation | default "/logs/" }} -index {{tpl .Values.sidecarLogging.splunkIndex . }}"
        volumeMounts:
        - mountPath: /logs
          readOnly: true
          name: "app-logs"
{{- end }}
      nodeSelector:
{{- with .Values.affinity }}
      affinity:
{{ toYaml . | indent 8 }}
{{- end }}
      tolerations:
{{- if .Values.container.imagePullSecrets }}
    {{- with .Values.container.imagePullSecrets }}
      imagePullSecrets:
{{ toYaml . | indent 6 }}
    {{- end }}
{{- end }}
      volumes:
      - name: "db-data"
        emptyDir: {}
      - name: "app-logs"
        emptyDir: {}
      - name: "hyperstream-configs"
        configMap:
          name: {{ template "hyperstream.fullname" . }}
      - name: "win-cert"
        secret:
          secretName: {{ template "hyperstream.fullname" $ }}.win-root-cert
          optional: true
