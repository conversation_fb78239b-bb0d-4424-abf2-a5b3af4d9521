How to deploy helm file with Hyperstream SSO (Certificate Based Authentication - CBA):

Prerequisite for deployment:
Certificate Based Authentication requires root certificate from Windows domain where <PERSON><PERSON><PERSON> belongs to.
The certificate must be stored into k8s secret named "{{ template "hyperstream.fullname" $ }}.win-root-cert"; typically, this is "hyperstream.win-root-cert". It can be supplied in the command line of the helm install like this:

'''
helm install hyperstream helm  --set k8sSecretMounts.win-root-cert.data="$(cat cert.pem | base64 | tr -d '\n')"
'''

or it can be manually entered into values.yaml. The certificate will be used for the complete hyperstream instance; this means that one hyperstream instance can use SSO (CBA) for one windows domain only.

Changes to the values.yaml:
New environment variables are added and must be supplied if SSO (CBA) is used:

    WIN_DOMAIN: SOME-DOMAIN.COM                             # Name of the windows domain, e.g. APPORTO.COM
    WIN_DOMAIN_PDC: PDC.SOME-DOMAIN.COM                     # FQDN of the primary domain controller, e.g. DNV-DC1.APPORTO.COM
    WIN_DOMAIN_ROOT_CERT: FILE:/srv/win-root-cert/cert.pem  # path to the domain root certificate, the default location is provided

Check if the deployment is correct:
- Once pods are started, check the rdp-client container within hyperstream-n pod. This container should have:
  - certificate mounted in the /srv/win-root-cert/cert.pem, with proper content
  - file /etc/krb5.conf must be present and it should contain realm named after WIN_DOMAIN, with other settings, like this:

  APPORTO.COM = {
    kdc = dnv-dc1.apporto.com
      kdc = DNV-DC1.apporto.com
      admin_server = DNV-DC1.apporto.com
      master_kdc = DNV-DC1.apporto.com
      default_domain = apporto.com

      pkinit_anchors = FILE:/etc/APPSCA.pem
      pkinit_eku_checking = kpServerAuth
      pkinit_kdc_hostname = DNV-DC1.apporto.com
  }

If SSO (CBA) is not used, the values above can stay with defaults and certificate does not have to be provided. The SSO (CBA) will not function, but regular connections with username and password will work.

