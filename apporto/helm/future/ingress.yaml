{{- if .Values.ingress.enabled -}}
{{- $fullName := include "hyperstream.fullname" . -}}
{{- $servicePort := .Values.hyperstream.service.port -}}
{{- $ingressPath := .Values.ingress.path -}}
{{- $ingressHosts := .Values.ingress.hosts -}}
{{- $ingressExtraPaths := .Values.ingress.extra_paths -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    app: {{ template "hyperstream.name" . }}
    chart: {{ template "hyperstream.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  namespace: {{ .Release.Namespace }}
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/affinity: cookie
    nginx.ingress.kubernetes.io/session-cookie-hash: sha1
    nginx.ingress.kubernetes.io/session-cookie-name: {{ $fullName }}-LB
spec:
{{- if .Values.ingress.tls }}
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
    - host: {{ . }}
      http:
        paths:
          - path: {{ $ingressPath }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  name: http
  {{- end }}
  {{- range $ingressExtraPaths -}}
  {{- $extraPath := . -}}
  {{- range $ingressHosts }}
    - host: {{ . }}
      http:
        paths:
          - path: {{ $extraPath }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  name: http
  {{- end }}{{- end }}
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}-directpath
  labels:
    app: {{ template "hyperstream.name" . }}
    chart: {{ template "hyperstream.chart" . }}
    release: {{ .Release.Name }}
    heritage: {{ .Release.Service }}
  namespace: {{ .Release.Namespace }}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: {{ .Values.ingress.path }}
spec:
{{- if .Values.ingress.tls }}
  tls:
  {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . }}
      {{- end }}
      secretName: {{ .secretName }}
  {{- end }}
{{- end }}
  rules:
  {{- $replicaCountPlusOne := (int (toString (int .Values.replicaCount) | add1)) }}
  {{- $directPathCounts := untilStep 1 $replicaCountPlusOne 1 -}}
  {{- range $index, $val := $directPathCounts }}
  {{- range $ingressHosts }}
    - host: {{ . }}
      http:
        paths:
          - path: /srv-{{$val}}{{ $ingressPath }}
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}-svc{{$index}}
                port:
                  name: http

  {{- end }}{{- end }}
{{- end }}
