<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_6" inherit-compiler-output="false">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/resources" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module-library" scope="TEST">
      <library>
        <CLASSES>
          <root url="jar://$APPLICATION_HOME_DIR$/lib/junit.jar!/" />
        </CLASSES>
        <JAVADOC />
        <SOURCES />
      </library>
    </orderEntry>
    <orderEntry type="module" module-name="guacamole-common" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.6.1" level="project" />
    <orderEntry type="module" module-name="guacamole-ext" />
    <orderEntry type="library" name="Maven: javax.servlet:servlet-api:2.5" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.security:xml-security-impl:1.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-all:1.9.5" level="project" />
  </component>
</module>

