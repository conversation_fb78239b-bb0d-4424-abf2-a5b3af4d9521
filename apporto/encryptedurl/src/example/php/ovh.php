<?php

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');
require_once(__DIR__ . '/security.php');
#$guac_base_url = 'http://apporto-test/hyperstream/#/client/';
#$guac_base_url = 'http://apporto-perftest/hyperstream/#/client/';
$guac_base_url = 'http://localhost:8080/hyperstream/#/client/';
#$guac_base_url = 'http://nc-guac1.apporto.com/hyperstream/#/client/';
#$guac_base_url = 'http://nv-guac1.apporto.com/hyperstream/#/client/';

$protocol = 'rdp';
#$hostname = 'OVH-BROKER1.apporto.com';
$hostname = 'project-rds1.apporto.com';
#$username = 't1';
#$username = 'joestudent_trial42';
$username = 'ovhtest2_vipitinc1';
#$username = 'guacadmin';
$password = 'biFpvAJ2tS';
#$password = 'NsovvNm8Zz';
#$password = 'KHrcFSAxjv';
$secret   = 'ApportoTestSecretKey';

$extraParams = array(
  'guac.security'    => 'any',
  'guac.ignore-cert' => 'true',
  'guac.domain' => 'apporto',
#  'guac.remote-app'  => '||winword',
#  'guac.color-depth'  => '8',
#  'guac.enable-printing' => 'true',
#  'guac.enable-sftp'  => 'true',
#  'guac.sftp-hostname'  => 'OVH-BROKER1.apporto.com',
#  'guac.sftp-port'  => '22',
#  'guac.sftp-username'  => $username,
#  'guac.sftp-password'  => $password,
  'guac.nid'    => '1182',
  'guac.subdomain' => 'vipitinc1',
  'guac.gateway-hostname' => 'OVH-BROKER1.apporto.com',
  'guac.load-balancing-info' => 'tsv:\/\/VMResource.1.OVH-VDI',
  'guac.port'    => '3389',
);

$urlBuilder = new GuacamoleUrlBuilder($secret, $guac_base_url);
$url = $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);

print "${url}\n";

