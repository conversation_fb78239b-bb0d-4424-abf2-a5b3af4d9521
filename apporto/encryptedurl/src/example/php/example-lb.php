<?php

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');
require_once(__DIR__ . '/security.php');
#$guac_base_url = 'http://apporto-test/hyperstream/#/client/';
$guac_base_url = 'https://nc-guac-dev-lb.apporto.com/hyperstream/#/client/';
#$guac_base_url = 'http://apporto-perftest/hyperstream/#/client/';
#$guac_base_url = 'http://localhost:8080/hyperstream/#/client/';
#$guac_base_url = 'http://msne-guac-main.apporto.com/hyperstream/#/client/';
#$guac_base_url = 'http://nc-guac1.apporto.com/hyperstream/#/client/';
#$guac_base_url = 'http://nv-guac1.apporto.com/hyperstream/#/client/';

$protocol = 'rdp';
#$hostname = 'nc-gpu1.apporto.com';
#$hostname = 'msnv-demo-rds2.apporto.com';
$hostname = 'nv-demo-gpu1.apporto.com';
#$hostname = '**********';
#$hostname = '************';
#$hostname = 'msne-sgapp002.apporto.com';
#$hostname = 'msnv-demo-rds2.apporto.com';
$username = 't1';
#$username = 'admin_ucla';
#$username = 'joestudent_trial42';
#$username = 'joestudent_demo';
#$username = 'guacadmin';
$password = 'QWE123ewq';
#$password = 'MGL5NAzUQT';
#$password = 'NsovvNm8Zz';
#$password = 'KHrcFSAxjv';
$secret   = 'ApportoTestSecretKey';

$extraParams = array(
  'guac.security'    => 'any',
  'guac.ignore-cert' => 'true',
  'guac.domain' => 'apporto',
#  'guac.remote-app'  => '||winproj',
#  'guac.color-depth'  => '8',
#  'guac.enable-sftp'  => 'true',
#  'guac.sftp-hostname'  => 'PROJECT-RDS1.apporto.com',
#  'guac.sftp-hostname'  => 'NC-DC1.apporto.com',
#  'guac.sftp-port'  => '22',
#  'guac.sftp-username'  => $username,
#  'guac.sftp-password'  => $password,
#  'guac.nid'    => '993',
  'guac.subdomain' => 'rohit',
  'guac.resize-method' => 'reconnect',
  'guac.enable-upload' => 'true',
  'guac.enable-download' => 'true',
  'guac.enable-screensharing' => 'true',
  'guac.enable-analytics' => 'true',
  'guac.enable-messenger' => 'true',
  'guac.enable-snapshots' => 'true',
  'guac.enable-annotations' => 'true',
  'guac.enable-printing' => 'true'
);

$urlBuilder = new GuacamoleUrlBuilder($secret, $guac_base_url);
$url = $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);

print "${url}\n";

