<?php

/**
 * Usage example:
 *
 *  $security = new Security("my secret key");
 *  $encrypted = $security->encrypt($message);
 *  $decrypted = $security->decrypt($encrypted);
 */

class Security {

  protected $sharedSecret;
  protected $key;

  public function __construct($secret) {
    $this->sharedSecret = base64_decode($secret);
    $this->key = substr(sha1($secret, TRUE),0,16);
  }

  public function getKey() {
    return base64_encode($this->key);
  }

  public function encrypt($input) {
    $pad = 16 - (strlen($input) % 16);
    $input = $input . str_repeat("\0", $pad);
    $encrypted = openssl_encrypt($input, 'AES-128-ECB', $this->key, $options=OPENSSL_RAW_DATA);
    return base64_encode($encrypted);
	}

	private static function pkcs5_pad ($text, $blocksize) {
		$pad = $blocksize - (strlen($text) % $blocksize);
		return $text . str_repeat(chr($pad), $pad);
	}

	public function decrypt($sStr) {
    $decrypted = openssl_decrypt(
        base64_decode($sStr), 
        'AES-128-ECB', 
        $this->key, 
        OPENSSL_RAW_DATA
    );

    // Remove padding
    $decrypted = rtrim($decrypted, "\0");

    return $decrypted;
	}
}
?>
