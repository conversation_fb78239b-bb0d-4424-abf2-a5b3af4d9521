<?php

if (count($argv) < 4) {
    echo "USAGE: php create_signed_url.php <PROTOCOL> <HOSTNAME> <USERNAME> <PASSWORD>\n";
    exit(1);
}

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');

list($_, $protocol, $hostname, $username, $password) = $argv;

$extraArgs   = array_slice($argv, 4);
$extraParams = array();

foreach ($extraArgs as $pair) {
    list($key, $value) = explode('=', $pair);
    $extraParams["guac.{$key}"] = $value;
}

$urlBuilder = new GuacamoleUrlBuilder("a shared secret phrase", "http://localhost/hyperstream/#/client/");
print $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);
