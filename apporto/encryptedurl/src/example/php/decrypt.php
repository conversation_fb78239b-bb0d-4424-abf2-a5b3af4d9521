<?php

require_once(__DIR__ . '/security.php');

// Check if the URL is provided as an argument
if ($argc < 2) {
    echo "No encrypted URL provided.\n";
    exit(1);
}

$url = $argv[1];
$secret   = 'somethingAleiaktlbeterathanpassword';

$decryptedString = decryptFromUrl($url, $secret);
echo "Decrypted string: \n" . $decryptedString . "\n";

function decryptFromUrl($url, $secret) {
    // Parse URL and extract encrypted URL from parameter 'q'
    $fragment = parse_url($url, PHP_URL_FRAGMENT);
    if ($fragment !== null) {
        $queryStringStart = strpos($fragment, '?');

        if ($queryStringStart !== false) {
            // Ekstract query string
            $queryString = substr($fragment, $queryStringStart + 1);

            // Parse query string params
            parse_str($queryString, $params);

            // Check if 'q' parameter is present
            if (isset($params['q'])) {
                // encrypted string
                $stringToDecrypt = $params['q'];

                // Init security class with secret key
                $security = new Security($secret);

                // call decrypt function
                $decrypted = $security->decrypt($stringToDecrypt);

                // return decrypted string
                return $decrypted;
            } else {
                return "Parameter 'q' not found.";
            }
        } else {
            return "Query string does not exists.";
        }
    } else {
        return "No URL fragment.";
    }
}


?>
