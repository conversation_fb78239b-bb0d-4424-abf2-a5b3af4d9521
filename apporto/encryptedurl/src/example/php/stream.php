<?php

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');
require_once(__DIR__ . '/security.php');
$guac_base_url = 'http://apporto-broker/hyperstream/#/client/';

$protocol = 'rdp';
#$hostname = 'nv-broker1.apporto.com';
$hostname = 'project-rds1.apporto.com';
$username = 'george2_demo';
$password = '3snjnh8Xb5';
#$username = 'demo_1491835858';
#$password = 'pqKaFqxcqp';
$secret   = 'ApportoTestSecretKey';

$extraParams = array(
  'guac.security'    => 'any',
  'guac.domain' => 'apporto',
  'guac.ignore-cert' => 'true',
#  'guac.remote-app'  => '||winproj',
  'guac.initial-program'  => '||winproj',
#  'guac.enable-sftp'  => 'true',
#  'guac.sftp-hostname'  => 'project-rds1.apporto.com',
#  'guac.sftp-port'  => '22',
#  'guac.sftp-username'  => 't1',
#  'guac.sftp-password'  => $password,
);

$urlBuilder = new GuacamoleUrlBuilder($secret, $guac_base_url);
$url = $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);

print "${url}\n";

