<?php

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');
require_once(__DIR__ . '/security.php');
$guac_base_url = 'http://localhost:8080/hyperstream/#/client/';

$protocol = 'rdp';
$hostname = 'project-rds1.apporto.com';
$username = 'guacadmin';
$password = 'MGL5NAzUQT';
$secret   = 'ApportoTestSecretKey';

$extraParams = array(
#  'guac.security'    => 'rdp',
#  'guac.ignore-cert' => 'true',
#  'guac.domain' => 'apporto',
#  'guac.remote-app'  => '||winproj',
#  'guac.color-depth'  => '8',
#  'guac.enable-sftp'  => 'true',
#  'guac.sftp-hostname'  => 'PROJECT-RDS1.apporto.com',
#  'guac.sftp-hostname'  => 'NV-DC1.apporto.com',
#  'guac.sftp-port'  => '22',
#  'guac.sftp-username'  => $username,
#  'guac.sftp-password'  => $password,
#  'guac.nid'    => '993',
#  'guac.subdomain' => 'demo',
#  'guac.resize-method' => 'display-update',
#  'guac.enable-upload' => 'true',
#  'guac.enable-download' => 'true',
#  'guac.enable-screensharing' => 'true',
#  'guac.enable-analytics' => 'true',
#  'guac.enable-messenger' => 'true',
#  'guac.enable-snapshots' => 'true',
#  'guac.enable-annotations' => 'true',
#  'guac.enable-printing' => 'true',
#  'guac.client-name' => '000001:deadbeef',
#  'guac.minimize-param' => 'true',
#  'guac.ClientName'=>'001'
);

$urlBuilder = new GuacamoleUrlBuilder($secret, $guac_base_url);
$url = $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);

print "${url}\n";

