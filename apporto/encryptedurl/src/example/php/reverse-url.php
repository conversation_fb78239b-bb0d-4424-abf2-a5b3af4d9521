<?php

// Check for the command line parameter
if ($argc < 2) {
    die("Usage: php reverse-url.php '<json_input>'\n");
}

// Load JSON from command line
$json_input = $argv[1];

// Parse JSON into php array
$data = json_decode($json_input, true);

// Check if JSO<PERSON> is valid
if (json_last_error() !== JSON_ERROR_NONE) {
    die("Invalid JSON input\n");
}

// setup variables
$protocol = isset($data['guac']['protocol']) ? $data['guac']['protocol'] : 'rdp';
$hostname = isset($data['guac']['hostname']) ? $data['guac']['hostname'] : 'dnv-proxy-lb.apporto.com';
$username = isset($data['guac']['username']) ? $data['guac']['username'] : 'markostudent2_dev';
$password = isset($data['guac']['password']) ? $data['guac']['password'] : '**********';
$secret = 'somethingAleiaktlbeterathanpassword';

// Extra params
$extraParams = array(
    'guac.security'    => isset($data['guac']['security']) ? $data['guac']['security'] : 'rdp',
    'guac.ignore-cert' => isset($data['guac']['ignore-cert']) ? $data['guac']['ignore-cert'] : 'true',
    'guac.domain'      => isset($data['guac']['domain']) ? $data['guac']['domain'] : 'apporto',
    'guac.enable-sftp' => isset($data['guac']['enable-sftp']) ? $data['guac']['enable-sftp'] : 'true',
    'guac.sftp-hostname' => isset($data['guac']['sftp-hostname']) ? $data['guac']['sftp-hostname'] : 'dnv-sftp-lb.apporto.com',
    'guac.sftp-port'   => isset($data['guac']['sftp-port']) ? $data['guac']['sftp-port'] : '22',
    'guac.sftp-username' => $username,
    'guac.sftp-password' => $password,
    'guac.nid'         => isset($data['guac']['nid']) ? $data['guac']['nid'] : '2',
    'guac.subdomain'   => isset($data['guac']['subdomain']) ? $data['guac']['subdomain'] : 'dev.apporto.com',
    'guac.resize-method' => isset($data['guac']['resize-method']) ? $data['guac']['resize-method'] : 'display-update',
    'guac.enable-async-collaboration' => isset($data['guac']['enable-async-collaboration']) ? $data['guac']['enable-async-collaboration'] : 'true',
    'guac.enable-audio-input' => isset($data['guac']['enable-audio-input']) ? $data['guac']['enable-audio-input'] : 'true',
    'guac.enable-audio-output-aac' => isset($data['guac']['enable-audio-output-aac']) ? $data['guac']['enable-audio-output-aac'] : 'false',
    'guac.enable-camera-input' => isset($data['guac']['enable-camera-input']) ? $data['guac']['enable-camera-input'] : 'false',
    'guac.enable-font-smoothing' => isset($data['guac']['enable-font-smoothing']) ? $data['guac']['enable-font-smoothing'] : 'true',
    'guac.enable-upload' => isset($data['guac']['enable-upload']) ? $data['guac']['enable-upload'] : 'true',
    'guac.enable-download' => isset($data['guac']['enable-download']) ? $data['guac']['enable-download'] : 'true',
    'guac.enable-screensharing' => isset($data['guac']['enable-screensharing']) ? $data['guac']['enable-screensharing'] : 'true',
    'guac.enable-analytics' => isset($data['guac']['enable-analytics']) ? $data['guac']['enable-analytics'] : 'true',
    'guac.enable-messenger' => isset($data['guac']['enable-messenger']) ? $data['guac']['enable-messenger'] : 'true',
    'guac.enable-snapshots' => isset($data['guac']['enable-snapshots']) ? $data['guac']['enable-snapshots'] : 'true',
    'guac.enable-annotations' => isset($data['guac']['enable-annotations']) ? $data['guac']['enable-annotations'] : 'true',
    'guac.enable-printing' => isset($data['guac']['enable-printing']) ? $data['guac']['enable-printing'] : 'true',
    'guac.enable-watermark' => isset($data['guac']['enable-watermark']) ? $data['guac']['enable-watermark'] : 'true',
    'guac.enable-touch' => isset($data['guac']['enable-touch']) ? $data['guac']['enable-touch'] : 'true',
    'guac.enable-url-security' => isset($data['guac']['enable-url-security']) ? $data['guac']['enable-url-security'] : 'false',
    'guac.enable-usb' => isset($data['guac']['enable-usb']) ? $data['guac']['enable-usb'] : 'false',
    'guac.enable-multimonitor' => isset($data['guac']['enable-multimonitor']) ? $data['guac']['enable-multimonitor'] : 'true',
    'guac.disable-glyph-caching' => isset($data['guac']['disable-glyph-caching']) ? $data['guac']['disable-glyph-caching'] : 'true',
    'guac.disable-offscreen-caching' => isset($data['guac']['disable-offscreen-caching']) ? $data['guac']['disable-offscreen-caching'] : 'true',
    'guac.enable-wallpaper' => isset($data['guac']['enable-wallpaper']) ? $data['guac']['enable-wallpaper'] : 'false',
    'guac.client-name' => isset($data['guac']['client-name']) ? $data['guac']['client-name'] : '000001:deadbeef',
    'guac.minimize-param' => isset($data['guac']['minimize-param']) ? $data['guac']['minimize-param'] : 'true',
    'guac.ClientName' => isset($data['guac']['ClientName']) ? $data['guac']['ClientName'] : '00',
    'guac.Http-Api-Key' => isset($data['guac']['Http-Api-Key']) ? $data['guac']['Http-Api-Key'] : '@pp0#10_Gen',
    'guac.AppName' => isset($data['guac']['AppName']) ? $data['guac']['AppName'] : 'DNV app',
    'guac.CustomerAD' => isset($data['guac']['CustomerAD']) ? $data['guac']['CustomerAD'] : 'apporto.com',
    'guac.idle-timeout' => isset($data['guac']['idle-timeout']) ? $data['guac']['idle-timeout'] : '60',
    'guac.user-id' => isset($data['guac']['user-id']) ? $data['guac']['user-id'] : '3048',
    'guac.industry-type' => isset($data['guac']['industry-type']) ? $data['guac']['industry-type'] : 'Higher ed',
    'guac.cloud_username' => isset($data['guac']['cloud_username']) ? $data['guac']['cloud_username'] : 'markostudent2_dev',
    'guac.cloud-provider' => isset($data['guac']['cloud-provider']) ? $data['guac']['cloud-provider'] : 'aws',
    'guac.subscription' => isset($data['guac']['subscription']) ? $data['guac']['subscription'] : 'apps',
    'guac.enable-classroom' => isset($data['guac']['enable-classroom']) ? $data['guac']['enable-classroom'] : 'true',
    'guac.enable-activitytracker' => isset($data['guac']['enable-activitytracker']) ? $data['guac']['enable-activitytracker'] : 'true',
    'guac.enable-statistics-frames' => isset($data['guac']['enable-statistics-frames']) ? $data['guac']['enable-statistics-frames'] : 'true',
    'guac.enable-clipboard' => isset($data['guac']['enable-clipboard']) ? $data['guac']['enable-clipboard'] : 'true',
    'guac.enable-h264' => isset($data['guac']['enable-h264']) ? $data['guac']['enable-h264'] : 'true',
    'h264-supported' => isset($data['guac']['h264-supported']) ? $data['guac']['h264-supported'] : 'true',
    'guac.enable-reboot' => isset($data['guac']['enable-reboot']) ? $data['guac']['enable-reboot'] : 'false',
    'guac.enable-backup' => isset($data['guac']['enable-backup']) ? $data['guac']['enable-backup'] : 'false',
    'guac.enable-filebrowser' => isset($data['guac']['enable-filebrowser']) ? $data['guac']['enable-filebrowser'] : 'false',
    'guac.server-layout' => isset($data['guac']['server-layout']) ? $data['guac']['server-layout'] : 'en-gb-qwerty',
    'guac.extended_compute' => isset($data['guac']['extended_compute']) ? $data['guac']['extended_compute'] : 'false',
    'guac.port' => isset($data['guac']['port']) ? $data['guac']['port'] : '3398',
    'guac.launch-full-screen' => isset($data['guac']['launch-full-screen']) ? $data['guac']['launch-full-screen'] : 'false',
    'guac.AppOS' => isset($data['guac']['AppOS']) ? $data['guac']['AppOS'] : 'Windows'
);

// Generate PHP code
echo "<?php\n\n";
echo "require_once(__DIR__ . '/GuacamoleUrlBuilder.php');\n";
echo "require_once(__DIR__ . '/security.php');\n";
echo "\$guac_base_url = 'http://localhost:8080/hyperstream/#/client/';\n\n";

echo "\$protocol = '$protocol';\n";
echo "\$hostname = '$hostname';\n";
echo "\$username = '$username';\n";
echo "\$password = '$password';\n";
echo "\$secret   = '$secret';\n\n";
echo "\$extraParams = array(\n";

// Add extra params
foreach ($extraParams as $key => $value) {
    // Special handling for 'sftp-username' i 'sftp-password'
    if ($key === 'guac.sftp-username') {
        echo "  '$key' => \$username,\n";
    } elseif ($key === 'guac.sftp-password') {
        echo "  '$key' => \$password,\n";
    } else {
        // If the value is boolean, show as 'true' or 'false'
        if (is_bool($value)) {
            $valueStr = $value ? 'true' : 'false';
            echo "  '$key' => '$valueStr',\n";
        } else {
            // Prikaži kao string
            echo "  '$key' => '$value',\n";
        }
    }
}

echo ");\n\n";

echo "\$urlBuilder = new GuacamoleUrlBuilder(\$secret, \$guac_base_url);\n";
echo "\$url = \$urlBuilder->url(\$protocol, \$hostname, \$username, \$password, \$extraParams);\n\n";

echo "print \"{\$url}\\n\";\n";


?>

