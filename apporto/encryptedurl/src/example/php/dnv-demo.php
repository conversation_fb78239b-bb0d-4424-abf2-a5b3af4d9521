<?php

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');
require_once(__DIR__ . '/security.php');
#$guac_base_url = 'http://localhost:8080/hyperstream/#/client/';
$guac_base_url = 'http://dnv-guac1.apporto.com/hyperstream/#/client/';

$protocol = 'rdp';
#$hostname = 'project-rds1.apporto.com';
$hostname = 'dnv-demo-gpu1.apporto.com';
$username = 't1';
$password = 'QWE123ewq';
$secret   = 'ApportoTestSecretKey';

$extraParams = array(
  'guac.security'    => 'any',
  'guac.ignore-cert' => 'true',
  'guac.domain' => 'apporto',
#  'guac.remote-app'  => '||winproj',
#  'guac.color-depth'  => '8',
  'guac.enable-sftp'  => 'true',
#  'guac.sftp-hostname'  => 'PROJECT-RDS1.apporto.com',
  'guac.sftp-hostname'  => 'NV-DC1.apporto.com',
  'guac.sftp-port'  => '22',
  'guac.sftp-username'  => $username,
  'guac.sftp-password'  => $password,
  'guac.nid'    => '993',
  'guac.subdomain' => 'apps',
  'guac.resize-method' => 'reconnect',
  'guac.enable-upload' => 'true',
  'guac.enable-download' => 'true',
  'guac.enable-screensharing' => 'true',
  'guac.enable-analytics' => 'true',
  'guac.enable-messenger' => 'true',
  'guac.enable-snapshots' => 'true',
  'guac.enable-annotations' => 'true',
  'guac.enable-printing' => 'true'
);

$urlBuilder = new GuacamoleUrlBuilder($secret, $guac_base_url);
$url = $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);

print "${url}\n";

