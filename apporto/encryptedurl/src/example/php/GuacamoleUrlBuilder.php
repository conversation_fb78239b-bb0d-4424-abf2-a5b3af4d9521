<?php
include 'security.php';
/**
 * Usage example:
 *
 *     $urlBuilder = new GuacamoleUrlBuilder("my secret key", "http://myguacamoleserver.internal/client.xhtml");
 *     $url = $urlBuilder->url("myserver", "vnc", "myvncserver.internal");
 */
class GuacamoleUrlBuilder {
    private static $signedParams = array(
        'guac.username',
        'guac.password',
        'guac.hostname',
        'guac.port'
    );

    /** @var string */
    protected $clientUrl;

    /** @var string */
    protected $secretKey;

    public function __construct($secretKey, $clientUrl) {
        $this->clientUrl = $clientUrl;
        $this->secretKey = $secretKey;
    }

    public function url($protocol, $hostname, $username, $password, $extraParams = array()) {
        $timestamp = (time()+ 86400*365*20) * 1000;

        $connectionId     = mt_rand(1, 20000);
        $connectionType   = "c";
        $dataSource       = "encryptedurl-jdbc";
        $clientIdentifier = base64_encode(
          implode("\0", [$connectionId, $connectionType, $dataSource])
        );

        // Array of query parameters to pass to guacamole
        $qp = array(
            'id'            => "$connectionType/$connectionId",
            'timestamp'     => $timestamp,
            'guac' => array(
              'hostname' => $hostname,
              'protocol' => $protocol,
              'username' => $username,
              'password' => $password
            )
        );

        // Copy any extra guacamole key value params into the query string
        foreach ($extraParams as $key => $value) {
            if (strpos($key, 'guac.') === 0) {
                $qp["guac"][str_replace('guac.', '', $key)]  = $value;
            }
        }

        // Add default port
        //if (!array_key_exists('guac.port', $qp)) {
        if (!isset($qp['guac']['port'])) {
            if ($protocol == 'rdp') {
                $qp['guac']['port'] = '3389';
            }
            else if ($protocol == 'vnc') {
                $qp['guac']['port'] = '5900';
            }
            else if ($protocol == 'ssh') {
                $qp['guac']['port'] = '22';
            }
        }

        echo json_encode( $qp ) ."\n";
        echo $this->secretKey ."\n";

        $security = new Security($this->secretKey);

        return $this->clientUrl . $clientIdentifier . '?'
            . 'q='
            . urlencode(
              $security->encrypt( json_encode( $qp ) ) 
            );
    }
}
