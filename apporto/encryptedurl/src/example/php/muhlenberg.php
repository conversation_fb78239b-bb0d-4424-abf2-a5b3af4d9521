<?php

require_once(__DIR__ . '/GuacamoleUrlBuilder.php');
require_once(__DIR__ . '/security.php');
#$guac_base_url = 'http://apporto-test/hyperstream/#/client/';
#$guac_base_url = 'http://apporto-mul/hyperstream/#/client/';
$guac_base_url = 'http://localhost:8080/hyperstream/#/client/';

$protocol = 'rdp';
#$hostname = 'OVH-BROKER1.apporto.com';
$hostname = 'NV-BROKER1.apporto.com';
#$username = 't1';
#$username = 'joestudent_trial42';
$username = 'snhu.user1';
#$username = 'guacadmin';
$password = 'QWE123ewq';
#$password = 'NsovvNm8Zz';
#$password = 'KHrcFSAxjv';
$secret   = 'ApportoTestSecretKey';

$extraParams = array(
  'guac.security'    => 'any',
  'guac.ignore-cert' => 'true',
  'guac.domain' => 'apporto',
#  'guac.remote-app'  => '||maplew',
#  'guac.initial-program'  => '||maplew',
#  'guac.color-depth'  => '8',
#  'guac.enable-printing' => 'true',
  'guac.enable-sftp'  => 'true',
  'guac.sftp-hostname'  => 'NV-DC1.apporto.com',
  'guac.sftp-port'  => '22',
  'guac.sftp-username'  => $username,
  'guac.sftp-password'  => $password,
  'guac.nid'    => '1182',
  'guac.subdomain' => 'vipitinc1',
#  'guac.gateway-hostname' => 'NV-BROKER1.apporto.com',
  'guac.load-balancing-info' => 'tsv:\/\/MS Terminal Services Plugin.1.SNHU-CL1',
  'guac.port'    => '3389',
);

$urlBuilder = new GuacamoleUrlBuilder($secret, $guac_base_url);
$url = $urlBuilder->url($protocol, $hostname, $username, $password, $extraParams);

print "${url}\n";

