/**
 * 
 */
package com.dmwl.guacamole.net.encryptedurl;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

import org.unitils.reflectionassert.ReflectionAssert;

import org.apache.guacamole.protocol.GuacamoleConfiguration;

/**
 * <AUTHOR>
 *
 */
public class JsonDecodeTest {

    private JsonDecode decoded; 
    /**
     * @throws java.lang.Exception
     */
    @Before
    public void setUp() throws Exception {
        String json = ""
            + "{"
            +    "\"timestamp\": 1407258262016,"
            +    "\"id\": 8158,"
            +    "\"guac\": {"
            +      "\"port\": \"eu\","
            +      "\"protocol\": \"culpa\","
            +      "\"password\": \"sint\","
            +      "\"username\": \"ullamco\","
            +      "\"hostname\": \"sunt\""
            +    "}"
            +  "}";
        
        decoded = new JsonDecode(json);
    }

    /**
     * Test method for {@link com.dmwl.guacamole.net.encryptedurl.JsonDecode#getConfig()}.
     */
    @Test
    public void testGetConfig() {
        GuacamoleConfiguration decodedConfig = decoded.getConfig();
        GuacamoleConfiguration expectedConfig = new GuacamoleConfiguration();
        
        expectedConfig.setProtocol("culpa");
        expectedConfig.setParameter("port", "eu");
        expectedConfig.setParameter("password", "sint");
        expectedConfig.setParameter("username", "ullamco");
        expectedConfig.setParameter("hostname", "sunt");
        
        ReflectionAssert.assertReflectionEquals(expectedConfig, decodedConfig);
    }

    /**
     * Test method for {@link com.dmwl.guacamole.net.encryptedurl.JsonDecode#getTimestamp()}.
     */
    @Test
    public void testGetTimestamp() {
        assertEquals("Timestamp decoded wrong", "1407258262016", decoded.getTimestamp());
    }

    /**
     * Test method for {@link com.dmwl.guacamole.net.encryptedurl.JsonDecode#getId()}.
     */
    @Test
    public void testGetId() {
        assertEquals("ID decoded wrong", "8158", decoded.getId());
    }

}
