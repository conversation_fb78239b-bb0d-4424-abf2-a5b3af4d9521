package com.dmwl.guacamole.net.encryptedurl;

import static org.junit.Assert.assertEquals;

import org.junit.Before;
import org.junit.Test;

public class SecurityTest {

    private Security security;
    private String   example, example2, sharedSecret, secretKey;

    @Before
    public void setUp() throws Exception {
        example = "4.size,4.1024,3.768,2.96;5.audio,9.audio/ogg;5.video;5.image,9.image/png,10.image/jpeg;7.connect,9.localhost,4.5900,0.,0.,0.;";
        example2 = "4.size,4.1024,3.768,2.96;5.audio,9.audio/ogg;5.video;5.image,9.image/png,10.image/jpeg;7.connect,9.localhost,4.5900,0.,0.,0.;         ";
        // A shared secret and its corresponding 128bit key
        sharedSecret = "example shared secret";
        secretKey = "UetaAionVB6NaNKFSX8iUA==";

        security = new Security(sharedSecret);
    }

    @Test
    public void testDecryptEncryptCall() {
        String encrypted = security.encrypt(example);
        String decrypted = security.decrypt(encrypted);
        assertEquals("failure - Decryption doesn't match", example, decrypted);
    }

    @Test
    public void testDecryptCall() {
        String encrypted = "DMOvVoOnvDs9mmen4B9lOZ/AyDJa0PWCcLzo6hfvM7mrH3NbGDjseySG0MMhgHYldOhqOL601tBmp3bEcIZcg4/T/dx5RukYJtbpDXsbur7GHAoNmWqisF5vapMsGDSkFLEUKCQoF0zVWToyvS9+53t5EMyB+EEybYwGOrVInCs=";
        String decrypted = security.decrypt(encrypted);
        assertEquals("failure - Decryption doesn't match", example, decrypted);
    }

    @Test
    public void testEncryptCall() {
        String expected = "DMOvVoOnvDs9mmen4B9lOZ/AyDJa0PWCcLzo6hfvM7mrH3NbGDjseySG0MMhgHYldOhqOL601tBmp3bEcIZcg4/T/dx5RukYJtbpDXsbur7GHAoNmWqisF5vapMsGDSkFLEUKCQoF0zVWToyvS9+53t5EMyB+EEybYwGOrVInCs=";
        String encrypted = security.encrypt(example);

        assertEquals("failure - Decryption doesn't match", expected, encrypted);
    }
    
    @Test
    public void testSpacingTrim() {
        String original = "trimmed\u0010";
        String trimmed = "trimmed";
        original = original.trim();
        assertEquals("failure - Decryption doesn't match", trimmed, original);
    }
    
    @Test
    public void testAesKey() {

        assertEquals("failure - secret keys don't match", secretKey,
                security.getKey());
    }

}