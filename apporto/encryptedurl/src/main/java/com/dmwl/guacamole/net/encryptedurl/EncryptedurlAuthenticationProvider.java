/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.dmwl.guacamole.net.encryptedurl;

import java.util.HashMap;
import java.util.Map;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.net.auth.AuthenticatedUser;
import org.apache.guacamole.net.auth.Credentials;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.net.auth.simple.SimpleAuthenticationProvider;
import org.apache.guacamole.net.auth.simple.SimpleConnection;
import org.apache.guacamole.net.auth.simple.SimpleConnectionDirectory;
import org.apache.guacamole.properties.GuacamoleProperties;
import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.apache.guacamole.protocol.GuacamoleConfiguration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EncryptedurlAuthenticationProvider
        extends SimpleAuthenticationProvider {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory
            .getLogger(EncryptedurlAuthenticationProvider.class);

    /**
     * Properties file params
     */
    private static final StringGuacamoleProperty SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "secret-key";
        }
    };

    private static final StringGuacamoleProperty DEFAULT_PROTOCOL = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "default-protocol";
        }
    };

    private static final IntegerGuacamoleProperty TIMESTAMP_AGE_LIMIT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "timestamp-age-limit";
        }
    };

    /**
     * these will be overridden by properties file if present
     */
    private String defaultProtocol   = "rdp";
    public static final long TEN_MINUTES = 10 * 60 * 1000;
    private long timestampAgeLimit = TEN_MINUTES; // 10 minutes

    /**
     * Per-request params
     */
    public static final String MESSAGE_PARAM = "q";

    /**
     * Security list for each secret key
     */
    private List<Security> securityList;

    /**
     * Others
     */
    private final TimeProviderInterface timeProvider;

    public EncryptedurlAuthenticationProvider(
            TimeProviderInterface timeProvider) {
        this.timeProvider = timeProvider;
    }

    public EncryptedurlAuthenticationProvider() {
        timeProvider = new DefaultTimeProvider();
    }

    public String getIdentifier() {
        return "encryptedurl";
    }

    @Override
    public Map<String, GuacamoleConfiguration> getAuthorizedConfigurations(
            Credentials credentials) throws GuacamoleException {
        if (securityList == null) {
            initFromProperties();
        }

        GuacamoleConfiguration config = getGuacamoleConfiguration(
                credentials.getRequest());

        if (config == null) {
            return null;
        }

        Map<String, GuacamoleConfiguration> configs = new HashMap<String, GuacamoleConfiguration>();
        configs.put(config.getParameter("id"), config);
        return configs;
    }

    @Override
    public UserContext updateUserContext(UserContext context,
            AuthenticatedUser user,
            Credentials credentials) throws GuacamoleException {
        HttpServletRequest request = credentials.getRequest();
        GuacamoleConfiguration config = getGuacamoleConfiguration(request);
        if (config == null) {
            return null;
        }

        String id = config.getParameter("id");
        SimpleConnectionDirectory connections = (SimpleConnectionDirectory) context
                .getConnectionDirectory();
        SimpleConnection connection = new SimpleConnection(id, id, config);
        connection.setParentIdentifier("ROOT");
        connections.putConnection(connection);

        return context;
    }

    @Override
    public AuthenticatedUser updateAuthenticatedUser(
            AuthenticatedUser authenticatedUser, Credentials credentials)
            throws GuacamoleException {

        return authenticateUser(credentials);
    }

    private GuacamoleConfiguration getGuacamoleConfiguration(
            HttpServletRequest request) throws GuacamoleException {

        if (securityList == null) {
            initFromProperties();
        }

        String message = request.getParameter(MESSAGE_PARAM);
        // Try the list of secret keys until decryption succeeds.
        for (Security security : securityList) {
            String messageTmp = security.decrypt(message);
            if (messageTmp != null) {
                message = messageTmp;
                break;
            }
        }
        message = message.trim();
        
        logger.debug("Decrypted message: {}", message);

        if (message == null) {
            return null;
        }

        JsonDecode connectionObj = new JsonDecode(message);
        GuacamoleConfiguration config = connectionObj.getConfig();
        String timestamp = connectionObj.getTimestamp();
        if (!checkTimestamp(timestamp)) {
            return null;
        }

        // Hostname is required!
        if (config.getParameter("hostname") == null) {
            return null;
        }

        // Protocol is required!
        if (config.getProtocol() == null) {
            return null;
        }

        String id = connectionObj.getId();
        if (id == null) {
            id = "DEFAULT";
        }
        else {
            // This should really use BasicGuacamoleTunnelServlet's
            // IdentfierType, but it is private!
            // Currently, the only prefixes are both 2 characters in length, but
            // this could become invalid at some point.
            // see:
            // guacamole-client@a0f5ccb:guacamole/src/main/java/org/glyptodon/guacamole/net/basic/BasicGuacamoleTunnelServlet.java:244-252
            id = id.substring(2);
        }

        // This isn't normally part of the config, but it makes it much easier
        // to return a single object
        config.setParameter("id", id);
        return config;
    }

    private boolean checkTimestamp(String ts) {
        if (timestampAgeLimit == 0) {
            return true;
        }

        if (ts == null) {
            return false;
        }

        long timestamp = Long.parseLong(ts, 10);
        long now = timeProvider.currentTimeMillis();
        return timestamp + timestampAgeLimit > now;
    }

    private void initFromProperties() throws GuacamoleException {
        // Split security keys into security key list and create Security list
        //   e.g. "sec_key1  , sec_key2  ,sec_key3  " -> ["sec_key1", "sec_key2", "sec_key3"]
        String secretKeys = GuacamoleProperties.getRequiredProperty(SECRET_KEY);
        List<String> secretKeyList = Arrays.asList(secretKeys.split(","));
        securityList = new ArrayList<Security>();
        for (String key : secretKeyList) {
            securityList.add(new Security(key.trim()));
        }

        defaultProtocol = GuacamoleProperties.getProperty(DEFAULT_PROTOCOL);
        if (defaultProtocol == null) defaultProtocol = "rdp";
        if (GuacamoleProperties.getProperty(TIMESTAMP_AGE_LIMIT) == null) {
            timestampAgeLimit = TEN_MINUTES;
        }
        else {
            timestampAgeLimit = GuacamoleProperties
                    .getProperty(TIMESTAMP_AGE_LIMIT);
        }
    }
}
