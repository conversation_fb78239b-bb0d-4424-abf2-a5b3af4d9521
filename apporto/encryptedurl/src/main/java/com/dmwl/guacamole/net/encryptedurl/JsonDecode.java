/**
 * 
 */
package com.dmwl.guacamole.net.encryptedurl;

import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;

import java.util.Map;
import java.util.Set;

import org.apache.guacamole.protocol.GuacamoleConfiguration;

/**
 * <AUTHOR>
 *
 */
public class JsonDecode {
    private String timestamp, id;

    private GuacamoleConfiguration config;

    /**
     * 
     */
    @SuppressWarnings("unchecked")
    public JsonDecode(String json) {
        JSONParser parser = new JSONParser();

        try {
            JSONObject obj = (JSONObject) parser.parse(json);

            id = obj.get("id").toString();
            timestamp = obj.get("timestamp").toString();

            config = new GuacamoleConfiguration();

            JSONObject guacObj = (JSONObject) obj.get("guac");
            Set<Map.Entry<String, String>> guacConfig = guacObj.entrySet();

            for (Map.Entry<String, String> entry : guacConfig) {
                Object k = entry.getKey();
                Object v = entry.getValue();

                if (k.toString().equals("protocol")) {
                    config.setProtocol(v.toString());
                }
                else {
                    config.setParameter(k.toString(), v.toString());
                }
            }
        }
        catch (ParseException e) {
            e.printStackTrace();
        }
    }

    public GuacamoleConfiguration getConfig() {
        return config;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public String getId() {
        return id;
    }
}
