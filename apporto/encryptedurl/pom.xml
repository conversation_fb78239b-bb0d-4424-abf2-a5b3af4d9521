<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.dmwl.guacamole</groupId>
	<artifactId>guacamole-auth-encryptedurl</artifactId>
	<version>1.1.0</version>

	<name>guacamole-auth-encryptedurl</name>
	<url>https://bitbucket.org/dmwl/encryptedurl</url>

	<properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

	<packaging>jar</packaging>

	<scm>
		<connection>scm:git:https://bitbucket.org/dmwl/encryptedurl.git</connection>
		<url>https://bitbucket.org/dmwl/encryptedurl.git</url>
	</scm>

	<issueManagement>
		<url>https://github.com/grncdr/guacamole-auth-encryptedurl/issues</url>
	</issueManagement>

	<developers>
		<developer>
			<id>grncdr</id>
			<name>Stephen Sugden</name>
		</developer>
		<developer>
			<id>davad</id>
			<name>David Landry</name>
		</developer>
		<developer>
			<id>jonathanlandry79</id>
			<name>Jonathan Landry</name>
		</developer>
	</developers>

	<contributors>
		<contributor>
			<name>Ryan Pessa</name>
		</contributor>
		<contributor>
			<name>Stephen Young</name>
		</contributor>
	</contributors>

	<build>
		<plugins>
			<!-- Written for 1.6 -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.3</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>

			<!-- Copy dependencies prior to packaging -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>2.10</version>
				<executions>
					<execution>
						<id>unpack-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>unpack-dependencies</goal>
						</goals>
						<configuration>
							<includeScope>runtime</includeScope>
							<outputDirectory>${project.build.directory}/classes</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.7.2</version>
				<configuration>
					<systemPropertyVariables>
						<guacamole.home>${project.build.sourceDirectory}/resources</guacamole.home>
					</systemPropertyVariables>
				</configuration>
			</plugin>
		</plugins>
	</build>

	<dependencies>
		<!-- Guacamole Extension API -->
		<dependency>
			<groupId>org.apache.guacamole</groupId>
			<artifactId>guacamole-ext</artifactId>
			<version>0.9.10-incubating</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>4.0.0-b01</version>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.security</groupId>
			<artifactId>xml-security-impl</artifactId>
			<version>1.0</version>
		</dependency>

		<!-- test deps -->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-all</artifactId>
			<version>1.9.5</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.10</version>
		</dependency>

		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
		</dependency>

		<dependency>
			<groupId>org.unitils</groupId>
			<artifactId>unitils-core</artifactId>
			<version>3.4.2</version>
		</dependency>
	</dependencies>

<!--
	<parent>
		<groupId>org.sonatype.oss</groupId>
		<artifactId>oss-parent</artifactId>
		<version>7</version>
	</parent>
-->
</project>
