/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
/**
 * Module for managing presenter display.
 */
angular.module('presenterModule').controller('presenterController',
    ['$scope', '$injector', '$timeout', '$routeParams', '$document',

        function presenterController($scope, $injector, $timeout, $routeParams, $document) {

            var $window = $injector.get('$window');

            $scope.ribbonService = $injector.get('ribbonService');
            $scope.infoService = $injector.get('infoService');

            $scope.selectedGroup = '';
            $scope.presenter_url = '';
            $scope.username = '';
            $scope.faculty_name = '';
            $scope.server_count = 1;

            var data = JSON.parse(atob(decodeURIComponent($routeParams.q)));
            $scope.selectedGroup = data.selectedGroup;
            $scope.presenter_url = data.presenter_url;
            $scope.username = data.username;
            $scope.faculty_name = data.faculty_name;
            $scope.isRemoteControl = data.isRemoteControl;
            $scope.isLoading = true;
            $scope.id = data.id;
            $scope.token = data.token;
            $scope.server_count = data.server_count;

            if (data.isSharingUrl) {
                $document[0].title = data.faculty_name + "'s screen";
                angular.element(window.document)[0].title = data.faculty_name + "'s screen";
            }

            $('#view_el').attr('src', $scope.presenter_url + "&name=" + data.faculty_name);

            $timeout(function () {
                $scope.isLoading = false;
                if (data.isSharingUrl) {
                    document.title = "Shared screen | Apporto";
                }
            }, 10000)

            $window.onpagehide = function (event) {
                $scope.onCloseWindow();
            }

            //When user2 close this tab, remove the share flag.
            $scope.onCloseWindow = function () {
                if (data.isSharingUrl) {
                    for(var i = 0; i < $scope.server_count; i++) {
                        var responseRequest = '?token=' + $scope.token + '&id=' + $scope.id;
                        if($scope.server_count > 1) {
                            navigator.sendBeacon('/srv-' + (i+1) +
                                                 '/hyperstream/api/session/ext/encryptedurl-jdbc/classroom/stopshare' +
                                                 responseRequest, false);
                        }
                        else {
                            navigator.sendBeacon('api/session/ext/encryptedurl-jdbc/classroom/stopshare' + responseRequest, false);
                        }
                    }
                }
                else {
                    var STOP_NEW_TAB = "Stop New Tab";
                    var param = '?text=&token=' + $scope.token + '&delay=0&level=notify&title=' + STOP_NEW_TAB;
                    navigator.sendBeacon('api/session/ext/encryptedurl-jdbc/notify/' + $scope.id + param, false);
                }

                if ($scope.oldOnUnload) {
                    return $scope.oldOnUnload();
                }
                else {
                    return '';
                }
            }

        }
    ]); // end presenter controller
