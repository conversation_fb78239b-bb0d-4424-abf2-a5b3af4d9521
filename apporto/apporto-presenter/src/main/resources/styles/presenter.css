/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.presenter-thumbnail {
    z-index: -1  !important;
    width: 50vw;
    height: 50vh;
}

.presenterUsername {
    background: #c6d6e8;
    height: 30px;
    width: 45vw;
    position: relative;
    top: -18.6em;
    left: -23em;
}

.presenter-title {
    display: flex;
    justify-content: center;
}

.presenter-left-username {
    min-width: 360px;
    display: inherit;
    text-shadow: 0 0 black;
    align-items: center;
    top: 0;
    overflow: overlay;
    text-align: center;
    position: absolute;
    cursor: pointer;
    justify-content: center;
    padding: 4px 16px;
    border-radius: 0px 0px 8px 8px;
    border-right: 1px solid #038C21;
    border-bottom: 1px solid #038C21;
    border-left: 1px solid #038C21;
    background: #EDFEEC;
    color: #053003;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.presenter-left-username.full {
    background-color: #ffce44;
    color: black;
    font-weight: 100;
    border-right: 1px solid #ffc422;
    border-bottom: 1px solid #ffc422;
    border-left: 1px solid #ffc422;
}

.presenter-dialog-outer {
    display: table;
    height: 100vh;
    width: 100vw;
    position: fixed;
    left: 0;
    top: 0;
    background: rgb(4 4 4 / 68%);
    margin-bottom: 0;
}

.presenter-display {
    position: relative;
    width: 480px;
    height: 360px;
    min-width: 480px;
    min-height: 360px;
}

.presenter-display .loader-wrapper {
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    z-index: 1000;
}

.presenter-close {
    color: #808080;
    cursor: pointer;
    float: right;
    font-weight: bold;
    line-height: 25px;
    position: absolute;
    right: 20px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    z-index: 99;
    margin: 0px;
    min-width: 25px;
    box-shadow: none;
    width: 59px;
    height: 24px;
    background: #D53737;
    color: #FFFFFF;
    border-radius: 8px !important;
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}

button.presenter-close:hover:enabled {
    background-color: #c36060;
    box-shadow: none;
}

button.presenter-close:active {
    background-color: transparent;
    box-shadow: none;
}

.presenter-minimize {
    cursor: pointer;
    float: right;
    font-weight: bold;
    line-height: 25px;
    position: absolute;
    right: 88px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    z-index: 99;
    margin: 0px;
    min-width: 25px;
    box-shadow: none;
    width: 100px;
    height: 24px;
    padding: 12px;
    border: 1px solid #1B2533;
    border-radius: 8px !important;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #FFFFFF;
    background: black;
}

button.presenter-minimize:hover:enabled {
    background-color: #1B2533;
    box-shadow: none;
}

button.presenter-minimize:active {
    background-color: transparent;
    box-shadow: none;
}

#presenter-content {
    position: absolute;
    display: flex;
    flex-direction: column;
    width: 100vw;
    min-height: calc(100vh - 55px);
}

div#presenterGroups {
    text-align: center;
}

.presenter-section {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

.center {
    text-align: center !important;
}

#presenter .presenter-boxes {
    z-index: 5;
    position: fixed;
    bottom: 45px !important;
    right: 110px;
    height: 40px !important;
}

#presenter .toggle-controlbox {
    background-color: #fafafd !important;
    border-radius: 0 !important;
    -moz-box-shadow: 0.1em 0.1em 0.2em rgba(0, 0, 0, 0.6);
    -webkit-box-shadow: 0.1em 0.1em 0.2em rgba(0, 0, 0, 0.6);
    box-shadow: 0.1em 0.1em 0.2em rgba(0, 0, 0, 0.6);
    cursor: pointer;
    text-align: center;
    color: #0a0a0a;
    float: right;
    height: 100%;
    margin: 0 var(--chat-gutter);
    padding: 1em;
    text-decoration: none;
    text-shadow: none;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 97px;
}

#presenter .toggle-controlbox span {
    color: #222124 !important;
}

#presenter .hidden {
    opacity: 0 !important;
    visibility: hidden !important;
}