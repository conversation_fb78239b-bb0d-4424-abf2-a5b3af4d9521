/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.presenter-dialog {
    width: 40vw;
    overflow: visible;
    border-radius: 6px;
    position: relative;
}

#dlg-presenter>* {
    margin: initial;
}

#dlg-presenter button {
  box-shadow: none;
}

/* The Close Button */
.presenter-dialog span.enable-close {
    color: #808080;
    cursor: pointer;
    float: right;
    font-weight: bold;
    font-size: 28px;
    line-height: 25px;
    position: absolute;
    right: 12px;
    text-align: center;
    text-decoration: none;
    top: 10px;
    width: 24px;
    z-index: 99;
}

.presenter-dialog .enable-close:hover .close-tooltip{
  visibility: visible;
  opacity: 1;
}

.presenter-dialog .enable-close:focus .close-tooltip{
  visibility: visible;
  opacity: 1;
}

.presenter-dialog #content {
    display: -ms-flexbox;
    -ms-flex-align: stretch;
    -ms-flex-direction: column;
    display: -moz-box;
    -moz-box-align: stretch;
    -moz-box-orient: vertical;
    display: -webkit-box;
    -webkit-box-align: stretch;
    -webkit-box-orient: vertical;
    display: -webkit-flex;
    -webkit-align-items: stretch;
    -webkit-flex-direction: column;
    display: flex;
    align-items: stretch;
    flex-direction: column;
}

.presenter-dialog #content li {
    align-items: center;
    display: flex;
}

.presenter-dialog #content span {
    font-size: 14px;
    font-weight: bold;
    width: 10em;
    text-align: left;
}

.presenter-dialog #content a {
    color: black;
    font-weight: initial;
    margin-left: 5px;
    margin-right: 20px;
    text-decoration: none;
    overflow: hidden;
}

.presenter-dialog #content button {
    background-color: rgba(54, 162, 235, 0.4);
    border: 0;

    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    border-radius: 12px;

    -moz-box-shadow: 1px 1px 3px #000;
    -webkit-box-shadow: 1px 1px 3px #000;
    box-shadow: 1px 1px 3px #000;

    color: black;
    margin-left: auto;
    text-shadow: none;
    width: 7vw;
}

.presenter-dialog #content button:hover {
    background-color: rgba(54, 162, 235, 0.2);
}

li {
    text-align: left;
}

.subtitle-presenter {
    margin-block-start: 1px;
    margin-top: 10px;
    font-weight: normal;
}

.title-presenter {
    padding-top: 22px;
    margin-block-end: 0px;
}

.list-presenter {
    list-style-type: none;
}

button.disabled {
    pointer-events: none;
    cursor: default;
    text-decoration: none;
}

.label-presenter {
    display: block;
    width: 95%;
    overflow: hidden;
    white-space: nowrap;
    text-align: left;
    padding-left: 5%;
}

div.footer {
    padding: 10px;
    padding-top: 20px;
}

.dialog .footer {
    text-align: right;
}

div.line {
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    margin: 0 1em;
}

.viewOnly.presenter {
    width: 8em;
    height: 2em;
    border-radius: 12px;
}

#selectedGroupPresenter {
    width: 240px;
    min-width: 240px;
    text-align-last: center;
    border-radius: 2em;
    height: 2.3em;
    outline: none;
}

a.btn-presenter-mandatory.ribbon-button.ng-scope[disabled='disabled'] {
    pointer-events: none;
    background-color: transparent;
    opacity: .1;
}

.disable-close {
    cursor: not-allowed!important;
}






.dialog-middle .custom-select, 
.dialog-middle  .custom-select-classroom {
    position: relative;
    width: 100%;
    max-width: 100%;
    font-size: 1.15rem;
    color: #000;
  }
  
.dialog-middle .select-button, 
.dialog-middle .select-button-classroom{
    width: 100%;
    font-size: 1.15rem;
    background-color: #fff;
    padding: 0.675em 1em;
    border: 1px solid #caced1;
    border-radius: 0.25rem;
    cursor: pointer;
  
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: black;
  }
  
.dialog-middle .selected-value {
    text-align: left;
  }
  
.dialog-middle .arrow {
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #000;
    transition: transform ease-in-out 0.3s;
  }
  
.dialog-middle .select-dropdown {
    position: absolute;
    list-style: none;
    width: 100%;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    background-color: #fff;
    border: 1px solid #caced1;
    border-radius: 4px;
    padding: 10px;
    margin-top: 0px;
    margin-left: 6px;
    max-height: 200px;
    overflow-y: auto;
    transition: 0.5s ease;
  
    /* transform: scaleY(0); */
    opacity: 0;
    visibility: hidden;
  }
  
.dialog-middle .select-dropdown:focus-within {
    box-shadow: 0 10px 25px rgba(94, 108, 233, 0.6);
  }
  
.dialog-middle .select-dropdown li {
    position: relative;
    cursor: pointer;
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  
.dialog-middle .select-dropdown li label {
    width: 100%;
    padding: 8px 10px;
    cursor: pointer;
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  
  .dialog-middle  .select-dropdown::-webkit-scrollbar {
    width: 7px;
  }
  .dialog-middle  .select-dropdown::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 25px;
  }
  
  .dialog-middle .select-dropdown::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 25px;
  }
  
  .dialog-middle  .select-dropdown li:hover,
  .dialog-middle .select-dropdown input:checked ~ label {
    background-color: #f2f2f2;
  }
  
  .dialog-middle  .select-dropdown input:focus ~ label {
    background-color: #dfdfdf;
  }
  
  .dialog-middle .select-dropdown input[type="radio"] {
    position: absolute;
    left: 0;
    opacity: 0;
  }
  
  /* interactivity */
  
  .dialog-middle .custom-select.active .arrow ,
  .dialog-middle .custom-select-classroom.active .arrow {
    transform: rotate(180deg);
  }
  
  .dialog-middle .custom-select.active .select-dropdown ,
  .dialog-middle .custom-select-classroom.active .select-dropdown {
    opacity: 1;
    visibility: visible;
    transform: scaleY(1);
    z-index: 1;
  }

  .dialog-middle .select-dropdown label:hover {
    background-color: #eff6ff !important;
}

#presenterCloseBtn:hover{
  background: #e0dada !important;
  cursor: pointer;
}

.presenterBtn{
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  height: 40px !important;
  padding: 12px;
  border-radius: 8px !important;
  cursor: pointer;
}

.presenterNotifiction{
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 24px !important;
  width: 558px !important;
  max-width: 558px !important;
}