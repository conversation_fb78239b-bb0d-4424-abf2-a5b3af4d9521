<div class="status-middle">
    <div class="notification error presenterNotifiction">
        <button style="position: absolute;" class="close" ng-click="closeDialog()" tabindex="0" aria-label="Close">&times;</button>
        <div class="title-bar" style="background-color: white; box-shadow: none; border: none; padding: 0px; padding-left: 4px; text-transform: none;">
            <div class="title">{{'PRESENTER.PRESENTER_TITLE' | translate}}</div>
        </div>

        <div class="body" style="padding: 0px; padding-left: 4px; margin: 0px; font-family: 'lato'; line-height: 20px;">
            <p style="margin: 0px 0px 0px 32px;" class="text">{{'PRESENTER.PRESENTER_MESSAGE' | translate}}</p>
        </div>

        <div class="buttons" style="text-align: left; margin: 0px 28px 18px 28px; display: flex;">
            <button ng-click="$root.btnInPageMode()" class="tClose presenterBtn" tabindex="0">{{'PRESENTER.IN_PAGE' | translate}}</button>
            <button ng-click="$root.btnNewTabMode()" class="tClose presenterBtn" tabindex="0">{{'PRESENTER.NEW_TAB' | translate}}</button>
        </div>
    </div>
</div>