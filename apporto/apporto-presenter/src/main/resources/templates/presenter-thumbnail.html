<div id="{{$index}}" class="presenter-display" style="position: relative; width: 888px; height: 568px; max-width: 90vw; padding: 8px 16px 8px 16px; background-color: black; display: flex; flex-direction: column-reverse; gap: 8px; border-radius: 8px;" ng-resize>
	<button class="presenter-minimize" ng-click="minViewDialog()" tabindex="0" aria-label="Minimize">Minimize</button>
	<button class="presenter-close" ng-click="closeViewDialog()" tabindex="0" aria-label="Close">Close</button>
	<div ng-show="presenterLoading" class="loader-wrapper" role="alert" aria-label="Loading">
		<div class="loader"></div>
	</div>
	<iframe id="view_el" class="cl-display" ng-if="ribbonService.presenterThumbnailVisible && !ribbonService.presenterMinimized"
	style="cursor: pointer;margin-top: .1vh;padding: 0; pointer-events: none; height: 100%; border: none;" title="" />
	<div class="presenter-title presenter-title-dragable" style="justify-content: flex-start;">
		<label class="presenter-left-username" style="background-color: black; position: static; color: white; border: 0px; background-color: #777777; border-radius: 8px; min-width: max-content;" data-username="{{username}}">You are viewing {{faculty_name}}'s screen</label>
	</div>
</div>