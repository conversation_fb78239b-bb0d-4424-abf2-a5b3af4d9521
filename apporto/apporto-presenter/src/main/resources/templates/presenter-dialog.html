<div class="dialog-middle">
    <div class="presenter-dialog dialog" id="dlg-presenter"
        style="width: 558px; min-width: 450px;min-height: 165px; position: relative; padding: 24px; margin-top: 0px; text-align: left;" 
        ng-style="{'height': !ribbonService.isPresenterEnabled ? '293px' : '165px'}"
        ng-click="closeDropDown()"
        >
        <button style="top: -14px; right: -14px;" ng-class="(loadingProfile) ? 'disable-close' : ''" class="enable-close presenter" ng-click="!loadingProfile && closeDialog($event)" tabindex="0" aria-labelledby="closeTooltip">&times;
            <div id="closeTooltip" class="dialog-tooltip close-tooltip" role="tooltip">Close</div>
        </button>
        <div style="display: flex; flex-direction: column; gap: 16px;">
            <h3 style="padding-top: 0px; padding-left: 5px; margin-top: 0px;" class="title-presenter">{{'PRESENTER.PRESENTER_TITLE' | translate}}</h3>
            <h4 class="subtitle-presenter" style="margin: 0px; text-align: left; padding-left: 5px;" ng-show="!ribbonService.isPresenterEnabled">{{'PRESENTER.PRESENTER_SUBTITLE' | translate}}</h4>
            <h4 style="margin: 0px !important; padding-left: 5px;" class="subtitle-presenter" style="margin-bottom: 35px; margin-top: 50px;" ng-show="ribbonService.isPresenterEnabled">{{'PRESENTER.PRESENTER_SUBTITLE_SHARED' | translate}}{{selectedGroup}}</h4>

            <div id="presenterGroups" ng-show="!ribbonService.isPresenterEnabled">
                <div style="text-align: left; padding-left: 5px;">{{'PRESENTER.GROUP' | translate}}</div>
            <div class="custom-select">
               <button
                 class="select-button"
                 role="combobox"
                 aria-labelledby="select button"
                 aria-haspopup="listbox"
                 aria-expanded="false"
                 aria-controls="select-dropdown"
               >
                 <span class="selected-value">{{selectedGroup}}</span>
                 <span class="arrow"></span>
               </button>
               <ul class="select-dropdown" role="listbox" id="select-dropdown">
                   <li ng-repeat="group in ribbonService.presenterGroups" role="option" ng-click="selectOption(group)">
                       <input type="radio" ng-attr-id="{{group}}" ng-attr-name="{{group}}" />
                       <label ng-style="{'background-color': group === selectedGroup ? '#d1ddec' : '#fff'}"
                         ><i></i>{{group}}</label
                       >
                     </li>
               </ul>
             </div>

             <br>
             <label class="label-share" style="display: flex; flex-direction: row; align-items: center; height: 20px; padding-left: 10px;">
                 <input ng-show="!ribbonService.isPresenterEnabled" type="checkbox" ng-click="toggleMicrophone()" tabindex="0" style="display: none;">
                 <div class="btn1-container">
                    <div class="btn-switch" ng-class="{'btn-switch--on': isTurnOnMicrophone }">
                        <div class="btn-switch-circle" ng-class="{'btn-switch-circle--on': isTurnOnMicrophone }"></div>
                    </div>
                </div>
                 <span>{{'PRESENTER.GROUPCALL_TITLE' | translate}}</span>
             </label>
             
            </div>
            <div class="footer" style="text-align: left !important; padding: 0px;">
                <button id="presenterCloseBtn" class="launch button" style="border-radius: 8px;  width: 67px; height: 40px; border: 1px solid #CED3D9; background: #ffffff; color: black;"
                ng-click="!loadingProfile && closeDialog($event)" tabindex="0">
                Close
            </button>
                <button class="launch button" style="border-radius: 8px;  width: 8em; height: 40px; cursor: pointer;"
                    ng-click="launchPresenter()" tabindex="0">
                    {{!ribbonService.isPresenterEnabled ? 'PRESENTER.SHARE_SCREEN' : 'PRESENTER.STOP_SCREEN' | translate}}
                </button>
            </div>
        </div>
    </div>
</div>