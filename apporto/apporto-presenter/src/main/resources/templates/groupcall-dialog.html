<div class="dialog-middle">
    <div class="presenter-dialog dialog" id="dlg-presenter"
        style="width: 500px;height: 190px;min-width: 500px;min-height: 190px;">
        <button class="close presenter" ng-click="closeDialog()" tabindex="0" aria-label="Close">&times;</button>
        <div>
            <h3 class="title-presenter">{{'PRESENTER.GROUPCALL_TITLE' | translate}}</h3>
            <h4 class="subtitle-presenter" style="margin-bottom: 0;">{{'PRESENTER.GROUPCALL_SUBTITLE' | translate}}</h4>

            <div id="presenterGroups">
                <select id="selectedGroupCall" ng-model="selectedGroup" style="width: 14vw;">
                    <option ng-repeat="group in ribbonService.presenterGroups" value="{{group}}">{{group}}</option>
                </select>
                <br>
            </div>
            <div class="footer">
                <button class="launch button" style="border-radius: 10px;  width: 8em;" ng-click="launchGroupcall()"
                    tabindex="0">{{'PRESENTER.GROUPCALL_ENABLE' | translate}}</button>
            </div>
        </div>
    </div>
</div>