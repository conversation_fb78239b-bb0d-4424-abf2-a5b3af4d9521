/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays presenter
 */
angular.module('presenterModule').directive('presenterDialog', [function presenterDirective() {

    return {
        restrict: 'E',
        scope: {},

        templateUrl: 'app/ext/presenter/templates/presenter-dialog.html',
        controller: ['$scope', '$injector', '$rootScope', '$timeout', '$location', function presenterDialogController($scope, $injector, $rootScope, $timeout, $location) {

            var $q = $injector.get('$q');
            var $http = $injector.get('$http');
            var ManagedClient = $injector.get('ManagedClient');
            var guacClientManager = $injector.get('guacClientManager');
            var tunnelService = $injector.get('tunnelService');
            var $routeParams = $injector.get('$routeParams');
            var $interval = $injector.get('$interval');
            var ClientIdentifier = $injector.get('ClientIdentifier');
            var authenticationService = $injector.get('authenticationService');
            var $window = $injector.get('$window');
            var ServiceParticipant = $injector.get('ServiceParticipant');
            var ServiceCall = $injector.get('ServiceCall');
            var ManagedShareLink = $injector.get('ManagedShareLink');
            var utils = $injector.get('utilityService');

            // When started, periodically query client until it is possible to retrieve sharing links.
            var queryClient;

            $scope.ribbonService = $injector.get('ribbonService');

            if ($routeParams.hasOwnProperty("key") || $location.path().indexOf("classroom") > -1) {
                return;
            }

            // This service is required by directive used in ribbon template html
            $scope.infoService = $injector.get('infoService');

            $scope.client = null;
            $scope.selectedGroup = null;
            $scope.appname = "";

            // Array for storing data about users retrieved from the server
            $scope.data = [];
            $scope.sentNotification = [];
            $scope.presenterInterval = null;
            $scope.isTurnOnMicrophone = false;
            $scope.kurento = null;

            var authRequests = []
            $scope.shareKey = "undefined";
            $scope.shareKeys = [];

            // to remove sharing profile  when close dialog
            $scope.enableRMProfile = false;
            $scope.loadingProfile = false;

            /**
             * Close the dialog.
             */
            $scope.closeDialog = function closeDialog(event) {
                event.stopPropagation();
                $scope.ribbonService.presenterDialogVisible = false;
                customSelect.classList.remove("active");
            };

            
            const customSelect = document.querySelector(".custom-select");
            const selectBtn = document.querySelector(".select-button");

            $scope.closeDropDown = function closeDropDown() {
                customSelect.classList.remove("active");
            };
            // add click event to select button
            selectBtn.addEventListener("click", (event) => {
                event.stopPropagation();
                customSelect.classList.toggle("active");
                });

            $scope.selectOption = function selectOption(val){
                customSelect.classList.remove("active");
                $scope.selectedGroup = val;
                $scope.ribbonService.selectOptionValue = val;
            }

            /**
             * launch the presenter display.
             */
            $scope.launchPresenter = function launchPresenter() {
                if ($scope.ribbonService.isPresenterEnabled) {
                    $scope.ribbonService.isPresenterEnabled = false;
                    $scope.ribbonService.presenterDialogVisible = false;
                    $scope.shareKey = $scope.shareLinkVO;
                    $timeout.cancel($scope.presenterInterval);

                    for (var i = 0; i < $scope.data.length; i++) {
                        $scope.stopPresenterNotification($scope.data[i]);
                    }

                    $scope.sentNotification = [];
                    angular.forEach($scope.shareKeys, function (profile) {
                        $rootScope.stopSharing(profile);
                    });
                    $scope.shareKeys = [];
                }
                else {
                    $scope.ribbonService.isPresenterEnabled = true;
                    $scope.ribbonService.presenterDialogVisible = false;
                    $scope.enableRMProfile = false;

                    if ($scope.isTurnOnMicrophone) {
                        registerGroupcall();
                    }
                }
            };

            $scope.ribbonService.stopSharing = function stopSharingFunction() {
                $scope.launchPresenter();
            }

            /**
             * Turn on Microphone
             */
            $scope.toggleMicrophone = function toggleMicrophone() {
                $scope.isTurnOnMicrophone = !$scope.isTurnOnMicrophone;
            }

            var registerGroupcall = function registerGroupcall() {
                if ($scope.ribbonService.kurentoHostName) {
                    var wsUriGroupCall = 'wss://' + $scope.ribbonService.kurentoHostName + ':4443/room';
                    var roomName = $scope.selectedGroup;
                    $scope.kurento = KurentoRoom(wsUriGroupCall, roomName, function (error, kurento) {
                        if (error) {
                            Rollbar.error("WebSocket connection failed with Kurento chat room for group call" + error);
                            return console.log(error);
                        }

                        var room = kurento.Room({
                            room: kurento.roomName,
                            user: $scope.ribbonService.userinfo.windows_username,
                            adu: $scope.ribbonService.userinfo.adu,
                            session: $routeParams.q,
                            uuid: $scope.ribbonService.uuid,
                            updateSpeakerInterval: $scope.ribbonService.updateSpeakerInterval,
                            thresholdSpeaker: $scope.ribbonService.thresholdSpeaker
                        });

                        var localStream = kurento.Stream(room, {
                            audio: true,
                            video: false,
                            data: false,
                            id: "localStream",
                            presenter: true,
                            faculty: true
                        });

                        localStream.addEventListener("access-accepted", function () {
                            room.addEventListener("room-connected", function (roomEvent) {
                                if (!$scope.ribbonService.isPresenterEnabled) {
                                    kurento.close();
                                    return;
                                }

                                var streams = roomEvent.streams;
                                localStream.publish();
                                $rootScope.groupcallStream = localStream.getWebRtcPeer();

                                for (var i = 0; i < streams.length; i++) {
                                    ServiceParticipant.addStream(streams[i]);
                                }
                            });

                            room.addEventListener("stream-published", function (streamEvent) {
                                localStream.getWebRtcPeer().videoEnabled = false;
                                if (ServiceCall.callState != 0) {
                                    localStream.getWebRtcPeer().audioEnabled = false;
                                }
                                ServiceParticipant.addLocalParticipant(localStream);
                            });

                            room.addEventListener("stream-added", function (streamEvent) {
                                ServiceParticipant.addStream(streamEvent.stream);
                            });

                            room.addEventListener("stream-removed", function (streamEvent) {
                                ServiceParticipant.removeParticipantByStream(streamEvent.stream);
                            });

                            room.addEventListener("stream-stopped-speaking", function (participantId) {
                                ServiceParticipant.streamStoppedSpeaking(participantId);
                            });

                            room.addEventListener("stream-speaking", function (participantId) {
                                ServiceParticipant.streamSpeaking(participantId);
                            });

                            room.addEventListener("update-main-speaker", function (participantId) {
                                ServiceParticipant.updateMainSpeaker(participantId);
                            });

                            room.addEventListener("error-room", function (error) {
                                Rollbar.error("WebSocket error-room with Kurento chat room for group call" + error);
                                console.error('error-room: ', error);
                                kurento.close(true);
                                $rootScope.groupcallStream = null;
                            });

                            room.addEventListener("error-media", function (msg) {
                                Rollbar.error("WebSocket error-media with Kurento chat room for group call" + msg);
                                console.error('error-media: ', msg.error);
                                kurento.close(true);
                                $rootScope.groupcallStream = null;
                            });

                            room.addEventListener("room-closed", function (msg) {
                                if (msg.room !== kurento.roomName) {
                                    console.error("Closed room name doesn't match this room's name",
                                        msg.room, kurento.roomName);
                                }
                                else {
                                    console.log('Room ' + msg.room + ' has been forcibly closed from server');
                                }
                                kurento.close(true);
                                $rootScope.groupcallStream = null;
                            });

                            room.addEventListener("lost-connection", function (msg) {
                                Rollbar.error("Websocket lost connection with Kurento chat room for group call" + msg);
                                kurento.close(true);
                                $rootScope.groupcallStream = null;
                            });

                            room.connect();
                        })

                        localStream.init();
                    });
                }
            }

            $scope.$watch('ribbonService.isPresenterEnabled', function (visible) {
                if (visible) {
                    if (!$scope.selectedGroup) {
                        $scope.selectedGroup = $scope.ribbonService.presenterGroups[0];
                        $scope.appname = $scope.ribbonService.licenses.appname;
                    }

                    $scope.shareKey = $scope.shareLinkVO;
                    $scope.retrieveAllSharedLinks();
                }
            })

            $scope.startPresenterNotification = function startPresenterNotification(info) {
                if (info.groups.indexOf($scope.selectedGroup) < 0) {
                    return;
                }

                var notify = new XMLHttpRequest();
                var text = JSON.stringify({
                    presenter_url: $scope.shareLinkVO,
                    selectedGroup: $scope.selectedGroup,
                    username: $scope.ribbonService.userinfo.windows_username,
                    faculty_name: $scope.ribbonService.userinfo.name,
                    isTurnOnMicrophone: $scope.isTurnOnMicrophone,
                    appname : $scope.appname
                });
                var PRESENTER_TITLE = "Presenter Mode";
                var param = '?text=' + encodeURIComponent(btoa(text)) + '&token=' +
                            info.token + '&delay=0&level=notify&title=' + PRESENTER_TITLE;

                notify.open('POST', info.location + 'api/session/ext/encryptedurl-jdbc/notify/' + info.id + param, false);
                console.log(notify);
                notify.send();
                $scope.sentNotification.push(info);
            }

            $scope.stopPresenterNotification = function stopPresenterNotification(info) {
                if (info.groups.indexOf($scope.selectedGroup) < 0) {
                    return;
                }

                var notify = new XMLHttpRequest();
                var text = JSON.stringify({
                    selectedGroup: $scope.selectedGroup,
                    username: $scope.ribbonService.userinfo.windows_username,
                    faculty_name: $scope.ribbonService.userinfo.name,
                    appname : $scope.appname
                });
                var STOP_PRESENTER_MODE = "Stop Presenter Mode";
                var param = '?text=' + encodeURIComponent(btoa(text)) + '&token=' +
                            info.token + '&delay=0&level=notify&title=' + STOP_PRESENTER_MODE;

                notify.open('POST', info.location + 'api/session/ext/encryptedurl-jdbc/notify/' + info.id + param, false);
                console.log(notify);
                notify.send();

                if ($scope.isTurnOnMicrophone) {
                    $scope.kurento.close();
                    $rootScope.groupcallStream = null;
                }
            }

            $scope.retrieveAllSharedLinks = function retrieveAllSharedLinks() {
                var promises = [];
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var datasource = encodeURIComponent(clientIdentifier.dataSource);

                if ($window.location.protocol == "http:") {
                    var httpParameters = {
                        token   : encodeURIComponent(authenticationService.getCurrentToken()),
                        id      : encodeURIComponent(clientIdentifier.id),
                        group   : $scope.selectedGroup,
                        appname : $scope.appname
                    };

                    var req = {
                        method: 'GET',
                        url: "api/session/ext/" + datasource + "/presenter/getConnections",
                        params: httpParameters
                    };

                    promises.push(
                        $http(req).then(function (response) {
                            var result = Object.keys(response.data).map(function (key) {
                                return [key, response.data[key]];
                            });
                            parseData(result);
                        })
                        .catch(function (response) {
                            console.debug("getConnections error ", response.message);
                        })
                    );
                }
                else {
                    for (var i = 0; i < authRequests.length; i++) {
                        var authToken = authRequests[i];

                        httpParameters = {
                            token   : authToken,
                            id      : encodeURIComponent(clientIdentifier.id),
                            group   : $scope.selectedGroup,
                            appname : $scope.appname
                        };

                        if (utils.countTokens(authRequests) == 1) {
                            req = {
                                method: 'GET',
                                url: "/hyperstream/api/session/ext/encryptedurl-jdbc/presenter/getConnections",
                                params: httpParameters
                            };
                        }
                        else {
                            req = {
                                method: 'GET',
                                url: "/srv-" + (i + 1) + "/hyperstream/api/session/ext/encryptedurl-jdbc/presenter/getConnections",
                                params: httpParameters
                            };
                        }

                        promises.push(
                            $http(req).then(function (response) {
                                var result = Object.keys(response.data).map(function (key) {
                                    return [key, response.data[key]];
                                });
                                parseData(result);
                            })
                            .catch(function (response) {
                                console.debug("getConnections error ", response.message);
                            })
                        );
                    }
                }

                $q.all(promises).then(function () {
                    $scope.presenterInterval = $timeout(function () {
                        if ($scope.ribbonService.isPresenterEnabled) {
                            $scope.retrieveAllSharedLinks();
                            addNewUsers();
                        }
                    }, 5000);
                })
            };

            var parseData = function parseData(filteredResults) {
                filteredResults.forEach(function (rData) {
                    if (!rData || rData.length == 0) {
                        return;
                    }

                    var vo_href;
                    var fc_href;
                    var sessionID = rData[0];
                    var profileData = rData[1];

                    if (profileData === undefined) {
                        return;
                    }

                    if (profileData.removed) {
                        var index = $scope.data.findIndex(function(user) {
                            return user.id === sessionID;
                        });

                        if (index > -1) {
                            $scope.data.splice(index, 1);
                        }

                        index = $scope.sentNotification.findIndex(function(user) {
                            return user.id === sessionID;
                        });

                        if (index > -1) {
                            $scope.sentNotification.splice(index, 1);
                        }

                        return;
                    }

                    var username = profileData.username;
                    var windows_name = profileData.windows_name;
                    var groups = profileData.groups;
                    var srv = profileData.server_id;
                    var VO = profileData.view_only_key;
                    var FC = profileData.full_controll_key;
                    var thumbnail = profileData.thumbnail;
                    var removed = profileData.removed;
                    var notificationName = '';

                    if (!VO) {
                        return;
                    }

                    var decodedThumbnail;
                    if (thumbnail != null) {
                        decodedThumbnail = atob(thumbnail);

                        if ($rootScope.usersData) {
                            for (var i = 0; i < $rootScope.usersData.length; i++) {
                                var id = Number($rootScope.usersData[i].id);
                                if (id == sessionID) {
                                    $rootScope.usersData[i].thumbnail = decodedThumbnail;
                                }
                            }
                        }
                    }

                    if (!$window.location.origin) {
                        $scope.linkOrigin = $window.location.protocol + '//' + $window.location.hostname +
                                           ($window.location.port ? (':' + $window.location.port) : '');
                    }
                    else {
                        $scope.linkOrigin = $window.location.origin;
                    }

                    // Build base link
                    if (!$window.location.pathname.includes('srv') && $window.location.protocol != "http:") {
                        $scope.link = $scope.linkOrigin + "/" + srv + $window.location.pathname + '#/';
                    }
                    else if ($window.location.protocol == "http:") {
                        $scope.link = $scope.linkOrigin + '/hyperstream/#/';
                    }
                    else {
                        // remove original server mark (/srv-x)
                        var pathname = $window.location.pathname.substring(6);
                        $scope.link = $scope.linkOrigin + "/" + srv + pathname + '#/';
                    }

                    if ($scope.ribbonService.location === '') {
                        $scope.ribbonService.location = $scope.link.split('#')[0];
                    }

                    if (VO != "") {
                        vo_href = $scope.link + '?key=' + VO + '&connection_type=presenter';
                    }
                    else {
                        vo_href = "";
                    }

                    if (FC != "") {
                        fc_href = $scope.link + '?key=' + FC + '&connection_type=presenter';
                    }
                    else {
                        fc_href = "";
                    }

                    var nnIndex = username.lastIndexOf('@');
                    if (nnIndex > -1) {
                        notificationName = username.substring(0, nnIndex);
                    }
                    else {
                        notificationName = username;
                    }

                    // Create info object from response for rendering html template
                    if ($window.location.protocol !== "http:") {
                        var index = srv.split("-")[1].trim() - 1;
                        var info = {
                            id: sessionID,
                            full: fc_href,
                            fc_key: FC,
                            view: vo_href,
                            vo_key: VO,
                            username: username,
                            windows_name: windows_name,
                            location: $scope.link.split('#')[0],
                            token: authRequests[index],
                            groups: groups,
                            waiting: false,
                            load: false,
                            thumbnail: decodedThumbnail,
                            raisedHand: false,
                            hoverTimer: null,
                            removed: removed,
                            permission: false,
                            notificationName: notificationName,
                            expanded: false,
                            ViewOnlyFrame: false,
                            fullControlLicence: false, //probabbly redudant with permission; earlier used to memorize licence duging one VC session
                            server_id: profileData.server_id

                        }
                    }
                    else {
                        info = {
                            id: sessionID,
                            full: fc_href,
                            fc_key: FC,
                            view: vo_href,
                            vo_key: VO,
                            username: username,
                            windows_name: windows_name,
                            location: $scope.link.split('#')[0],
                            token: authRequests[0],
                            groups: groups,
                            waiting: false,
                            load: false,
                            thumbnail: decodedThumbnail,
                            raisedHand: false,
                            hoverTimer: null,
                            removed: removed,
                            permission: false,
                            notificationName: notificationName,
                            expanded: false,
                            ViewOnlyFrame: false,
                            fullControlLicence: false,
                            server_id: profileData.server_id

                        }
                    }

                    $scope.data.push(info);

                    if ($scope.noActiveMsg && groups.includes($scope.selectedGroup)) {
                        $scope.noActiveMsg = false;
                    }
                })
            };

            var addNewUsers = function addNewUsers() {
                if ($scope.data && $scope.data.length > 0) {
                    var usersIds = [];
                    for (i = 0; i < $scope.sentNotification.length; i++) {
                        usersIds[i] = $scope.sentNotification[i].id;
                    }

                    var data = $scope.data.filter(function (el) {
                        if (!usersIds.includes(el.id)) {
                            return el;
                        }
                    });

                    for (var i = 0; i < data.length; i++) {
                        if ($scope.ribbonService.isPresenterEnabled) {
                            $scope.startPresenterNotification(data[i]);
                        }
                    }
                }
            }

            $rootScope.$watch("srv1", function () {
                if ($rootScope.srv1) {
                    authRequests.push($rootScope.srv1);
                }

                if ($rootScope.srv2) {
                    authRequests.push($rootScope.srv2);
                }

                if ($rootScope.srv3) {
                    authRequests.push($rootScope.srv3);
                }

                if ($rootScope.srv4) {
                    authRequests.push($rootScope.srv4);
                }
            })

            // Get client reference when route changes
            $scope.$on('$routeChangeSuccess', getClient);

            $rootScope.onCloseWindowsForPresenter = function onCloseWindowsForPresenter() {
                if ($scope.ribbonService.isPresenterEnabled) {
                    $scope.ribbonService.isPresenterEnabled = false;
                    for (var i = 0; i < $scope.data.length; i++) {
                        if ($scope.data[i].groups.indexOf($scope.selectedGroup) < 0) {
                            return;
                        }

                        var text = JSON.stringify({
                            selectedGroup: $scope.selectedGroup,
                            username: $scope.ribbonService.userinfo.windows_username,
                            faculty_name: $scope.ribbonService.userinfo.name,
                            appname : $scope.appname
                        });
                        var STOP_PRESENTER_MODE = "Stop Presenter Mode";
                        var param = '?text=' + encodeURIComponent(btoa(text)) + '&token=' +
                                        $scope.data[i].token + '&delay=0&level=notify&title=' + STOP_PRESENTER_MODE;

                        navigator.sendBeacon($scope.data[i].location + 'api/session/ext/encryptedurl-jdbc/notify/' +
                                                $scope.data[i].id + param, false);
                    }
                    $scope.sentNotification = [];

                    if ($scope.ribbonService.presenterVisible) {
                        for (i = 0; i < authRequests.length; i++) {
                            var params = "?token=" + authRequests[i];
                            var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                            params = params + "&id=" + clientIdentifier.id + "&group=" + $scope.selectedGroup + "&appname=" + $scope.appname;
                            if (utils.countTokens(authRequests) > 1) {
                                navigator.sendBeacon('/srv-' + (i + 1) +
                                                     '/hyperstream/api/session/ext/encryptedurl-jdbc/presenter/close' + params);
                            }
                            else {
                                navigator.sendBeacon('api/session/ext/encryptedurl-jdbc/presenter/close' + params);
                            }
                        }
                    }

                    if ($scope.kurento) {
                        $scope.kurento.close();
                        $scope.kurento = null;
                    }
                }
            }

            // When dialog is presented, get client reference (maybe it is not needed, TBC)
            $scope.$watch('ribbonService.presenterDialogVisible', function (visible) {
                if (visible && !$scope.ribbonService.isPresenterEnabled) {
                    $('.launch.button').attr('disabled', true);
                    $('input[type=checkbox]').attr('disabled', true);
                    getClient();
                    if ($scope.ribbonService.presenterGroups && $scope.ribbonService.presenterGroups.length !== 0) {
                        $scope.selectedGroup = $scope.ribbonService.presenterGroups[0];
                        $scope.ribbonService.selectOptionValue = $scope.ribbonService.presenterGroups[0];
                        $scope.appname = $scope.ribbonService.licenses.appname;
                    }

                    $scope.loadingProfile = true;
                    retrieveSharingProfiles($scope.client.tunnel.uuid);
                }
            });

            $scope.$watch('selectedGroup', function (group) {
                $rootScope.presenterSelectedGroup = group;
            })

            // If we experience angular bug #1213 (https://github.com/angular/angular.js/issues/1213)
            // try to get client after 10 seconds.
            queryClient = $interval(function () {
                if ($scope.client == null) {
                    getClient();
                }
                else {
                    $interval.cancel(queryClient);
                    queryClient = null;
                }
            }, 5 * 1000);

            function getClient() {
                if ($routeParams.id) {
                    var cl = guacClientManager.getManagedClient($routeParams.id, $routeParams.params);
                    if (cl == null || cl.tunnel.uuid == null) {
                        console.debug("Sharing: Client or tunnel not yet available.");
                        return;
                    }

                    $scope.client = cl;
                    console.debug("Sharing: current uuid: " + $scope.client.tunnel.uuid);

                    var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                    var datasource = clientIdentifier.dataSource;
                    if (datasource === 'encryptedurl-jdbc') {
                        $scope.ribbonService.sharingEnabled = true;
                    }
                }
            }
            var ERROR_SHARE_LINK = '--- Sharing is not possible at the moment, please reload the page ---';

            /**
             * The view only share link and name of the profile.
             *
             * @type String
             */
            var DEFAULT_VO_LINK = '--- Generating share link... ---';
            var PROFILE_VO = "share-VO";
            $scope.shareLinkVO = DEFAULT_VO_LINK;
            $scope.shortShareLinkVO = DEFAULT_VO_LINK;

            function retrieveSharingProfiles(uuid) {
                // Only pull sharing profiles if tunnel UUID is actually available
                if (!uuid || $routeParams.hasOwnProperty("key")) {
                    console.debug("Sharing: Cannot create profiles - uuid is undefined or shared session");
                    return;
                }

                $scope.shareLinkVO = DEFAULT_VO_LINK;
                $scope.shortShareLinkVO = DEFAULT_VO_LINK;

                // Pull sharing profiles for the current connection
                // Make a delay, to allow tunnel to form completely.
                let profileCreator = $interval(function () {
                    var sharingProfile = tunnelService.getSharingProfiles(uuid);
                    if (sharingProfile === undefined) {
                        console.debug("Sharing: Cannot create profiles - tunnel is not yet availabale. Will retry in 2s.");
                        return;
                    }

                    sharingProfile.then(async function sharingProfilesRetrieved(sharingProfiles) {
                        // create sharing profiles related to "share-VO"
                        for (var str in sharingProfiles) {
                            if (sharingProfiles[str].name == PROFILE_VO) {
                                var sharingCredentials = await ManagedClient.createShareLink($scope.client, sharingProfiles[str]);

                                $scope.client.shareLinks[sharingProfiles[str].identifier] =
                                    ManagedShareLink.getInstance(sharingProfiles[str], sharingCredentials);
                                console.debug("Sharing: Created link: " + sharingProfiles[str].name);
                            }
                        }

                        angular.forEach($scope.client.shareLinks, function (profile) {
                            if (profile.name == PROFILE_VO) {
                                $scope.shareKeys.push(profile.href);
                            }
                        });

                        createShareLinks();
                    })
                    .catch(function (err) {
                        console.error("Sharing: " + err);
                        $scope.shareLinkVO = ERROR_SHARE_LINK;
                    });

                    $interval.cancel(profileCreator);
                }, 2 * 1000);
            }

            function createShareLinks() {
                // Only process share links if they are available (skip setting initial value to null/nothing etc....)
                if (!$scope.client || !$scope.client.shareLinks || $scope.ribbonService.serverId == null)
                    return;

                $scope.enableRMProfile = true;
                $scope.loadingProfile = false;

                for (var id in $scope.client.shareLinks) {
                    var str = $scope.client.shareLinks[id].href.replace(
                        /hyperstream\/#/,
                        ($scope.ribbonService.serverId != "" ? $scope.ribbonService.serverId + "/" : "") +
                        "hyperstream/#");
                    str += "&connection_type=presenter"

                    if ($scope.client.shareLinks[id].name === PROFILE_VO) {
                        $scope.shareLinkVO = str;
                        $scope.shortShareLinkVO = str.substr(0, 70) + "...";
                        $scope.ribbonService.shareLinkVO = str;
                    }
                }
            }

            // When generated the share url, enable the button and checkbox
            $scope.$watch('shareLinkVO', function (share_url) {
                if (!!share_url && share_url !== DEFAULT_VO_LINK && share_url !== ERROR_SHARE_LINK) {
                    if ($('.launch.button').is(":disabled")) {
                        $('.launch.button').attr('disabled', false);
                    }

                    if ($('input[type=checkbox]').is(":disabled")) {
                        $('input[type=checkbox]').attr('disabled', false);
                    }
                }
            });
        }] // end presenter dialog controller
    };
}]);
