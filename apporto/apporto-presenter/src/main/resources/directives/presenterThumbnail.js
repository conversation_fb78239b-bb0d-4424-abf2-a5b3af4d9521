/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/**
 * Directive which displays loading circle
 */
angular.module('presenterModule').directive('presenterThumbnail', [function presenterThumbnail() {

    return {
        restrict: 'E',
        scope: {
        },
        templateUrl: 'app/ext/presenter/templates/presenter-thumbnail.html',
        controller: ['$scope', '$injector', '$rootScope', function presenterThumbnailController($scope, $injector, $rootScope) {
            // Required services
            var $window = $injector.get('$window');
            var $routeParams = $injector.get('$routeParams');
            var ServiceParticipant = $injector.get('ServiceParticipant');
            var ClientIdentifier        = $injector.get('ClientIdentifier');
            var authenticationService   = $injector.get('authenticationService');

            $scope.ribbonService = $injector.get('ribbonService');
            $scope.dragOptions = {
                container: 'presenter-thumbnail',
                handle: '.chat-head.controlbox-head',
            }
            $scope.selectedGroup = '';
            $scope.presenter_url = '';
            $scope.username = '';
            $scope.faculty_name = '';
            $scope.isTurnOnMicrophone = false;
            $scope.isMinimize = false;
            $scope.ribbonService.presenterMinimized = false;
            $scope.kurento = null;
            $rootScope.isPresenterEnabled = false;
            $scope.isNewPage = false;
            $scope.presenterLoading = false;

            $scope.$on('presenterMode:start', function (event, params) {
                if (params) {
                    var data = JSON.parse(atob(decodeURIComponent(params.text)));

                    $scope.selectedGroup = data.selectedGroup;
                    $scope.presenter_url = data.presenter_url;
                    $scope.username = data.username;
                    $scope.faculty_name = data.faculty_name;
                    $scope.isTurnOnMicrophone = data.isTurnOnMicrophone;
                }

                if (!$rootScope.isPresenterEnabled) {
                    $scope.ribbonService.isPresenter = true;
                    if (!$scope.isMinimize) {
                        if (!$rootScope.isKioskMode) {
                            $scope.ribbonService.presenterRequestDialogVisible = true;
                        }
                    }
                    else {
                        $scope.ribbonService.presenterThumbnailVisible = true;
                        $scope.ribbonService.isPresenter = true;
                        $scope.ribbonService.presenterRequestDialogVisible = false;
                        $rootScope.isPresenterEnabled = true;
                        $scope.ribbonService.presenterMinimized = false;
                        $scope.isMinimize = false;
                    }
                }
                else if (!$scope.isNewPage) {
                    $scope.ribbonService.presenterMinimized = !$scope.ribbonService.presenterMinimized;
                    $scope.isMinimize = !$scope.isMinimize;

                    if (!$scope.isMinimize) {
                        setTimeout(function () {
                            $('#view_el').attr('src', $rootScope.presenter_url);
                        }, 0);
                    }
                }
            })

            $scope.$on('presenterMode:stop', function () {
                $scope.ribbonService.presenterThumbnailVisible = false;
                $scope.ribbonService.isPresenter = false;
                $rootScope.isPresenterEnabled = false;
                $scope.isMinimize = false;
                $scope.ribbonService.presenterMinimized = false;
                $scope.ribbonService.presenterRequestDialogVisible = false;
                $scope.presenter_url = '';
                $('#view_el').attr('src', '');
            })

            $scope.$on('newtab:stop', function () {
                $rootScope.isPresenterEnabled = false;
                $scope.isNewPage = false;
            })

            $rootScope.$watch('isPresenterEnabled', function (visible) {
                if (visible) {
                    if ($scope.ribbonService.kurentoHostName && $scope.isTurnOnMicrophone && !$scope.isMinimize) {
                        var wsUriGroupCall = 'wss://' + $scope.ribbonService.kurentoHostName + ':4443/room';
                        var roomName = $scope.selectedGroup;
                        $scope.kurento = KurentoRoom(wsUriGroupCall, roomName, function (error, kurento) {
                            if (error) {
                                Rollbar.error("WebSocket connection failed with Kurento chat room for group call" + error);
                                return console.log(error);
                            }

                            var room = kurento.Room({
                                room: kurento.roomName,
                                user: $scope.ribbonService.userinfo.windows_username,
                                adu: $scope.ribbonService.userinfo.adu,
                                session: $routeParams.q,
                                uuid: $scope.ribbonService.uuid,
                                updateSpeakerInterval: $scope.ribbonService.updateSpeakerInterval,
                                thresholdSpeaker: $scope.ribbonService.thresholdSpeaker
                            });

                            var localStream = kurento.Stream(room, {
                                audio: false,
                                video: false,
                                data: false,
                                presenter: true,
                                id: "localStream"
                            });

                                room.addEventListener("room-connected", function (roomEvent) {
                                    if (!$rootScope.isPresenterEnabled) {
                                        kurento.close();
                                        return;
                                    }
                                    var streams = roomEvent.streams;
                                    localStream.publish();

                                    for (var i = 0; i < streams.length; i++) {
                                        ServiceParticipant.addStream(streams[i]);
                                    }
                                });

                                room.addEventListener("stream-published", function (streamEvent) {
                                    localStream.getWebRtcPeer().audioEnabled = false;
                                    localStream.getWebRtcPeer().videoEnabled = false;
                                    ServiceParticipant.addLocalParticipant(localStream);
                                });

                                room.addEventListener("stream-added", function (streamEvent) {
                                    ServiceParticipant.addStream(streamEvent.stream);
                                });

                                room.addEventListener("stream-removed", function (streamEvent) {
                                    ServiceParticipant.removeParticipantByStream(streamEvent.stream);
                                });

                                room.addEventListener("stream-stopped-speaking", function (participantId) {
                                    ServiceParticipant.streamStoppedSpeaking(participantId);
                                });

                                room.addEventListener("stream-speaking", function (participantId) {
                                    ServiceParticipant.streamSpeaking(participantId);
                                });

                                room.addEventListener("update-main-speaker", function (participantId) {
                                    ServiceParticipant.updateMainSpeaker(participantId);
                                });

                                room.addEventListener("error-room", function (error) {
                                    Rollbar.error("WebSocket error-room with Kurento chat room for group call" + error);
                                    console.error('error-room: ', error);
                                    kurento.close(true);
                                });

                                room.addEventListener("error-media", function (msg) {
                                    Rollbar.error("WebSocket error-media with Kurento chat room for group call" + msg);
                                    console.error('error-media: ', msg.error);
                                    kurento.close(true);
                                });

                                room.addEventListener("room-closed", function (msg) {
                                    if (msg.room !== kurento.roomName) {
                                        console.error("Closed room name doesn't match this room's name",
                                            msg.room, kurento.roomName);
                                    } else {
                                        kurento.close(true);
                                        console.log('Room ' + msg.room + ' has been forcibly closed from server');
                                    }
                                });

                                room.addEventListener("lost-connection", function (msg) {
                                    Rollbar.error("Websocket lost connection with Kurento chat room for group call" + msg);
                                    kurento.close(true);
                                });

                                room.connect();
                        });
                    }
                }
                else {
                    if ($scope.kurento && $scope.isTurnOnMicrophone && !$scope.isMinimize) {
                        $scope.kurento.close();
                        $scope.kurento = null;
                    }
                }
            })

            $scope.closeViewDialog = function closeViewDialog() {
                $scope.ribbonService.presenterThumbnailVisible = false;
                $rootScope.isPresenterEnabled = false;
                $scope.isMinimize = false;
                $scope.ribbonService.presenterMinimized = false;
            }

            $scope.minViewDialog = function minViewDialog() {
                $scope.ribbonService.presenterThumbnailVisible = false;
                $scope.isMinimize = true;
                $scope.ribbonService.presenterMinimized = true;
            }

            //When user clicks the "In Page" button
            $rootScope.btnInPageMode = function btnInPageMode() {
                $scope.ribbonService.presenterThumbnailVisible = true;
                $scope.ribbonService.isPresenter = true;

                $('.presenter-left-username').text("You are viewing " + $scope.faculty_name + "'s screen");
                $scope.ribbonService.presenterRequestDialogVisible = false;
                $rootScope.isPresenterEnabled = true;
                $scope.isNewPage = false;
                $rootScope.presenter_url = $scope.presenter_url + "&name=" + $scope.faculty_name;

                setTimeout(function () {
                    $scope.presenterLoading = true;
                    $('#view_el').attr('src', $rootScope.presenter_url);

                    setTimeout(function () {
                        $scope.presenterLoading = false;
                    }, 5000);
                }, 0);
            }

            //When user clicks the "New Tab" button
            $rootScope.btnNewTabMode = function btnNewTabMode() {
                $scope.ribbonService.isPresenter = true;
                var clientIdentifier = ClientIdentifier.fromString($routeParams.id);
                var q = {
                    selectedGroup: $scope.selectedGroup,
                    presenter_url: $scope.presenter_url,
                    username: $scope.username,
                    faculty_name: $scope.faculty_name,
                    id: encodeURIComponent(clientIdentifier.id),
                    token: encodeURIComponent(authenticationService.getCurrentToken())
                }
                $('#view_el').attr('src', $scope.presenter_url);
                $('.presenter-left-username').text("You are viewing " + $scope.faculty_name + "'s screen");
                $window.open($window.location.origin + $window.location.pathname + '#/classroom?q=' + encodeURIComponent(btoa(JSON.stringify(q))), '_blank');
                $scope.ribbonService.presenterRequestDialogVisible = false;
                $rootScope.isPresenterEnabled = true;
                $scope.isNewPage = true;
            }
        }] // end presenter thumbnail controller
    };
}]);

