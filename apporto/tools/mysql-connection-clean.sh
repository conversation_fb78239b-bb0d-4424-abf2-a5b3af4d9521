#!/bin/sh
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

mysql -uroot -p guacamole_db -e \
    "delete from guacamole_connection where connection_id not in (select distinct connection_name from guacamole_connection_history where end_date IS NULL and start_date >= date_sub(curdate(), interval 16 day)); \
     delete from guacamole_connection_history where end_date < date_sub(curdate(), interval 14 day) or (start_date < date_sub(curdate(), interval 16 day) and end_date IS NULL);"

