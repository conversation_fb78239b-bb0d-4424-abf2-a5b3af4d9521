#!/bin/sh
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
#
set -x

GUACAMOLE_HOME="${GUACAMOLE_HOME:-$HOME/Development/apporto/guacamole-client-apporto}"
GUACAMOLE_APP_NAME="hyperstream"

# Copy wasm to tomcat8 app
sudo cp $GUACAMOLE_HOME/apporto/apporto-h264/src/main/resources/components/avc.wasm /var/lib/tomcat8/webapps/guacamole
sudo chown tomcat8:tomcat8 /var/lib/tomcat8/webapps/$GUACAMOLE_APP_NAME/avc.wasm
sudo chmod 640 /var/lib/tomcat8/webapps/$GUACAMOLE_APP_NAME/avc.wasm

sudo systemctl restart tomcat8

