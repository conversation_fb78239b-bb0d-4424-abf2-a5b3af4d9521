#!/bin/bash
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
#

CP="sudo cp"
CHOWN="sudo chown"
CHMOD="sudo chmod"

while [[ $# -gt 0 ]]; do
    key="$1"

    case $key in
        --docker-deploy)
        DOCKER_DEPLOY="true"
        CP="cp"
        CHOWN="chown"
        CHMOD="chmod"
        shift # past argument
        ;;
        *)    # unknown option
        shift # past argument
        ;;
    esac
done

set -x
set -e

GUACAMOLE_HOME="${GUACAMOLE_HOME:-$HOME/Development/apporto/guacamole-client-apporto}"
GUACAMOLE_APP_NAME="hyperstream"

if [ -z $DOCKER_DEPLOY ]; then
    # Traditional deployment.  Cleanup previoud deployment.

    # Stop tomcat service
    sudo systemctl stop tomcat9

    # Copy main application to Tomcat webapps directory
    sudo rm -rf ~tomcat/webapps/$GUACAMOLE_APP_NAME*
    sudo cp $GUACAMOLE_HOME/guacamole/target/guacamole-1.1.0.war ~tomcat/webapps/$GUACAMOLE_APP_NAME.war

    # Clear extensions directory
    sudo rm ~tomcat/.guacamole/extensions/*
else
    mkdir -p ~tomcat/.guacamole/bin/
    $CHOWN tomcat:tomcat ~tomcat/.guacamole/bin/

    mkdir -p ~tomcat/.guacamole/extensions/
    $CHOWN tomcat:tomcat ~tomcat/.guacamole/extensions/

    mkdir -p ~tomcat/webapps/$GUACAMOLE_APP_NAME
    cd ~tomcat/webapps/$GUACAMOLE_APP_NAME && jar xvf $GUACAMOLE_HOME/guacamole/target/guacamole-*.war
    $CHOWN -Rh tomcat:tomcat ~tomcat/webapps/
fi

# Copy extensions to Guacamole extensions directory
$CP $GUACAMOLE_HOME/apporto/apporto-auth-mysql/target/apporto-auth-mysql-1.13.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-stats-collect/target/apporto-stats-1.4.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-ribbon/target/apporto-ribbon-1.14.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-highlight/target/apporto-highlight-1.1.0.jar ~tomcat/.guacamole/extensions/
#$CP $GUACAMOLE_HOME/apporto/apporto-onexit/target/apporto-onexit-1.4.1.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-core/target/apporto-core-1.2.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-h264/target/apporto-h264-1.1.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-tunnel/target/apporto-tunnel-0.1.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-classroom/target/apporto-classroom-1.1.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-presenter/target/apporto-presenter-1.0.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/apporto/apporto-auth-ott/target/apporto-auth-1-ott-1.1.0.jar ~tomcat/.guacamole/extensions/
$CP $GUACAMOLE_HOME/extensions/guacamole-display-statistics/target/guacamole-display-statistics-1.1.0.jar ~tomcat/.guacamole/extensions/

# Copy scripts to Guacamole bin directory
$CP -rp $GUACAMOLE_HOME/apporto/bin/* ~tomcat/.guacamole/bin
$CP $GUACAMOLE_HOME/apporto/encryptedurl/src/example/php/security.php ~tomcat/.guacamole/bin
$CP $GUACAMOLE_HOME/apporto/scripts/* ~tomcat/.guacamole/bin
$CP $GUACAMOLE_HOME/apporto/tools/logback.xml ~tomcat/.guacamole/

if [ -z $DOCKER_DEPLOY ]; then
    # Traditional deployment.  Startup tomcat to deploy war.

    sudo systemctl start tomcat9

    # Wait until Guacamole directory is created
    until [ -d ~tomcat/webapps/$GUACAMOLE_APP_NAME ]
    do
        echo "#"
        sleep 1
    done
fi

# Copy wasm to tomcat app
$CP $GUACAMOLE_HOME/apporto/apporto-h264/src/main/resources/components/avc.wasm ~tomcat/webapps/$GUACAMOLE_APP_NAME
$CHOWN tomcat:tomcat ~tomcat/webapps/$GUACAMOLE_APP_NAME/avc.wasm
$CHMOD 640 ~tomcat/webapps/$GUACAMOLE_APP_NAME/avc.wasm

$CP $GUACAMOLE_HOME/apporto/apporto-h264/src/main/resources/components/aac.wasm ~tomcat/webapps/$GUACAMOLE_APP_NAME
$CHOWN tomcat:tomcat ~tomcat/webapps/$GUACAMOLE_APP_NAME/aac.wasm
$CHMOD 640 ~tomcat/webapps/$GUACAMOLE_APP_NAME/aac.wasm


