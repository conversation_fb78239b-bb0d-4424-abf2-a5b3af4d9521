#!/bin/bash

set -e

MAX_THREADS="${MAX_THREADS:-200}"
perl -i.orig -npe "s/MAX_THREADS/$MAX_THREADS/;" $CATALINA_BASE/conf/server.xml

mkdir -p /etc/guacamole
if [ -f "/srv/hyperstream/guacamole.properties" ]; then
    SERVER_ID="$((${HOSTNAME##*-} + 1))"
    perl -npe "s|SERVER_ID|$SERVER_ID|g; s|CATALINA_BASE|$CATALINA_BASE|g;" /srv/hyperstream/guacamole.properties > /etc/guacamole/guacamole.properties
    perl -npe "s|SERVER_ID|$SERVER_ID|g; s|CATALINA_BASE|$CATALINA_BASE|g;" /srv/hyperstream/secretkey.properties > /etc/guacamole/secretkey.properties
fi

GUAC_LOG_LEVEL=${GUAC_LOG_LEVEL:-INFO}
perl -i.orig -npe "s/<root level=\"\w+\">/<root level=\"$GUAC_LOG_LEVEL\">/" $CATALINA_BASE/.guacamole/logback.xml

export JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true -Dlogback.configurationFile=$CATALINA_BASE/.guacamole/logback.xml"

# Insert windows domain root certificate into Java keystore
WIN_DOMAIN_ROOT_CERT=${WIN_DOMAIN_ROOT_CERT:-/srv/win-root-cert/cert.pem}

# If WIN_DOMAIN_ROOT_CERT starts with "FILE:", remove prefix. "FILE:" prefix is needed for guacd.
if [[ "$WIN_DOMAIN_ROOT_CERT" == FILE:* ]]; then
    WIN_DOMAIN_ROOT_CERT="${WIN_DOMAIN_ROOT_CERT#FILE:}"
fi

# Check if the file exists
if [[ ! -f "$WIN_DOMAIN_ROOT_CERT" ]]; then
    echo "Warning: Certificate file '$WIN_DOMAIN_ROOT_CERT' does not exists, skipping import."
else
    # Only if file exists, import certificate
    keytool -importcert -trustcacerts -cacerts -storepass changeit -file "$WIN_DOMAIN_ROOT_CERT" -alias win-root-cert -noprompt
    echo "Certificate successfully imported."
fi


# powershell env vars required on 22.04
export OMI_SKIP_CA_CHECK=1
export OMI_SKIP_CN_CHECK=1

exec $CATALINA_BASE/bin/catalina.sh run

