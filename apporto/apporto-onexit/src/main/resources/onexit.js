/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

// URL query parameters

function getQueryVariable(name) {
    var query = window.location.search.substring(1);
    var vars = query.split('&');
    for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split('=');
        if (decodeURIComponent(pair[0]) == name)
            return decodeURIComponent(pair[1]);
    }
	 return "";
}

// Check connection state

function hasBeenConnected() {
	var e = $("guac-viewport");
	if (e) {
		var scp = angular.element(e).scope();
		if (scp && scp.client && scp.client.clientState.connectionState != "IDLE")
			return true;
	}
	return false;
}

// OnBeforeUnload handler (modern browsers will ignore the provided string)
/*
window.onbeforeunload = function(e) {
	if (hasBeenConnected()) {
		var message = "If you whish to keep your files please download them before closing";
		// e.returnValue = message;
		return message;
	}
};
*/
