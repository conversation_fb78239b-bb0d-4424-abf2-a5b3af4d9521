! xscreensaver ---------------------------------------------------------------

!font settings
xscreensaver.Dialog.headingFont:        -*-dina-bold-r-*-*-10-*-*-*-*-*-*-*
xscreensaver.Dialog.bodyFont:           -*-dina-medium-r-*-*-10-*-*-*-*-*-*-*
xscreensaver.Dialog.labelFont:          -*-dina-medium-r-*-*-10-*-*-*-*-*-*-*
xscreensaver.Dialog.unameFont:          -*-dina-medium-r-*-*-10-*-*-*-*-*-*-*
xscreensaver.Dialog.buttonFont:         -*-dina-bold-r-*-*-10-*-*-*-*-*-*-*
xscreensaver.Dialog.dateFont:           -*-dina-medium-r-*-*-10-*-*-*-*-*-*-*
xscreensaver.passwd.passwdFont:         -*-dina-bold-r-*-*-10-*-*-*-*-*-*-*
!general dialog box (affects main hostname, username, password text)
xscreensaver.Dialog.foreground:         #EDEDED
xscreensaver.Dialog.background:         #202020
xscreensaver.Dialog.topShadowColor:     #202024
xscreensaver.Dialog.bottomShadowColor:  #202024
xscreensaver.Dialog.Button.foreground:  #EDEDFF
xscreensaver.Dialog.Button.background:  #444
!username/password input box and date text colour
xscreensaver.Dialog.text.foreground:    #EDEDFF
xscreensaver.Dialog.text.background:    #444
xscreensaver.Dialog.internalBorderWidth:24
xscreensaver.Dialog.borderWidth:        0
xscreensaver.Dialog.shadowThickness:    2
!timeout bar (background is actually determined by Dialog.text.background)
xscreensaver.passwd.thermometer.foreground:  #A9B7C4
xscreensaver.passwd.thermometer.background:  #202020
xscreensaver.passwd.thermometer.width:       8
!datestamp format--see the strftime(3) manual page for details
xscreensaver.dateFormat:    %I:%M%P %a %b %d, %Y

