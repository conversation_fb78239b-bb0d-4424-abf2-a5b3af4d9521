/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Test;

public class GuacamoleCommonUtilityTest {

    @Test
    public void testRemoveHostName() {
        // Test for FQDN with hostname
        assertEquals("asd.example.com", GuacamoleCommonUtility.removeHostName("qwe.asd.example.com"));

        // Test for base domain with subdomain
        assertEquals("example.com", GuacamoleCommonUtility.removeHostName("asd.example.com"));

        // Test for base domain without hostname
        assertEquals("example.com", GuacamoleCommonUtility.removeHostName("example.com"));

        // Test for hostname without base domain
        assertEquals("localhost", GuacamoleCommonUtility.removeHostName("localhost"));

        // Test for empty string
        assertNull(GuacamoleCommonUtility.removeHostName(""));

        // Test for null input
        assertNull(GuacamoleCommonUtility.removeHostName(null));
    }
}
