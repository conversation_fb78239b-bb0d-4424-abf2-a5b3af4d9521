/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto.metrics;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.exporter.otlp.trace.OtlpGrpcSpanExporter;
import io.opentelemetry.sdk.OpenTelemetrySdk;
import io.opentelemetry.sdk.resources.Resource;
import io.opentelemetry.sdk.trace.SdkTracerProvider;
import io.opentelemetry.sdk.trace.export.BatchSpanProcessor;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;
import io.opentelemetry.proto.collector.trace.v1.TraceServiceGrpc;
import io.opentelemetry.proto.collector.trace.v1.TraceServiceGrpc.TraceServiceBlockingStub;

public class GuacamoleOpenTelemetry {

    private static final String OTEL_SERVICE_NAME = "experienceservice";

    private static final String OTEL_SPAN_NAME = "Metrics";

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(GuacamoleOpenTelemetry.class);

    /**
     * The value of URL parameter "id".
     */
    private String connId;

    /**
     * The value of URL parameter "cloud_username".
     */
    private String cloudUserName;

    /**
     * The value of URL parameter "connection_type".
     */
    private String connectionType;

    /**
     * The endpoint of OpenTelemetry Collector.
     */
    private String otelCollectorEndpoint;

    /**
     * The interval to send metrics to OpenTelemetry Collector.
     */
    private Integer otelMetricsInterval;

    /**
     * The GuacamoleConfiguration associated with this connection.
     */
    private GuacamoleConfiguration configuration;

    /**
     * Load the User Properties.
     */
    private Map<String, Object> userProperties;

    /**
     * Login-Time metrics.
     */
    private String metricsLoginTime;

    /**
     * RTT metrics.
     */
    private String metricsRTT;

    /**
     * Bandwidth metrics.
     */
    private String metricsBandwidth;

    /**
     * Frame rate metrics.
     */
    private String metricsFrameRate;

    /**
     * Rendering (decoding & rendering) Time metrics.
     */
    private String metricsRenderingTime;

    /**
     * Latency metrics.
     */
    private String metricsLatency;

    /**
     * Max of user input delay about a specified session.
     */
    private String metricsSessionDelay;

    /**
     * Service to send metrics to Opentelemetry Collector.
     */
    private ScheduledExecutorService executorService = null;

    /**
     * Service to get metrics regarding the User Input Delay.
     */
    private ScheduledExecutorService userInputDelayService = null;
    private ScheduledExecutorService tokenApportoServiceHandler = null;

    private String tokenApportoService = "";

    /**
     * The instructions related to metrics using OpenTelemetry
     */
    private static final String OTEL_COLLECTOR_ENDPOINT_PROPERTY = "otel_collector_endpoint";
    private static final String OTEL_METRICS_INTERVAL_PROPERTY = "otel_metrics_interval";
    private static final String OTEL_METRICS_CONNECTION_ID = "connection_id";
    private static final String OTEL_METRICS_USER_NAME = "username";
    private static final String OTEL_METRICS_RTT = "RTT";
    private static final String OTEL_METRICS_RENDERING_TIME = "rendering_time";
    private static final String OTEL_METRICS_MAX_SESSION = "max_user_input_delay_session";

    public static final String OTEL_METRICS_INSTRUCTION_OPCODE = "metrics-otel";

    public static final String OTEL_METRICS_INSTRUCTION_BANDWIDTH = "bandwidth";
    public static final String OTEL_METRICS_INSTRUCTION_LATENCY = "latency";
    public static final String OTEL_METRICS_INSTRUCTION_FRAME = "frame";
    public static final String OTEL_METRICS_INSTRUCTION_FRAME_RATE = "frame_rate";
    public static final String OTEL_METRICS_INSTRUCTION_FRAME_RENDERING = "frame_rendering";
    public static final String OTEL_METRICS_INSTRUCTION_LOGIN = "login_time";
    public static final String OTEL_METRICS_INSTRUCTION_RTT = "rtt";

    public GuacamoleOpenTelemetry(String connId, String connectionType, String cloudUserName,
                                  GuacamoleConfiguration configuration, Map<String, Object> userProperties) {

        this.connId = connId;
        this.connectionType = connectionType;
        this.cloudUserName = cloudUserName;
        this.otelCollectorEndpoint = (String) userProperties.get(OTEL_COLLECTOR_ENDPOINT_PROPERTY);
        this.otelMetricsInterval = Integer.parseInt((String) userProperties.get(OTEL_METRICS_INTERVAL_PROPERTY));
        this.configuration = configuration;
        this.userProperties = userProperties;
    }

    private void getUserInputDelay() {
        GuacamoleMetrics metricsInstance = new GuacamoleMetrics(configuration, userProperties);

        userInputDelayService = Executors.newSingleThreadScheduledExecutor();
        Runnable task = () -> {
            logger.info("[{}:{}:{}] tokenApportoService: {}",
                        connId, connectionType, cloudUserName, tokenApportoService);
            if (!tokenApportoService.isEmpty()) {
                String response = metricsInstance.getUserInputDelay(tokenApportoService);
                if (!response.isEmpty()) {
                    try {
                        // Parse response using org.json.JSONObject
                        JSONObject json = new JSONObject(response);
                        JSONObject jsonData = json.optJSONObject("data");
                        
                        boolean status = json.optBoolean("success", false);

                        if (status && jsonData != null) {
                            metricsSessionDelay = jsonData.optString("sessionDelay", "");
                        } else {
                            logger.info("[{}:{}:{}] Error message from ApprotService: {}",
                                        connId, connectionType, cloudUserName, json.optString("message", "Unknown error"));
                        }
                    } catch (Exception e) {
                        logger.error("[{}:{}:{}] Exception log in parsing of UserInputDelay: ", 
                                     connId, connectionType, cloudUserName, e);
                    }
                }
            }
        };
        
        tokenApportoServiceHandler = Executors.newSingleThreadScheduledExecutor();
        Runnable tokenTask = new Runnable() {
            @Override
            public void run() {
                tokenApportoService = metricsInstance.getTokenForApportoService();
                if (!tokenApportoService.isEmpty()) {
                    tokenApportoServiceHandler.shutdown();
                    userInputDelayService.scheduleAtFixedRate(task, otelMetricsInterval, otelMetricsInterval, TimeUnit.SECONDS);
                }
            }
        };
 
        tokenApportoServiceHandler.scheduleAtFixedRate(tokenTask, otelMetricsInterval, otelMetricsInterval, TimeUnit.SECONDS);
    }

    public void setMetricsLoginTime(String metricsLoginTime) {
        this.metricsLoginTime = metricsLoginTime;
    }

    public void setMetricsRTT(String metricsRTT) {
        this.metricsRTT = metricsRTT;
    }

    public void setMetricsBandwidth(String metricsBandwidth) {
        this.metricsBandwidth = metricsBandwidth;
    }

    public void setMetricsFrameRate(String metricsFrameRate) {
        this.metricsFrameRate = metricsFrameRate;
    }

    public void setMetricsRenderingTime(String metricsRenderingTime) {
        this.metricsRenderingTime = metricsRenderingTime;
    }

    public void setMetricsLatency(String metricsLatency) {
        this.metricsLatency = metricsLatency;
    }

    public boolean isEndpointAvailable(String endpoint) {
        URL url;
        String protocol = "", host = "";
        int port = -1;
        ManagedChannel channel = null;

        try {
            url = new URL(endpoint);
            protocol = url.getProtocol();
            host = url.getHost();
            port = url.getPort();
            
            if (protocol.equals("http")) {
                channel = ManagedChannelBuilder.forAddress(host, port)
                .usePlaintext()
                .build();
            }
            else if(protocol.equals("https")) {
                channel = ManagedChannelBuilder.forAddress(host, port).build();
            }

            TraceServiceBlockingStub stub = TraceServiceGrpc.newBlockingStub(channel);
            stub.export(ExportTraceServiceRequest.newBuilder().build());
            logger.info("[{}:{}:{}] OpenTelemetry Collector Endpoint is available. (protocol={} host={} port={})",
                        connId, connectionType, cloudUserName, protocol, host, port);

            return true;
        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] OpenTelemetry Collector Endpoint URL({}) parsing error({}).",
                         connId, connectionType, cloudUserName, endpoint, e.getMessage());
            e.printStackTrace();

            return false;
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] OpenTelemetry Collector Endpoint is unavailable. (protocol={} host={} port={}) reason: {}",
                         connId, connectionType, cloudUserName, protocol, host, port, e.getMessage());

            return false;
        }
        finally {
            channel.shutdown();
        }
    }

    public void sendMetrics() {
        if (executorService != null) {
            executorService.shutdown();
        }

        if (userInputDelayService != null) {
            userInputDelayService.shutdown();
        }

        if (tokenApportoServiceHandler != null) {
            tokenApportoServiceHandler.shutdown();
        }
        getUserInputDelay();
        
        Resource serviceNameResource = Resource.getDefault().merge(Resource.create(Attributes.builder()
        .put("service.name", OTEL_SERVICE_NAME)
        .build()));

        OpenTelemetry openTelemetry = OpenTelemetrySdk.builder()
        .setTracerProvider(SdkTracerProvider.builder()
        .setResource(serviceNameResource)
        .addSpanProcessor(BatchSpanProcessor.builder(OtlpGrpcSpanExporter.builder()
            .setEndpoint(otelCollectorEndpoint)
            .build())
        .build())
        .build())
        .build();

        // Get a Tracer
        Tracer tracer = openTelemetry.getTracer(getClass().getName());

        executorService = Executors.newSingleThreadScheduledExecutor();
        Runnable task = () -> {
            // Create a span
            Span span = tracer.spanBuilder(OTEL_SPAN_NAME).startSpan();
            try {

                span.setAttribute(OTEL_METRICS_CONNECTION_ID, connId);
                span.setAttribute(OTEL_METRICS_USER_NAME, cloudUserName);
                span.setAttribute(OTEL_METRICS_INSTRUCTION_LOGIN, metricsLoginTime);
                span.setAttribute(OTEL_METRICS_RTT, metricsRTT);
                span.setAttribute(OTEL_METRICS_INSTRUCTION_BANDWIDTH, metricsBandwidth);
                span.setAttribute(OTEL_METRICS_INSTRUCTION_LATENCY, metricsLatency);
                span.setAttribute(OTEL_METRICS_INSTRUCTION_FRAME_RATE, metricsFrameRate);
                span.setAttribute(OTEL_METRICS_RENDERING_TIME, metricsRenderingTime);
                span.setAttribute(OTEL_METRICS_MAX_SESSION, metricsSessionDelay);
                span.setStatus(StatusCode.OK, "Success");

            }
            catch (Exception e) {
                span.setStatus(StatusCode.ERROR, "Error Message: " + e.getMessage());
                logger.error("[{}:{}:{}] Failed to create span due to {}", connId, connectionType, cloudUserName, e.getMessage());
            }
            finally {
                // Close the span
                span.end();
            }
        };

        executorService.scheduleAtFixedRate(task, otelMetricsInterval, otelMetricsInterval, TimeUnit.SECONDS);
    }

    public void onExitTask() {
        if (executorService != null) {
            executorService.shutdown();
        }

        if (userInputDelayService != null) {
            userInputDelayService.shutdown();
        }

        if (tokenApportoServiceHandler != null) {
            tokenApportoServiceHandler.shutdown();
        }
    }
    
}
