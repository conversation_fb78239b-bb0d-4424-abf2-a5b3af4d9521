/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto.metrics;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Map;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.codec.binary.Base64;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class GuacamoleMetrics {
    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(GuacamoleMetrics.class);

    /* https://{hostname}:{port}/api/Auth/login */
    private static final String login_apporto_service_template = "https://%s:%s/api/Auth/login";
    /* https://{hostname}:{port}/api/metrics/getMetrics */
    private static final String input_delay_apporto_service_template = "https://%s:%s/api/metrics/getMetrics";

    private static final String WIN_SERVER_NAME_PARM = "win-server-name";
    private static final String WIN_SESSION_ID_PARM = "win-session-id";
    private static final String ID_PARM = "id";
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String CONNECTION_TYPE_PARM = "connection_type";
    private static final String APPORTO_SERVICE_SECRET_KEY = "apporto-service-secret-key";
    private static final String APPORTO_SERVICE_USER_NAME = "apporto-service-username";
    private static final String APPORTO_SERVICE_PASSWORD = "apporto-service-password";
    private static final String APPORTO_SERVICE_PORT = "apporto-service-port";
    private static final String LOCAL_DNS_ZONE = "local-dns-zone";
    private static final String APPOS_LICENCE_PARAM = "AppOS";
    // Default domain if not supplied in subdomain parameter
    private static String DEFAULT_DOMAIN = "apporto.com";
    // Default port to use apporto service
    private static final String DEFAULT_PORT = "50018";
    private static String PORT;
    private static String SECRET_KEY;
    // Default user name to use apporto service
    private static String DEFAULT_USER_NAME;
    // Default password to use apporto service
    private static String DEFAULT_PASSWORD;
    // Domain to use apporto service
    private static String DOMAIN;
    private static final String USER_AGENT = "Mozilla/5.0";
    private static final String CONTENT_TYPE = "application/json; charset=utf-8";
    private static final String USERNAME_PARM = "username";

    /**
     * The GuacamoleConfiguration associated with this connection.
     */
    private GuacamoleConfiguration configuration;
    String conn_id = "";
    String cloud_user = "";
    String conn_type = "";

    public GuacamoleMetrics(GuacamoleConfiguration configuration, Map<String, Object> userProperties) {
        this.configuration = configuration;
        conn_id = configuration.getParameter(ID_PARM);
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        SECRET_KEY = (String) userProperties.get(APPORTO_SERVICE_SECRET_KEY);
        DEFAULT_USER_NAME = (String) userProperties.get(APPORTO_SERVICE_USER_NAME);
        DEFAULT_PASSWORD = (String) userProperties.get(APPORTO_SERVICE_PASSWORD);
        PORT = (String) userProperties.get(APPORTO_SERVICE_PORT);

        if (PORT == null || PORT.isEmpty()) {
            logger.warn("apporto-service-port is empty. The default port is {}.", DEFAULT_PORT);
            PORT = DEFAULT_PORT;
        }

        DOMAIN = (String) userProperties.get(LOCAL_DNS_ZONE);

        if (DOMAIN == null || DOMAIN.isEmpty()) {
            logger.warn("local-dns-zone is empty. The default domain is {}.", DEFAULT_DOMAIN);
            DOMAIN = DEFAULT_DOMAIN;
        }
    }

    /**
     * Gets token for ApportoService.
     *
     * @return - token
     */
    public String getTokenForApportoService () {
        // Get the connection info
        Map<String, String> params = configuration.getParameters();

        String response = "";
        String winServerName = params.get(WIN_SERVER_NAME_PARM);
        String appOS = params.get(APPOS_LICENCE_PARAM);

        if (winServerName == null) {
            if (appOS == "Windows")
                logger.warn("[{}:{}:{}] Windows server name not specified, cannot get Apporto service Api token.",
                            conn_id, conn_type, cloud_user);
            return response;
        }

        logger.info("[{}:{}:{}] getTokenForApportoService winservername {}", conn_id, conn_type, cloud_user, winServerName);

        if (!winServerName.contains(".")) { // Not a FQDN
            winServerName = winServerName + "." + DOMAIN;
        }

        String token = "";
        try {
            String login_url_str = String.format(login_apporto_service_template, winServerName, PORT);
            URL login_url = new URL(login_url_str);
            String loginResponse = loginUsingApportoService(conn_id, conn_type, cloud_user, winServerName, login_url);
            JSONObject json = new JSONObject(loginResponse);
            token = json.getString("data");

            logger.info("[{}:{}:{}] Apporto service Api token for user input delay= {}", conn_id, conn_type, cloud_user, token);
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return token;
    }

    /**
     * Gets the metrics regarding User Input Delay using ApportoService.
     *
     * @param token - bearer token for header
     *
     * @return - the User Input Delay metrics that the windows server measured
     */
    public String getUserInputDelay(String token) {
        // Get the connection info
        Map<String, String> params = configuration.getParameters();

        String response = "";
        String winServerName = params.get(WIN_SERVER_NAME_PARM);

        if (winServerName == null) {
            logger.warn("[{}:{}:{}] Windows server name not specified, cannot get user input delay.",
                        conn_id, conn_type, cloud_user);
            return response;
        }

        logger.info("[{}:{}:{}] getUserInputDelay winservername {}", conn_id, conn_type, cloud_user, winServerName);

        if (!winServerName.contains(".")) { // Not a FQDN
            winServerName = winServerName + "." + DOMAIN;
        }

        String input_delay_url_str = String.format(input_delay_apporto_service_template, winServerName, PORT);

        try {
            URL input_delay_url = new URL(input_delay_url_str);

            if (params.get(WIN_SESSION_ID_PARM) != null) {
                logger.info("[{}:{}:{}] Windows Session ID for user input delay - {}", conn_id, conn_type, cloud_user, params.get(WIN_SESSION_ID_PARM));
                response = getInputDelayUsingApportoService(conn_id, conn_type, cloud_user, winServerName,
                                    input_delay_url, token, "SessionID", params.get(WIN_SESSION_ID_PARM));
            }
            else {
                logger.info("[{}:{}:{}] Windows Session ID for user input delay is null, trying with username - {}",
                            conn_id, conn_type, cloud_user, params.get(USERNAME_PARM));
                response = getInputDelayUsingApportoService(conn_id, conn_type, cloud_user, winServerName,
                                    input_delay_url, token, "UserName", params.get(USERNAME_PARM));
            }
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response;
    }

    /**
     * Login using ApportoService.
     *
     * @param conn_id - the connection ID
     * @param cloud_user - the value of "cloud_username" URL parameter
     * @param winServerName - the windows server name
     * @param url - the endpoint for login
     */
    @SuppressWarnings("unchecked")
    private String loginUsingApportoService(String conn_id, String conn_type, String cloud_user,
                                String winServerName, URL url) {
        StringBuffer response = new StringBuffer();

        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Add body with raw data
            JSONObject httpBody = new JSONObject();
            httpBody.put("Username", DEFAULT_USER_NAME);
            httpBody.put("Password", DEFAULT_PASSWORD);
            String message = httpBody.toString();

            // Calculate the signature of the http body
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Connect to WebAPI
            logger.info("[{}:{}:{}] Connecting to {} for user input delay ...", conn_id, conn_type, cloud_user, winServerName);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", CONTENT_TYPE);
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());
            os.close();

            logger.info("[{}:{}:{}] Send 'POST' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            int responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            logger.info("[{}:{}:{}] Connected to {}(response code={}). ApportoService login api url for user input delay = {}",
                        conn_id, conn_type, cloud_user, winServerName, responseCode, url);
            logger.info("[{}:{}:{}] ResponseMsg = {}", conn_id, conn_type, cloud_user, response.toString());
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to ApportoService login for user input delay, URL = {} Reason={}",
                        conn_id, conn_type, cloud_user, url.toString(), e.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response.toString();
    }

    /**
     * Get User Input Dealy using ApportoService.
     *
     * @param conn_id - the connection ID
     * @param cloud_user - the value of "cloud_username" URL parameter
     * @param winServerName - the windows server name
     * @param url - the endpoint to kill session
     * @param token - bearer token for header
     * @param type - the name of user logged in server
     * @param serverInfo - this indicates the session id if the type param is "SessionID",
     *               this indicates the user name if the type param is "UserName"
     *
     * @return Response - Object containing Max, Average and Max for session Delays.
     */
    @SuppressWarnings("unchecked")
    private String getInputDelayUsingApportoService(String conn_id, String conn_type, String cloud_user,
                             String winServerName, URL url, String token, String type, String serverInfo) {
        StringBuffer response = new StringBuffer();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;

        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Add body with raw data
            JSONObject httpBody = new JSONObject();
            httpBody.put(type, serverInfo);
            String message = httpBody.toString();

            // Calculate the signature of the http body
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Connect to WebAPI
            logger.info("[{}:{}:{}] Connecting to {} for user input delay...", conn_id, conn_type, cloud_user, winServerName);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", CONTENT_TYPE);
            con.setRequestProperty("Authorization", "Bearer " + token);
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());
            os.close();

            logger.info("[{}:{}:{}] Send 'POST' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            logger.info("[{}:{}:{}] Connected to {}(response code={}). user input dealy session api url = {}",
                        conn_id, conn_type, cloud_user, winServerName, responseCode, url);
            logger.info("[{}:{}:{}] ResponseMsg = {}", conn_id, conn_type, cloud_user, response.toString());
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to ApportoService for user input delay, URL = {}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response.toString();
    }
}
