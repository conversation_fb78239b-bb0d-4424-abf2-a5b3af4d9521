/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.metrics;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.GuacamoleCommonUtility;

public class GuacamoleAppstore {

    private static final Logger logger = LoggerFactory.getLogger(GuacamoleAppstore.class);

    private static final String SUBDOMAIN_PARM = "subdomain";
    private static final String USER_ID_PARM = "user-id";
    private static final String ID_PARM = "id";
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String CONNECTION_TYPE_PARM = "connection_type";

    /**
     * The default of the session type
     */
    private static final String DEFAULT_CONNECTION_TYPE = "primary";

    // Default domain if not supplied in subdomain parameter
    private static final String DEFAULT_DOMAIN = "apporto.com";

    /**
     * Service to send latency data to NG session.
     */
    private ScheduledExecutorService sendLatencyDataService;

    /**
     * Latency Data
     */
    private volatile String latencyData;

    public GuacamoleAppstore() {
    }

    public void setLatencyData(String latencyData) {
        this.latencyData = latencyData;
    }

    /* https://{subdomain}/measure/{Bsession_id}/{cloud_username}/{user_id}/{compression} */
    private static final String send_compression_url_template = "https://%s/measure/%s/%s/%s/%s";
    /* https://{subdomain}/measure/{Bsession_id}/{cloud_username}/{user_id}/{compression}/{latency} */
    private static final String send_latency_data_url_template = "https://%s/measure/%s/%s/%s/%s/%s";

    /**
     * Send the video compression to NG session.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param enableH264 - Video compression status
     */
    public static void sendCompression(GuacamoleConfiguration configuration, Boolean enableH264) {

        // Get the connection info
        Map<String, String> params = configuration.getParameters();

        String conn_id = params.get(ID_PARM);
        String cloud_user = params.get(CLOUD_USERNAME_PARM);
        String conn_type = params.get(CONNECTION_TYPE_PARM);
        String subdomain = params.get(SUBDOMAIN_PARM);
        String user_id = params.get(USER_ID_PARM);

        if (!validateParams(conn_id, conn_type, cloud_user, user_id, subdomain)) {
            return;
        }

        String compression = Boolean.TRUE.equals(enableH264) ? "Enabled" : "Disabled";
        String fqdn = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Connect to WebAPI
        String url_str = String.format(send_compression_url_template, fqdn, conn_id, cloud_user, user_id, compression);

        try {
            URL url = new URL(url_str);
            GuacamoleCommonUtility.sendStatus(url, conn_id, conn_type, cloud_user);
        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

    }

    /**
     * Send the latency data to NG session.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param enableH264 - Video compression status
     */
    public void sendLatencyData(GuacamoleConfiguration configuration, Boolean enableH264) {

        String compression = Boolean.TRUE.equals(enableH264) ? "Enabled" : "Disabled";

        // Get the connection info
        Map<String, String> params = configuration.getParameters();

        String conn_id = params.get(ID_PARM);
        String cloud_user = params.get(CLOUD_USERNAME_PARM);
        String conn_type = params.get(CONNECTION_TYPE_PARM);
        String subdomain = params.get(SUBDOMAIN_PARM);
        String user_id = params.get(USER_ID_PARM);

        if (!validateParams(conn_id, conn_type, cloud_user, user_id, subdomain)) {
            return;
        }

        String fqdn = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);
        Runnable task = () -> {
            if (latencyData != null && DEFAULT_CONNECTION_TYPE.equals(conn_type)) {
                // Connect to WebAPI
                String url_str = String.format(send_latency_data_url_template, fqdn, conn_id, cloud_user, user_id, compression, latencyData);

                try {
                    URL url = new URL(url_str);
                    GuacamoleCommonUtility.sendStatus(url, conn_id, conn_type, cloud_user);
                }
                catch (MalformedURLException e) {
                    logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
                }
            }
        };


        if (sendLatencyDataService == null || sendLatencyDataService.isShutdown()) {
            sendLatencyDataService = Executors.newSingleThreadScheduledExecutor();
            sendLatencyDataService.scheduleAtFixedRate(task, 0, 1, TimeUnit.MINUTES);
        }
    }

    private static boolean validateParams(String conn_id, String conn_type, String cloud_user, String user_id, String subdomain) {
        if (subdomain == null) {
            logger.warn("[{}:{}:{}] NG session's subdomain not specified, cannot send status info.", conn_id, conn_type, cloud_user);
            return false;
        }
        if (cloud_user == null) {
            logger.warn("[{}:{}:{}] NG session's cloud username not specified, cannot send status info.", conn_id, conn_type, cloud_user);
            return false;
        }
        if (user_id == null) {
            logger.warn("[{}:{}:{}] NG session's user_id not specified, cannot send status info.", conn_id, conn_type, cloud_user);
            return false;
        }
        return true;
    }

    public void onExitTask() {
        if (sendLatencyDataService != null) {
            sendLatencyDataService.shutdown();
        }
    }

}
