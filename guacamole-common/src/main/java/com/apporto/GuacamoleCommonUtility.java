/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class GuacamoleCommonUtility {

    private static final Logger logger = LoggerFactory.getLogger(GuacamoleCommonUtility.class);

    // Next two params are not used by Guacamole, needed only to be returned to Drupal
    private static final String USER_AGENT = "Mozilla/5.0";

    // Value of the "Http-Api-Key" parameter
    protected static String HTTP_API_KEY;

    private GuacamoleCommonUtility() {}

    public static String getSubDomain(String subdomain, String basedomain) {
        if (subdomain != null && basedomain != null && subdomain.contains(basedomain)) {
            subdomain = subdomain.replace("." + basedomain, "");
        }

        return subdomain;
    }

    public static String getFQDN(String subdomain, String basedomain) {
        if (subdomain != null && basedomain != null && !subdomain.contains(basedomain)) { // Not a FQDN
            return subdomain + "." + basedomain;
        }

        return subdomain;
    }

    /**
     * Removes host name from FQDN.
     * 
     * @param fqdn
     * @return fqdn without host name
     * 
     * Examples:
     * System.out.println(removeHostName("qwe.asd.example.com")); // Output: asd.example.com
     * System.out.println(removeHostName("asd.example.com"));     // Output: example.com
     * System.out.println(removeHostName("example.com"));         // Output: example.com
     * System.out.println(removeHostName("localhost"));           // Output: localhost
     * System.out.println(removeHostName(null));                  // Output: null
     * System.out.println(removeHostName(""));                    // Output: null
     */
    public static String removeHostName(String fqdn) {
        if (fqdn == null || fqdn.isEmpty()) {
            return null;
        }

        int firstDotIndex = fqdn.indexOf('.');
        if (firstDotIndex == -1 || fqdn.split("\\.").length == 2) {
            return fqdn; // If there are no dot or it is in format of a base domain, (e.g. example.com), return complete input
        }

        // Return everything after the first dot
        return fqdn.substring(firstDotIndex + 1);
    }

    /**
     * Sends connection status to Appstore.
     *
     * @param url - the url where status is to be sent
     * @param conn_id - the connection id
     * @param conn_type - the connection type
     * @param cloud_user - the cloud user name associated to the connection
     */
    public static String sendStatus(URL url, String conn_id, String conn_type, String cloud_user) {

        StringBuffer response = new StringBuffer();

        try {
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            if (HTTP_API_KEY != null && !HTTP_API_KEY.isEmpty()) {
                con.setRequestProperty("Http-Api-Key", HTTP_API_KEY);
            }

            // Optional default is GET
            con.setRequestMethod("GET");
            // Fix for AP-9650
            con.setConnectTimeout(5000);
            con.setReadTimeout(20000);

            // Add request header
            con.setRequestProperty("User-Agent", USER_AGENT);

            logger.info("[{}:{}:{}] Send 'GET' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            int responseCode = con.getResponseCode();
            logger.info("[{}:{}:{}] Sent 'GET' request to URL : \"{}\" Response Code : \"{}\"",
                        conn_id, conn_type, cloud_user, url, responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            if (!response.toString().isEmpty()) {
                logger.info("[{}:{}:{}] Response Result : \"{}\".", conn_id, conn_type, cloud_user,
                            response.toString());
            }
        }
        catch (SocketTimeoutException e) {
            logger.warn("[{}:{}:{}] SocketTimeoutException when connecting to Drupal, URL={}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to Drupal, URL={}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response.toString();
    }
}
