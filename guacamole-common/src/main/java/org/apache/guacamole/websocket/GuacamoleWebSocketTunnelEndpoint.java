
/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.websocket;

import java.io.IOException;
import java.util.List;
import java.util.ArrayList;
import javax.websocket.CloseReason;
import javax.websocket.CloseReason.CloseCode;
import javax.websocket.Endpoint;
import javax.websocket.EndpointConfig;
import javax.websocket.MessageHandler;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.io.GuacamoleReader;
import org.apache.guacamole.io.GuacamoleWriter;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.GuacamoleClientException;
import org.apache.guacamole.GuacamoleServerException;
import org.apache.guacamole.GuacamoleConnectionClosedException;
import org.apache.guacamole.protocol.FilteredGuacamoleWriter;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.protocol.GuacamoleParser;
import org.apache.guacamole.protocol.GuacamoleFilter;
import org.apache.guacamole.protocol.GuacamoleInstruction;
import org.apache.guacamole.protocol.GuacamoleStatus;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.metrics.GuacamoleAppstore;
import com.apporto.metrics.GuacamoleOpenTelemetry;

/**
 * A WebSocket implementation of GuacamoleTunnel functionality, compatible with
 * the Guacamole.WebSocketTunnel object included with the JavaScript API.
 * Messages sent/received are simply chunks of the Guacamole protocol
 * instruction stream.
 */
public abstract class GuacamoleWebSocketTunnelEndpoint extends Endpoint {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(GuacamoleWebSocketTunnelEndpoint.class);

    /**
     * The default, minimum buffer size for instructions.
     */
    private static final int BUFFER_SIZE = 8192;

    /**
     * The opcode of the instruction used to indicate a connection stability
     * test ping request or response. Note that this instruction is
     * encapsulated within an internal tunnel instruction (with the opcode
     * being the empty string), thus this will actually be the value of the
     * first element of the received instruction.
     */
    private static final String PING_OPCODE = "ping";

    /**
     * The instructions related to the H264
     */
    private static final String H264_OPCODE = "h264";
    private static final String BLOB_OPCODE = "blob";
    private static final String END_OPCODE = "end";
    private static final String SIZE_OPCODE = "size";
    private static final String MONITOR_SIZE_OPCODE = "monitor_size";
    private static final String SYNC_OPCODE = "sync";

    /**
     * The instructions related to the Clipboard
     */
    private static final String CLIP_OPCODE = "clipboard";
    private static final String CLIP_BLOB_OPCODE = "clipblob";
    private static final String CLIP_END_OPCODE = "clipend";

    /**
     * The index of H264 stream
     */
    private static final String H264_STREAM_INDEX = "1";

    /**
     * Endpoint string to point OpenTelemetryCollector.
     */
    private static final String OTEL_COLLECTOR_ENDPOINT_PROPERTY = "otel_collector_endpoint";

     /**
     * Interval to send metrics to OpenTelemetry Collector.
     */
    private static final String OTEL_METRICS_INTERVAL_PROPERTY = "otel_metrics_interval";

    /**
     * URL parameters
     */
    private static final String H264_LICENCE_PARAM = "enable-h264";

    /**
     * The underlying GuacamoleTunnel. WebSocket reads/writes will be handled
     * as reads/writes to this tunnel. This value may be null if no connection
     * has been established.
     */
    private GuacamoleTunnel tunnel;

    /**
     * The timeout value for checking the last time the wrote a message to
     * the Guacamole server.
     */
    private static final long WRITE_TIMEOUT = 30000; // 30 seconds

    /**
     * Parser for reading instructions prior to writing, such that they can be
     * passed on to the filter.
     */
    private final GuacamoleParser parser = new GuacamoleParser();

    /**
     * The last time that wrote a message to the Guacamole server.
     */
    private long lastWriteTime = System.currentTimeMillis();

    /**
     * The value of URL parameter "id".
     */
    protected String connId;

    /**
     * The value of URL parameter "cloud_username".
     */
    protected String cloudUserName;

    /**
     * The value of URL parameter "connection_type".
     */
    protected String connectionType;

    /**
     * The value of URL parameter "enable-h264".
     */
    protected Boolean enableH264;

    /**
     * The GuacamoleConfiguration associated with this connection.
     */
    protected GuacamoleConfiguration configuration;

    /**
     * The last time that sent an "nop" message to the browser via the websocket tunnel.
     */
    private long lastSendTime = System.currentTimeMillis();

    /**
     * Instance that integrates with OpenTelemetry to send metrics.
     */
    private GuacamoleOpenTelemetry guacOpenTelemetry = null;

    /**
     * Instance to send latency data.
     */
    private GuacamoleAppstore guacAppstore = null;

    /**
     * The timeout value for checking the last time the sent an instruction to
     * the browser via the websocket tunnel.
     * This value is used for preventing the disconnect of the h264 tunnel
     * when any instruction isn't sent in 15 seconds.
     * The unit of this value is seconds.
     */
    private static final long SEND_TIMEOUT = 5000; // 5 seconds

    /**
     * The "nop" instruction used as a heartbeat when there is no any h264
     * related instruction.
     */
    private static final GuacamoleInstruction NOP_INSTRUCTION
                                             = new GuacamoleInstruction(
                                                   "nop"
                                               );

    /**
     * Remote (client) side of this connection. This value will always be
     * non-null if tunnel is non-null.
     */
    private RemoteEndpoint.Basic remote;

    /**
     * Sends the numeric Guacamole Status Code and Web Socket
     * code and closes the connection.
     *
     * @param session
     *     The outbound WebSocket connection to close.
     *
     * @param guacamoleStatusCode
     *     The numeric Guacamole status to send.
     *
     * @param webSocketCode
     *     The numeric WebSocket status to send.
     */
    private void closeConnection(Session session, int guacamoleStatusCode,
            int webSocketCode) {

        logger.info("[{}:{}:{}] Close connection. (guacamoleStatusCode={}, webSocketCode={})",
                    connId, connectionType, cloudUserName, guacamoleStatusCode, webSocketCode);

        try {
            CloseCode code = CloseReason.CloseCodes.getCloseCode(webSocketCode);
            String message = Integer.toString(guacamoleStatusCode);
            session.close(new CloseReason(code, message));
        }
        catch (IOException e) {
            logger.error("[{}:{}:{}] Unable to close WebSocket connection.",
                         connId, connectionType, cloudUserName, e);
        }

    }

    /**
     * Sends the given Guacamole Status and closes the given
     * connection.
     *
     * @param session
     *     The outbound WebSocket connection to close.
     *
     * @param guacStatus
     *     The status to use for the connection.
     */
    private void closeConnection(Session session, GuacamoleStatus guacStatus) {

        closeConnection(session, guacStatus.getGuacamoleStatusCode(),
                guacStatus.getWebSocketCode());

    }

    /**
     * Sends a Guacamole instruction along the outbound WebSocket connection to
     * the connected Guacamole client. If an instruction is already in the
     * process of being sent by another thread, this function will block until
     * in-progress instructions are complete.
     *
     * @param instruction
     *     The instruction to send.
     *
     * @throws IOException
     *     If an I/O error occurs preventing the given instruction from being
     *     sent.
     */
    private void sendInstruction(String instruction)
            throws IOException {

        // NOTE: Synchronization on the non-final remote field here is
        // intentional. The remote (the outbound websocket connection) is only
        // sensitive to simultaneous attempts to send messages with respect to
        // itself. If the remote changes, then the outbound websocket
        // connection has changed, and synchronization need only be performed
        // in context of the new remote.
        synchronized (remote) {
            remote.sendText(instruction);
        }

    }

    /**
     * Sends a Guacamole instruction along the outbound WebSocket connection to
     * the connected Guacamole client. If an instruction is already in the
     * process of being sent by another thread, this function will block until
     * in-progress instructions are complete.
     *
     * @param instruction
     *     The instruction to send.
     *
     * @throws IOException
     *     If an I/O error occurs preventing the given instruction from being
     *     sent.
     */
    private void sendInstruction(GuacamoleInstruction instruction)
            throws IOException {

        sendInstruction(instruction.toString());

    }

    /**
     * Returns a new tunnel for the given session. How this tunnel is created
     * or retrieved is implementation-dependent.
     *
     * @param session The session associated with the active WebSocket
     *                connection.
     * @param config Configuration information associated with the instance of
     *               the endpoint created for handling this single connection.
     * @return A connected tunnel, or null if no such tunnel exists.
     * @throws GuacamoleException If an error occurs while retrieving the
     *                            tunnel, or if access to the tunnel is denied.
     */
    protected abstract GuacamoleTunnel createTunnel(Session session, EndpointConfig config)
            throws GuacamoleException;

    /**
     * Get the connection info needed to print the log.
     *
     * @param config Configuration information associated with the instance of
     *               the endpoint created for handling this single connection.
     * @return true if it successfully gets the session info, false if not.
     */
    protected abstract boolean getConnectionInfo(EndpointConfig config);

    @Override
    @OnOpen
    public void onOpen(final Session session, EndpointConfig config) {

        // Get the connection info
        getConnectionInfo(config);

        // Print log
        logger.info("[{}:{}:{}] Open the tunnel connection", connId, connectionType, cloudUserName);

        Boolean enableH264 = "true".equalsIgnoreCase(configuration.getParameters().get(H264_LICENCE_PARAM));
        guacAppstore = new GuacamoleAppstore();
        guacAppstore.sendLatencyData(configuration, enableH264);

        String otelCollectorEndpoint = (String) config.getUserProperties().get(OTEL_COLLECTOR_ENDPOINT_PROPERTY);
        String otelMetricsInterval = (String) config.getUserProperties().get(OTEL_METRICS_INTERVAL_PROPERTY);
        if (otelCollectorEndpoint != null && otelMetricsInterval != null) {
            guacOpenTelemetry = new GuacamoleOpenTelemetry(connId, connectionType, cloudUserName, configuration, config.getUserProperties());
            
            boolean isAvailableEndpoint = guacOpenTelemetry.isEndpointAvailable(otelCollectorEndpoint);
            if (isAvailableEndpoint) {
                guacOpenTelemetry.sendMetrics();
            }
        }
        else {
            logger.error("[{}:{}:{}] `otel_collector_endpoint` or `otel_metrics_interval` does not exist in property file.",
                         connId, connectionType, cloudUserName);
        }

        // Store underlying remote for future use via sendInstruction()
        remote = session.getBasicRemote();

        try {

            // Get tunnel
            tunnel = createTunnel(session, config);
            if (tunnel == null) {
                closeConnection(session, GuacamoleStatus.RESOURCE_NOT_FOUND);
                return;
            }

            logger.info("Connection ID is " + tunnel.getConnectionID());

        }
        catch (NullPointerException e) {
            logger.error("[{}:{}:{}] Creation of WebSocket tunnel to guacd failed: {}",
                         connId, connectionType, cloudUserName, e.getMessage());
        }
        catch (GuacamoleException e) {
            logger.error("Creation of WebSocket tunnel to guacd failed: {}", e);
            closeConnection(session, e.getStatus().getGuacamoleStatusCode(),
                    e.getWebSocketCode());
            return;
        }

        // Print log
        logger.info("[{}:{}:{}] Open the main connection, h264={}", connId, connectionType, cloudUserName, enableH264);

        // Manually register message handler
        session.addMessageHandler(new MessageHandler.Whole<String>() {

            @Override
            public void onMessage(String message) {
                GuacamoleWebSocketTunnelEndpoint.this.onMessage(message);
            }

        });

        // The list of the instructions related to the H264.
        ArrayList<String> h264OpCodeList = new ArrayList<>();
        h264OpCodeList.add(H264_OPCODE);
        h264OpCodeList.add(SIZE_OPCODE);
        h264OpCodeList.add(MONITOR_SIZE_OPCODE);
        h264OpCodeList.add(SYNC_OPCODE);

        // The list of the instructions related to the Clipboard.
        ArrayList<String> clipOpCodeList = new ArrayList<>();
        clipOpCodeList.add(CLIP_OPCODE);
        clipOpCodeList.add(CLIP_BLOB_OPCODE);
        clipOpCodeList.add(CLIP_END_OPCODE);

        // Prepare read transfer thread
        Thread readThread = new Thread() {

            @Override
            public void run() {

                StringBuilder buffer = new StringBuilder(BUFFER_SIZE);
                GuacamoleReader reader = tunnel.acquireReader();
                char[] readMessage;
                boolean isH264 = false;

                try {

                    // Send tunnel UUID
                    sendInstruction(new GuacamoleInstruction(
                        GuacamoleTunnel.INTERNAL_DATA_OPCODE,
                        tunnel.getUUID().toString()
                    ));

                    // Send the connection id of guacd
                    sendInstruction(new GuacamoleInstruction(
                        "connection-id",
                        tunnel.getConnectionID()
                    ));

                    sendInstruction(new GuacamoleInstruction(
                        SYNC_OPCODE,
                        "0"
                    ));

                    try {

                        // Attempt to read
                        while ((readMessage = reader.read()) != null) {

                            // Buffer message
                            buffer.append(readMessage);

                            // Flush if we expect to wait or buffer is getting full
                            if (!reader.available() || buffer.length() >= BUFFER_SIZE) {
                                String instructions = buffer.toString();
                                char[] chunk = instructions.toCharArray();
                                int length = chunk.length;
                                int offset = 0;

                                // GuacamoleInstruction instruction;
                                while (length > 0) {

                                    // Pass as much data through the parser as possible
                                    int parsed;

                                    while ((parsed = parser.append(chunk, offset, length)) != 0) {
                                        offset += parsed;
                                        length -= parsed;
                                    }

                                    // If no instruction is available, it must be incomplete
                                    if (!parser.hasNext())
                                        throw new GuacamoleServerException("Filtered write() contained an incomplete instruction.");

                                    // Write single instruction through filter
                                    GuacamoleInstruction temp_instruction = parser.next();
                                    List<String> temp_args = new ArrayList<String>();
                                    temp_args.addAll(temp_instruction.getArgs());
                                    GuacamoleInstruction instruction = new GuacamoleInstruction(temp_instruction.getOpcode(), temp_args);

                                    if (guacOpenTelemetry != null && instruction.getOpcode().equals(GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_LOGIN)) {
                                        int argsLength = instruction.getArgs().size();
                                        if (argsLength > 0) {
                                            guacOpenTelemetry.setMetricsLoginTime(instruction.getArgs().get(0));
                                        }
                                        else {
                                            logger.info("[{}:{}:{}] The parameters for login time instruction are incorrect.(param count={})",
                                                        connId, connectionType, cloudUserName, argsLength);
                                        }

                                        continue;
                                    }

                                    if (guacOpenTelemetry != null && instruction.getOpcode().equals(GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_RTT)) {
                                        int argsLength = instruction.getArgs().size();
                                        if (argsLength > 0) {
                                            guacOpenTelemetry.setMetricsRTT(instruction.getArgs().get(0));
                                        }
                                        else {
                                            logger.info("[{}:{}:{}] The parameters for RTT instruction are incorrect.(param count={})",
                                                        connId, connectionType, cloudUserName, argsLength);
                                        }

                                        continue;
                                    }

                                    if (enableH264 && h264OpCodeList.contains(instruction.getOpcode())) {
                                        tunnel.getH264Instructions().add(instruction);

                                        if (instruction.getOpcode().equals(H264_OPCODE)) {
                                            isH264 = true;
                                        }

                                        continue;
                                    }

                                    if (isH264 &&
                                        instruction.getOpcode().equals(BLOB_OPCODE) &&
                                        instruction.getArgs().get(0).equals(H264_STREAM_INDEX)) {

                                        tunnel.getH264Instructions().add(instruction);
                                        continue;
                                    }

                                    if (isH264 &&
                                        instruction.getOpcode().equals(END_OPCODE) &&
                                        instruction.getArgs().get(0).equals(H264_STREAM_INDEX)) {

                                        tunnel.getH264Instructions().add(instruction);
                                        isH264 = false;
                                        continue;
                                    }

                                    if (clipOpCodeList.contains(instruction.getOpcode())) {
                                        tunnel.getClipInstructions().add(instruction);

                                        continue;
                                    }

                                    sendInstruction(instruction);
                                }

                                // sendInstruction(buffer.toString());
                                buffer.setLength(0);

                                // Check the last time that wrote the message to the Guacamole server
                                long currentTime = System.currentTimeMillis();
                                if (currentTime - lastWriteTime > WRITE_TIMEOUT) {
                                    // If exceeds the timeout
                                    forceWriteInstruction();
                                    lastWriteTime = currentTime;
                                }

                                // Check the last time that send the message to the websocket of browser
                                if ((currentTime - lastSendTime) > SEND_TIMEOUT) {
                                    // If exceeds the timeout
                                    sendInstruction(NOP_INSTRUCTION.toString());
                                    lastSendTime = currentTime;
                                }
                            }

                        }

                        // No more data
                        closeConnection(session, GuacamoleStatus.SUCCESS);

                    }

                    // Catch any thrown guacamole exception and attempt
                    // to pass within the WebSocket connection, logging
                    // each error appropriately.
                    catch (GuacamoleClientException e) {
                        logger.info("[{}:{}:{}] WebSocket connection terminated: {}",
                                    connId, connectionType, cloudUserName, e.getMessage());
                        logger.error("[{}:{}:{}] WebSocket connection terminated due to client error.",
                                     connId, connectionType, cloudUserName, e);
                        closeConnection(session, e.getStatus().getGuacamoleStatusCode(),
                                        e.getWebSocketCode());
                    }
                    catch (GuacamoleConnectionClosedException e) {
                        logger.error("[{}:{}:{}] Connection to guacd closed.",
                                     connId, connectionType, cloudUserName, e);
                        closeConnection(session, GuacamoleStatus.SUCCESS);
                    }
                    catch (GuacamoleException e) {
                        logger.error("[{}:{}:{}] Connection to guacd terminated abnormally: {}",
                                     connId, connectionType, cloudUserName, e.getMessage());
                        logger.error("[{}:{}:{}] Internal error during connection to guacd.",
                                     connId, connectionType, cloudUserName, e);
                        closeConnection(session, e.getStatus().getGuacamoleStatusCode(),
                                        e.getWebSocketCode());
                    }
                    catch (IllegalStateException e) {
                        logger.error("[{}:{}:{}] IllegalStateException occurred.",
                                     connId, connectionType, cloudUserName, e);
                    }

                }
                catch (IllegalStateException e) {
                    logger.error("[{}:{}:{}] IllegalStateException occurred.",
                                 connId, connectionType, cloudUserName, e);
                }
                catch (IOException e) {
                    logger.error("[{}:{}:{}] I/O error prevents further reads.",
                                 connId, connectionType, cloudUserName, e);
                    closeConnection(session, GuacamoleStatus.SERVER_ERROR);
                }

            }

        };

        readThread.start();

    }

    @OnMessage
    public void onMessage(String message) {

        // Ignore inbound messages if there is no associated tunnel
        if (tunnel == null)
            return;

        // Filter received instructions, handling tunnel-internal instructions
        // without passing through to guacd
        GuacamoleWriter writer = new FilteredGuacamoleWriter(tunnel.acquireWriter(), new GuacamoleFilter() {

            @Override
            public GuacamoleInstruction filter(GuacamoleInstruction instruction)
                    throws GuacamoleException {

                // Filter out all tunnel-internal instructions
                if (instruction.getOpcode().equals(GuacamoleTunnel.INTERNAL_DATA_OPCODE)) {

                    // Respond to ping requests
                    List<String> args = instruction.getArgs();
                    if (args.size() >= 2 && args.get(0).equals(PING_OPCODE)) {

                        try {
                            sendInstruction(new GuacamoleInstruction(
                                GuacamoleTunnel.INTERNAL_DATA_OPCODE,
                                PING_OPCODE, args.get(1)
                            ));
                        }
                        catch (IOException e) {
                            logger.debug("[{}:{}:{}] Unable to send \"ping\" response for WebSocket tunnel.",
                                         connId, connectionType, cloudUserName, e);
                        }

                    }

                    return null;

                }

                if (GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_OPCODE.equals(instruction.getOpcode())) {

                    List<String> args = instruction.getArgs();
                    int argsLength = args.size();

                    if (GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_LATENCY.equals(args.get(0))) {
                        if (argsLength >=2) {
                            guacAppstore.setLatencyData(args.get(1));
                        }
                        else {
                            logger.info("[{}:{}:{}] The parameters for latency instruction are incorrect.(param count={})",
                                        connId, connectionType, cloudUserName, argsLength);
                        }
                    }

                    if (guacOpenTelemetry != null) {
                        if (GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_BANDWIDTH.equals(args.get(0))) {
                            if (argsLength >=2) {
                                guacOpenTelemetry.setMetricsBandwidth(args.get(1));
                            }
                            else {
                                logger.info("[{}:{}:{}] The parameters for bandwidth instruction are incorrect.(param count={})",
                                            connId, connectionType, cloudUserName, argsLength);
                            }
                            
                        }

                        if (GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_LATENCY.equals(args.get(0))) {
                            if (argsLength >=2) {
                                guacOpenTelemetry.setMetricsLatency(args.get(1));
                            }
                            else {
                                logger.info("[{}:{}:{}] The parameters for latency instruction are incorrect.(param count={})",
                                            connId, connectionType, cloudUserName, argsLength);
                            }
                            
                        }

                        if (GuacamoleOpenTelemetry.OTEL_METRICS_INSTRUCTION_FRAME.equals(args.get(0))) {
                            if (argsLength >=3) {
                                guacOpenTelemetry.setMetricsFrameRate(args.get(1));
                                guacOpenTelemetry.setMetricsRenderingTime(args.get(2));
                            }
                            else {
                                logger.info("[{}:{}:{}] The parameters for frame instruction are incorrect.(param count={})",
                                            connId, connectionType, cloudUserName, argsLength);
                            }
                            
                        }
                    }

                    return null;
                }

                // Pass through all non-internal instructions untouched
                return instruction;

            }

        });

        try {
            // Write received message
            writer.write(message.toCharArray());

            // Record the time to write a message
            lastWriteTime = System.currentTimeMillis();
        }
        catch (GuacamoleConnectionClosedException e) {
            logger.debug("[{}:{}:{}] Connection to guacd closed.", connId, connectionType, cloudUserName, e);
        }
        catch (GuacamoleException e) {
            logger.debug("[{}:{}:{}] WebSocket tunnel write failed.", connId, connectionType, cloudUserName, e);
        }

        tunnel.releaseWriter();

    }

    @Override
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        if (guacOpenTelemetry != null) {
            guacOpenTelemetry.onExitTask();
        }

        if (guacAppstore != null) {
            guacAppstore.onExitTask();
        }

        // Show the log messages
        logger.info("[{}:{}:{}] The websocket tunnel is closed. (reason={})",
                    connId, connectionType, cloudUserName, closeReason.toString());

        // Close the tunnel
        try {
            if (tunnel != null)
                tunnel.close();
        }
        catch (GuacamoleException e) {
            logger.debug("[{}:{}:{}] Unable to close WebSocket tunnel.", connId, connectionType, cloudUserName, e);
        }

    }

    /**
     * If the connected Guacamole client doesn't receives any messages from the
     * browser and doesn't write it to the Guacamole server for 60 seconds,
     * the Guacamole server remove the relevant sessions.
     * In the case that the network isn't good, it may happen.
     * To avoid it, it needs to check the last time that the Guacamole client
     * writes any message to the Guacamole server and if the current time is
     * larger 30 seconds than the last time, this function is called to write
     * a 'nop' message to the Guacamole server.
     *
     * This function writes a 'nop' message to the Guacamole server.
     *
     */
    private void forceWriteInstruction() {

        // Ignore inbound messages if there is no associated tunnel
        if (tunnel == null)
            return;

        // Create a new instruction
        GuacamoleInstruction instruction = new GuacamoleInstruction(
            "nop"
        );

        // Create the writer to write the message to the Guacamole server
        GuacamoleWriter writer = new FilteredGuacamoleWriter(tunnel.acquireWriter(), new GuacamoleFilter() {

            @Override
            public GuacamoleInstruction filter(GuacamoleInstruction instruction)
                    throws GuacamoleException {

                // Pass through all non-internal instructions untouched
                return instruction;

            }

        });

        logger.debug(String.format("forceWriteInstruction=%s ", instruction.toString()));

        try {
            // Write received message
            writer.write(instruction.toString().toCharArray());

            // Record the time to write a message
            lastWriteTime = System.currentTimeMillis();
        }
        catch (GuacamoleConnectionClosedException e) {
            logger.debug("[{}:{}:{}] Connection to guacd closed.", connId, connectionType, cloudUserName, e);
        }
        catch (GuacamoleException e) {
            logger.debug("[{}:{}:{}] WebSocket tunnel write failed.", connId, connectionType, cloudUserName, e);
        }

        tunnel.releaseWriter();

    }

    /**
     * Returns the value of URL parameter "id" associated with this set of credentials.
     *
     * @return The id associated with this session, or
     *         null if no cloud_user has been set.
     */
    public String getConnId() {
        return connId;
    }

    /**
     * Sets the value of URL parameter "id" associated with this set of credentials.
     *
     * @param connId The value of URL parameter "id" to associate with this session
     */
    public void setConnId(String connId) {
        this.connId = connId;
    }

    /**
     * Returns the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @return The cloud_username associated with this session, or
     *         null if no cloud_username has been set.
     */
    public String getCloudUserName() {
        return cloudUserName;
    }

    /**
     * Sets the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @param cloudUserName The value of URL parameter "cloud_username" to associate with this session
     */
    public void setCloudUserName(String cloudUserName) {
        this.cloudUserName = cloudUserName;
    }

}
