/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.websocket;

import java.io.IOException;
import java.util.List;
import javax.websocket.CloseReason;
import javax.websocket.CloseReason.CloseCode;
import javax.websocket.Endpoint;
import javax.websocket.EndpointConfig;
import javax.websocket.MessageHandler;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.RemoteEndpoint;
import javax.websocket.Session;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.io.GuacamoleWriter;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.GuacamoleServerException;
import org.apache.guacamole.GuacamoleConnectionClosedException;
import org.apache.guacamole.protocol.FilteredGuacamoleWriter;
import org.apache.guacamole.protocol.GuacamoleParser;
import org.apache.guacamole.protocol.GuacamoleFilter;
import org.apache.guacamole.protocol.GuacamoleInstruction;
import org.apache.guacamole.protocol.GuacamoleStatus;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A WebSocket implementation of GuacamoleTunnel functionality, compatible with
 * the Guacamole.WebSocketTunnel object included with the JavaScript API.
 * Messages sent/received are simply chunks of the Guacamole protocol
 * instruction stream.
 */
public abstract class GuacamoleWebSocketMediaTunnelEndpoint extends Endpoint {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(GuacamoleWebSocketMediaTunnelEndpoint.class);

    /**
     * The opcode of the instruction used to indicate a connection stability
     * test ping request or response. Note that this instruction is
     * encapsulated within an internal tunnel instruction (with the opcode
     * being the empty string), thus this will actually be the value of the
     * first element of the received instruction.
     */
    private static final String PING_OPCODE = "ping";

    /**
     * The "nop" instruction used as a heartbeat when there is no any 
     * related instruction.
     */
    private static final GuacamoleInstruction NOP_INSTRUCTION
                                             = new GuacamoleInstruction(
                                                   "nop"
                                               );

    /**
     * The timeout value for checking the last time the sent an instruction to
     * the browser via the websocket tunnel.
     * This value is used for preventing the disconnect of the this websocket tunnel
     * when any instruction isn't sent in 15 seconds.
     * The unit of this value is seconds.
     */
    private static final long SEND_TIMEOUT = 5000;

    /**
     * The underlying GuacamoleTunnel. WebSocket reads/writes will be handled
     * as reads/writes to this tunnel. This value may be null if no connection
     * has been established.
     */
    private GuacamoleTunnel tunnel;

    /**
     * Parser for reading instructions prior to writing, such that they can be
     * passed on to the filter.
     */
    private final GuacamoleParser parser = new GuacamoleParser();

    /**
     * UUID of main tunnel
     */
    private String uuid;

    /**
     * EndpointConfig of this class
     */
    private EndpointConfig config;

    /**
     * Remote (client) side of this connection. This value will always be
     * non-null if tunnel is non-null.
     */
    private RemoteEndpoint.Basic remote;

    /**
     * The value of URL parameter "id".
     */
    protected String connId;

    /**
     * The value of URL parameter "cloud_username".
     */
    protected String cloudUserName;

    /**
     * The value of URL parameter "connection_type".
     */
    protected String connType;

    /**
     * The flag indicating that the WebSocket session is closed
     */
    private Boolean is_session_closed = false;

    /**
     * Sends the numeric Guacamole Status Code and Web Socket
     * code and closes the connection.
     *
     * @param session
     *     The outbound WebSocket connection to close.
     *
     * @param guacamoleStatusCode
     *     The numeric Guacamole status to send.
     *
     * @param webSocketCode
     *     The numeric WebSocket status to send.
     */
    private void closeConnection(Session session, int guacamoleStatusCode,
            int webSocketCode) {

        logger.info("[{}:{}:{}] Close connection. (guacamoleStatusCode={}, webSocketCode={})",
                    connId, connType, cloudUserName, guacamoleStatusCode, webSocketCode);

        try {
            CloseCode code = CloseReason.CloseCodes.getCloseCode(webSocketCode);
            String message = Integer.toString(guacamoleStatusCode);
            session.close(new CloseReason(code, message));
        }
        catch (IOException e) {
            logger.error("[{}:{}:{}] Unable to close WebSocket connection.",
                          connId, connType, cloudUserName, e);
        }

    }

    /**
     * Sends the given Guacamole Status and closes the given
     * connection.
     *
     * @param session
     *     The outbound WebSocket connection to close.
     *
     * @param guacStatus
     *     The status to use for the connection.
     */
    private void closeConnection(Session session, GuacamoleStatus guacStatus) {

        closeConnection(session, guacStatus.getGuacamoleStatusCode(),
                guacStatus.getWebSocketCode());

    }

    /**
     * Sends a Guacamole instruction along the outbound WebSocket connection to
     * the connected Guacamole client. If an instruction is already in the
     * process of being sent by another thread, this function will block until
     * in-progress instructions are complete.
     *
     * @param instruction
     *     The instruction to send.
     *
     * @throws IOException
     *     If an I/O error occurs preventing the given instruction from being
     *     sent.
     */
    private void sendInstruction(String instruction)
            throws IOException {

        // If the WebSocket session is closed, we don't need to send
        // the instructions to the client.
        if (is_session_closed)
            return;

        // NOTE: Synchronization on the non-final remote field here is
        // intentional. The remote (the outbound websocket connection) is only
        // sensitive to simultaneous attempts to send messages with respect to
        // itself. If the remote changes, then the outbound websocket
        // connection has changed, and synchronization need only be performed
        // in context of the new remote.
        synchronized (remote) {
            remote.sendText(instruction);
        }

    }

    /**
     * Sends a Guacamole instruction along the outbound WebSocket connection to
     * the connected Guacamole client. If an instruction is already in the
     * process of being sent by another thread, this function will block until
     * in-progress instructions are complete.
     *
     * @param instruction
     *     The instruction to send.
     *
     * @throws IOException
     *     If an I/O error occurs preventing the given instruction from being
     *     sent.
     */
    private void sendInstruction(GuacamoleInstruction instruction)
            throws IOException {

        sendInstruction(instruction.toString());

    }

    /**
     * Returns a new tunnel for the given session. How this tunnel is created
     * or retrieved is implementation-dependent.
     *
     * @param session The session associated with the active WebSocket
     *                connection.
     * @param config Configuration information associated with the instance of
     *               the endpoint created for handling this single connection.
     * @return A connected tunnel, or null if no such tunnel exists.
     * @throws GuacamoleException If an error occurs while retrieving the
     *                            tunnel, or if access to the tunnel is denied.
     */
    protected abstract GuacamoleTunnel createTunnel(Session session, EndpointConfig config)
            throws GuacamoleException;

    /**
     * Returns a main tunnel for the given uuid.
     *
     * @param uuid The uuid of main tunnel
     * @param config Configuration information associated with the instance of
     *               the endpoint created for handling this single connection.
     * @return A main tunnel, or null if no such tunnel exists.
     * @throws GuacamoleException If an error occurs while retrieving the
     *                            tunnel, or if access to the tunnel is denied.
     */
    protected abstract GuacamoleTunnel getMainTunnel(String uuid, EndpointConfig config)
            throws GuacamoleException;

    /**
     * Get the connection info needed to print the log.
     *
     * @param config Configuration information associated with the instance of
     *               the endpoint created for handling this single connection.
     * @return true if it successfully gets the session info, false if not.
     */
    protected abstract boolean getConnectionInfo(EndpointConfig config);

    @Override
    @OnOpen
    public void onOpen(final Session session, EndpointConfig config) {

        // Get the connection info
        getConnectionInfo(config);

        // Print log
        logger.info("[{}:{}:{}] Open the media connection", connId, connType, cloudUserName);

        // Store underlying remote for future use via sendInstruction()
        remote = session.getBasicRemote();

        // Set the flag indicating that the WebSocket session is opened
        is_session_closed = false;

        try {

            // Send tunnel UUID
            sendInstruction(new GuacamoleInstruction(
                GuacamoleTunnel.INTERNAL_DATA_OPCODE,
                ""
            ));

            this.config = config;

            // Prepare read transfer thread
            Thread readThread = new Thread() {

                @Override
                public void run() {

                    try {

                        // Attempt to read
                        while (!is_session_closed) {

                            sendInstruction(NOP_INSTRUCTION.toString());

                            try {
                                Thread.sleep(SEND_TIMEOUT);
                            }
                            catch (InterruptedException e) {
                                logger.error("[{}:{}:{}] Exception log: ", connId, connType, cloudUserName, e);
                            }

                        }

                    }
                    catch (IllegalStateException e) {
                        logger.error("[{}:{}:{}] IllegalStateException occurred.",
                                      connId, connType, cloudUserName, e);
                    }
                    catch (IOException e) {
                        logger.error("[{}:{}:{}] I/O error prevents further reads.",
                                      connId, connType, cloudUserName, e);
                        closeConnection(session, GuacamoleStatus.SERVER_ERROR);
                    }

                }

            };

            readThread.start();

        }
        catch (IOException e) {
            logger.error("[{}:{}:{}] Creation of WebSocket tunnel to guacd failed: {}",
                          connId, connType, cloudUserName, e.getMessage());
            return;
        }

        // Manually register message handler
        session.addMessageHandler(new MessageHandler.Whole<String>() {

            @Override
            public void onMessage(String message) {
                GuacamoleWebSocketMediaTunnelEndpoint.this.onMessage(message);
            }

        });

    }

    @OnMessage
    public void onMessage(String message) {

        uuid = "";
        if (message.substring(0, 2).equals("0.")) {

            char[] chunk = message.toCharArray();
            int length = chunk.length;
            int offset = 0;
            while (length > 0) {

                // Pass as much data through the parser as possible
                int parsed;
                try {

                    while ((parsed = parser.append(chunk, offset, length)) != 0) {
                        offset += parsed;
                        length -= parsed;
                    }

                    // If no instruction is available, it must be incomplete
                    if (!parser.hasNext())
                        throw new GuacamoleServerException("Filtered write() contained an incomplete instruction.");

                    // Write single instruction through filter
                    GuacamoleInstruction instruction = parser.next();
                    if (instruction.getOpcode().equals(GuacamoleTunnel.INTERNAL_DATA_OPCODE)) {

                        List<String> args = instruction.getArgs();
                        if (args.size() >= 1) {
                            uuid = args.get(0);
                            tunnel = getMainTunnel(uuid, config);
                        }

                    }

                }
                catch (GuacamoleException e) {
                    logger.debug("[{}:{}:{}] WebSocket tunnel write failed.", connId, connType, cloudUserName, e);
                }

            }

            return;
        }

        // Ignore inbound messages if there is no associated tunnel
        if (tunnel == null)
            return;

        // Filter received instructions, handling tunnel-internal instructions
        // without passing through to guacd
        GuacamoleWriter writer = new FilteredGuacamoleWriter(tunnel.acquireWriter(), new GuacamoleFilter() {

            @Override
            public GuacamoleInstruction filter(GuacamoleInstruction instruction)
                    throws GuacamoleException {

                // Filter out all tunnel-internal instructions
                if (instruction.getOpcode().equals(GuacamoleTunnel.INTERNAL_DATA_OPCODE)) {

                    // Respond to ping requests
                    List<String> args = instruction.getArgs();
                    if (args.size() >= 2 && args.get(0).equals(PING_OPCODE)) {

                        try {
                            sendInstruction(new GuacamoleInstruction(
                                GuacamoleTunnel.INTERNAL_DATA_OPCODE,
                                PING_OPCODE, args.get(1)
                            ));
                        }
                        catch (IOException e) {
                            logger.debug("[{}:{}:{}] Unable to send \"ping\" response for WebSocket tunnel.",
                                          connId, connType, cloudUserName, e);
                        }

                    }

                    return null;

                }

                // Pass through all non-internal instructions untouched
                return instruction;

            }

        });

        try {
            // Write received message
            writer.write(message.toCharArray());
        }
        catch (GuacamoleConnectionClosedException e) {
            logger.debug("[{}:{}:{}] Connection to guacd closed.", connId, connType, cloudUserName, e);
        }
        catch (GuacamoleException e) {
            logger.debug("[{}:{}:{}] WebSocket tunnel write failed.", connId, connType, cloudUserName, e);
        }

        tunnel.releaseWriter();

    }

    @Override
    @OnClose
    public void onClose(Session session, CloseReason closeReason) {

        // Show the log messages
        logger.info("[{}:{}:{}] The media websocket tunnel is closed. (reason={})",
                    connId, connType, cloudUserName, closeReason.toString());

        // Set the flag indicating that the WebSocket session is closed
        is_session_closed = true;

    }

    @Override
    public void onError(Session session, Throwable ex) {

        logger.error("[{}:{}:{}] Unable to close Media webSocket tunnel. {}",
                     connId, connType, cloudUserName, ex);

        // Set the flag indicating that the WebSocket session is closed
        is_session_closed = true;

    }

    /**
     * Returns the value of URL parameter "id" associated with this set of credentials.
     *
     * @return The id associated with this session, or
     *         null if no cloud_user has been set.
     */
    public String getConnId() {
        return connId;
    }

    /**
     * Sets the value of URL parameter "id" associated with this set of credentials.
     *
     * @param connId The value of URL parameter "id" to associate with this session
     */
    public void setConnId(String connId) {
        this.connId = connId;
    }

    /**
     * Returns the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @return The cloud_username associated with this session, or
     *         null if no cloud_username has been set.
     */
    public String getCloudUserName() {
        return cloudUserName;
    }

    /**
     * Sets the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @param cloudUserName The value of URL parameter "cloud_username" to associate with this session
     */
    public void setCloudUserName(String cloudUserName) {
        this.cloudUserName = cloudUserName;
    }

    /**
     * Returns the value of URL parameter "connection_type" associated with this set of credentials.
     *
     * @return The connection_type associated with this session, or
     *         null if no connection_type has been set.
     */
    public String getConnType() {
        return connType;
    }

    /**
     * Sets the value of URL parameter "connection_type" associated with this set of credentials.
     *
     * @param connType The value of URL parameter "connection_type" to associate with this session
     */
    public void setConnType(String connType) {
        this.connType = connType;
    }

}
