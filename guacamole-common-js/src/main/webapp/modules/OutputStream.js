/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

var Guacamole = Guacamole || {};

/**
 * Abstract stream which can receive data.
 * 
 * @constructor
 * @param {!Guacamole.Client} client
 *     The client owning this stream.
 *
 * @param {!number} index
 *     The index of this stream.
 */
Guacamole.OutputStream = function(client, index, type) {

    /**
     * Reference to this stream.
     *
     * @private
     * @type {!Guacamole.OutputStream}
     */
    var guac_stream = this;

    /**
     * The index of this stream.
     * @type {!number}
     */
    this.index = index;

    /**
     * The type of this stream.
     * @type {String}
     */
    this.type = type;

    /**
     * Fired whenever an acknowledgement is received from the server, indicating
     * that a stream operation has completed, or an error has occurred.
     * 
     * @event
     * @param {!Guacamole.Status} status
     *     The status of the operation.
     */
    this.onack = null;

    /**
     * Writes the given base64-encoded data to this stream as a blob.
     * 
     * @param {!string} data
     *     The base64-encoded data to send.
     */
    this.sendBlob = function(data) {
        if (type === 'audio' || type === 'video') {
            client.sendMedia(guac_stream.index, data);
        }
        else {
            client.sendBlob(guac_stream.index, data);
        }
    };

    /**
     * Closes this stream.
     */
    this.sendEnd = function() {
        if (type === 'audio' || type === 'video') {
            client.endMediaStream(guac_stream.index);
        }
        else {
            client.endStream(guac_stream.index);
        }
    };

};
