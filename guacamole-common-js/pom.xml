<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                        http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.apache.guacamole</groupId>
    <artifactId>guacamole-common-js</artifactId>
    <packaging>pom</packaging>
    <version>1.1.0</version>
    <name>guacamole-common-js</name>
    <url>http://guacamole.apache.org/</url>

    <description>
        The base JavaScript API of the Guacamole project, providing JavaScript
        support for the Guacamole stack, including a full client
        implementation for the Guacamole protocol.
    </description>

    <!-- All applicable licenses -->
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <!-- Git repository -->
    <scm>
        <url>https://github.com/apache/guacamole-client</url>
        <connection>scm:git:https://git.wip-us.apache.org/repos/asf/guacamole-client.git</connection>

    </scm>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <plugins>

            <!-- Assemble JS files into single .zip -->
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>static.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-zip</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attached</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- JS/CSS Minification Plugin -->
            <plugin>
                <groupId>com.samaxes.maven</groupId>
                <artifactId>minify-maven-plugin</artifactId>
                <version>1.6.1</version>
                <executions>
                    <execution>
                        <id>default-minify</id>
                        <configuration>

                            <charset>UTF-8</charset>
                            <jsEngine>CLOSURE</jsEngine>

                            <jsSourceDir>/</jsSourceDir>
                            <jsTargetDir>/</jsTargetDir>
                            <jsFinalFile>all.js</jsFinalFile>

                            <jsSourceFiles>
                                <jsSourceFile>common/license.js</jsSourceFile> 
                            </jsSourceFiles>

                            <jsSourceIncludes>
                                <jsSourceInclude>modules/**/*.js</jsSourceInclude>
                            </jsSourceIncludes>

                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-utils</artifactId>
                        <version>3.5.1</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- Verify format using Apache RAT -->
            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <version>0.12</version>

                <configuration>
                    <excludes>
                        <exclude>**/*.json</exclude>
                    </excludes>
                </configuration>

                <!-- Bind RAT to validate phase -->
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>

            <!-- Unit test using Jasmin and PhantomJS -->
            <plugin>
                <groupId>com.github.searls</groupId>
                <artifactId>jasmine-maven-plugin</artifactId>
                <version>2.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <phantomjs>
                        <version>2.1.1</version>
                    </phantomjs>
                    <sourceIncludes>
                        <sourceInclude>**/*.min.js</sourceInclude>
                    </sourceIncludes>
                    <jsSrcDir>${project.build.directory}/${project.build.finalName}</jsSrcDir>
                </configuration>
            </plugin>

        </plugins>
    </build>



</project>
