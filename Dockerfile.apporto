# syntax=docker/dockerfile:1.3-labs
# vim: set ft=dockerfile expandtab ts=4 sw=4:

#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

#------------------------------------------------------------
# Install Guacamole
#------------------------------------------------------------
FROM docker.apporto.com/hyperstream-builder:2024.2 AS hyperstream-builder

ARG MYSQL_CONNECTOR_VERSION=8.0.24

ENV GUACAMOLE_HOME=/builds/src
WORKDIR /builds/src
COPY ./ ./

#RUN mvn clean package
RUN ./apporto/tools/g-deploy --docker-deploy && \
    cd /var/lib/tomcat9 && rm -rf bin conf lib logs work && \
    ls -al /var/lib/tomcat9/webapps/hyperstream

RUN wget https://dev.mysql.com/get/Downloads/Connector-J/mysql-connector-java-${MYSQL_CONNECTOR_VERSION}.tar.gz -O /tmp/mysql-connector-java-${MYSQL_CONNECTOR_VERSION}.tar.gz && \
    tar xvzf /tmp/mysql-connector-java-${MYSQL_CONNECTOR_VERSION}.tar.gz -C /tmp/   && \
    mkdir -p /var/lib/tomcat9/.guacamole/lib/ && \
    cp /tmp/mysql-connector-java-${MYSQL_CONNECTOR_VERSION}/mysql-connector-java-${MYSQL_CONNECTOR_VERSION}.jar /var/lib/tomcat9/.guacamole/lib/ && \
    rm -rf /tmp/mysql-connector-java-${MYSQL_CONNECTOR_VERSION}*

#------------------------------------------------------------
# Final Layer
#------------------------------------------------------------
FROM tomcat:9-jre17-temurin-jammy

ARG DRONE_REPO_NAME
ARG DRONE_SOURCE_BRANCH
ARG DRONE_COMMIT_SHA
ARG DRONE_TAG
ARG DRONE_COMMIT_REF
ARG DRONE_BUILD_NUMBER
ARG APP_BUILD_VERSION

ARG RUNTIME_DEPENDENCIES="              \
        ca-certificates                 \
        curl                            \
        dumb-init                       \
        netcat-openbsd                  \
        apt-transport-https             \
        gnupg                           \
        openssh-client"

ENV CATALINA_BASE /usr/local/tomcat
ENV GUACAMOLE_HOME=/usr/local/tomcat/.guacamole

RUN --mount=type=cache,target=/var/cache/apt --mount=type=cache,target=/var/lib/apt \
    apt-get update && \
    apt-get install -y $RUNTIME_DEPENDENCIES --no-install-recommends


COPY --from=hyperstream-builder /var/lib/tomcat9 $CATALINA_BASE

RUN echo 'OK' > $CATALINA_BASE/webapps/ROOT/index.html

# Powershell is a depenency for guacamole
RUN --mount=type=cache,target=/var/cache/apt --mount=type=cache,target=/var/lib/apt \
    curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add -      && \
    echo "deb [arch=amd64] https://packages.microsoft.com/ubuntu/22.04/prod jammy main" > /etc/apt/sources.list.d/powershell-repo.list && \
    apt-get update && \
    apt-get install -y powershell=7.2.8-1.deb


# Save build properties
RUN mkdir -p $CATALINA_BASE/webapps/hyperstream/app/build_info && \
    cat <<EOF > $CATALINA_BASE/webapps/hyperstream/app/build_info/client_version.json
{
    "client": [{
        "githash": "$(echo $DRONE_COMMIT_SHA | cut -c1-8)",
        "version": "$APP_BUILD_VERSION",
        "build-number": "$DRONE_BUILD_NUMBER"
    }]
}
EOF

RUN ln -s /etc/guacamole/guacamole.properties $CATALINA_BASE/.guacamole/guacamole.properties && \
    ln -s /etc/guacamole/secretkey.properties $CATALINA_BASE/.guacamole/secretkey.properties 

WORKDIR $CATALINA_BASE

COPY apporto/tomcat-conf/ conf/
COPY apporto/entrypoint/entrypoint.sh /

EXPOSE 8080
CMD [ "/entrypoint.sh" ]

