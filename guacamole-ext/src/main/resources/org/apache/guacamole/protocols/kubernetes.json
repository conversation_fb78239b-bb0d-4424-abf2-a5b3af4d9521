{"name": "kubernetes", "connectionForms": [{"name": "network", "fields": [{"name": "hostname", "type": "TEXT"}, {"name": "port", "type": "NUMERIC"}, {"name": "use-ssl", "type": "BOOLEAN", "options": ["true"]}, {"name": "ignore-cert", "type": "BOOLEAN", "options": ["true"]}, {"name": "ca-cert", "type": "MULTILINE"}]}, {"name": "container", "fields": [{"name": "namespace", "type": "TEXT"}, {"name": "pod", "type": "TEXT"}, {"name": "container", "type": "TEXT"}, {"name": "exec-command", "type": "TEXT"}]}, {"name": "authentication", "fields": [{"name": "client-cert", "type": "MULTILINE"}, {"name": "client-key", "type": "MULTILINE"}]}, {"name": "display", "fields": [{"name": "color-scheme", "type": "TERMINAL_COLOR_SCHEME", "options": ["", "black-white", "gray-black", "green-black", "white-black"]}, {"name": "font-name", "type": "TEXT"}, {"name": "font-size", "type": "ENUM", "options": ["", "8", "9", "10", "11", "12", "14", "18", "24", "30", "36", "48", "60", "72", "96"]}, {"name": "scrollback", "type": "NUMERIC"}, {"name": "read-only", "type": "BOOLEAN", "options": ["true"]}]}, {"name": "behavior", "fields": [{"name": "backspace", "type": "ENUM", "options": ["", "127", "8"]}]}, {"name": "typescript", "fields": [{"name": "typescript-path", "type": "TEXT"}, {"name": "typescript-name", "type": "TEXT"}, {"name": "create-typescript-path", "type": "BOOLEAN", "options": ["true"]}]}, {"name": "recording", "fields": [{"name": "recording-path", "type": "TEXT"}, {"name": "recording-name", "type": "TEXT"}, {"name": "recording-exclude-output", "type": "BOOLEAN", "options": ["true"]}, {"name": "recording-exclude-mouse", "type": "BOOLEAN", "options": ["true"]}, {"name": "recording-include-keys", "type": "BOOLEAN", "options": ["true"]}, {"name": "create-recording-path", "type": "BOOLEAN", "options": ["true"]}]}], "sharingProfileForms": [{"name": "display", "fields": [{"name": "read-only", "type": "BOOLEAN", "options": ["true"]}]}]}