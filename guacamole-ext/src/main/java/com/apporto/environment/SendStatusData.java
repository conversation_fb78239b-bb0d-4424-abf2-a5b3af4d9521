/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.environment;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ForkJoinPool;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.codec.binary.Base64;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.properties.StringGuacamoleProperty;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.GuacamoleCommonUtility;

public final class SendStatusData {

    private static final Logger logger = LoggerFactory.getLogger(SendStatusData.class);

    // Next two params are not used by Guacamole, needed only to be returned to Drupal
    private static final String NID_PARM = "nid";
    private static final String SUBDOMAIN_PARM = "subdomain";
    private static final String HTTP_API_KEY_PARM = "Http-Api-Key";
    private static final String USER_ID_PARM = "user-id";
    private static final String WIN_SERVER_NAME_PARM = "win-server-name";
    private static final String WIN_SESSION_ID_PARM = "win-session-id";
    private static final String ID_PARM = "id";
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String CONNECTION_TYPE_PARM = "connection_type";
    private static final String USERNAME_PARM = "username";
    private static final String RDP_FARM_NAME_PARAM = "rdp-farm-name";

    // Default domain if not supplied in subdomain parameter
    private static String DEFAULT_DOMAIN = "apporto.com";

    // Domain to use apporto service
    private static String DNS_DOMAIN;

    // Default port to use apporto service
    private static final String DEFAULT_PORT = "50018";
    private static String PORT;

    // Default user name to use apporto service
    private static String DEFAULT_USER_NAME;

    // Default password to use apporto service
    private static String DEFAULT_PASSWORD;

    // Value of the "Http-Api-Key" parameter
    protected static String HTTP_API_KEY;

    protected static String rdpRouterAPI;

    // Name of the server-id parameter in Guacamole configuration
    public static final StringGuacamoleProperty SERVER_ID = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "server-id";
        }
    };

    // Name of the local-dns-zone parameter in Guacamole configuration
    public static final StringGuacamoleProperty LOCAL_DNS_ZONE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "local-dns-zone";
        }
    };

    // The secret key for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-secret-key";
        }
    };

    // The secret key for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_USERNAME = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-username";
        }
    };

    // The secret key for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_PASSWORD = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-password";
        }
    };

    // The port for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_PORT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-port";
        }
    };

    // The address for the RDP Router Service
    public static final StringGuacamoleProperty RDP_ROUTER_API_SERVICE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-api-service";
        }
    };

    // The secret key for the RDP Router Service
    public static final StringGuacamoleProperty ROUTER_SERVICE_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "rdp-router-api-secret-key";
        }
    };

    /* https://{subdomain}/sessions/{Bsession_id}/{cloud_username}/{nid}/session_started/{status}/{message}/{server_id} */
    private static final String start_url_template = "https://%s/sessions/%s/%s/%s/session_started/%d/%s/%s";
    /* https://{subdomain}/sessions/{Bsession_id}/{cloud_username}/{nid}/session_ended/{status}/{message}/{server_id} */
    private static final String end_url_template = "https://%s/sessions/%s/%s/%s/session_ended/%d/%s/%s";
    /* https://{subdomain}.apporto.com/userstats/{sessionid}/{totaltime}/{activitytime}/{idletime} */
    private static final String idle_stats_url_template = "https://%s/userstats/%s/%s/%s/%s";
    /* {Base_url}/userstats/events/{session_id}/#even2t */
    private static final String event_stats_url_template = "https://%s/userstats/events/%s/%s";
    private static final String user_data_url_template = "https://%s/userstats/all/sessions/userdataaverage/timezone/%s/%s";
    private static final String group_data_url_template = "https://%s/userstats/all/sessions/groupaverage/timezone/%s/%s";
    /* https://subdomain/admin/sessions/poor/network/{session_id}/{status_message} */
    private static final String send_network_state = "https://%s/admin/sessions/poor/network/%s/%s";
    /* https://{hostname}:{port}/api/Auth/login */
    private static final String login_apporto_service_template = "https://%s:%s/api/Auth/login";
    /* https://{hostname}:{port}/api/command */
    private static final String kill_session_apporto_service_template = "https://%s:%s/api/command";
    /* https://subdomain/guac/active_users/{cloud_name} */
    private static final String classroom_data_url_template = "https://%s/guac/active_users/%s";

    private static String SECRET_KEY;
    private static String SECRET_KEY_ROUTER;
    private static final String USER_AGENT = "Mozilla/5.0";
    private static final String CONTENT_TYPE = "application/json; charset=utf-8";

    private SendStatusData() {}

    /**
     * Load the proper configuration variable(s)
     */
    static {

        try {

            Environment environment = new LocalEnvironment();
            // If exists, get the Apporto service secret key from guacamole.properties
            SECRET_KEY = environment.getProperty(APPORTO_SERVICE_SECRET_KEY);

            if (SECRET_KEY == null || SECRET_KEY.isEmpty()) {
                logger.error("apporto-service-secret-key is empty.");
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `apporto-service-secret-key` " +
                        "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {

            Environment environment = new LocalEnvironment();

            // if exists, get the Apporto service username from guacamole.properties
            DEFAULT_USER_NAME = environment.getProperty(APPORTO_SERVICE_USERNAME);

            if (DEFAULT_USER_NAME == null || DEFAULT_USER_NAME.isEmpty()) {
                logger.error("apporto-service-username is empty.");
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `apporto-service-username` " +
                        "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {

            Environment environment = new LocalEnvironment();

            // if exists, get the Apporto service password from guacamole.properties
            DEFAULT_PASSWORD = environment.getProperty(APPORTO_SERVICE_PASSWORD);

            if (DEFAULT_PASSWORD == null || DEFAULT_PASSWORD.isEmpty()) {
                logger.error("apporto-service-password is empty.");
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `apporto-service-password` " +
                        "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {

            Environment environment = new LocalEnvironment();

            // if exists, get the Apporto service password from guacamole.properties
            PORT = environment.getProperty(APPORTO_SERVICE_PORT);

            if (PORT == null || PORT.isEmpty()) {
                logger.warn("apporto-service-port is empty. The default port is {}.", DEFAULT_PORT);
                PORT = DEFAULT_PORT;
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `apporto-service-port` " +
                        "parameter from configuration, please check /etc/guacamole/guacamole.properties. " +
                        "The default port is {}.", DEFAULT_PORT);
        }

        try {

            Environment environment = new LocalEnvironment();

            // if exists, get the Apporto service password from guacamole.properties
            rdpRouterAPI = environment.getProperty(RDP_ROUTER_API_SERVICE);

            if (rdpRouterAPI == null || rdpRouterAPI.isEmpty()) {
                logger.error("rdp-router-api-service is empty.");
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-api-service` " +
                        "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        try {

            Environment environment = new LocalEnvironment();
            // If exists, get the Router service secret key from guacamole.properties
            SECRET_KEY_ROUTER = environment.getProperty(ROUTER_SERVICE_SECRET_KEY);

            if (SECRET_KEY_ROUTER == null || SECRET_KEY_ROUTER.isEmpty()) {
                logger.error("rdp-router-api-secret-key is empty.");
            }

        }
        catch (Exception e) {
            logger.warn("Cannot get `rdp-router-api-secret-key` " +
                        "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

    }

    /**
     * Sends start connection status to Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param error - Apporto error object
     * @param authToken - Authentication token
     */
    public static void sendStartStatus(GuacamoleConfiguration configuration,
                                       ApportoError error, String authToken) {
        // Get the connection info
        Map<String, String> params = configuration.getParameters();
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        // Check subdomain
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send status info. Status={}",
                        conn_id, conn_type, cloud_user, error.toString());
            return;
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Check server id
        String serverId = "";
        try {
            Environment environment = new LocalEnvironment();
            serverId = environment.getProperty(SERVER_ID);
        }
        catch (GuacamoleException e) {
            logger.warn("[{}:{}:{}] Cannot get server-id from local configuration.",
                        conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        // Get "Http-Api-Key"
        HTTP_API_KEY = configuration.getParameter(HTTP_API_KEY_PARM);

        // Get "Cloud_username"
        String cloud_username = params.get(CLOUD_USERNAME_PARM);
        if (cloud_username == null || cloud_username.isEmpty()){
            logger.warn("[{}:{}:{}] Cloud_username not specified, cannot send status info. Status={}",
                        conn_id, conn_type, cloud_user, error.toString());
            return;
        }

        // Connect to WebAPI
        String url_str = String.format(start_url_template, subdomain, conn_id, cloud_username, params.get(NID_PARM),
                                       error.getId(), error.toString(), serverId);

        // If "user-id" parameter exists, convert the above URL to that for New API
        if (params.get(USER_ID_PARM) != null) {
            url_str += "/" + params.get(USER_ID_PARM);
        }

        // Add auth token to end of Drupal API
        url_str += "/" + authToken;

        // For debug purposes.
        ForkJoinPool pool = ForkJoinPool.commonPool();
        if (pool != null)
            logger.info("[{}:{}:{}] ForkJoinPool session_started total:{} active:{} running:{} tasks:{} ",
                    conn_id, conn_type, cloud_user, pool.getParallelism(), pool.getActiveThreadCount(), pool.getRunningThreadCount(), pool.getQueuedSubmissionCount());
        else
            logger.info("[{}:{}:{}] ForkJoinPool session_started is null", conn_id, conn_type, cloud_user);
        // End of debug purposes.

        // Call the `session_started` API
        final String url_text = url_str;
        CompletableFuture.runAsync(() -> {
            logger.info("[{}:{}:{}] runAsync session_started, start", conn_id, conn_type, cloud_user);
            try {
                URL url = new URL(url_text);
                logger.info("[{}:{}:{}] runAsync session_started, create URL", conn_id, conn_type, cloud_user);
                sendStatus(url, conn_id, conn_type, cloud_user);
                logger.info("[{}:{}:{}] runAsync session_started, send status", conn_id, conn_type, cloud_user);
            }
            catch (MalformedURLException e) {
                logger.error("[{}:{}:{}] MalformedURLException log: ",
                             conn_id, conn_type, cloud_user, e);
            }
            logger.info("[{}:{}:{}] runAsync session_started, end", conn_id, conn_type, cloud_user);
        });
    }

    /**
     * Sends end connection status to Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param error - Apporto error object
     */
    public static void sendEndStatus(GuacamoleConfiguration configuration, ApportoError error) {
        Map<String, String> params = configuration.getParameters();
        sendEndStatus(params, error);
    }

    /**
     * Sends end connection status to Drupal.
     *
     * @param params - Map containing all parameters related to the guacamole configuration
     * @param error - Apporto error object
     */
    public static void sendEndStatus(Map<String, String> params, ApportoError error) {
        // Get the connection info
        String conn_id = params.get(ID_PARM);
        String cloud_user = params.get(CLOUD_USERNAME_PARM);
        String conn_type = params.get(CONNECTION_TYPE_PARM);

        // Check subdomain
        String subdomain = params.get(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send status info. Status={}",
                        conn_id, conn_type, cloud_user, error.toString());
            return;
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Check server id
        String serverId = "";
        try {
            Environment environment = new LocalEnvironment();
            serverId = environment.getProperty(SERVER_ID);
        }
        catch (GuacamoleException e) {
            logger.warn("[{}:{}:{}] Cannot get server-id from local configuration.",
                        conn_id, conn_type, cloud_user);
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        // Get "Http-Api-Key"
        HTTP_API_KEY = params.get(HTTP_API_KEY_PARM);

        // Get "Cloud_username"
        String cloud_username = params.get(CLOUD_USERNAME_PARM);
        if (cloud_username == null || cloud_username.isEmpty()){
            logger.warn("[{}:{}:{}] Cloud_username not specified, cannot send status info. Status={}",
                        conn_id, conn_type, cloud_user, error.toString());
            return;
        }

        // Connect to WebAPI
        String url_str = String.format(end_url_template, subdomain, conn_id, cloud_username, params.get(NID_PARM),
                                       error.getId(), error.toString(), serverId);

        // If "user-id" parameter exists, convert the above URL to that for New API
        if (params.get(USER_ID_PARM) != null) {
            url_str += "/" + params.get(USER_ID_PARM);
        }

        try {
            URL url = new URL(url_str);
            sendStatus(url, conn_id, conn_type, cloud_user);
        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
    }

    /**
     * Sends close window status to Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     *
     * @return - the status that the windows server is terminated
     */
    public static int sendCloseWindowStatus(GuacamoleConfiguration configuration) {
        // Get the connection info
        Map<String, String> params = configuration.getParameters();
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        try {
            Environment environment = new LocalEnvironment();

            // Indicate the local-dns-zone from guacamole.properties file.
            DNS_DOMAIN = environment.getProperty(LOCAL_DNS_ZONE);
            if (DNS_DOMAIN == null || DNS_DOMAIN.isEmpty()) {
                logger.warn("local-dns-zone is empty. The default domain is {}.", DEFAULT_DOMAIN);
                DNS_DOMAIN = DEFAULT_DOMAIN;
            }
        }
        catch (Exception e) {
            logger.warn("Cannot get `local-dns-zone`" +
                        " parameter from configuration, please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }

        int retVal = 1;
        String winServerName = params.get(WIN_SERVER_NAME_PARM);

        if (winServerName == null || winServerName.isEmpty()) {
            logger.warn("[{}:{}:{}] Windows server name not specified, cannot kill windows session.",
                        conn_id, conn_type, cloud_user);
            return retVal;
        }

        if (!winServerName.contains(".")) { // Not a FQDN
            winServerName = winServerName + "." + DNS_DOMAIN;
        }

        String login_url_str = String.format(login_apporto_service_template, winServerName, PORT);
        String kill_session_url_str = String.format(kill_session_apporto_service_template, winServerName, PORT);

        try {
            URL login_url = new URL(login_url_str);
            URL kill_session_url = new URL(kill_session_url_str);

            String loginResponse = loginUsingApportoService(conn_id, conn_type, cloud_user, winServerName, login_url);
            JSONObject json = new JSONObject(loginResponse);
            String token = json.getString("data");

            if (params.get(WIN_SESSION_ID_PARM) != null) {
                logger.info("[{}:{}:{}] Windows Session ID - {}", conn_id, conn_type, cloud_user, params.get(WIN_SESSION_ID_PARM));
                retVal = killSessionUsingApportoService(conn_id, conn_type, cloud_user, winServerName,
                                    kill_session_url, token, "SessionID", params.get(WIN_SESSION_ID_PARM));
            }
            else {
                logger.info("[{}:{}:{}] Windows Session ID is null, trying with username - {}",
                            conn_id, conn_type, cloud_user, params.get(USERNAME_PARM));
                retVal = killSessionUsingApportoService(conn_id, conn_type, cloud_user, winServerName,
                                    kill_session_url, token, "UserName", params.get(USERNAME_PARM));
            }
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return retVal;
    }

    /**
     * Sends close window status to RDP Router Service.
     *
     * @param configuration - Guacamole configuration containing all parameters
     *
     * @return - the status that the windows server is terminated
     */
    public static int sendCloseWindowToRouter(GuacamoleConfiguration configuration) {
        // Get the connection info
        Map<String, String> params = configuration.getParameters();
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        int retVal = 1;

        String rdpFarmName = params.get(RDP_FARM_NAME_PARAM);

        if (rdpFarmName == null || rdpFarmName.isEmpty()) {
            logger.warn("[{}:{}:{}] RDP Farm Name not specified, cannot kill windows session.",
                        conn_id, conn_type, cloud_user);
            return retVal;
        }

        String kill_session_url_str = rdpRouterAPI + "/rdp-router/api/stop-session";

        try {
            URL kill_session_url = new URL(kill_session_url_str);

            retVal = killSessionUsingRouterService(conn_id, conn_type, cloud_user,
                                kill_session_url, params.get(RDP_FARM_NAME_PARAM), params.get(USERNAME_PARM));
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: {}", conn_id, conn_type, cloud_user, e.toString());
        }

        return retVal;
    }

    public static void sendNetworkStatus(GuacamoleConfiguration configuration, String statusMessage) {
        // Get the connection info
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        // Check subdomain
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send network status info.",
                        conn_id, conn_type, cloud_user);
            return;
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Get "Http-Api-Key"
        HTTP_API_KEY = configuration.getParameter(HTTP_API_KEY_PARM);

        // Connect to WebAPI
        String url_str = String.format(send_network_state, subdomain, conn_id, statusMessage);

        try {
            URL url = new URL(url_str);
            sendStatus(url, conn_id, conn_type, cloud_user);
        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
    }

    /**
     * Sends connection status to Drupal.
     *
     * @param url - the url where status is to be sent
     * @param conn_id - the connection id
     * @param cloud_user - the cloud user name associated to the connection
     */
    private static String sendStatus(URL url, String conn_id, String conn_type, String cloud_user) {

        StringBuffer response = new StringBuffer();

        try {
            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            if (HTTP_API_KEY != null && !HTTP_API_KEY.isEmpty()) {
                con.setRequestProperty("Http-Api-Key", HTTP_API_KEY);
            }

            // Optional default is GET
            con.setRequestMethod("GET");

            // Add request header
            con.setRequestProperty("User-Agent", USER_AGENT);
            // Fix for AP-9650
            con.setConnectTimeout(5000);
            con.setReadTimeout(20000);

            logger.info("[{}:{}:{}] Send 'GET' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            int responseCode = con.getResponseCode();
            logger.info("[{}:{}:{}] Sent 'GET' request to URL : \"{}\" Response Code : \"{}\"",
                        conn_id, conn_type, cloud_user, url, responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            if (!response.toString().isEmpty()) {
                logger.info("[{}:{}:{}] Response Result : \"{}\".", conn_id, conn_type, cloud_user,
                            response.toString());
            }
        }
        catch (SocketTimeoutException e) {
            logger.warn("[{}:{}:{}] SocketTimeoutException with http request, URL={}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to Drupal, URL={}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response.toString();
    }

    /**
     * Login using ApportoService.
     *
     * @param conn_id - the connection ID
     * @param cloud_user - the value of "cloud_username" URL parameter
     * @param winServerName - the windows server name
     * @param url - the endpoint for login
     */
    private static String loginUsingApportoService(String conn_id, String conn_type, String cloud_user,
                                String winServerName, URL url) {
        StringBuffer response = new StringBuffer();

        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Add body with raw data
            JSONObject httpBody = new JSONObject();
            httpBody.put("Username", DEFAULT_USER_NAME);
            httpBody.put("Password", DEFAULT_PASSWORD);
            String message = httpBody.toString();

            // Calculate the signature of the http body
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Connect to WebAPI
            logger.info("[{}:{}:{}] Connecting to {}...", conn_id, conn_type, cloud_user, winServerName);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", CONTENT_TYPE);
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());
            os.close();

            logger.info("[{}:{}:{}] Send 'POST' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            int responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            logger.info("[{}:{}:{}] Connected to {}(response code={}). ApportoService login api url = {}",
                        conn_id, conn_type, cloud_user, winServerName, responseCode, url);
            logger.info("[{}:{}:{}] ResponseMsg = {}", conn_id, conn_type, cloud_user, response.toString());
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to ApportoService, URL = {}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response.toString();
    }

    /**
     * Kill session using ApportoService.
     *
     * @param conn_id - the connection ID
     * @param cloud_user - the value of "cloud_username" URL parameter
     * @param winServerName - the windows server name
     * @param url - the endpoint to kill session
     * @param token - bearer token for header
     * @param type - the name of user logged in server
     * @param killInfo - this indicates the session id if the type param is "SessionID",
     *               this indicates the user name if the type param is "UserName"
     *
     * @return Response - 0 if successful, 1 if fail
     */
    private static int killSessionUsingApportoService(String conn_id, String conn_type, String cloud_user,
                             String winServerName, URL url, String token, String type, String killInfo) {
        StringBuffer response = new StringBuffer();
        int responseCode = HttpsURLConnection.HTTP_NOT_FOUND;

        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Add body with raw data
            JSONObject httpBody = new JSONObject();
            httpBody.put(type, killInfo);
            String message = httpBody.toString();

            // Calculate the signature of the http body
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Connect to WebAPI
            logger.info("[{}:{}:{}] Connecting to {}...", conn_id, conn_type, cloud_user, winServerName);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();
            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", CONTENT_TYPE);
            con.setRequestProperty("Authorization", "Bearer " + token);
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());
            os.close();

            logger.info("[{}:{}:{}] Send 'POST' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            logger.info("[{}:{}:{}] Connected to {}(response code={}). kill session api url = {}",
                        conn_id, conn_type, cloud_user, winServerName, responseCode, url);
            logger.info("[{}:{}:{}] ResponseMsg = {}", conn_id, conn_type, cloud_user, response.toString());
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to ApportoService, URL = {}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return responseCode == HttpsURLConnection.HTTP_OK ? 0 : 1;
    }

    /**
     * Kill session using RDP Router Service.
     *
     * @param conn_id - the connection ID
     * @param cloud_user - the value of "cloud_username" URL parameter
     * @param winServerName - the windows server name
     * @param url - the endpoint to kill session
     * @param token - bearer token for header
     * @param type - the name of user logged in server
     * @param killInfo - this indicates the session id if the type param is "SessionID",
     *               this indicates the user name if the type param is "UserName"
     *
     * @return Response - 0 if successful, 1 if fail
     */
    private static int killSessionUsingRouterService(String conn_id, String conn_type, String cloud_user,
                                 URL url, String farmName, String killInfo) {
        StringBuffer response = new StringBuffer();
        int responseCode = HttpURLConnection.HTTP_NOT_FOUND;

        try {
            // Add body with raw data
            JSONObject httpBody = new JSONObject();
            httpBody.put("username", killInfo);
            httpBody.put("farm_name", farmName);
            httpBody.put("connection_id", conn_id);
            String message = httpBody.toString();

            // Calculate the signature of the http body
            Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secret_key = new SecretKeySpec(SECRET_KEY_ROUTER.getBytes(), "HmacSHA256");
            sha256_HMAC.init(secret_key);
            String hash = Base64.encodeBase64String(sha256_HMAC.doFinal(message.getBytes()));

            // Connect to WebAPI
            logger.info("[{}:{}:{}] Connecting to {}...", conn_id, conn_type, cloud_user, farmName);
            HttpURLConnection con = getURLConnection(url);

            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", CONTENT_TYPE);
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setRequestProperty("X-Apporto-Webhook-Signature", hash);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.write(message.getBytes());
            os.close();

            logger.info("[{}:{}:{}] Send 'POST' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            responseCode = con.getResponseCode();

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            logger.info("[{}:{}:{}] Connected to {}(response code={}). kill session RDP router api url = {}",
                        conn_id, conn_type, cloud_user, farmName, responseCode, url);
            logger.info("[{}:{}:{}] ResponseMsg = {}", conn_id, conn_type, cloud_user, response.toString());
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception when connecting to RDP Router Service, URL = {}",
                        conn_id, conn_type, cloud_user, url.toString());
            logger.warn("[{}:{}:{}] Exception log: {}", conn_id, conn_type, cloud_user, e.toString());
        }

        return responseCode == HttpURLConnection.HTTP_OK ? 0 : 1;
    }

    /**
     * Sends idle statistics to Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param totalTime - Total session time
     * @param idleTime - User idle time
     */
    public static void sendIdleStats(GuacamoleConfiguration configuration,
                            String totalTime, String idleTime) {
        // Get the connection info
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        // Calculate the activity time
        String activityTime;
        try {
            activityTime = String.valueOf(Integer.parseInt(totalTime) - Integer.parseInt(idleTime));
        }
        catch (NumberFormatException e) {
            logger.warn("[{}:{}:{}] Exception when calculating activity time; activity time is set to zero.",
                        conn_id, conn_type, cloud_user);
            activityTime = "0";
        }

        // Check subdomain
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send statistics.",
                        conn_id, conn_type, cloud_user);
            return;
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Get "Http-Api-Key"
        HTTP_API_KEY = configuration.getParameter(HTTP_API_KEY_PARM);

        // Connect to WebAPI
        String url_str = String.format(idle_stats_url_template, subdomain,
                            conn_id, totalTime, activityTime, idleTime);

        try {
            URL url = new URL(url_str);
            sendStatus(url, conn_id, conn_type, cloud_user);
        }
        catch (MalformedURLException e) {
            logger.warn("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
    }

    /**
     * Sends event statistics to Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param events - Number of events recorded
     */
    public static String sendEventStats(GuacamoleConfiguration configuration, String events) {
        // Get the connection info
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        // Check subdomain
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send statistics.",
                        conn_id, conn_type, cloud_user);
            return "";
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Get "Http-Api-Key"
        HTTP_API_KEY = configuration.getParameter(HTTP_API_KEY_PARM);

        // Connect to WebAPI
        String url_str = String.format(event_stats_url_template, subdomain, conn_id, events);

        String response = "";
        try {
            URL url = new URL(url_str);
            response = sendStatus(url, conn_id, conn_type, cloud_user);
            logger.info("[{}:{}:{}] sendEventStats response = {}",
                        conn_id, conn_type, cloud_user, response);
        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response;
    }

    /**
     * Get user chart data from Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param timezone - Clients time zone
     */
    public static String getUserData(GuacamoleConfiguration configuration, String timezone) {
        return getChartData(user_data_url_template, configuration, timezone);
    }

    /**
     * Get group chart data from Drupal.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param timezone - Clients time zone
     */
    public static String getGroupData(GuacamoleConfiguration configuration, String timezone) {
        return getChartData(group_data_url_template, configuration, timezone);
    }

    /**
     * Get chart data from Drupal.
     *
     * @param chart_url - chart url to use
     * @param configuration - Guacamole configuration containing all parameters
     * @param timezone - Clients time zone
     */
    private static String getChartData(String chart_url,
                            GuacamoleConfiguration configuration, String timezone) {
        // Get the connection info
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        // Check subdomain
        String result = "";
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send statistics.",
                        conn_id, conn_type, cloud_user);
            return result;
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Get "Http-Api-Key"
        HTTP_API_KEY = configuration.getParameter(HTTP_API_KEY_PARM);

        // Connect to WebAPI
        String url_str = String.format(chart_url, subdomain, conn_id, timezone);
        logger.info("[{}:{}:{}] Requesting chart data: {}", conn_id, conn_type, cloud_user, url_str);

        try {
            URL url = new URL(url_str);
            result = sendStatus(url, conn_id, conn_type, cloud_user);
        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        return result;
    }

    /**
     * Get the classroom group data.
     *
     * @param configuration - Guacamole configuration containing all parameters
     * @param cloudUserName - The cloud name of the faculty
     * @param groupName - Classroom group name
     */
    public static String getClassroomGroupData(GuacamoleConfiguration configuration,
                            String cloudUserName, String groupName) {
        // Get the connection info
        String conn_id = configuration.getParameter(ID_PARM);
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        // Check subdomain
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        if (subdomain == null || subdomain.isEmpty()) {
            logger.warn("[{}:{}:{}] Drupal subdomain not specified, cannot send statistics.",
                        conn_id, conn_type, cloud_user);
            return "";
        }

        subdomain = GuacamoleCommonUtility.getFQDN(subdomain, DEFAULT_DOMAIN);

        // Connect to WebAPI
        String url_str = String.format(classroom_data_url_template, subdomain, cloudUserName);
        StringBuffer response = new StringBuffer();

        try {
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
            };

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Get "Http-Api-Key"
            HTTP_API_KEY = configuration.getParameter(HTTP_API_KEY_PARM);

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);

            // Connect to WebAPI
            URL url = new URL(url_str);
            HttpsURLConnection con = (HttpsURLConnection) url.openConnection();

            if(HTTP_API_KEY!= null && !HTTP_API_KEY.isEmpty()) {
                con.setRequestProperty("http-api-key", HTTP_API_KEY);
            }

            con.setRequestMethod("POST");
            con.setRequestProperty("Content-Type", CONTENT_TYPE);
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setRequestProperty("Group-Name", groupName);
            con.setDoOutput(true);

            OutputStream os = con.getOutputStream();
            os.close();

            logger.info("[{}:{}:{}] Send 'POST' request to URL: \"{}\"", conn_id, conn_type, cloud_user, url);
            int responseCode = con.getResponseCode();
            logger.info("[{}:{}:{}] Sent 'POST' request to URL: \"{}\"  Response Code : \"{}\"",
                        conn_id, conn_type, cloud_user, url, responseCode);

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
            String inputLine;
            response = new StringBuffer();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();

            // Print result
            if (!response.toString().isEmpty()) {
                logger.info("[{}:{}:{}] Response Result : \"{}\".", conn_id, conn_type, cloud_user,
                            response.toString());
            }
        }
        catch (MalformedURLException e) {
            logger.warn("[{}:{}:{}] MalformedURLException with http request, URL={}",
                        conn_id, conn_type, cloud_user, url_str);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        catch (ProtocolException e) {
            logger.warn("[{}:{}:{}] ProtocolException with http request, URL={}",
                        conn_id, conn_type, cloud_user, url_str);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        catch (IOException e) {
            logger.warn("[{}:{}:{}] IOException with http request, URL={}",
                        conn_id, conn_type, cloud_user, url_str);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }
        catch (Exception e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        return response.toString();
    }

    public static HttpURLConnection getURLConnection(URL url) throws Exception {
        HttpURLConnection con = (HttpURLConnection) url.openConnection();
        if (con instanceof HttpsURLConnection) {
            TrustManager[] trustAllCerts = new TrustManager[] {new X509TrustManager() {
                public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                    return null;
                }
                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }
                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }};

            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

            // Create all-trusting host name verifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };

            // Install the all-trusting host verifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            ((HttpsURLConnection) con).setSSLSocketFactory(sc.getSocketFactory());
            ((HttpsURLConnection) con).setHostnameVerifier(allHostsValid);

            return con;
        }
        return con;
    }
}
