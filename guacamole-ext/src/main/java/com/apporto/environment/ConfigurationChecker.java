/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.environment;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;

import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.GuacamoleException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Class that checks if all conditions for establishing a tunnel are met
 * and provides reporting capability to the outside server.
 * Currently it checks SFTP server availability and directory structure.
 */
public final class ConfigurationChecker {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChecker.class);

    private static final String check_port_cmd_template = "nc -z -w 1 %s %s";
    private static final String check_sftp_dir_cmd_template = System.getProperty("user.home") +
                                                              "/.guacamole/bin/check_sftp_dirs.sh %s %s %s %s %s";
    private static final String SFTP_ROOT_DIR = "/";

    /**
     * Default time for waiting to check the sftp directory structure.
     */
    private static final int DEFAULT_CHECK_SFTP_DIR_TIMEOUT = 15; // seconds

    /**
     * Default time for waiting to check the sftp directory structure.
     */
    private static int check_sftp_dir_timeout = DEFAULT_CHECK_SFTP_DIR_TIMEOUT;

    /**
     * Constructor
     */
    private ConfigurationChecker() {}

    /**
     * A name of the "check-sftp-dir-timeout" property
     */
    public static final IntegerGuacamoleProperty CHECK_SFTP_DIR_TIMEOUT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "check-sftp-dir-timeout";
        }
    };

    /**
     * Load the proper configuration variable(s)
     */
    static {
        try {
            Environment environment = new LocalEnvironment();
            Integer propValue;

            propValue = environment.getProperty(CHECK_SFTP_DIR_TIMEOUT);
            if (propValue != null) {
                check_sftp_dir_timeout = propValue.intValue();
                logger.debug("Read the `check-sftp-dir-timeout` property, check_sftp_dir_timeout={}",
                              check_sftp_dir_timeout);
            }
            else {
                logger.warn("The `check-sftp-dir-timeout` property doesn't exist in the configuration, " +
                            "please check /etc/guacamole/guacamole.properties.");
            }
        }
        catch (GuacamoleException e) {
            logger.warn("Couldn't read `check-sftp-dir-timeout` property from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    /**
     * Check if the SFTP root directory exists.
     *
     * @param hostname sftp server hostname
     * @param port sftp server port
     * @param username sftp server username
     * @param password sftp server password
     * @param conn_id the connection id
     * @param cloud_user the value of URL parameter "cloud_username"
     *
     * @return true if directory exists, false otherwise
     */
    public static boolean check_sftp_dir(String hostname, String port, String username,
                                         String password, String conn_id, String conn_type, String cloud_user) {
        return check_sftp_dir(hostname, port, username, password, SFTP_ROOT_DIR, conn_id, conn_type, cloud_user);
    }

    /**
     * Check if the SFTP directory exists.
     *
     * @param hostname sftp server hostname
     * @param port sftp server port
     * @param username sftp server username
     * @param password sftp server password
     * @param directory sftp server directory
     * @param conn_id the connection id
     * @param cloud_user the value of URL parameter "cloud_username"
     *
     * @return true if directory exists, false otherwise
     */
    public static boolean check_sftp_dir(String hostname, String port, String username, String password,
                                         String directory, String conn_id, String conn_type, String cloud_user) {
        boolean result=false;
        Process proc = null;

        try {
            String cmd = String.format(check_sftp_dir_cmd_template, username, password, hostname, port, directory);
            proc = Runtime.getRuntime().exec(cmd);
            proc.waitFor(check_sftp_dir_timeout, TimeUnit.SECONDS);
            result = proc.exitValue() == 0;
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception while checking SFTP directory structure, " +
                        "check if script check_sftp_dirs.sh exists in tomcat user home dir.",
                        conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        if (result) {
            logger.debug("[{}:{}:{}] The SFTP directory structure is CORRECT", conn_id, conn_type, cloud_user);
        }
        else {
            logger.warn("[{}:{}:{}] The SFTP directory structure is INVALID", conn_id, conn_type, cloud_user);
        }

        // Check if the `check_sftp_dirs.sh` is running
        if (proc != null && proc.isAlive()) {
            String pid = "";

            try {
                // Get the PID of check_sftp_dirs.sh
                Field field = proc.getClass().getDeclaredField("pid");
                field.setAccessible(true);
                pid = field.get(proc).toString();
                logger.warn("[{}:{}:{}] PID of check_sftp_dirs.sh is {}", conn_id, conn_type, cloud_user, pid);

                // Kill the process of check_sftp_dirs.sh and its subprocesses
                String cmd = String.format("pkill -TERM -P %s", pid);
                Process kill_proc = Runtime.getRuntime().exec(cmd);
                kill_proc.waitFor(1, TimeUnit.SECONDS);

                if(kill_proc.exitValue() == 0) {
                    logger.warn("[{}:{}:{}] Killed the check_sftp_dirs.sh with PID={}", conn_id, conn_type, cloud_user, pid);
                }
                else {
                    logger.error("[{}:{}:{}] Failed to Kill the check_sftp_dirs.sh with PID={}", conn_id, conn_type, cloud_user, pid);
                }
            }
            catch (IllegalAccessException | IllegalArgumentException | NoSuchFieldException | SecurityException e) {
                logger.error("[{}:{}:{}] Couldn't get the PID of check_sftp_dirs.sh, error message={}",
                        conn_id, conn_type, cloud_user, e.getMessage());
            }
            catch (IOException | InterruptedException e) {
                logger.error("[{}:{}:{}] Couldn't kill the check_sftp_dirs.sh with PID={}, error message={}",
                        conn_id, conn_type, cloud_user, pid, e.getMessage());
            }
        }

        return result;
    }

    /**
     * Check if the SFTP port is reachable on the target machine.
     *
     * @param hostname sftp server hostname
     * @param port sftp server port
     * @param conn_id the connection id
     * @param cloud_user the value of URL parameter "cloud_username"
     * @return true if port is reachable, false otherwise
     */
    public static boolean check_sftp_port(String hostname, String port, String conn_id, String conn_type, String cloud_user) {
        boolean result = false;
        Process proc;

        try {
            String cmd = String.format(check_port_cmd_template, hostname, port);
            proc = Runtime.getRuntime().exec(cmd);
            proc.waitFor();
            result = proc.exitValue() == 0;
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception while checking SFTP port availability, check if nc command exists on the system.",
                         conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        if (result) {
            logger.debug("[{}:{}:{}] The SFTP port is reachable", conn_id, conn_type, cloud_user);
        }
        else {
            logger.warn("[{}:{}:{}] The SFTP port can't be reached", conn_id, conn_type, cloud_user);
        }

        return result;
    }


    /**
     * Check if the RDP port is reachable on the target machine.
     *
     * @param hostname rdp server hostname
     * @param port rdp server port
     * @param conn_id the connection id
     * @param cloud_user the value of URL parameter "cloud_username"
     * @return true if port is reachable, false otherwise
     */
    public static boolean check_rdp_port(String hostname, String port, String conn_id, String conn_type, String cloud_user) {
        boolean result = false;
        Process proc;

        try {
            String cmd = String.format(check_port_cmd_template, hostname, port);
            proc = Runtime.getRuntime().exec(cmd);
            proc.waitFor();
            result = proc.exitValue() == 0;
        }
        catch (Exception e) {
            logger.warn("[{}:{}:{}] Exception while checking RDP port availability, check if nc command exists on the system.",
                         conn_id, conn_type, cloud_user);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, conn_type, cloud_user, e);
        }

        if (result) {
            logger.debug("[{}:{}:{}] The RDP port is reachable", conn_id, conn_type, cloud_user);
        }
        else {
            logger.warn("[{}:{}:{}] The RDP port can't be reached", conn_id, conn_type, cloud_user);
        }

        return result;
    }
}
