/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.environment;

public enum ApportoError {
    OK(0),
    FAILED(1),
    SFTP_NA(2),
    SFTP_DIRS(3),
    RDP_NA(4),
    SFTP_NOLOGIN(5);

    private final int id;

    ApportoError(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }
    
    public static ApportoError fromInt(int i) {
        switch(i) {
        case 0: return ApportoError.OK;
        case 1: return ApportoError.FAILED;
        case 2: return ApportoError.SFTP_NA;
        case 3: return ApportoError.SFTP_DIRS;
        case 4: return ApportoError.RDP_NA;
        case 5: return ApportoError.SFTP_NOLOGIN;
        default: return null;
        }
    }
    
    public static ApportoError fromString(String s) {
        return fromInt(Integer.parseInt(s));
    }
    
    @Override
    public String toString() {
        switch(this) {
        case OK: return "Success";
        case FAILED: return "Failed.";
        case SFTP_NA: return "SFTP_Server_not_available.";
        case SFTP_DIRS: return "SFTP_directory_structure_invalid.";
        case RDP_NA: return "RDP_Server_is_not_reachable.";
        case SFTP_NOLOGIN: return "Cannot_login_to_SFTP_server.";
        default: throw new IllegalArgumentException();
        }
    }
}
