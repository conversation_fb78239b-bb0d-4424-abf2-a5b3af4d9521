/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package org.apache.guacamole.environment;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleServerException;
import org.apache.guacamole.properties.GuacamoleProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SecretKeyEnvironment {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(SecretKeyEnvironment.class);

    /**
     * All properties read from secretkey.properties.
     */
    private final Properties properties;

    /**
     * The location of GUACAMOLE_HOME, which may not truly exist.
     */
    private final File guacHome;

    /**
     * Creates a new Environment, initializing that environment based on the
     * location of GUACAMOLE_HOME and the contents of secretkey.properties.
     *
     * @throws GuacamoleException If an error occurs while determining the
     * environment of this Guacamole instance.
     */
    public SecretKeyEnvironment() throws GuacamoleException {

        // Determine location of GUACAMOLE_HOME
        guacHome = findGuacamoleHome();
        logger.debug("GUACAMOLE_HOME is \"{}\".", guacHome.getAbsolutePath());

        // Read properties
        properties = new Properties();
        try {

            InputStream stream = null;

            logger.info("guacHome.isDirectory is: {}", guacHome.isDirectory());
            // If not a directory, load from classpath
            if (!guacHome.isDirectory()) {
                stream = SecretKeyEnvironment.class.getResourceAsStream("/secretkey.properties");
            } // Otherwise, try to load from file
            else {
                File propertiesFile = new File(guacHome, "secretkey.properties");
                if (propertiesFile.exists()) {
                    stream = new FileInputStream(propertiesFile);
                }
            }

            // Load properties from stream, if any, always closing stream when done
            if (stream != null) {
                try {
                    properties.load(stream);
                } finally {
                    stream.close();
                }
            } // Notify if we're proceeding without secretkey.properties
            else {
                logger.error("No secretkey.properties file found within GUACAMOLE_HOME or the classpath.");
            }

        } catch (IOException e) {
            logger.warn("The secretkey.properties file within GUACAMOLE_HOME cannot be read: {}", e.getMessage());
            logger.debug("Error reading secretkey.properties.", e);
        }
        
    }

    /**
     * Locates the Guacamole home directory by checking, in order: the
     * guacamole.home system property, the GUACAMOLE_HOME environment variable,
     * and finally the .guacamole directory in the home directory of the user
     * running the servlet container. If even the .guacamole directory doesn't
     * exist, then /etc/guacamole will be used.
     *
     * @return The File representing the Guacamole home directory, which may or
     * may not exist, and may turn out to not be a directory.
     */
    private static File findGuacamoleHome() {

        // Attempt to find Guacamole home
        File guacHome;

        // Use system property by default
        String desiredDir = System.getProperty("guacamole.home");

        // Failing that, try the GUACAMOLE_HOME environment variable
        if (desiredDir == null) {
            desiredDir = System.getenv("GUACAMOLE_HOME");
        }

        // If successful, use explicitly specified directory
        if (desiredDir != null) {
            guacHome = new File(desiredDir);
        } // If not explicitly specified, use standard locations
        else {

            // Try ~/.guacamole first
            guacHome = new File(System.getProperty("user.home"), ".guacamole");

            // If that doesn't exist, try /etc/guacamole if the /etc directory
            // exists on this system
            if (!guacHome.exists() && new File("/etc").exists()) {
                guacHome = new File("/etc/guacamole");
            }

        }

        // Return discovered directory
        return guacHome;

    }

    public File getGuacamoleHome() {
        return guacHome;
    }

    /**
     * Gets the string value for a property name.
     *
     * The value may come from either the OS environment (if property override
     * is enabled) or the Properties collection that was loaded from
     * secretkey.properties. When checking the environment for the named
     * property, the name is first transformed by converting all hyphens to
     * underscores and converting the string to upper case letter, in accordance
     * with common convention for environment strings.
     *
     * @param name The name of the property value to retrieve.
     *
     * @return The corresponding value for the property. If property override is
     * enabled and the value is found in the OS environment, the value from the
     * environment is returned. Otherwise, the value from secretkey.properties,
     * if any, is returned.
     */
    public String getPropertyValue(String name) {

            // Transform the name according to common convention
            final String envName = name.replace('-', '_').toUpperCase();
            final String envValue = System.getenv(envName);

            if (envValue != null) {
                return envValue;
            }

        return properties.getProperty(name);
    }

    /**
     * Given a GuacamoleProperty, parses and returns the value set for that
     * property in secretkey.properties, if any.
     *
     * @param <Type> The type that the given property is parsed into.
     * @param property The property to read from secretkey.properties.
     * @return The parsed value of the property as read from
     * secretkey.properties.
     * @throws GuacamoleException If an error occurs while parsing the value for
     * the given property in secretkey.properties.
     */
    public <Type> Type getProperty(GuacamoleProperty<Type> property) throws GuacamoleException {
        return property.parseValue(getPropertyValue(property.getName()));
    }

    /**
     * Given a GuacamoleProperty, parses and returns the value set for that
     * property in secretkey.properties, if any. If no value is found, the
     * provided default value is returned.
     *
     * @param <Type> The type that the given property is parsed into.
     * @param property The property to read from secretkey.properties.
     * @param defaultValue The value to return if no value was given in
     * secretkey.properties.
     * @return The parsed value of the property as read from
     * secretkey.properties, or the provided default value if no value was
     * found.
     * @throws GuacamoleException If an error occurs while parsing the value for
     * the given property in secretkey.properties.
     */
    public <Type> Type getProperty(GuacamoleProperty<Type> property,
            Type defaultValue) throws GuacamoleException {

        Type value = getProperty(property);
        if (value == null) {
            return defaultValue;
        }

        return value;

    }

    /**
     * Given a GuacamoleProperty, parses and returns the value set for that
     * property in secretkey.properties. An exception is thrown if the value is
     * not provided.
     *
     * @param <Type> The type that the given property is parsed into.
     * @param property The property to read from secretkey.properties.
     * @return The parsed value of the property as read from
     * secretkey.properties.
     * @throws GuacamoleException If an error occurs while parsing the value for
     * the given property in secretkey.properties, or if the property is not
     * specified.
     */
    public <Type> Type getRequiredProperty(GuacamoleProperty<Type> property)
            throws GuacamoleException {

        Type value = getProperty(property);
        if (value == null) {
            throw new GuacamoleServerException("Property " + property.getName() + " is required.");
        }

        return value;

    }

}
