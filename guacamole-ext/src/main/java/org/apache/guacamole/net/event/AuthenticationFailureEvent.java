/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.net.event;

import org.apache.guacamole.net.auth.Credentials;

/**
 * An event which is triggered whenever a user's credentials fail to be
 * authenticated. The credentials that failed to be authenticated are included
 * within this event, and can be retrieved using getCredentials().
 */
public class AuthenticationFailureEvent implements CredentialEvent {

    /**
     * The credentials which failed authentication.
     */
    private Credentials credentials;

    /**
     * Creates a new AuthenticationFailureEvent which represents the failure
     * to authenticate the given credentials.
     *
     * @param credentials The credentials which failed authentication.
     */
    public AuthenticationFailureEvent(Credentials credentials) {
        this.credentials = credentials;
    }

    @Override
    public Credentials getCredentials() {
        return credentials;
    }

}
