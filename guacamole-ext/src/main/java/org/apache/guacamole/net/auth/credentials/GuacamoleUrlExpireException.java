/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

 package org.apache.guacamole.net.auth.credentials;

import org.apache.guacamole.GuacamoleClientException;

/**
  * A security-related exception thrown when access is denied to a user because
  * the provided url was expired.
  */
public class GuacamoleUrlExpireException extends GuacamoleClientException {
 
    /**
     * The subdomain of the Appstore.
     */
    private final String subdomain;

    /**
     * Creates a new GuacamoleUrlExpireException with the given
    * message and subdomain of the Appstore.
    *
    * @param message
    *     A human readable description of the exception that occurred.
    *
    * @param subdomain
    *     The subdomain of the Appstore.
    */
    public GuacamoleUrlExpireException(String subdomain, String message) {
        super(message);
        this.subdomain = subdomain;
    }

    /**
     * Returns the subdomain of the Appstore.
     *
     * @return
     *     The subdomain of the Appstore.
     */
    public String getSubdomain() {
        return subdomain;
    }
 
 }
 