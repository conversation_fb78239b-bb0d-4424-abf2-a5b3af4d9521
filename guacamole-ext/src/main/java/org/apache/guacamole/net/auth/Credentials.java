/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.net.auth;

import java.io.Serializable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import java.util.Map;
import java.util.HashMap;

/**
 * Simple arbitrary set of credentials, including a username/password pair,
 * the HttpServletRequest associated with the request for authorization
 * (if any) and the HttpSession associated with that request.
 *
 * This class is used along with AuthenticationProvider to provide arbitrary
 * HTTP-based authentication for Guacamole.
 */
public class Credentials implements Serializable {

    /**
     * Unique identifier associated with this specific version of Credentials.
     */
    private static final long serialVersionUID = 1L;

    /**
     * An arbitrary username.
     */
    private String username;

    /**
     * An arbitrary password.
     */
    private String password;

    /**
     * The address of the client end of the connection which provided these
     * credentials, if known.
     */
    private String remoteAddress;

    /**
     * The value of URL parameter "id".
     */
    private String connId;

    /**
     * The value of URL parameter "connection_type".
     */
    private String connType;

    /**
     * The value of URL parameter "cloud_username".
     */
    private String cloudUserName;

    /**
     * The value of URL parameter "username".
     */
    protected String user_name;

    /**
     * The value of URL parameter "nid".
     */
    protected String nid;

    /**
     * The value of URL parameter "subdomain".
     */
    protected String subdomain;

    /**
     * The value of URL parameter "Http-Api-Key".
     */
    protected String httpApiKey;

    /**
     * The value of URL parameter "user-id".
     */
    protected String userId;

    /**
     * The value of URL parameter "payload_version".
     */
    protected String payloadVersion;

    /**
     * The hostname or, if the hostname cannot be determined, the address of
     * the client end of the connection which provided these credentials, if
     * known.
     */
    private String remoteHostname;

    /**
     * The HttpServletRequest carrying additional credentials, if any.
     */
    private transient HttpServletRequest request;

    /**
     * The HttpSession carrying additional credentials, if any.
     */
    private transient HttpSession session;

    /**
     * Construct a Credentials object with the given username, password,
     * and HTTP request.  The information is assigned to the various
     * storage objects, and the remote hostname and address is parsed out
     * of the request object.
     * 
     * @param username
     *     The username that was provided for authentication.
     * 
     * @param password
     *     The password that was provided for authentication.
     * 
     * @param request 
     *     The HTTP request associated with the authentication
     *     request.
     */
    public Credentials(String username, String password, HttpServletRequest request) {

        this.username = username;
        this.password = password;
        this.request = request;

        // Set the remote address
        this.remoteAddress = request.getRemoteAddr();

        // Get the remote hostname
        this.remoteHostname = request.getRemoteHost();

        // If session exists get it, but don't create a new one.
        this.session = request.getSession(false);

    }
    
    /**
     * Returns the password associated with this set of credentials.
     *
     * @return The password associated with this username/password pair, or
     *         null if no password has been set.
     */
    public String getPassword() {
        return password;
    }

    /**
     * Sets the password associated with this set of credentials.
     *
     * @param password The password to associate with this username/password
     *                 pair.
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * Returns the username associated with this set of credentials.
     *
     * @return The username associated with this username/password pair, or
     *         null if no username has been set.
     */
    public String getUsername() {
        return username;
    }

    /**
     * Sets the username associated with this set of credentials.
     *
     * @param username The username to associate with this username/password
     *                 pair.
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * Returns the HttpServletRequest associated with this set of credentials.
     *
     * @return The HttpServletRequest associated with this set of credentials,
     *         or null if no such request exists.
     */
    public HttpServletRequest getRequest() {
        return request;
    }

    /**
     * Sets the HttpServletRequest associated with this set of credentials.
     *
     * @param request  The HttpServletRequest to associated with this set of
     *                 credentials.
     */
    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    /**
     * Returns the HttpSession associated with this set of credentials.
     *
     * @return The HttpSession associated with this set of credentials, or null
     *         if no such request exists.
     */
    public HttpSession getSession() {
        return session;
    }

    /**
     * Sets the HttpSession associated with this set of credentials.
     *
     * @param session The HttpSession to associated with this set of
     *                credentials.
     */
    public void setSession(HttpSession session) {
        this.session = session;
    }

    /**
     * Returns the address of the client end of the connection which provided
     * these credentials, if known.
     *
     * @return
     *     The address of the client end of the connection which provided these
     *     credentials, or null if the address is not known.
     */
    public String getRemoteAddress() {
        return remoteAddress;
    }

    /**
     * Sets the address of the client end of the connection which provided
     * these credentials.
     *
     * @param remoteAddress
     *     The address of the client end of the connection which provided these
     *     credentials, or null if the address is not known.
     */
    public void setRemoteAddress(String remoteAddress) {
        this.remoteAddress = remoteAddress;
    }

    /**
     * Returns the hostname of the client end of the connection which provided
     * these credentials, if known. If the hostname of the client cannot be
     * determined, but the address is known, the address may be returned
     * instead.
     *
     * @return
     *     The hostname or address of the client end of the connection which
     *     provided these credentials, or null if the hostname is not known.
     */
    public String getRemoteHostname() {
        return remoteHostname;
    }

    /**
     * Sets the hostname of the client end of the connection which provided
     * these credentials, if known. If the hostname of the client cannot be
     * determined, but the address is known, the address may be specified
     * instead.
     *
     * @param remoteHostname
     *     The hostname or address of the client end of the connection which
     *     provided these credentials, or null if the hostname is not known.
     */
    public void setRemoteHostname(String remoteHostname) {
        this.remoteHostname = remoteHostname;
    }

    /**
     * Returns the value of URL parameter "id" associated with this set of credentials.
     *
     * @return The id associated with this session, or
     *         null if no "id" has been set.
     */
    public String getConnId() {
        return connId;
    }

    /**
     * Sets the value of URL parameter "id" associated with this set of credentials.
     *
     * @param connId The value of URL parameter "id" to associate with this session
     */
    public void setConnId(String connId) {
        this.connId = connId;
    }

    /**
     * Returns the value of URL parameter "connection_type" associated with this set of credentials.
     *
     * @return The session type associated with this session, or
     *         null if no "id" has been set.
     */
    public String getConnType() {
        return connType;
    }

    /**
     * Sets the value of URL parameter "connection_type" associated with this set of credentials.
     *
     * @param connType The value of URL parameter "connection_type" to associate with this session
     */
    public void setConnType(String connType) {
        this.connType = connType;
    }

    /**
     * Returns the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @return The cloud_username associated with this session, or
     *         null if no "cloud_username" has been set.
     */
    public String getCloudUserName() {
        return cloudUserName;
    }

    /**
     * Sets the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @param cloudUserName The value of URL parameter "cloud_username" to associate with this session
     */
    public void setCloudUserName(String cloudUserName) {
        this.cloudUserName = cloudUserName;
    }

    /**
     * Returns the value of URL parameter "username" associated with this set of credentials.
     *
     * @return The user_name associated with this session, or
     *         null if no "username" has been set.
     */
    public String getUserName() {
        return user_name;
    }

    /**
     * Sets the value of URL parameter "username" associated with this set of credentials.
     *
     * @param username The value of URL parameter "username" to associate with this session
     */
    public void setUserName(String username) {
        this.user_name = username;
    }

    /**
     * Returns the value of URL parameter "nid" associated with this set of credentials.
     *
     * @return The nid associated with this session, or
     *         null if no "nid" has been set.
     */
    public String getNid() {
        return nid;
    }

    /**
     * Sets the value of URL parameter "nid" associated with this set of credentials.
     *
     * @param nid The value of URL parameter "nid" to associate with this session
     */
    public void setNid(String nid) {
        this.nid = nid;
    }

    /**
     * Returns the value of URL parameter "subdomain" associated with this set of credentials.
     *
     * @return The subdomain associated with this session, or
     *         null if no "subdomain" has been set.
     */
    public String getSubdomain() {
        return subdomain;
    }

    /**
     * Sets the value of URL parameter "subdomain" associated with this set of credentials.
     *
     * @param subdomain The value of URL parameter "subdomain" to associate with this session
     */
    public void setSubdomain(String subdomain) {
        this.subdomain = subdomain;
    }

    /**
     * Returns the value of URL parameter "Http-Api-Key" associated with this set of credentials.
     *
     * @return The httpApiKey associated with this session, or
     *         null if no "Http-Api-Key" has been set.
     */
    public String getHttpApiKey() {
        return httpApiKey;
    }

    /**
     * Sets the value of URL parameter "Http-Api-Key" associated with this set of credentials.
     *
     * @param httpApiKey The value of URL parameter "Http-Api-Key" to associate with this session
     */
    public void setHttpApiKey(String httpApiKey) {
        this.httpApiKey = httpApiKey;
    }

    /**
     * Returns the value of URL parameter "user-id" associated with this set of credentials.
     *
     * @return The userId associated with this session, or
     *         null if no "user-id" has been set.
     */
    public String getUserId() {
        return userId;
    }

    /**
     * Sets the value of URL parameter "user-id" associated with this set of credentials.
     *
     * @param userId The value of URL parameter "user-id" to associate with this session
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * Sets the value of URL parameter "payload_version" associated with this set of credentials.
     *
     * @param payloadVersion The value of URL parameter "payload_version" to associate with this session
     */
    public void setPayloadVersion(String payloadVersion) {
        this.payloadVersion = payloadVersion;
    }

    /**
     * Returns a map which contains parameter name/value pairs held by this set of credentials
     * as key/value pairs.
     *
     * @return
     *     A map which contains all parameter name/value pairs as key/value
     *     pairs.
     */
    public Map<String, String> getParameters() {
        Map<String, String> parameters = new HashMap<String, String>();
        parameters.put("id", connId);
        parameters.put("cloud_username", cloudUserName);
        parameters.put("connection_type", connType);
        parameters.put("username", user_name);
        parameters.put("nid", nid);
        parameters.put("subdomain", subdomain);
        parameters.put("Http-Api-Key", httpApiKey);
        parameters.put("user-id", userId);
        parameters.put("payload_version", payloadVersion);

        return parameters;
    }

}
