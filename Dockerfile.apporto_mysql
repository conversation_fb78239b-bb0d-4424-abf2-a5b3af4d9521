# syntax=docker/dockerfile:experimental
# vim: set ft=dockerfile expandtab ts=4 sw=4:

#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

#------------------------------------------------------------
# Customize mysql instance for us
#------------------------------------------------------------
FROM mysql/mysql-server:5.7

# Only need initial schema since we're building from scratch each deployment.
COPY extensions/guacamole-auth-jdbc/modules/guacamole-auth-jdbc-mysql/schema/ /docker-entrypoint-initdb.d/

#------------------------------------------------------------
# Runtime Configruation:
#------------------------------------------------------------
#
# Set the following ENV variables:
#   MYSQL_ROOT_PASSWORD
#   MYSQL_DATABASE
#   MYSQL_USER
#   MYSQL_PASSWORD
#
# Root password will be set according to variable.  A database will be
# created named after MYSQL_DATABASE and MYSQL_USER/MYSQL_PASSWORD will
# be granted all rights to MYSQL_DATABASE.
#
# *.sh, *.sql, *.sql.gz will be loaded automatically by /docker-entrypoint-initdb.d/
#
# Can mount /var/lib/mysql to gain persistence of the database.
