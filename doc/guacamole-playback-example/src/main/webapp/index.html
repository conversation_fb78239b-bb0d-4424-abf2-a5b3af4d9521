<!DOCTYPE HTML>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<html>

    <head>
        <title>Guacamole Recording Playback (EXAMPLE)</title>
        <meta charset="utf-8"/>
        <link rel="stylesheet" type="text/css" href="playback.css">
    </head>

    <body>

        <!-- Guacamole recording player -->
        <div id="player">

            <!-- Player display -->
            <div id="display">
                <div class="notification-container">
                    <div class="seek-notification">
                        <p>
                            Seek in progress...
                            <button id="cancel-seek">Cancel</button>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Player controls -->
            <div class="controls">
                <button id="play-pause">Play</button>
                <input id="position-slider" type="range">
                <span id="position">00:00</span>
                <span>/</span>
                <span id="duration">00:00</span>
            </div>

        </div>

        <!-- Guacamole JavaScript API -->
        <script type="text/javascript"
            src="guacamole-common-js/all.min.js"></script>

        <!-- -->
        <script type="text/javascript" src="playback.js"></script>

    </body>

</html>
