
------------------------------------------------------------
 Contributing to Apache Guacamole
------------------------------------------------------------

Thank you for contributing to the Apache Guacamole project!

There are certain procedures that must be followed for all contributions. These
procedures are necessary to allow us to allocate resources for reviewing and
testing your contribution, as well as communicate effectively with you during
the review process.

1) Create an issue in our JIRA

    All changes to Guacamole must have corresponding issues in JIRA so the
    change can be properly tracked:

        https://issues.apache.org/jira/browse/GUACAMOLE/

    If you do not already have an account on the Apache Software Foundation's
    JIRA, you will need to create one before creating your new issue.

2) Make and test your changes locally

    The Guacamole source is maintained in git repositories hosted on GitHub:

        https://github.com/apache/guacamole-client
        https://github.com/apache/guacamole-manual
        https://github.com/apache/guacamole-server
        https://github.com/apache/guacamole-website

    To make your changes, fork the applicable repositories and make commits
    to a topic branch in your fork. Commits should be made in logical units
    and must reference the JIRA issue number:

    $ git commit -m "GUACAMOLE-123: High-level message describing the changes."

    Avoid commits which cover multiple, distinct goals that could (and should)
    be handled separately.

    If you do not already have an account on GitHub, you will need to create
    one before making your changes.

3) Submit your changes via a pull request on GitHub

    Once your changes are ready, submit them by creating a pull request for
    the corresponding topic branch you created when you began working on your
    changes.

    The Guacamole team will then review your changes and, if they pass review,
    your changes will be merged.

