<?xml version="1.0" encoding="UTF-8"?>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
                        http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>org.apache.guacamole</groupId>
    <artifactId>guacamole</artifactId>
    <packaging>war</packaging>
    <version>1.1.0</version>
    <name>guacamole</name>
    <url>http://guacamole.apache.org/</url>

    <description>
        The Guacamole web application, providing authentication and an HTML5
        remote desktop client.
    </description>

    <!-- All applicable licenses -->
    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <!-- Git repository -->
    <scm>
        <url>https://github.com/apache/guacamole-client</url>
        <connection>scm:git:https://git.wip-us.apache.org/repos/asf/guacamole-client.git</connection>
    </scm>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <gitversion>${git.commit.id.abbrev}</gitversion>
    </properties>

    <profiles>
        <profile>
            <activation>
                <property>
                    <name>!prod</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.samaxes.maven</groupId>
                        <artifactId>minify-maven-plugin</artifactId>
                        <version>1.7.5</version>
                        <executions>
                            <execution>
                                <id>default-cli</id>
                                <configuration>
                                    <closureCompilationLevel>SIMPLE_OPTIMIZATIONS</closureCompilationLevel>
                                </configuration>
                            </execution>
                        </executions>
                        <dependencies>
                            <dependency>
                                <groupId>org.codehaus.plexus</groupId>
                                <artifactId>plexus-utils</artifactId>
                                <version>3.5.1</version>
                            </dependency>
                        </dependencies>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <build>
        <plugins>

            <!-- Written for Java 1.8 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.3</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-Werror</arg>
                    </compilerArgs>
                    <fork>true</fork>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.4.0</version>
                <configuration>
                    <webResources>
                        <!-- Filter translation strings -->
                        <resource>
                            <directory>src/main/webapp</directory>
                            <filtering>true</filtering>
                            <includes>
                                <include>translations/*.json</include>
                                <include>index.html</include>
                            </includes>
                        </resource>

                        <!-- Include all licenses within META-INF -->
                        <resource>
                            <directory>src/licenses</directory>
                            <targetPath>META-INF</targetPath>
                        </resource>
                    </webResources>
                    <!-- Add files from guacamole-common-js -->
                    <overlays>
                        <overlay>
                            <groupId>org.apache.guacamole</groupId>
                            <artifactId>guacamole-common-js</artifactId>
                            <type>zip</type>
                        </overlay>
                    </overlays>
                </configuration>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>exploded</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-utils</artifactId>
                        <version>3.5.1</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- Pre-cache Angular templates with maven-angular-plugin -->
            <plugin>
                <groupId>com.keithbranton.mojo</groupId>
                <artifactId>angular-maven-plugin</artifactId>
                <version>0.3.2</version>
                <executions>
                    <execution>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>html2js</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <sourceDir>${basedir}/src/main/webapp/app/</sourceDir>
                    <include>**/*.html</include>
                    <target>${basedir}/src/main/webapp/generated/templates-main/templates.js</target>
                    <prefix>app</prefix>
                </configuration>
            </plugin>

            <!-- JS/CSS Minification Plugin -->
            <plugin>
                <groupId>com.samaxes.maven</groupId>
                <artifactId>minify-maven-plugin</artifactId>
                <version>1.7.5</version>
                <executions>
                    <execution>
                        <id>default-cli</id>
                        <configuration>
                            <charset>UTF-8</charset>

                            <webappSourceDir>${project.build.directory}/${project.build.finalName}</webappSourceDir>

                            <cssSourceDir>/</cssSourceDir>
                            <cssTargetDir>/</cssTargetDir>
                            <cssFinalFile>guacamole.css</cssFinalFile>

                            <cssSourceFiles>
                                <cssSourceFile>license.txt</cssSourceFile>
                            </cssSourceFiles>
                            
                            <cssSourceIncludes>
                                <cssSourceInclude>app/**/*.css</cssSourceInclude>
                            </cssSourceIncludes>

                            <jsSourceDir>/</jsSourceDir>
                            <jsTargetDir>/</jsTargetDir>
                            <jsFinalFile>guacamole.js</jsFinalFile>
                            
                            <jsSourceFiles>
                                <jsSourceFile>license.txt</jsSourceFile>
                                <jsSourceFile>guacamole-common-js/all.js</jsSourceFile>
                            </jsSourceFiles>

                            <jsSourceIncludes>
                                <jsSourceInclude>app/**/*.js</jsSourceInclude>
                                <jsSourceInclude>generated/**/*.js</jsSourceInclude>
                            </jsSourceIncludes>

                            <!-- Do not minify and include tests -->
                            <jsSourceExcludes>
                                <jsSourceExclude>**/*.test.js</jsSourceExclude>
                            </jsSourceExcludes>
                            <jsEngine>CLOSURE</jsEngine>

                            <!-- Disable warnings for JSDoc annotations -->
                            <closureWarningLevels>
                                <misplacedTypeAnnotation>OFF</misplacedTypeAnnotation>
                                <nonStandardJsDocs>OFF</nonStandardJsDocs>
                            </closureWarningLevels>

                        </configuration>
                        <goals>
                            <goal>minify</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.codehaus.plexus</groupId>
                        <artifactId>plexus-utils</artifactId>
                        <version>3.5.1</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- Verify format using Apache RAT -->
            <plugin>
                <groupId>org.apache.rat</groupId>
                <artifactId>apache-rat-plugin</artifactId>
                <version>0.12</version>

                <configuration>
                    <excludes>
                        <exclude>src/main/webapp/app/*/templates/*.html</exclude>
                        <exclude>**/*.json</exclude>
                        <exclude>src/licenses/**/*</exclude>
                    </excludes>
                </configuration>

                <!-- Bind RAT to validate phase -->
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>

            </plugin>

            <!-- Enable git commit id -->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.4</version>
                <executions>
                    <execution>
                        <id>get-the-git-infos</id>
                        <goals>
                            <goal>revision</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
                    <prefix>git</prefix>
                    <verbose>false</verbose>
                    <injectAllReactorProjects>true</injectAllReactorProjects>
                    <generateGitPropertiesFile>true</generateGitPropertiesFile>
                    <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
                    <format>json</format>
                    <gitDescribe>
                        <skip>false</skip>
                        <always>false</always>
                        <dirty>-dirty</dirty>
                    </gitDescribe>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>

        <!-- Java servlet API -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>2.5</version>
            <scope>provided</scope>
        </dependency>

        <!-- JSR 356 WebSocket API -->
        <dependency>
            <groupId>javax.websocket</groupId>
            <artifactId>javax.websocket-api</artifactId>
            <version>1.0</version>
            <scope>provided</scope>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.32</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.13</version>
        </dependency>
        
        <!-- Guacamole Java API -->
        <dependency>
            <groupId>org.apache.guacamole</groupId>
            <artifactId>guacamole-common</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- Guacamole Extension API -->
        <dependency>
            <groupId>org.apache.guacamole</groupId>
            <artifactId>guacamole-ext</artifactId>
            <version>1.1.0</version>
        </dependency>

        <!-- Guacamole JavaScript API -->
        <dependency>
            <groupId>org.apache.guacamole</groupId>
            <artifactId>guacamole-common-js</artifactId>
            <version>1.1.0</version>
            <type>zip</type>
            <scope>runtime</scope>
        </dependency>

        <!-- WebJars servlet (for serving org.webjars.* resources) -->
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>webjars-servlet-2.x</artifactId>
            <version>1.5</version>
        </dependency>

        <!-- Polyfills for the Blob and FileSaver APIs -->
        <dependency>
            <groupId>org.webjars.npm</groupId>
            <artifactId>blob-polyfill</artifactId>
            <version>5.0.20210201</version>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>filesaver</artifactId>
            <version>1.3.3</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Lodash -->
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>lodash</artifactId>
            <version>4.17.21</version>
            <scope>runtime</scope>
        </dependency>

        <!-- jQuery -->
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>jquery</artifactId>
            <version>3.3.1</version>
            <scope>runtime</scope>
        </dependency>

        <!-- AngularJS -->
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular</artifactId>
            <version>1.8.2</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-cookies</artifactId>
            <version>1.6.9</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-route</artifactId>
            <version>1.8.2</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-touch</artifactId>
            <version>1.8.2</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Shim allowing arbitrary retrieval/creation order for AngularJS modules -->
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-module-shim</artifactId>
            <version>0.0.4</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.webjars.bower</groupId>
                    <artifactId>angular</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Translation support for AngularJS -->
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-translate</artifactId>
            <version>2.18.3</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-translate-interpolation-messageformat</artifactId>
            <version>2.18.3</version>
            <scope>runtime</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.webjars.bower</groupId>
                    <artifactId>messageformat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>messageformat</artifactId>
            <version>1.0.2</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.bower</groupId>
            <artifactId>angular-translate-loader-static-files</artifactId>
            <version>2.18.3</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Polyfill for <datalist> element support -->
        <dependency>
            <groupId>org.webjars.npm</groupId>
            <artifactId>datalist-polyfill</artifactId>
            <version>1.24.4</version>
            <scope>runtime</scope>
        </dependency>

        <!-- Jetty 8 servlet API (websocket)  -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-websocket</artifactId>
            <version>8.1.1.v20120215</version>
            <scope>provided</scope>
        </dependency>

        <!-- Jetty 9.0 servlet API (websocket)  -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-parent</artifactId>
            <version>20</version>
            <scope>provided</scope>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>websocket-api</artifactId>
            <version>9.0.7.v20131107</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty.websocket</groupId>
            <artifactId>websocket-servlet</artifactId>
            <version>9.0.7.v20131107</version>
            <scope>provided</scope>
        </dependency>

        <!-- Tomcat servlet API (websocket)  -->
        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-catalina</artifactId>
            <version>[9.0.96,)</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.tomcat</groupId>
            <artifactId>tomcat-coyote</artifactId>
            <version>7.0.109</version>
            <scope>provided</scope>
        </dependency>

        <!-- Guice - Dependency Injection -->
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
            <version>3.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.inject.extensions</groupId>
            <artifactId>guice-assistedinject</artifactId>
            <version>3.0</version>
        </dependency>

        <!-- Guice Servlet -->
        <dependency>
            <groupId>com.google.inject.extensions</groupId>
            <artifactId>guice-servlet</artifactId>
            <version>3.0</version>
        </dependency>

        <!-- Jersey - JAX-RS Implementation -->
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-server</artifactId>
            <version>1.17.1</version>
        </dependency>

        <!-- Jersey - Guice extension -->
        <dependency>
            <groupId>com.sun.jersey.contribs</groupId>
            <artifactId>jersey-guice</artifactId>
            <version>1.17.1</version>
        </dependency> 
        
        <!-- JSR-250 annotations -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>jsr250-api</artifactId>
            <version>1.0</version>
        </dependency>
        
        <!-- Jackson for JSON support -->
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-json</artifactId>
            <version>1.17.1</version>

            <!-- Exclude StAX API, which is part of Java 6 -->
            <exclusions>
                <exclusion>
                    <groupId>javax.xml.stream</groupId>
                    <artifactId>stax-api</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <!-- Guava Base Libraries -->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>[32.0.0,)</version>
        </dependency>

        <!-- JSON Manipulation -->
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!-- JSTZ for TimeZone Detection -->
        <dependency>
            <groupId>org.webjars.npm</groupId>
            <artifactId>jstz</artifactId>
            <version>2.1.1</version>
        </dependency>

        <!-- Pickr (JavaScript color picker) -->
        <dependency>
            <groupId>org.webjars.npm</groupId>
            <artifactId>simonwep__pickr</artifactId>
            <version>1.8.2</version>
        </dependency>

    </dependencies>

</project>
