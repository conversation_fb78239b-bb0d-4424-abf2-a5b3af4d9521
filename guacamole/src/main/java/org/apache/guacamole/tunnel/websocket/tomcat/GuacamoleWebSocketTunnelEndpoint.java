/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

 package org.apache.guacamole.tunnel.websocket.tomcat;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import javax.servlet.http.HttpServletRequest;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleConnectionClosedException;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.protocol.GuacamoleInstruction;
import org.apache.guacamole.protocol.GuacamoleStatus;
import org.apache.guacamole.tunnel.TunnelRequest;
import org.apache.guacamole.tunnel.http.HTTPTunnelRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * WebSocket endpoint for Guacamole, using the Java WebSocket API.
 */
@ServerEndpoint(value = "/websocket-tunnel")
public abstract class GuacamoleWebSocketTunnelEndpoint {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(GuacamoleWebSocketTunnelEndpoint.class);

    /**
     * Active WebSocket sessions mapped to their respective tunnels.
     */
    private static final ConcurrentHashMap<Session, GuacamoleTunnel> tunnels = new ConcurrentHashMap<>();

    /**
     * Called when a new WebSocket connection is established.
     *
     * @param session
     *     The WebSocket session associated with the new connection.
     *
     * @param config
     *     The endpoint configuration.
     */
    @OnOpen
    public void onOpen(Session session, EndpointConfig config) {
        try {
            HttpServletRequest request = (HttpServletRequest) config.getUserProperties().get(HttpServletRequest.class.getName());
            TunnelRequest tunnelRequest = new HTTPTunnelRequest(request);

            // Create a new tunnel
            GuacamoleTunnel tunnel = doConnect(tunnelRequest);
            if (tunnel == null) {
                sendClose(session, GuacamoleStatus.RESOURCE_NOT_FOUND);
                return;
            }

            // Store tunnel for this session
            tunnels.put(session, tunnel);

            // Send tunnel UUID
            sendText(session, new GuacamoleInstruction(
                GuacamoleTunnel.INTERNAL_DATA_OPCODE,
                tunnel.getUUID().toString()
            ).toString());

            // Start reading from the tunnel in a separate thread
            new Thread(() -> tunnelReadLoop(session, tunnel)).start();
        }
        catch (GuacamoleException e) {
            logger.error("Failed to establish WebSocket tunnel: {}", e.getMessage());
            sendClose(session, e.getStatus());
        }
    }

    /**
     * Reads data from the tunnel and sends it via WebSocket to the client.
     *
     * @param session
     *     The WebSocket session associated with the tunnel.
     *
     * @param tunnel
     *     The GuacamoleTunnel to read data from.
     */
    private void tunnelReadLoop(Session session, GuacamoleTunnel tunnel) {
        try {
            char[] buffer = new char[8192];
            StringBuilder message = new StringBuilder();
            while (true) {
                char[] data = tunnel.acquireReader().read();
                if (data == null) break;

                message.append(data);

                // Send if buffer is full or no more data is available
                if (message.length() >= buffer.length || !tunnel.acquireReader().available()) {
                    sendText(session, message.toString());
                    message.setLength(0);
                }
            }

            // Close the tunnel gracefully
            sendClose(session, GuacamoleStatus.SUCCESS);
        }
        catch (GuacamoleConnectionClosedException e) {
            logger.debug("Tunnel connection closed.", e);
        }
        catch (GuacamoleException e) {
            logger.error("Error in WebSocket tunnel read loop.", e);
            sendClose(session, GuacamoleStatus.SERVER_ERROR);
        }
    }

    /**
     * Called when a WebSocket message is received from the client.
     *
     * @param session
     *     The WebSocket session that sent the message.
     *
     * @param message
     *     The message received.
     */
    @OnMessage
    public void onMessage(Session session, String message) {
        GuacamoleTunnel tunnel = tunnels.get(session);
        if (tunnel == null) return;

        try {
            tunnel.acquireWriter().write(message.toCharArray());
        }
        catch (GuacamoleConnectionClosedException e) {
            logger.debug("Tunnel connection closed.", e);
        }
        catch (GuacamoleException e) {
            logger.error("Error writing to tunnel.", e);
        }
    }

    /**
     * Called when a WebSocket connection is closed.
     *
     * @param session
     *     The session that was closed.
     *
     * @param reason
     *     The reason the connection was closed.
     */
    @OnClose
    public void onClose(Session session, CloseReason reason) {
        GuacamoleTunnel tunnel = tunnels.remove(session);
        if (tunnel != null) {
            try {
                tunnel.close();
            }
            catch (GuacamoleException e) {
                logger.error("Unable to close tunnel.", e);
            }
        }
    }

    /**
     * Called when an error occurs on the WebSocket connection.
     *
     * @param session
     *     The session that encountered the error.
     *
     * @param throwable
     *     The error that occurred.
     */
    @OnError
    public void onError(Session session, Throwable throwable) {
        logger.error("WebSocket error.", throwable);
        sendClose(session, GuacamoleStatus.SERVER_ERROR);
    }

    /**
     * Sends a text message to the client.
     *
     * @param session
     *     The WebSocket session to send the message to.
     *
     * @param message
     *     The message to send.
     */
    private void sendText(Session session, String message) {
        try {
            session.getBasicRemote().sendText(message);
        }
        catch (IOException e) {
            logger.error("Error sending WebSocket message.", e);
        }
    }

    /**
     * Closes the WebSocket connection with the given status.
     *
     * @param session
     *     The WebSocket session to close.
     *
     * @param status
     *     The status to use when closing the connection.
     */
    private void sendClose(Session session, GuacamoleStatus status) {
        try {
            session.close(new CloseReason(
                CloseReason.CloseCodes.getCloseCode(status.getWebSocketCode()),
                Integer.toString(status.getGuacamoleStatusCode())
            ));
        }
        catch (IOException e) {
            logger.error("Error closing WebSocket session.", e);
        }
    }

    /**
     * Establishes a new Guacamole tunnel for the given connection request.
     *
     * @param request
     *     The connection request.
     *
     * @return
     *     The established tunnel.
     *
     * @throws GuacamoleException
     *     If an error occurs while establishing the tunnel.
     */
    protected abstract GuacamoleTunnel doConnect(TunnelRequest request) throws GuacamoleException;

}

