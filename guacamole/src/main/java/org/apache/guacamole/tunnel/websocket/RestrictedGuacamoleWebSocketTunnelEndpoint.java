/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.tunnel.websocket;

import com.google.inject.Provider;
import java.util.Map;
import javax.websocket.EndpointConfig;
import javax.websocket.HandshakeResponse;
import javax.websocket.Session;
import javax.websocket.server.HandshakeRequest;
import javax.websocket.server.ServerEndpointConfig;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.tunnel.TunnelRequest;
import org.apache.guacamole.tunnel.TunnelRequestService;
import org.apache.guacamole.websocket.GuacamoleWebSocketTunnelEndpoint;
import com.apporto.guacamole.tunnel.ApportoTunnelRequestService;
import com.apporto.guacamole.tunnel.ApportoConnectInfo;
import com.apporto.guacamole.tunnel.ApportoProperties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Tunnel implementation which uses WebSocket as a tunnel backend, rather than
 * HTTP, properly parsing connection IDs included in the connection request.
 */
public class RestrictedGuacamoleWebSocketTunnelEndpoint extends GuacamoleWebSocketTunnelEndpoint {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(RestrictedGuacamoleWebSocketTunnelEndpoint.class);

    /**
     * Unique string which shall be used to store the TunnelRequest
     * associated with a WebSocket connection.
     */
    private static final String TUNNEL_REQUEST_PROPERTY = "WS_GUAC_TUNNEL_REQUEST";

    /**
     * Unique string which shall be used to store the TunnelRequestService to
     * be used for processing TunnelRequests.
     */
    private static final String TUNNEL_REQUEST_SERVICE_PROPERTY = "WS_GUAC_TUNNEL_REQUEST_SERVICE";

    /**
     * Endpoint string to point OpenTelemetry Collector.
     */
    private static final String OTEL_COLLECTOR_ENDPOINT_PROPERTY = "otel_collector_endpoint";

    /**
     * Interval to send metrics to OpenTelemetry Collector.
     */
    private static final String OTEL_METRICS_INTERVAL_PROPERTY = "otel_metrics_interval";

    // Default port to use apporto service
    private static final String DEFAULT_PORT = "50018";
    private static final String APPORTO_SERVICE_SECRET_KEY = "apporto-service-secret-key";
    private static final String APPORTO_SERVICE_USER_NAME = "apporto-service-username";
    private static final String APPORTO_SERVICE_PASSWORD = "apporto-service-password";
    private static final String APPORTO_SERVICE_PORT = "apporto-service-port";
    private static final String LOCAL_DNS_ZONE = "local-dns-zone";

    /**
     * Configurator implementation which stores the requested GuacamoleTunnel
     * within the user properties. The GuacamoleTunnel will be later retrieved
     * during the connection process.
     */
    public static class Configurator extends ServerEndpointConfig.Configurator {

        /**
         * Provider which provides instances of a service for handling
         * tunnel requests.
         */
        private final Provider<TunnelRequestService> tunnelRequestServiceProvider;

        /**
         * Creates a new Configurator which uses the given tunnel request
         * service provider to retrieve the necessary service to handle new
         * connections requests.
         *
         * @param tunnelRequestServiceProvider
         *     The tunnel request service provider to use for all new
         *     connections.
         */
        public Configurator(Provider<TunnelRequestService> tunnelRequestServiceProvider) {
            this.tunnelRequestServiceProvider = tunnelRequestServiceProvider;
        }

        @Override
        public void modifyHandshake(ServerEndpointConfig config,
                HandshakeRequest request, HandshakeResponse response) {

            super.modifyHandshake(config, request, response);

            // Store tunnel request and tunnel request service for retrieval
            // upon WebSocket open
            Map<String, Object> userProperties = config.getUserProperties();
            userProperties.clear();
            userProperties.put(TUNNEL_REQUEST_PROPERTY, new WebSocketTunnelRequest(request));
            userProperties.put(TUNNEL_REQUEST_SERVICE_PROPERTY, tunnelRequestServiceProvider.get());

            try {
                Environment environment = new LocalEnvironment();

                String otelCollectorEndpoint = environment.getProperty(ApportoProperties.OTEL_COLLECTOR_ENDPOINT);
                if (otelCollectorEndpoint != null) {
                    userProperties.put(OTEL_COLLECTOR_ENDPOINT_PROPERTY, otelCollectorEndpoint);
                }
            }
            catch (Exception e) {
                logger.warn("Cannot get `otel-collector-endpoint` parameter from configuration, " +
                            "please check /etc/guacamole/guacamole.properties.");
                logger.error(e.getMessage());
            }

            try {
                Environment environment = new LocalEnvironment();

                String otelMetricsInterval = environment.getProperty(ApportoProperties.OTEL_METRICS_INTERVAL);
                if (otelMetricsInterval != null) {
                    userProperties.put(OTEL_METRICS_INTERVAL_PROPERTY, otelMetricsInterval);
                }
            }
            catch (Exception e) {
                logger.warn("Cannot get `otel-metrics-interval` parameter from configuration, " +
                            "please check /etc/guacamole/guacamole.properties.");
                logger.error(e.getMessage());
            }

            try {

                Environment environment = new LocalEnvironment();
                // If exists, get the Apporto service secret key from guacamole.properties
                String secretKey = environment.getProperty(ApportoProperties.APPORTO_SERVICE_SECRET_KEY);
    
                if (secretKey == null || secretKey.isEmpty()) {
                    logger.error("apporto-service-secret-key is empty.");
                }
                else {
                    userProperties.put(APPORTO_SERVICE_SECRET_KEY, secretKey);
                }
    
            }
            catch (Exception e) {
                logger.warn("Cannot get `apporto-service-secret-key` " +
                            "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
                logger.error(e.getMessage());
                logger.error(e.getStackTrace().toString());
            }
    
            try {
    
                Environment environment = new LocalEnvironment();
    
                // if exists, get the Apporto service username from guacamole.properties
                String defaultUserName = environment.getProperty(ApportoProperties.APPORTO_SERVICE_USERNAME);
    
                if (defaultUserName == null || defaultUserName.isEmpty()) {
                    logger.error("apporto-service-username is empty.");
                }
                else {
                    userProperties.put(APPORTO_SERVICE_USER_NAME, defaultUserName);
                }
    
            }
            catch (Exception e) {
                logger.warn("Cannot get `apporto-service-username` " +
                            "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
                logger.error(e.getMessage());
                logger.error(e.getStackTrace().toString());
            }
    
            try {
    
                Environment environment = new LocalEnvironment();
    
                // if exists, get the Apporto service password from guacamole.properties
                String defaultPassword = environment.getProperty(ApportoProperties.APPORTO_SERVICE_PASSWORD);
    
                if (defaultPassword == null || defaultPassword.isEmpty()) {
                    logger.error("apporto-service-password is empty.");
                }
                else {
                    userProperties.put(APPORTO_SERVICE_PASSWORD, defaultPassword);
                }
    
            }
            catch (Exception e) {
                logger.warn("Cannot get `apporto-service-password` " +
                            "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
                logger.error(e.getMessage());
                logger.error(e.getStackTrace().toString());
            }
    
            try {
    
                Environment environment = new LocalEnvironment();
    
                // if exists, get the Apporto service password from guacamole.properties
                String servicePort = environment.getProperty(ApportoProperties.APPORTO_SERVICE_PORT);
    
                if (servicePort == null || servicePort.isEmpty()) {
                    logger.warn("apporto-service-port is empty. The default port is {}.", DEFAULT_PORT);
                    servicePort = DEFAULT_PORT;
                }
                else {
                    userProperties.put(APPORTO_SERVICE_PORT, servicePort);
                }
    
            }
            catch (Exception e) {
                logger.warn("Cannot get `apporto-service-port` " +
                            "parameter from configuration, please check /etc/guacamole/guacamole.properties. " +
                            "The default port is {}.", DEFAULT_PORT);
            }

            try {
    
                Environment environment = new LocalEnvironment();
    
                // if exists, get the Apporto service username from guacamole.properties
                String defaultDomain= environment.getProperty(ApportoProperties.LOCAL_DNS_ZONE);
    
                if (defaultDomain == null || defaultDomain.isEmpty()) {
                    logger.error("local-dns-zone is empty.");
                }
                else {
                    userProperties.put(LOCAL_DNS_ZONE, defaultDomain);
                }
    
            }
            catch (Exception e) {
                logger.warn("Cannot get `local-dns-zone` " +
                            "parameter from configuration, please check /etc/guacamole/guacamole.properties.");
                logger.error(e.getMessage());
                logger.error(e.getStackTrace().toString());
            }

        }

    }

    @Override
    protected GuacamoleTunnel createTunnel(Session session,
            EndpointConfig config) throws GuacamoleException {

        Map<String, Object> userProperties = config.getUserProperties();

        // Get original tunnel request
        TunnelRequest tunnelRequest = (TunnelRequest) userProperties.get(TUNNEL_REQUEST_PROPERTY);
        if (tunnelRequest == null)
            return null;

        // Get tunnel request service
        TunnelRequestService tunnelRequestService = (TunnelRequestService) userProperties.get(TUNNEL_REQUEST_SERVICE_PROPERTY);
        if (tunnelRequestService == null)
            return null;

        // Create and return tunnel
        return tunnelRequestService.createTunnel(tunnelRequest);

    }

    @Override
    protected boolean getConnectionInfo(EndpointConfig config) {

        Map<String, Object> userProperties = config.getUserProperties();

        // Get original tunnel request
        TunnelRequest tunnelRequest = (TunnelRequest) userProperties.get(TUNNEL_REQUEST_PROPERTY);
        if (tunnelRequest == null)
            return false;

        // Get tunnel request service
        TunnelRequestService tunnelRequestService = (TunnelRequestService) userProperties.get(TUNNEL_REQUEST_SERVICE_PROPERTY);
        if (tunnelRequestService == null)
            return false;

        try {

            // Parse request parameters
            ApportoConnectInfo connectInfo =
                ((ApportoTunnelRequestService)tunnelRequestService).getConnectionInfo(tunnelRequest);

            // Set connection info
            this.connId = connectInfo.getConnId();
            this.cloudUserName = connectInfo.getCloudUserName();
            this.connectionType = connectInfo.getConnectionType();
            this.enableH264 = connectInfo.getEnableH264();
            this.configuration = connectInfo.getConfiguration();

        }
        catch (GuacamoleException e) {

            logger.warn("An unknown error occurs while getting the connection info", e);

        }
        catch (NullPointerException e) {

            logger.warn("NullPointerException occurred", e);

        }

        return true;

    }

}
