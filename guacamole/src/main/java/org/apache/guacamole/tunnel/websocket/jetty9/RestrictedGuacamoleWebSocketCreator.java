/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package org.apache.guacamole.tunnel.websocket.jetty9;

import org.eclipse.jetty.websocket.api.UpgradeRequest;
import org.eclipse.jetty.websocket.api.UpgradeResponse;
import org.eclipse.jetty.websocket.servlet.WebSocketCreator;
import org.apache.guacamole.tunnel.TunnelRequestService;

/**
 * WebSocketCreator which selects the appropriate WebSocketListener
 * implementation if the "guacamole" subprotocol is in use.
 */
public class RestrictedGuacamoleWebSocketCreator implements WebSocketCreator {

    /**
     * Service for handling tunnel requests.
     */
    private final TunnelRequestService tunnelRequestService;

    /**
     * Creates a new WebSocketCreator which uses the given TunnelRequestService
     * to create new GuacamoleTunnels for inbound requests.
     *
     * @param tunnelRequestService The service to use for inbound tunnel
     *                             requests.
     */
    public RestrictedGuacamoleWebSocketCreator(TunnelRequestService tunnelRequestService) {
        this.tunnelRequestService = tunnelRequestService;
    }

    @Override
    public Object createWebSocket(UpgradeRequest request, UpgradeResponse response) {

        // Validate and use "guacamole" subprotocol
        for (String subprotocol : request.getSubProtocols()) {

            if ("guacamole".equals(subprotocol)) {
                response.setAcceptedSubProtocol(subprotocol);
                return new RestrictedGuacamoleWebSocketTunnelListener(tunnelRequestService);
            }

        }

        // Invalid protocol
        return null;

    }

}
