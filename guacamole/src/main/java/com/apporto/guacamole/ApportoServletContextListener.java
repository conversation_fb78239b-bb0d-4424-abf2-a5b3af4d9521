/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.guacamole;

import javax.servlet.ServletContextEvent;

import org.apache.guacamole.EnvironmentModule;
import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;
import org.apache.guacamole.extension.ExtensionModule;
import org.apache.guacamole.log.LogModule;
import org.apache.guacamole.rest.auth.HashTokenSessionMap;
import org.apache.guacamole.rest.auth.TokenSessionMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.guacamole.rest.ApportoRESTServiceModule;
import com.apporto.guacamole.tunnel.ApportoTunnelModule;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.Stage;
import com.google.inject.servlet.GuiceServletContextListener;

/**
 * Modified Guacamole servlet context listener to include Apporto modified classes.
 * 
 * <AUTHOR> Nikolić
 *
 */
public class ApportoServletContextListener extends GuiceServletContextListener {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(ApportoServletContextListener.class);

    /**
     * The Guacamole server environment.
     */
    private Environment environment;

    /**
     * Singleton instance of a TokenSessionMap.
     */
    private TokenSessionMap sessionMap;

    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {

        try {
            environment = new LocalEnvironment();
            sessionMap = new HashTokenSessionMap(environment);
        }
        catch (GuacamoleException e) {
            logger.error("Unable to read guacamole.properties: {}", e.getMessage());
            logger.debug("Error reading guacamole.properties.", e);
            throw new RuntimeException(e);
        }

        super.contextInitialized(servletContextEvent);

    }

    @Override
    protected Injector getInjector() {
        return Guice.createInjector(Stage.PRODUCTION,
            new EnvironmentModule(environment),
            new LogModule(environment),
            new ExtensionModule(environment),
            new ApportoRESTServiceModule(sessionMap),
            new ApportoTunnelModule()
        );
    }

    @Override
    public void contextDestroyed(ServletContextEvent servletContextEvent) {

        super.contextDestroyed(servletContextEvent);

        // Shutdown TokenSessionMap
        if (sessionMap != null)
            sessionMap.shutdown();

    }

}
