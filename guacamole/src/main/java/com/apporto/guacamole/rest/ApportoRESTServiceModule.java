/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto.guacamole.rest;

import org.apache.guacamole.rest.RESTServiceModule;
import org.apache.guacamole.rest.auth.TokenSessionMap;

import com.apporto.guacamole.rest.chartdata.ChartDataRESTService;
import com.apporto.guacamole.rest.stats.StatsRESTService;
import com.apporto.guacamole.rest.lti.LtiRESTService;


/**
 * REST module that registers additional REST services.
 *
 * <AUTHOR>
 */
public class ApportoRESTServiceModule extends RESTServiceModule {

	public ApportoRESTServiceModule(TokenSessionMap tokenSessionMap) {
		super(tokenSessionMap);
	}

	@Override
	protected void configureServlets() {
        // Set up the API endpoints
        bind(StatsRESTService.class);
        bind(ChartDataRESTService.class);
        bind(LtiRESTService.class);
        
		super.configureServlets();
	}
}
