/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto.guacamole.rest.chartdata;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleSession;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.rest.auth.AuthenticationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.SendStatusData;
import com.google.inject.Inject;

/**
 * A REST Service for handling closing the browser window.
 *
 * <AUTHOR> Nikolić
 */
@Path("/chartdata")
@Produces(MediaType.APPLICATION_JSON)
public class ChartDataRESTService {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(ChartDataRESTService.class);

    /**
     * A service for authenticating users from auth tokens.
     */
    @Inject
    private AuthenticationService authenticationService;

    /**
     * URL parameters.
     */
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String CONNECTION_TYPE_PARM = "connection_type";

    /**
     * Session info.
     */
    private static String conn_id = "unknown";
    private static String cloud_user = "unknown";
    private static String conn_type = "unknown";

    /**
     * Send idle time statistics to server.
     *
     * @return String with JSON data describing user activity.
     *
     * @throws GuacamoleException
     */
    @Path("/user")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response getUserData(@QueryParam("token") String authToken,
                                @QueryParam("id") String id,
                                @QueryParam("datasource") String dataSource,
                                @QueryParam("timezone") String timezone) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();
        
        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        /* Print log */
        logger.info("[{}:{}:{}] Preparing to request user data: token={}, datasource={}, timezone={}",
                     conn_id, conn_type, cloud_user, authToken, dataSource, timezone);

        /* Get the user data */
        String result = SendStatusData.getUserData(configuration, timezone);
        logger.info("[{}:{}:{}] User data received.", conn_id, conn_type, cloud_user);

        return Response.ok(result)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Send event time statistics to server.
     *
     * @return String with JSON data describing group activity.
     *
     * @throws GuacamoleException
     */
    @Path("/group")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    public Response sendStats(@QueryParam("token") String authToken,
                              @QueryParam("id") String id,
                              @QueryParam("datasource") String dataSource,
                              @QueryParam("timezone") String timezone) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();
        
        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        conn_type = configuration.getParameter(CONNECTION_TYPE_PARM);

        /* Print log */
        logger.info("[{}:{}:{}] Preparing to request group data: token={}, datasource={}, timezone={}",
                     conn_id, conn_type, cloud_user, authToken, dataSource, timezone);

        /* Get the group data */
        String result = SendStatusData.getGroupData(configuration, timezone);
        logger.info("[{}:{}:{}] Group data received.", conn_id, conn_type, cloud_user);

        return Response.ok(result)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }
}
