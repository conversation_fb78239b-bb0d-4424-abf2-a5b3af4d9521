/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto.guacamole.rest.stats;

import java.util.HashMap;

import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.QueryParam;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.core.Response.Status;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleSession;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.rest.auth.AuthenticationService;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.json.simple.parser.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.SendStatusData;
import com.google.inject.Inject;

/**
 * A REST Service for handling closing the browser window.
 * 
 * <AUTHOR> Nikolić
 */
@Path("/stats")
@Produces(MediaType.APPLICATION_JSON)
public class StatsRESTService {

    /**
     * Logger for this class.
     */
    private final Logger logger = LoggerFactory.getLogger(StatsRESTService.class);
    
    /**
     * A service for authenticating users from auth tokens.
     */
    @Inject
    private AuthenticationService authenticationService;
    
    /**
     * The names of some URL parameters
     */
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String CONECTION_TYPE_PARM = "connection_type";

    /**
     * Send idle time statistics to server.
     * 
     * @return Response - 200 ok
     * 
     * @throws GuacamoleException 
     */
    @Path("/idle")
    @GET
    public Response sendIdleStats(@QueryParam("token") String authToken,
                                  @QueryParam("id") String id,
                                  @QueryParam("datasource") String dataSource,
                                  @QueryParam("totaltime") String totalTime,
                                  @QueryParam("idletime") String idleTime)
    		throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();
        
        /* Send the idle stats */
        SendStatusData.sendIdleStats(configuration, totalTime, idleTime);

        /* Print log */
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONECTION_TYPE_PARM);
        logger.info("[{}:{}:{}] Sent idle status.", id, conn_type, cloud_user);
        
        return Response.status(Status.OK).build();
    }

    /**
     * Send event time statistics to server.
     * 
     * @return Response - 200 ok
     * 
     * @throws GuacamoleException 
     */
    @Path("/events")
    @GET
    public Response sendStats(@QueryParam("token") String authToken,
                              @QueryParam("id") String id,
                              @QueryParam("datasource") String dataSource,
                              @QueryParam("events") String events)
    		throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();
        
         /* Print log */
        String cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameter(CONECTION_TYPE_PARM);
        /* Send the event stats */
        String response = SendStatusData.sendEventStats(configuration, events);
        JSONObject jsonResult = new JSONObject();
        if (!response.isEmpty()) {
          JSONParser jsonParser = new JSONParser();
          JSONObject json = null;

          try {
              json = (JSONObject) jsonParser.parse(response);
          }
          catch (ParseException e) {
              logger.error("[{}:{}:{}] Exception log: ", id, conn_type, cloud_user, e);
          }

          HashMap<String, Object> map = new HashMap<>();
          map.put("extended_compute", json.get("extended_compute"));
          map.put("remaining_time", json.get("remaining_time"));
          jsonResult = new JSONObject(map);
        }

        /* Print log */
        logger.info("[{}:{}:{}] Sent events statistics.", id, conn_type, cloud_user);

        return Response.ok(jsonResult)
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }
}
