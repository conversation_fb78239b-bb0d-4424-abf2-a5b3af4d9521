/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.guacamole.rest.lti;

import java.io.*;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleSession;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.rest.auth.AuthenticationService;
import org.apache.guacamole.tunnel.UserTunnel;
import org.json.JSONException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.netty.handler.codec.http.HttpMethod;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.List;

import org.json.JSONObject;
import org.json.JSONArray;

import com.google.inject.Inject;
import org.apache.guacamole.environment.Environment;
import org.apache.guacamole.environment.LocalEnvironment;

import com.apporto.GuacamoleCommonUtility;
import com.apporto.guacamole.tunnel.ApportoProperties;

/**
 * A REST Service for handling closing the browser window.
 *
 * <AUTHOR> Nikolić
 */
@Path("/lti")
@Produces(MediaType.APPLICATION_JSON)
public class LtiRESTService {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(LtiRESTService.class);

    /**
     * A service for authenticating users from auth tokens.
     */
    @Inject
    private AuthenticationService authenticationService;

    /**
     * API templates for assignment.
     */
    private static final String API_BASE_URL = "https://%s.%s/api/lms";
    private static final String ASSIGNMENT_LIST = "/assignment-list?%s";
    private static final String SUBMISSION_DETAILS = "/submission_details?%s";
    private static final String UPLOAD_ASSIGNMENT_DETAILS = "/upload-assignment-details?%s";
    private static final String UPLOAD_ASSIGNMENT_FULFILLED = "/upload-assignment-fulfilled?%s";
    // D2L upload API template
    private static final String D2L_UPLOAD_ASSIGNMENT_FULFILLED = "/d2l-upload-assignment-fulfilled?%s";
    // Blackboard upload API template
    private static final String BB_UPLOAD_ASSIGNMENT_FULFILLED = "/bb-upload-assignment-fulfilled?%s";

    /**
     * URL parameters.
     */
    private static final String SUBDOMAIN_PARM = "subdomain";
    private static final String LTI_USER_ID_PARM = "lti_user_id";
    private static final String LTI_COURSE_ID_PARM = "lti_course_id";
    private static final String LTI_ISSUER_PARM = "lti_issuer";
    private static final String HTTP_API_KEY = "Http-Api-Key";
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String CONNECTION_TYPE = "connection_type";

    /**
     * Others.
     */
    private static final String USER_AGENT = "Mozilla/5.0";
    private static final ByteArrayOutputStream output = new ByteArrayOutputStream();

    private static String uploadUrl = "";
    private static final String LOCK_INFO = "lock_info";

    private static String DEFAULT_DOMAIN = "apporto.com";

    // LTI issuer (it can be one among canvas, d2l and blackboard)
    private static String issuer = "";

    /**
     * LTI issuer constants
     */
    private static final String LTI_CANVAS = "canvas";
    private static final String LTI_BLACKBOARD = "blackboard";

    /**
     * Session info.
     */
    private static String conn_id = "unknown";
    private static String cloud_user = "unknown";
    private static String connection_type = "unknown";

    // Load the proper configuration variable(s)
    static {
        try {
            Environment environment = new LocalEnvironment();
            String propValue;

            // Indicate the api-base-domain from guacamole.properties file.
            propValue = environment.getProperty(ApportoProperties.API_BASE_DOMAIN);
            if (propValue != null) {
                DEFAULT_DOMAIN = propValue;
            }
        }
        catch (Exception e) {
            logger.warn("Cannot get `api-base-domain` parameter from configuration, " +
                        "please check /etc/guacamole/guacamole.properties.");
            logger.error(e.getMessage());
            logger.error(e.getStackTrace().toString());
        }
    }

    /**
     * Get the list of the assignments associated with current user.
     *
     * @return The assignment list if successful, the error code if fail.
     */
    @Path("/getAssignmentList")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response getAssignmentList(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] getAssignmentList: token={}, datasource={}",
                    conn_id, connection_type, cloud_user, authToken, dataSource);

        /* Check the base parameters */
        Map<String, String> url_params = checkBaseParams(configuration);
        if(url_params.get("error") != null) {
            JSONObject error = new JSONObject();
            error.put("error", url_params.get("error"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error).build();
        }

        /* Get the subdomain parameter */
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        /* Build the api url */
        String api_url = API_BASE_URL + ASSIGNMENT_LIST;
        String url_params_value = convertMapToString(url_params);

        logger.info("[{}:{}:{}] getAssignmentList: subdomain={}, api_url={}, url_params_value={}",
                    conn_id, connection_type, cloud_user, subdomain, api_url, url_params_value);

        /* Get the json data from api */
        String data = getData(configuration, api_url, HttpMethod.GET, subdomain, DEFAULT_DOMAIN, url_params_value);
        JSONArray responseArray = null;
        JSONObject responseObject = null;
        String courseName = null;
        String message = "getAssignmentList: invalid json data.";
        if (!data.isEmpty()) {
            logger.info("[{}:{}:{}] getAssignmentList: Success response={}", conn_id, connection_type, cloud_user, data);
            JSONObject json = null;

            try {
                json = new JSONObject(data);
            }
            catch (JSONException e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }

            int status = json.optInt("status");
            if (status == Response.Status.OK.getStatusCode()) {
                Object lmsResponse = json.get("lms_response");
                courseName = (String) json.get("course_name");
                if (lmsResponse instanceof JSONArray) {
                    responseArray = (JSONArray) lmsResponse;
                }
                else if (lmsResponse instanceof JSONObject) {
                    responseObject = (JSONObject) lmsResponse;
                }
            }
            else {
                message = (String) json.get("message");
                logger.error("[{}:{}:{}] getAssignmentList: Fail, status code={} message={}",
                             conn_id, connection_type, cloud_user, status, message);
            }
        }

        /* Check json data */
        if (responseObject != null && responseObject.get("errors") != null) {
            JSONArray errors = (JSONArray) responseObject.get("errors");
            if (errors != null && !errors.isEmpty()) {
                JSONObject error = (JSONObject) errors.get(0);
                if (error != null) {
                    message = (String) error.get("message");
                }
            }
        }

        /* Preparing the response */
        JSONObject jsonResponse = new JSONObject();

        /* Extract the data from json */
        JSONArray jsonData = null;
        if (responseArray != null) {
            jsonData = new JSONArray();
            Iterator iterator = responseArray.iterator();
            // blackboard
            if (issuer.equals(LTI_BLACKBOARD)) {
                while (iterator.hasNext()) {
                    JSONObject entry = (JSONObject) iterator.next();
                    if (!entry.has(LOCK_INFO)) {
                        // assn_id is not number format in blackboard, for instance: "_87_1"
                        String assn_id = entry.optString("id", entry.optString("Id", null));
                        String assn_name = entry.optString("name", entry.optString("Name", null));
                        JSONObject content_handler = entry.optJSONObject("contentHandler"); // Safely fetch contentHandler
                        String grade_column_id = content_handler != null ? content_handler.optString("gradeColumnId", null) : null;

                        JSONObject assn_data = new JSONObject()
                                .put("id", assn_id)
                                .put("name", assn_name)
                                .put("grade_column_id", grade_column_id);
                        jsonData.put(assn_data);
                    }
                }
            }
            // canvas or d2l
            else {
                while (iterator.hasNext()) {
                    JSONObject entry = (JSONObject) iterator.next();
                    String assn_name = entry.optString("name", entry.optString("Name", null));
                    String assn_due_at = entry.optString("due_at", entry.optString("Due_At", null));
                    int assn_id = entry.optInt("id", entry.optInt("Id", entry.optInt("Id", 0)));
                    String description = entry.optString("description", entry.optString("Description", null));
                    String assn_submitted_at = entry.optString("submitted_at", entry.optString("Submitted_At", null));
                    int assn_assignment_upload_files = entry.optInt("assignment_upload_files", entry.optInt("Assignment_Upload_Files"));
                    int assn_has_submitted_submissions_count = entry.optInt("has_submitted_submissions_count", entry.optInt("Has_Submitted_Submissions_Count"));
                    boolean assn_has_submitted_submissions = entry.optBoolean("has_submitted_submissions", entry.optBoolean("Has_Submitted_Submissions", false));
                    if(issuer.equals(LTI_CANVAS)) {
                        assn_has_submitted_submissions = assn_submitted_at != null;
                    }
                    int allowedAttempts = entry.optInt("allowed_attempts", 0);
                    String assn_lock_at = entry.optString("lock_at", entry.optString("Lock_At", null));
                    String assn_unlock_at = entry.optString("unlock_at", entry.optString("Unlock_At", null));

                    JSONObject assn_data = new JSONObject()
                            .put("id", assn_id)
                            .put("name", assn_name)
                            .put("due_at", assn_due_at)
                            .put("has_submitted_submissions", assn_has_submitted_submissions)
                            .put("assignment_upload_files", assn_assignment_upload_files)
                            .put("has_submitted_submissions_count", assn_has_submitted_submissions_count)
                            .put("description", description);
                    assn_data.put("allowed_attempts", allowedAttempts);
                    if(assn_has_submitted_submissions) assn_data.put("submitted_at", assn_submitted_at);
                    if(assn_lock_at != null) assn_data.put("lock_at", assn_lock_at);
                    if(assn_unlock_at != null) assn_data.put("unlock_at", assn_unlock_at);

                    jsonData.put(assn_data);
                }
            }
        }

        jsonResponse.put("assignments", jsonData);
        jsonResponse.put("course_name", courseName);

        /* Check the data */
        if (jsonData != null) {
            return Response.ok(jsonResponse.toString())
                           .header("Cache-Control", "no-cache, no-store, must-revalidate")
                           .header("Pragma", "no-cache")
                           .header("Expires", "0")
                           .build();
        }

        JSONObject error = new JSONObject();
        error.put("Message", message);
        logger.error("[{}:{}:{}] getAssignmentList: Error message={}", conn_id, connection_type, cloud_user, error.get("Message"));
        return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
    }

    @Path("/getAssignment")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response getAssignment(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource,
            @QueryParam("assn_id") String assn_id) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] getAssignmentList: token={}, datasource={}",
                conn_id, connection_type, cloud_user, authToken, dataSource);

        /* Check the base parameters */
        Map<String, String> url_params = checkBaseParams(configuration);
        url_params.put("assn_id", assn_id);
        if(url_params.get("error") != null) {
            JSONObject error = new JSONObject();
            error.put("error", url_params.get("error"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error).build();
        }

        /* Get the subdomain parameter */
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        /* Build the api url */
        String api_url = API_BASE_URL + SUBMISSION_DETAILS;
        String url_params_value = convertMapToString(url_params);

        logger.info("[{}:{}:{}] getAssignment: subdomain={}, api_url={}, url_params_value={}",
                conn_id, connection_type, cloud_user, subdomain, api_url, url_params_value);

        /* Get the json data from api */
        String data = getData(configuration, api_url, HttpMethod.GET, subdomain, DEFAULT_DOMAIN, url_params_value);
        JSONObject jsonResponse = new JSONObject();
        String message = "getAssignment: invalid json data.";

        if(!data.isEmpty()) {
            logger.info("[{}:{}:{}] getAssignment: Success response={}", conn_id, connection_type, cloud_user, data);
            JSONObject json = null;

            try {
                json = new JSONObject(data);
            } catch (JSONException e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }

            /* Preparing a response */
            int status = json.optInt("status");
            if (status == Response.Status.OK.getStatusCode()) {
                /* Extract the data from json */
                JSONObject lmsResponse = (JSONObject) json.get("lms_response");

                if (lmsResponse != null && !lmsResponse.isEmpty()) {
                    /* Extracting the required values */
                    String submittedAt = lmsResponse.optString("submitted_at", null);
                    int assignmentId = lmsResponse.optInt("assignment_id", 0);
                    JSONArray submissionAttachments = lmsResponse.optJSONArray("attachments", new JSONArray());
                    JSONObject attachments = lmsResponse.optJSONObject("assignment_attachments");
                    JSONArray assignmentAttachments = (attachments != null && attachments.has("attached_files")) ? attachments.optJSONArray("attached_files", new JSONArray()) : new JSONArray();
                    int submittedFilesCount = submissionAttachments.length();
                    int assignmentFilesCount = assignmentAttachments.length();
                    int attempt = lmsResponse.optInt("attempt");
                    JSONArray submittedFiles = new JSONArray();
                    for (Object obj : submissionAttachments) {
                        JSONObject attachment = (JSONObject) obj;
                        JSONObject file = new JSONObject()
                            .put("url", attachment.get("url"))
                            .put("file_name", attachment.optString("filename"))
                            .put("updated_at", attachment.optString("updated_at"))
                            .put("content_type", attachment.optString("content-type"))
                            .put("display_name", attachment.optString("display_name"))
                            .put("id", attachment.getInt("id"));
                        submittedFiles.put(file);
                    }

                    jsonResponse.put("assignment_id", assignmentId);
                    jsonResponse.put("submitted_files_count", submittedFilesCount);
                    jsonResponse.put("assignment_files_count", assignmentFilesCount);
                    jsonResponse.put("submitted_at", submittedAt);
                    jsonResponse.put("attempt", attempt);
                    if (submittedFilesCount > 0) jsonResponse.put("submitted_files", submittedFiles);
                    if (assignmentFilesCount > 0) jsonResponse.put("assignment_files", assignmentAttachments);

                }
            } else {
                message = (String) json.get("message");
                logger.error("[{}:{}:{}] getAssignment: Fail, status code={} message={}",
                        conn_id, connection_type, cloud_user, status, message);
            }

            if (!jsonResponse.isEmpty()) {
                return Response.ok(jsonResponse.toString())
                        .header("Cache-Control", "no-cache, no-store, must-revalidate")
                        .header("Pragma", "no-cache")
                        .header("Expires", "0")
                        .build();
            }
        }

        JSONObject error = new JSONObject();
        error.put("Message", message);
        logger.error("[{}:{}:{}] getAssignment: Error message={}", conn_id, connection_type, cloud_user, error.get("Message"));
        return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
    }

    @Path("/downloadAssignment")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces("application/octet-stream")
    @SuppressWarnings("unchecked")
    public Response downloadAssignment(@QueryParam("token") String authToken,
                                       @QueryParam("id") String id,
                                       @QueryParam("datasource") String dataSource,
                                       String jsonRequest) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] downloadAssignment: token={}, datasource={}",
                conn_id, connection_type, cloud_user, authToken, dataSource);

        String message;
        String fileUrlStr;
        try {
            JSONObject jsonObject = new JSONObject(jsonRequest);
            fileUrlStr = jsonObject.getString("fileUrl");
        } catch (JSONException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"Message\":\"Invalid JSON request.\"}")
                    .build();
        }

        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(fileUrlStr).openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.connect();

            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                String fileName;
                String contentDisposition = connection.getHeaderField("Content-Disposition");
                if (contentDisposition != null && contentDisposition.contains("filename=")) {
                    fileName = contentDisposition.split("filename=")[1].replaceAll("\"", "").trim();
                } else {
                    fileName = fileUrlStr.substring(fileUrlStr.lastIndexOf('/') + 1);
                }

                String fileType = connection.getContentType();

                try (InputStream inputStream = connection.getInputStream();
                     ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
                    byte[] buffer = new byte[4096];
                    int length;
                    while ((length = inputStream.read(buffer)) != -1) {
                        byteArrayOutputStream.write(buffer, 0, length);
                    }
                    return Response.ok(byteArrayOutputStream.toByteArray())
                            .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                            .header("Content-Type", fileType != null ? fileType : "application/octet-stream")
                            .build();
                }
            } else {
                message = (String) connection.getContent();
                logger.error("[{}:{}:{}] downloadAssignment: Fail, status code={} message={}", conn_id, connection_type, cloud_user, connection.getResponseCode(), message);
                return buildErrorResponse(Response.Status.fromStatusCode(connection.getResponseCode()), message);
            }
        } catch (Exception e) {
            logger.error("[{}:{}:{}] downloadAssignment: failed.", conn_id, connection_type, cloud_user, e);
            message = "Error downloading the assignment file.";
            return buildErrorResponse(Response.Status.INTERNAL_SERVER_ERROR, message);
        }
    }

    private Response buildErrorResponse(Response.Status status, String message) {
        JSONObject error = new JSONObject();
        error.put("Message", message);
        return Response.status(status).entity(error.toString()).build();
    }

    @Path("/downloadDescription")
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces("text/html")
    @SuppressWarnings("unchecked")
    public Response downloadDescription(@QueryParam("token") String authToken,
                                        @QueryParam("datasource") String dataSource,
                                        @QueryParam("id") String id,
                                        String jsonRequest) throws GuacamoleException {
        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] downloadDescription: token={}, datasource={}",
                conn_id, connection_type, cloud_user, authToken, dataSource);

        String htmlContent;
        int assignmentId;
    
        try {
            JSONObject jsonObject = new JSONObject(jsonRequest);
            htmlContent = jsonObject.optString("description");
            assignmentId = jsonObject.getInt("assignmentId");
            
            if (htmlContent == null || htmlContent.isEmpty()) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"Message\":\"No HTML content provided.\"}")
                        .build();
            }
        } catch (JSONException e) {
            logger.error("[{}:{}:{}] downloadDescription: Fail, status code={} message={}",
                    conn_id, connection_type, cloud_user, Response.Status.BAD_REQUEST, e.getMessage());
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"Message\":\"Invalid JSON request.\"}")
                    .build();
        }

        try {
            // Generate a filename based on timestamp
            String fileName = "description_" + assignmentId + ".html";
        
            // Convert the HTML content to bytes
            byte[] htmlBytes = htmlContent.getBytes(StandardCharsets.UTF_8);
        
            // Return the response with HTML content
            return Response.ok(htmlBytes)
                    .header("Content-Disposition", "attachment; filename=\"" + fileName + "\"")
                    .header("Content-Type", "text/html")
                    .build();
        } catch (Exception e) {
            logger.error("[{}:{}:{}] downloadDescription: Fail, status code={} message={}",
                    conn_id, connection_type, cloud_user, Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity("{\"Message\":\"Error creating HTML file.\"}")
                    .build();
        }
    }

    /**
     * Upload the selected assignment file to the Canvas.
     *
     * @return The upload id if successful, the error code if fail.
     */
    @Path("/uploadAssignmentFileToCanvas")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response uploadAssignmentFileToCanvas(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource,
            @QueryParam("assn_id") String assn_id,
            @QueryParam("fileName") String fileName) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] uploadAssignmentFileToCanvas: datasource={}, assn_id={}, fileName={}",
                     conn_id, connection_type, cloud_user, dataSource, assn_id, fileName);

        logger.info("[{}:{}:{}] Start uploading file to Canvas", conn_id, connection_type, cloud_user);

        /* Call the uploadAssignmentFile */
        int upload_id = uploadAssignmentFile(fileName, uploadUrl);
        if (upload_id == 0) {
            JSONObject error = new JSONObject();
            error.put("Message", "Couldn't upload the file to canvas");
            logger.error("[{}:{}:{}] uploadAssignmentFileToCanvas: message={}",
                         conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        JSONObject response = new JSONObject();
        response.put("upload_id", upload_id);
        logger.info("[{}:{}:{}] Successfully uploaded file to Canvas with upload_id={}", 
                conn_id, connection_type, cloud_user, upload_id);
        return Response.ok(response.toString())
               .header("Cache-Control", "no-cache, no-store, must-revalidate")
               .header("Pragma", "no-cache")
               .header("Expires", "0")
               .build();
        }
    
    /**
      * Submit multiple files to a Canvas assignment.
      *
      * @return The status code 200 if successful, the error code if fail.
    */
    @Path("/submitCanvasAssignment")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response submitCanvasAssignment(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource,
            @QueryParam("assn_id") String assn_id,
            @QueryParam("file_id") List<Integer> fileIds) throws GuacamoleException {
    
        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();
    
        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);
    
        /* Print log */
        logger.info("[{}:{}:{}] submitCanvasAssignment: datasource={}, assn_id={}, file_ids={}",
                     conn_id, connection_type, cloud_user, dataSource, assn_id, fileIds);
        logger.info("[{}:{}:{}] Start submitting files to Canvas", conn_id, connection_type, cloud_user);
    
        /* Call the uploadAssignmentFulfilled with multiple file IDs */
        int status = uploadAssignmentFulfilled(configuration, assn_id, fileIds);

        // Check the status code
        if (!(status >= 200 && status < 300)) {
            JSONObject error = new JSONObject();
            error.put("Message", "Couldn't submit the files to Canvas");
            logger.error("[{}:{}:{}] submitCanvasAssignment: message={}",
                         conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }
    
        logger.info("[{}:{}:{}] Successfully submitted files to Canvas", conn_id, connection_type, cloud_user);
    
        JSONObject response = new JSONObject();
        response.put("status", "success");
        response.put("message", "Files successfully submitted to Canvas");
    
        return Response.ok(response.toString())
               .header("Cache-Control", "no-cache, no-store, must-revalidate")
               .header("Pragma", "no-cache")
               .header("Expires", "0")
               .build();
        }

    /**
     * Upload the selected assignment to the D2L.
     *
     * @return The status code 200 if successful, the error code if fail.
     */
    @Path("/uploadAssignmentFileToD2L")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response uploadAssignmentFileToD2L(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource,
            @QueryParam("assn_id") String assn_id,
            @QueryParam("fileName") String fileName) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] uploadAssignmentFileToLMS: datasource={}, assn_id={}, fileName={}",
                     conn_id, connection_type, cloud_user, dataSource, assn_id, fileName);

        /* Check the base parameters */
        Map< String, String> url_params = checkBaseParams(configuration);
        if(url_params.get("error") != null) {
            JSONObject error = new JSONObject();
            error.put("error", url_params.get("error"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        /* Get the subdomain parameter */
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        /* Get the other parameters */
        url_params.put("assn_id", assn_id);

        /* Build the api url */
        String api_url = API_BASE_URL + D2L_UPLOAD_ASSIGNMENT_FULFILLED;
        String url_params_value = convertMapToString(url_params);

        uploadUrl = String.format(api_url, subdomain, DEFAULT_DOMAIN, url_params_value);
        logger.info("[{}:{}:{}] uploadAssignmentFileToD2L: uploadUrl={}",
                     conn_id, connection_type, cloud_user, uploadUrl);

        /* Call the uploadAssignmentFile */
        int upload_id = uploadAssignmentFile(fileName, uploadUrl);
        if (upload_id == 0) {
            JSONObject error = new JSONObject();
            error.put("Message", "Couldn't upload the file.");
            throw new WebApplicationException(Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build());
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Upload the selected assignment to the Blackboard.
     *
     * @return The status code 200 if successful, the error code if fail.
     */
    @Path("/uploadAssignmentFileToBlackboard")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response uploadAssignmentFileToBlackboard(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource,
            @QueryParam("assn_id") String assn_id,
            @QueryParam("fileName") String fileName,
            @QueryParam("grade_column_id") String grade_column_id) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.info("[{}:{}:{}] uploadAssignmentFileToBlackboard: datasource={}, assn_id={}, fileName={}",
                     conn_id, connection_type, cloud_user, dataSource, assn_id, fileName);

        /* Check the base parameters */
        Map< String, String> url_params = checkBaseParams(configuration);

        /* Get the subdomain parameter */
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        /* Get the other parameters */
        url_params.put("grade_column_id", grade_column_id);

        /* Build the api url */
        String api_url = API_BASE_URL + BB_UPLOAD_ASSIGNMENT_FULFILLED;
        String url_params_value = convertMapToString(url_params);

        uploadUrl = String.format(api_url, subdomain, DEFAULT_DOMAIN, url_params_value);
        logger.info("[{}:{}:{}] uploadAssignmentFileToBlackboard: uploadUrl={}",
                     conn_id, connection_type, cloud_user, uploadUrl);

        logger.info("[{}:{}:{}] Start uploading file to Blackboard", conn_id, connection_type, cloud_user);

        /* Call the uploadAssignmentFile */
        int upload_id = uploadAssignmentFile(fileName, uploadUrl);
        if (upload_id == 0) {
            JSONObject error = new JSONObject();
            error.put("Message", "Couldn't upload the file to Blackboard");
            logger.error("[{}:{}:{}] uploadAssignmentFileToBlackboard: message={}",
                         conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        logger.info("[{}:{}:{}] End uploading file to Blackboard", conn_id, connection_type, cloud_user);
        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Upload the detailed information of the selected assignment.
     *
     * @return The success code if successful, the error code if fail.
     */
    @Path("/uploadAssignmentDetails")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response uploadAssignmentDetails(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("datasource") String dataSource,
            @QueryParam("assn_id") String assn_id,
            @QueryParam("fileName") String fileName,
            @QueryParam("fileSize") String fileSize) throws GuacamoleException {

        /* Get the guacamole configuration */
        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(dataSource);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        /* Get the connection info */
        conn_id = id;
        cloud_user = configuration.getParameter(CLOUD_USERNAME_PARM);
        connection_type = configuration.getParameter(CONNECTION_TYPE);

        /* Print log */
        logger.debug("[{}:{}:{}] uploadAssignmentDetails: datasource={}, assn_id={}, fileName={}, fileSize={}",
                      conn_id, connection_type, cloud_user, dataSource, assn_id, fileName, fileSize);

        /* Check the base parameters */
        Map< String, String> url_params = checkBaseParams(configuration);
        if(url_params.get("error") != null) {
            JSONObject error = new JSONObject();
            error.put("error", url_params.get("error"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        /* Get the subdomain parameter */
         String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
         subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);

        /* Get the other parameters */
        url_params.put("assn_id", assn_id);
        url_params.put("file_name", fileName);
        url_params.put("file_size", fileSize);
        url_params.put("file_content_type", "application/octet-stream");

        /* Build the api url */
        String api_url = API_BASE_URL + UPLOAD_ASSIGNMENT_DETAILS;
        String url_params_value = convertMapToString(url_params);

        /* Get the json data from api */
        String data = getData(configuration, api_url, HttpMethod.GET, subdomain, DEFAULT_DOMAIN, url_params_value);
        JSONObject jsonData = null;
        String message = "uploadAssignmentDetails: invalid json data.";
        if (!data.isEmpty()) {
            JSONObject json;

            try {
                json = new JSONObject(data);
            }
            catch (JSONException e) {
                JSONObject error = new JSONObject();
                error.put("Message", message);
                logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
                return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
            }

            int status = json.getInt("status");
            if (status == Response.Status.OK.getStatusCode()) {
                jsonData = (JSONObject) json.get("lms_response");
            }
            else {
                logger.error("[{}:{}:{}] uploadAssignmentDetails: status code={}",
                             conn_id, connection_type, cloud_user, status);
                message = (String) json.get("message");
            }
        }

        /* Check json data */
        if (jsonData != null && !jsonData.isEmpty()) {
            if (jsonData.opt("errors") != null) {
                JSONArray errors = (JSONArray) jsonData.opt("errors");
                JSONObject error = (JSONObject) errors.opt(0);
                message = (String) error.opt("message");
                jsonData = null;
            }
        }

        /* Extract the data from json */
        if (jsonData != null && !jsonData.isEmpty()) {
            uploadUrl = (String) jsonData.get("upload_url");
        }

        /* Check the data */
        if (jsonData == null || jsonData.isEmpty()) {
            JSONObject error = new JSONObject();
            error.put("Message", message);
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        JSONObject response = new JSONObject();
        response.put("status", Response.Status.OK.getStatusCode());

        return Response.ok(response.toString())
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    /**
     * Upload the selected assignment file.
     *
     * @return The upload id if successful, 0 if fail.
     */
    private int uploadAssignmentFile(String fileName, String upload_url) {

        logger.info("[{}:{}:{}] uploadAssignmentFile: fileName={}, upload_url={}",
                     conn_id, connection_type, cloud_user, fileName, upload_url);

        int upload_id = 0;
        String data;

        /* Call the api url */
        try {
            URL url = new URL(upload_url);
            data = executeAPI(fileName, url);

        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] uploadAssignmentFile: Exception log: ",
                         conn_id, connection_type, cloud_user, e);
            return upload_id;
        }

        /* Parse the json data from api */
        String message;
        if (!data.isEmpty()) {
            JSONObject json = null;

            try {
                json = new JSONObject(data);
            }
            catch (Exception e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }

            String status = (String) json.get("upload_status");
            if (status.equals("success")) {
                if (issuer.equals(LTI_CANVAS)) {
                    upload_id = Integer.parseInt(json.get("id").toString());
                }
                else {
                    upload_id = 1; // upload success in d2l or blackboard
                }

                logger.info("[{}:{}:{}] uploadAssignmentFile: Success upload_id : {}", conn_id, connection_type, cloud_user,upload_id);

            }
            else {
                message = (String) json.get("lms_response");
                logger.error("[{}:{}:{}] uploadAssignmentFile: Fail, status={}, message={}",
                             conn_id, connection_type, cloud_user, status, message);
            }
        }

        /* Return upload id */
        return upload_id;
    }

    /**
     * Upload the fulfilled information of the upload assignment.
     *
     * @return The status code 200 if successful, the error code if fail.
     */
    private int uploadAssignmentFulfilled(GuacamoleConfiguration configuration, String assn_id, List<Integer> fileIds) {

        logger.info("[{}:{}:{}] uploadAssignmentFulfilled: assign_id={}, fileIds={}",
                     conn_id, connection_type, cloud_user, assn_id, fileIds);

        int status = 0;

        /* Check the base parameters */
        Map< String, String> url_params = checkBaseParams(configuration);
        if(url_params.get("error") != null) {
            return 0;
        }

        /* Get the subdomain parameter */
        String subdomain = configuration.getParameter(SUBDOMAIN_PARM);
        subdomain = GuacamoleCommonUtility.getSubDomain(subdomain, DEFAULT_DOMAIN);
        

        /* Get the other parameters */
        url_params.put("assn_id", assn_id);

        /* Build the api url */
        String api_url = API_BASE_URL + UPLOAD_ASSIGNMENT_FULFILLED;
        StringBuilder url_params_value = new StringBuilder(convertMapToString(url_params));

        for (Integer fileId : fileIds) {
            if (url_params_value.length() == 0) {
                url_params_value = new StringBuilder("file_id[]=" + fileId);
            } else {
                url_params_value.append("&file_id[]=").append(fileId);
            }
        }

        /* Get the json data from api */
        String data = getData(configuration, api_url, HttpMethod.POST, subdomain, DEFAULT_DOMAIN, url_params_value.toString());
        JSONObject jsonData = null;
        /* warning or error message */
        String message;
        if (!data.isEmpty()) {
            JSONObject json;

            try {
                json = new JSONObject(data);
            }
            catch (JSONException e) {
                logger.error("[{}:{}:{}] uploadAssignmentFulfilledMultiple: Error parsing response: {}",
                            conn_id, connection_type, cloud_user, e.getMessage());
                return status;
            }

            status = json.getInt("status");
            if (status == Response.Status.OK.getStatusCode()) {
                jsonData = json.optJSONObject("lms_response");
            }
            else {
                message = json.getString("message");
                logger.warn("[{}:{}:{}] uploadAssignmentFulfilled: status code={} message={}",
                             conn_id, connection_type, cloud_user, status, message);
            }
        }

        /* Check json data */
        if (jsonData != null && !jsonData.isEmpty()) {
            if (jsonData.opt("errors") != null) {
                JSONArray errors = (JSONArray) jsonData.opt("errors");
                JSONObject error = (JSONObject) errors.opt(0);
                message = (String) error.opt("message");
                logger.error("[{}:{}:{}] uploadAssignmentFulfilled: Error message={}",
                         conn_id, connection_type, cloud_user, message);
            }
        }

        /* Return the status code */
        return status;
    }

    /**
     * Send idle time statistics to server.
     *
     * @return String with JSON data describing user activity.
     *
     * @throws GuacamoleException
     */
    @Path("/publishStream")
    @GET
    @Produces(MediaType.APPLICATION_JSON)
    @SuppressWarnings("unchecked")
    public Response publishStream(@QueryParam("token") String authToken,
            @QueryParam("id") String id,
            @QueryParam("uuid") String uuid,
            @QueryParam("fileName") String fileName,
            @QueryParam("streamIndex") Integer streamIndex) throws GuacamoleException {

        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        Map< String, UserTunnel> tunnels = session.getTunnels();
        UserTunnel tunnel = tunnels.get(uuid);

        if (tunnel == null) {
            JSONObject error = new JSONObject();
            error.put("Message", "Couldn't get the user tunnel.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        try {
            output.reset();
            tunnel.interceptStream(streamIndex.intValue(), output);
        }
        catch (GuacamoleException e) {
            JSONObject error = new JSONObject();
            error.put("Message", "Couldn't receive the published stream.");
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error.get("Message"));
            return Response.status(Response.Status.BAD_REQUEST).entity(error.toString()).build();
        }

        return Response.ok()
                       .header("Cache-Control", "no-cache, no-store, must-revalidate")
                       .header("Pragma", "no-cache")
                       .header("Expires", "0")
                       .build();
    }

    private Map< String, String> checkBaseParams(GuacamoleConfiguration configuration) {

        Map< String, String> params = new HashMap< String, String>();

        /* Get the lti_user_id parameter */
        String user_id = configuration.getParameter(LTI_USER_ID_PARM);
        if (user_id == null || user_id.isEmpty()) {
            String error_log = String.format("Invalid %s", LTI_USER_ID_PARM);
            params.put("error", error_log);
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error_log);
            return params;
        }

        /* Get the lti_course_id parameter */
        String course_id = configuration.getParameter(LTI_COURSE_ID_PARM);
        if (course_id == null || course_id.isEmpty()) {
            String error_log = "" + String.format("Invalid %s", LTI_COURSE_ID_PARM);
            params.put("error", error_log);
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error_log);
            return params;
        }

        /* Get the lti_issuer parameter */
        issuer = configuration.getParameter(LTI_ISSUER_PARM);
        if (issuer == null || issuer.isEmpty()) {
            String error_log = "" + String.format("Invalid %s", LTI_ISSUER_PARM);
            params.put("error", error_log);
            logger.error("[{}:{}:{}] {}", conn_id, connection_type, cloud_user, error_log);
            return params;
        }

        /* Add the parameters to map */
        params.put("user_id", user_id);
        params.put("course_id", course_id);
        params.put("issuer", issuer);

        return params;
    }

    private String convertMapToString(Map< String, String> map) {
        StringBuilder result = new StringBuilder();

        if (map == null) {
            return result.toString();
        }

        for (Map.Entry< String, String> entry : map.entrySet()) {
            if (result.length() == 0) {
                result.append(entry.getKey()).append("=").append(entry.getValue());
            }
            else {
                result.append("&").append(entry.getKey()).append("=").append(entry.getValue());
            }
        }

        result = new StringBuilder(result.toString().replaceAll(" ", "%20"));

        return result.toString();
    }

    /**
     * Get the data from the given API URL.
     *
     * @param param The parameters to be used in the URL.
     * value.
     * @return Wanted parameter value if successful, empty String if id
     * parameter is invalid, null value if paramName is not specified for
     * current user.
     *
     * @throws GuacamoleException
     */
    private String getData(GuacamoleConfiguration configuration, String template, HttpMethod httpMethod, String... param) {
        String url_str = String.format(template, param);
        logger.info("[{}:{}:{}] getData url={}", conn_id, connection_type, cloud_user, url_str);

        try {
            URL url = new URL(url_str);
            return sendToDrupal(configuration, url, httpMethod);

        }
        catch (MalformedURLException e) {
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            return "";
        }
    }

    protected String sendToDrupal(GuacamoleConfiguration configuration, URL url, HttpMethod httpMethod) {
        logger.info("[{}:{}:{}] sendToDrupal url={}", conn_id, connection_type, cloud_user, url);

        StringBuilder response = new StringBuilder();
        try {

            HttpURLConnection con = (HttpURLConnection) url.openConnection();
            String http_api_key = configuration.getParameter(HTTP_API_KEY);

            // set request header
            con.setRequestMethod(httpMethod.toString());
            con.setRequestProperty("User-Agent", USER_AGENT);
            con.setReadTimeout(30000);
            con.setConnectTimeout(30000);

            if (http_api_key != null && !http_api_key.isEmpty()) {
                con.setRequestProperty("http-api-key", http_api_key);
            }

            if (httpMethod.equals(HttpMethod.POST)) {
                con.setDoOutput(true);
                con.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                con.setRequestProperty("Content-Length", "0");

                // Send empty body explicitly
                try (OutputStream os = con.getOutputStream()) {
                    os.write(new byte[0]);
                    os.flush();
                }
            }

            BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
            String inputLine;
            response = new StringBuilder();

            while ((inputLine = in.readLine()) != null) {
                response.append(inputLine);
            }
            in.close();
            logger.info("[{}:{}:{}] sendToDrupal()-> response = {}",
                        conn_id, connection_type, cloud_user, getShortString(response.toString()));
            logger.debug("[{}:{}:{}] sendToDrupal()-> response = {}",
                        conn_id, connection_type, cloud_user, getShortString(response.toString()));

        }
        catch (MalformedURLException e) {
            logger.warn("[{}:{}:{}] MalformedURLException with http request, URL={}",
                        conn_id, connection_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);

        }
        catch (ProtocolException e) {
            logger.warn("[{}:{}:{}] ProtocolException with http request, URL={}",
                        conn_id, connection_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }
        catch (IOException e) {
            logger.warn("[{}:{}:{}] IOException with http request, URL={}", conn_id, connection_type, cloud_user, url.toString());
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }

        return response.toString();
    }

    /**
     * Return only n leading characters of the string and append "..." at the
     * end.
     *
     * This is used in the log files, to indicate that result is longer.
     * Typically this is used to dump results of URL APIs and it is possible to
     * get
     */
    private String getShortString(String str) {
        return str.length() < 200 ? str : (str.substring(0, 200) + "...");
    }

    protected String executeAPI(String fileName, URL url) {

        logger.debug("[{}:{}:{}] executeAPI  url={}", conn_id, connection_type, cloud_user, url);

        StringBuilder response = new StringBuilder();
        String LINE_FEED = "\r\n";

        try {
            HttpURLConnection con = (HttpURLConnection) url.openConnection();

            // Indicate a POST request
            con.setDoOutput(true);

            // A unique boundary to use for the multipart/form-data
            String boundary = Long.toHexString(System.currentTimeMillis());

            // Construct the body of the request
            con.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

            try {
                OutputStream os = con.getOutputStream();
                os.write(("--" + boundary).getBytes());
                os.write(LINE_FEED.getBytes());
                // uploading to canvas
                if (issuer.equals(LTI_CANVAS)) {
                    os.write(("Content-Disposition: form-data; name=\"file\"; filename=\"" + fileName + "\"").getBytes());
                }
                // uploading file to d2l or blackboard
                else {
                    os.write(("Content-Disposition: form-data; name=\"assignment_file\"; filename=\"" + fileName + "\"").getBytes());
                }
                os.write(LINE_FEED.getBytes());
                os.write(("Content-Type: text/plain").getBytes());
                os.write(LINE_FEED.getBytes());
                os.write(LINE_FEED.getBytes());

                os.write(output.toByteArray());
                os.write(LINE_FEED.getBytes());

                os.write(LINE_FEED.getBytes());
                os.write(("--" + boundary + "--").getBytes());
                os.write(LINE_FEED.getBytes());
                os.write(LINE_FEED.getBytes());
                os.close();

                String inputLine;
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
                response = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();

            }
            catch (Exception e) {
                logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
            }

        }
        catch (IOException e) {
            logger.warn("[{}:{}:{}] IOException with http request, URL={}",
                        conn_id, connection_type, cloud_user, url);
            logger.error("[{}:{}:{}] Exception log: ", conn_id, connection_type, cloud_user, e);
        }

        return response.toString();
    }
}
