/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

package com.apporto.guacamole.tunnel;

import org.apache.guacamole.protocol.GuacamoleConfiguration;

/**
 * Class storing the connection info
 *
 * <AUTHOR>
 */
public class ApportoConnectInfo {

    /**
     * The value of URL parameter "id".
     */
    protected String connId;

    /**
     * The value of URL parameter "cloud_username".
     */
    protected String cloudUserName;

    /**
     * The value of URL parameter "connection_type".
     */
    protected String connectionType;

    /**
     * The value of URL parameter "cloud_username".
     */
    protected Boolean enableH264 = false;

    /**
     * The GuacamoleConfiguration associated with this connection.
     */
    protected GuacamoleConfiguration configuration;
    
    /**
     * Constructor
     * @param connId The value of URL parameter "id" to associate with this session
     * @param cloudUserName The value of URL parameter "cloud_username" to associate with this session
     * @param enable_h264 The value of URL parameter "enable-h264" to associate with this session
     */
    public ApportoConnectInfo(String connId, String connectionType, String cloudUserName, Boolean enableH264,
                              GuacamoleConfiguration configuration) {
        this.connId = connId;
        this.connectionType = connectionType;
        this.cloudUserName = cloudUserName;
        this.enableH264 = enableH264;
        this.configuration = configuration;
    }

    /**
     * Returns the value of URL parameter "id" associated with this set of credentials.
     *
     * @return The id associated with this session, or
     *         null if no cloud_user has been set.
     */
    public String getConnId() {
        return connId;
    }

    /**
     * Sets the value of URL parameter "id" associated with this set of credentials.
     *
     * @param connId The value of URL parameter "id" to associate with this session
     */
    public void setConnId(String connId) {
        this.connId = connId;
    }

    /**
     * Returns the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @return The cloud_username associated with this session, or
     *         null if no cloud_username has been set.
     */
    public String getCloudUserName() {
        return cloudUserName;
    }

    /**
     * Sets the value of URL parameter "cloud_username" associated with this set of credentials.
     *
     * @param cloudUserName The value of URL parameter "cloud_username" to associate with this session
     */
    public void setCloudUserName(String cloudUserName) {
        this.cloudUserName = cloudUserName;
    }

    /**
     * Returns the value of URL parameter "connection_type" associated with this set of credentials.
     *
     * @return The connection_type associated with this session, or
     *         null if no connection_type has been set.
     */
    public String getConnectionType() {
        return connectionType;
    }

    /**
     * Sets the value of URL parameter "connection_type" associated with this set of credentials.
     *
     * @param connectionType The value of URL parameter "connection_type" to associate with this session
     */
    public void setConnectionType(String connectionType) {
        this.connectionType = connectionType;
    }

    /**
     * Returns the value of URL parameter "enable-h264" associated with this set of credentials.
     *
     * @return The h264 flag associated with this session, or
     *         false if no "enable-h264" has been set.
     */
    public Boolean getEnableH264() {
        return enableH264;
    }

    /**
     * Sets the value of URL parameter "enable-h264" associated with this set of credentials.
     *
     * @param enableH264 The value of URL parameter "enable-h264" to associate with this session
     */
    public void setEnableH264(Boolean enableH264) {
        this.enableH264 = enableH264;
    }

    /**
     * Returns the configuration.
     *
     * @return configuration.
     */
    public GuacamoleConfiguration getConfiguration() {
        return configuration;
    }

}
