/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.guacamole.tunnel;

import org.apache.guacamole.properties.IntegerGuacamoleProperty;
import org.apache.guacamole.properties.StringGuacamoleProperty;

/**
 * Properties used for configuring apporto system.
 *
 * <AUTHOR>
 *
 */
public final class ApportoProperties {

    // Properties file params
    public static final StringGuacamoleProperty SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "secret-key";
        }
    };

    public static final StringGuacamoleProperty DEFAULT_PROTOCOL = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "default-protocol";
        }
    };

    public static final IntegerGuacamoleProperty TIMESTAMP_AGE_LIMIT = new IntegerGuacamoleProperty() {
        @Override
        public String getName() {
            return "timestamp-age-limit";
        }
    };

    public static final StringGuacamoleProperty DC_SERVER_ID = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "dc-server-id";
        }
    };

    public static final StringGuacamoleProperty SERVER_ID = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "server-id";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_ENUMERATE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-enumerate";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_RESTORE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-restore";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_BACKUP = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-backup";
        }
    };

    public static final StringGuacamoleProperty SNAPSHOT_USERNAME = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "snapshot-username";
        }
    };

    public static final StringGuacamoleProperty PORT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "port";
        }
    };

    public static final StringGuacamoleProperty REGION = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "region";
        }
    };

    public static final StringGuacamoleProperty MESSENGER = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "messenger-server";
        }
    };

    public static final StringGuacamoleProperty API_BASE_DOMAIN = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "api-base-domain";
        }
    };

    public static final StringGuacamoleProperty POWERSHELL_RUNNER_HOST = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "powershell-runner-host";
        }
    };

    // Interval to send metrics to Opentelemetry collector
    public static final StringGuacamoleProperty OTEL_METRICS_INTERVAL = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "otel-metrics-interval";
        }
    };

    // Opentelemetery Collector for measuring metrics
    public static final StringGuacamoleProperty OTEL_COLLECTOR_ENDPOINT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "otel-collector-endpoint";
        }
    };

    // The secret key for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_SECRET_KEY = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-secret-key";
        }
    };

    // The user name for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_USERNAME = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-username";
        }
    };

    // The password for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_PASSWORD = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-password";
        }
    };

    // The port for the Apporto Service
    public static final StringGuacamoleProperty APPORTO_SERVICE_PORT = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "apporto-service-port";
        }
    };

    // The domain for Apporto Service
    public static final StringGuacamoleProperty LOCAL_DNS_ZONE = new StringGuacamoleProperty() {
        @Override
        public String getName() {
            return "local-dns-zone";
        }
    };

    private ApportoProperties() {
    }

    public static final Object getProp(String name) {
        switch (name) {
            case "secret-key":
                return SECRET_KEY;
            case "default-protocol":
                return DEFAULT_PROTOCOL;
            case "timestamp-age-limit":
                return TIMESTAMP_AGE_LIMIT;
            case "dc-server-id":
                return DC_SERVER_ID;
            case "server-id":
                return SERVER_ID;
            case "snapshot-enumerate":
                return SNAPSHOT_ENUMERATE;
            case "snapshot-restore":
                return SNAPSHOT_RESTORE;
            case "snapshot-backup":
                return SNAPSHOT_BACKUP;
            case "snapshot-username":
                return SNAPSHOT_USERNAME;
            case "port":
                return PORT;
            case "region":
                return REGION;
            case "messenger-server":
                return MESSENGER;
            case "api-base-domain":
                return API_BASE_DOMAIN;
            case "powershell-runner-host":
                return POWERSHELL_RUNNER_HOST;
            case "otel-metrics-interval":
                return OTEL_METRICS_INTERVAL;
            case "otel-collector-endpoint":
                return OTEL_COLLECTOR_ENDPOINT;
            case "apporto-service-secret-key":
                return APPORTO_SERVICE_SECRET_KEY;
            case "apporto-service-username":
                return APPORTO_SERVICE_USERNAME;
            case "apporto-service-password":
                return APPORTO_SERVICE_PASSWORD;
            case "apporto-service-port":
                return APPORTO_SERVICE_PORT;
            case "local-dns-zone":
                return LOCAL_DNS_ZONE;
            default:
                return null;
        }
    }
}
