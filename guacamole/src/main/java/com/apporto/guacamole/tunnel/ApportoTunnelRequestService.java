/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.apporto.guacamole.tunnel;

import java.util.List;
import java.util.Map;

import org.apache.guacamole.GuacamoleException;
import org.apache.guacamole.GuacamoleResourceNotFoundException;
import org.apache.guacamole.GuacamoleSession;
import org.apache.guacamole.GuacamoleUnauthorizedException;
import org.apache.guacamole.net.GuacamoleTunnel;
import org.apache.guacamole.net.auth.AuthenticatedUser;
import org.apache.guacamole.net.auth.Connectable;
import org.apache.guacamole.net.auth.Credentials;
import org.apache.guacamole.net.auth.UserContext;
import org.apache.guacamole.net.event.TunnelCloseEvent;
import org.apache.guacamole.net.event.TunnelConnectEvent;
import org.apache.guacamole.protocol.GuacamoleClientInformation;
import org.apache.guacamole.protocol.GuacamoleConfiguration;
import org.apache.guacamole.rest.auth.AuthenticationService;
import org.apache.guacamole.rest.event.ListenerService;
import org.apache.guacamole.tunnel.StandardTokenMap;
import org.apache.guacamole.tunnel.TunnelRequest;
import org.apache.guacamole.tunnel.TunnelRequestService;
import org.apache.guacamole.tunnel.TunnelRequestType;
import org.apache.guacamole.tunnel.UserTunnel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.apporto.environment.ApportoError;
import com.apporto.environment.SendStatusData;
import com.apporto.metrics.GuacamoleAppstore;
import com.google.inject.Inject;
import com.google.inject.Singleton;

/**
 * Replacement class for Tunnel creation that adds basic checking of the remote
 * server availability and reports the status of the checks to the master web
 * portal.
 *
 * <AUTHOR> Nikolić
 */
@Singleton
public class ApportoTunnelRequestService extends TunnelRequestService {

    /**
     * Logger for this class.
     */
    private static final Logger logger = LoggerFactory.getLogger(ApportoTunnelRequestService.class);

    /**
     * A service for authenticating users from auth tokens.
     */
    @Inject
    private AuthenticationService authenticationService;

    /**
     * A service for notifying listeners about tunnel connect/closed events.
     */
    @Inject
    private ListenerService listenerService;

    /**
     * URL parameters
     */
    private static final String CONF_STATUS_PARM = "conf-status";
    private static final String ID_PARM = "id";
    private static final String CONNECTION_TYPE_PARM = "connection_type";
    private static final String CLOUD_USERNAME_PARM = "cloud_username";
    private static final String H264_LICENCE_PARAM = "enable-h264";

    /**
     * Notifies bound listeners that a new tunnel has been connected. Listeners
     * may veto a connected tunnel by throwing any GuacamoleException.
     *
     * @param authenticatedUser The AuthenticatedUser associated with the user
     * for whom the tunnel is being created.
     *
     * @param credentials Credentials that authenticate the user.
     *
     * @param tunnel The tunnel that was connected.
     *
     * @throws GuacamoleException If thrown by a listener or if any listener
     * vetoes the connected tunnel.
     */
    private void fireTunnelConnectEvent(AuthenticatedUser authenticatedUser,
            Credentials credentials, GuacamoleTunnel tunnel) throws GuacamoleException {
        listenerService.handleEvent(new TunnelConnectEvent(authenticatedUser,
                credentials, tunnel));
    }

    /**
     * Notifies bound listeners that a tunnel is to be closed. Listeners are
     * allowed to veto a request to close a tunnel by throwing any
     * GuacamoleException.
     *
     * @param authenticatedUser The AuthenticatedUser associated with the user
     * for whom the tunnel is being closed.
     *
     * @param credentials Credentials that authenticate the user.
     *
     * @param tunnel The tunnel that was connected.
     *
     * @throws GuacamoleException If thrown by a listener.
     */
    private void fireTunnelClosedEvent(AuthenticatedUser authenticatedUser,
            Credentials credentials, GuacamoleTunnel tunnel)
            throws GuacamoleException {
        listenerService.handleEvent(new TunnelCloseEvent(authenticatedUser,
                credentials, tunnel));
    }

    /**
     * Reads and returns the client information provided within the given
     * request.
     *
     * @param request The request describing tunnel to create.
     *
     * @return GuacamoleClientInformation An object containing information about
     * the client sending the tunnel request.
     *
     * @throws GuacamoleException If the parameters of the tunnel request are
     * invalid.
     */
    protected GuacamoleClientInformation getClientInformation(TunnelRequest request)
            throws GuacamoleException {

        // Get client information
        GuacamoleClientInformation info = new GuacamoleClientInformation();

        // Set width if provided
        Integer width = request.getWidth();
        if (width != null) {
            info.setOptimalScreenWidth(width);
        }

        // Set height if provided
        Integer height = request.getHeight();
        if (height != null) {
            info.setOptimalScreenHeight(height);
        }

        // Set resolution if provided
        Integer dpi = request.getDPI();
        if (dpi != null) {
            info.setOptimalResolution(dpi);
        }

        // Add audio mimetypes
        List< String> audioMimetypes = request.getAudioMimetypes();
        if (audioMimetypes != null) {
            info.getAudioMimetypes().addAll(audioMimetypes);
        }

        // Add video mimetypes
        List< String> videoMimetypes = request.getVideoMimetypes();
        if (videoMimetypes != null) {
            info.getVideoMimetypes().addAll(videoMimetypes);
        }

        // Add image mimetypes
        List< String> imageMimetypes = request.getImageMimetypes();
        if (imageMimetypes != null) {
            info.getImageMimetypes().addAll(imageMimetypes);
        }

        // Set timezone if provided
        String timezone = request.getTimezone();
        if (timezone != null && !timezone.isEmpty()) {
            info.setTimezone(timezone);
        }

        // Set width if provided for the 2nd monitor
        Integer width2 = request.getWidth2();
        if (width2 != null) {
            info.setOptimalSecondScreenWidth(width2);
        }

        // Set height if provided for the 2nd monitor
        Integer height2 = request.getHeight2();
        if (height2 != null) {
            info.setOptimalSecondScreenHeight(height2);
        }

        // Set width if provided for the 3rd monitor
        Integer width3 = request.getWidth3();
        if (width3 != null) {
            info.setOptimalThirdScreenWidth(width3);
        }

        // Set height if provided for the 3rd monitor
        Integer height3 = request.getHeight3();
        if (height3 != null) {
            info.setOptimalThirdScreenHeight(height3);
        }

        return info;
    }

    /**
     * Creates a new tunnel using which is connected to the connection or
     * connection group identifier by the given ID. Client information is
     * specified in the {@code info} parameter.
     *
     * @param context The UserContext associated with the user for whom the
     * tunnel is being created.
     *
     * @param type The type of object being connected to (connection or group).
     *
     * @param id The id of the connection or group being connected to.
     *
     * @param info Information describing the connected Guacamole client.
     *
     * @param tokens A Map containing the token names and corresponding values
     * to be applied as parameter tokens when establishing the connection.
     *
     * @param connId The value of URL parameter "id" to associate with this session.
     *
     * @param cloudUserName The value of URL parameter "cloud_username" to associate
     * with this session.
     *
     * @return A new tunnel, connected as required by the request.
     *
     * @throws GuacamoleException If an error occurs while creating the tunnel.
     */
    protected GuacamoleTunnel createConnectedTunnel(UserContext context,
            final TunnelRequestType type, String id,
            GuacamoleClientInformation info, Map< String, String> tokens,
            String connId, String connType, String cloudUserName) throws GuacamoleException {

        // Retrieve requested destination object
        Connectable connectable = type.getConnectable(context, id);
        if (connectable == null) {
            throw new GuacamoleResourceNotFoundException("Requested tunnel "
                    + "destination does not exist.");
        }

        // Connect tunnel to destination
        GuacamoleTunnel tunnel = connectable.connect(info, tokens);
        logger.info("[{}:{}:{}] User \"{}\" connected to {} \"{}\".",
                    connId, connType, cloudUserName, context.self().getIdentifier(), type.NAME, id);

        return tunnel;

    }

    /**
     * Associates the given tunnel with the given session, returning a wrapped
     * version of the same tunnel which automatically handles closure and
     * removal from the session.
     *
     * @param tunnel The connected tunnel to wrap and monitor.
     *
     * @param authToken The authentication token associated with the given
     * session. If provided, this token will be automatically invalidated (and
     * the corresponding session destroyed) if tunnel errors imply that the user
     * is no longer authorized.
     *
     * @param session The Guacamole session to associate the tunnel with.
     *
     * @param context The UserContext associated with the user for whom the
     * tunnel is being created.
     *
     * @param type The type of object being connected to (connection or group).
     *
     * @param id The id of the connection or group being connected to.
     *
     * @return A new tunnel, associated with the given session, which delegates
     * all functionality to the given tunnel while monitoring and automatically
     * handling closure.
     *
     * @throws GuacamoleException If an error occurs while obtaining the tunnel.
     */
    @Override
    protected GuacamoleTunnel createAssociatedTunnel(final GuacamoleTunnel tunnel,
            final String authToken, final GuacamoleSession session,
            final UserContext context, final TunnelRequestType type,
            final String id) throws GuacamoleException {

        // Monitor tunnel closure and data
        UserTunnel monitoredTunnel = new UserTunnel(context, tunnel) {

            /**
             * The time the connection began, measured in milliseconds since
             * midnight, January 1, 1970 UTC.
             */
            private final long connectionStartTime = System.currentTimeMillis();

            @Override
            public void close() throws GuacamoleException {

                // Notify listeners to allow close request to be vetoed
                AuthenticatedUser authenticatedUser = session.getAuthenticatedUser();
                Credentials credentials = authenticatedUser.getCredentials();
                fireTunnelClosedEvent(authenticatedUser, credentials, tunnel);

                long connectionEndTime = System.currentTimeMillis();
                long duration = connectionEndTime - connectionStartTime;
                String connId = credentials.getConnId();
                String cloudUserName = credentials.getCloudUserName();
                String conn_type = credentials.getConnType();

                logger.info("[{}:{}:{}] User \"{}\" disconnected from {} \"{}\". Duration: {} milliseconds", 
                            connId, conn_type, cloudUserName, authenticatedUser.getIdentifier(), type.NAME, id, duration);

                GuacamoleConfiguration configuration = null;
                try {

                    // Get the proper configuration
                    UserContext userContext = session.getUserContext(authenticatedUser.getAuthenticationProvider().getIdentifier());
                    configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

                } // Ensure that the `session_ended` API is called if it couldn't get the proper user context
                catch (GuacamoleResourceNotFoundException e) {

                    logger.error("[{}:{}:{}] An error occurred while getting the user context related to the session, error: {}",
                                 connId, conn_type, cloudUserName, e.getMessage());

                    // Inform Drupal on disconnection success
                    SendStatusData.sendEndStatus(credentials.getParameters(), ApportoError.FAILED);

                    // Continue with exception processing
                    throw e;

                }

                logger.info("[{}:{}:{}] Got the guacamole configuration for sending the end status.", 
                            connId, conn_type, cloudUserName);

                try {

                    // Close and clean up tunnel
                    session.removeTunnel(getUUID().toString());
                    super.close();

                    // Inform Drupal on disconnection success
                    SendStatusData.sendEndStatus(configuration.getParameters(), ApportoError.OK);

                } // Ensure any associated session is invalidated if unauthorized
                catch (GuacamoleUnauthorizedException e) {

                    logger.error("[{}:{}:{}] error= {}", connId, conn_type, cloudUserName, e.getMessage());

                    // If there is an associated auth token, invalidate it
                    if (authenticationService.destroyGuacamoleSession(authToken)) {
                        logger.info("[{}:{}:{}] Implicitly invalidated session for token \"{}\".",
                                    connId, conn_type, cloudUserName, authToken);
                    }

                    // Inform Drupal on disconnection failure
                    SendStatusData.sendEndStatus(configuration.getParameters(), ApportoError.FAILED);

                    // Continue with exception processing
                    throw e;

                }

            }

        };

        // Associate tunnel with session
        session.addTunnel(monitoredTunnel);
        return monitoredTunnel;

    }

    /**
     * Creates a new tunnel using the parameters and credentials present in the
     * given request.
     *
     * @param request The request describing the tunnel to create.
     *
     * @return The created tunnel, or null if the tunnel could not be created.
     *
     * @throws GuacamoleException If an error occurs while creating the tunnel.
     */
    @Override
    public GuacamoleTunnel createTunnel(TunnelRequest request)
            throws GuacamoleException {

        // Parse request parameters
        String authToken = request.getAuthenticationToken();
        String id = request.getIdentifier();
        TunnelRequestType type = request.getType();
        String authProviderIdentifier = request.getAuthenticationProviderIdentifier();
        GuacamoleClientInformation info = getClientInformation(request);

        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        AuthenticatedUser authenticatedUser = session.getAuthenticatedUser();
        UserContext userContext = session.getUserContext(authProviderIdentifier);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();

        String connId = id.matches("[0-9]+")? id: configuration.getParameters().get(ID_PARM);
        String cloudUserName = configuration.getParameters().get(CLOUD_USERNAME_PARM);
        String conn_type = configuration.getParameters().get(CONNECTION_TYPE_PARM);

        Credentials credentials = authenticatedUser.getCredentials();
        if (conn_type == null || conn_type.isEmpty()) {
            conn_type = credentials.getConnType();
        }

        try {

            // Create connected tunnel using provided connection ID and client information
            GuacamoleTunnel tunnel = createConnectedTunnel(userContext, type,
                    id, info, new StandardTokenMap(authenticatedUser), connId, conn_type, cloudUserName);

            // Notify listeners to allow connection to be vetoed
            fireTunnelConnectEvent(authenticatedUser, authenticatedUser.getCredentials(), tunnel);

            // conf_status is created in authentication part, it is not part of the configuration
            // or encrypted URL. When checks for the RDP and SFTP ports are made, conf_status is
            // set to appropriate value.
            String conf_status = configuration.getParameters().get(CONF_STATUS_PARM);
            if (conf_status != null) { // Inform Drupal on connection success
                SendStatusData.sendStartStatus(configuration, ApportoError.fromString(conf_status), authToken);

                Boolean enableH264 = "true".equalsIgnoreCase(configuration.getParameters().get(H264_LICENCE_PARAM));
                GuacamoleAppstore.sendCompression(configuration, enableH264);
            }
            else {
                logger.warn("[{}:{}:{}] Configuration status is missing, no reporting to admin tool possible.",
                            connId, conn_type, cloudUserName);
            }

            // Associate tunnel with session
            return createAssociatedTunnel(tunnel, authToken, session, userContext, type, id);

        }
        // Ensure any associated session is invalidated if unauthorized
        catch (GuacamoleUnauthorizedException e) {

            // If there is an associated auth token, invalidate it
            if (authenticationService.destroyGuacamoleSession(authToken)) {
                logger.debug("[{}:{}:{}] Implicitly invalidated session for token \"{}\".",
                             connId, conn_type, cloudUserName, authToken);
            }

            // Inform Drupal on connection failure
            SendStatusData.sendStartStatus(configuration, ApportoError.FAILED, authToken);

            // Continue with exception processing
            throw e;

        }
        // Ensure any associated session is invalidated if any exception occurred
        catch (Exception e) {

            // Print the exception log
            logger.error("[{}:{}:{}] Any exception occurred while creating a tunnel.",
                         connId, conn_type, cloudUserName, e);

            // If there is an associated auth token, invalidate it
            if (authenticationService.destroyGuacamoleSession(authToken)) {
                logger.debug("[{}:{}:{}] Implicitly invalidated session for token \"{}\".",
                             connId, conn_type, cloudUserName, authToken);
            }

            // Continue with exception processing
            throw e;

        }

    }

    /**
     * Get the connection info needed to print the log.
     *
     * @param request The request describing the tunnel to create.
     */
    public ApportoConnectInfo getConnectionInfo(TunnelRequest request) throws GuacamoleException {

        // Parse request parameters
        String authToken = request.getAuthenticationToken();
        String id = request.getIdentifier();
        String authProviderIdentifier = request.getAuthenticationProviderIdentifier();

        GuacamoleSession session = authenticationService.getGuacamoleSession(authToken);
        UserContext userContext = session.getUserContext(authProviderIdentifier);
        GuacamoleConfiguration configuration = userContext.getConnectionDirectory().get(id).getConfiguration();
        
        String connId = id.matches("[0-9]+")? id: configuration.getParameters().get(ID_PARM);
        String cloudUserName = configuration.getParameters().get(CLOUD_USERNAME_PARM);
        String connectionType = configuration.getParameters().get(CONNECTION_TYPE_PARM);

        AuthenticatedUser authenticatedUser = session.getAuthenticatedUser();
        Credentials credentials = authenticatedUser.getCredentials();
        if (connectionType == null || connectionType.isEmpty()) {
            connectionType = credentials.getConnType();
        }

        Boolean enableH264 = "true".equalsIgnoreCase(configuration.getParameters().get(H264_LICENCE_PARAM));

        ApportoConnectInfo connectInfo = new ApportoConnectInfo(connId, connectionType, cloudUserName, enableH264, configuration);
        return connectInfo;

    }

}
