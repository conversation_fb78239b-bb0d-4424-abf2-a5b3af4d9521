<form class="notification" ng-class="notification.className" id="guacNotification"
      ng-submit="notification.formSubmitCallback()">

    <!-- Notification title -->
    <div ng-show="notification.title" class="title-bar">
        <h2 class="title" style="padding-bottom: 0px;">{{notification.title | translate}}</h2>
    </div>

    <div class="body">

        <!-- Notification text -->
        <p ng-show="notification.text" class="text" style="padding-top: 24px !important;"
           translate="{{notification.text.key}}"
           translate-values="{{notification.text.variables}}"></p>

        <!-- Arbitrary parameters -->
        <div class="parameters" ng-show="notification.forms">
            <guac-form
                namespace="notification.formNamespace"
                content="notification.forms"
                model="notification.formModel"
                model-only="true"></guac-form>
        </div>

        <!-- Current progress -->
        <div class="progress" ng-show="notification.progress"><div class="bar" ng-show="progressPercent" ng-style="{'width': progressPercent + '%'}"></div><div
                ng-show="notification.progress.text"
                translate="{{notification.progress.text}}"
                translate-values="{PROGRESS: notification.progress.value, UNIT: notification.progress.unit}"></div></div>

        <!-- Default action countdown text -->
        <p class="countdown-text"
           ng-show="notification.countdown.text"
           translate="{{notification.countdown.text}}"
           translate-values="{REMAINING: timeRemaining}"></p>

    </div>

    <div class="notification-footer">
        <div class="footer-left">
            <label ng-if="notification.checkbox" class="checkbox">
                <input type="checkbox" ng-model="doNotShowAgain" ng-change="notification.checkbox.onChange()">
                <span translate="CLIENT.TEXT_DO_NOT_SHOW_AGAIN"></span>
            </label>
            <button tabindex="0" ng-show="notification.actions.length" ng-repeat="action in notification.actions" ng-click="action.callback()" ng-class="action.className"
                class="tClose" aria-label="{{action.name | translate}}">{{action.name | translate}}</button>
        </div>
    </div>

</div>
