
<div class="view" ng-class="{loading: !isLoaded()}">

    <div class="connection-list-ui">

        <!-- The recent connections for this user -->
        <div class="header">
            <h2>{{'HOME.SECTION_HEADER_RECENT_CONNECTIONS' | translate}}</h2>
            <guac-user-menu></guac-user-menu>
        </div>
        <div class="recent-connections">
            <guac-recent-connections root-groups="rootConnectionGroups"></guac-recent-connections>
        </div>

        <!-- All connections for this user -->
        <div class="header">
            <h2>{{'HOME.SECTION_HEADER_ALL_CONNECTIONS' | translate}}</h2>
            <guac-group-list-filter connection-groups="rootConnectionGroups"
                filtered-connection-groups="filteredRootConnectionGroups"
                placeholder="'HOME.FIELD_PLACEHOLDER_FILTER' | translate"
                connection-properties="filteredConnectionProperties"
                connection-group-properties="filteredConnectionGroupProperties"></guac-group-list-filter>
        </div>
        <div class="all-connections">
            <guac-group-list
                context="context"
                connection-groups="filteredRootConnectionGroups"
                templates="{
                    'connection'       : 'app/home/<USER>/connection.html',
                    'connection-group' : 'app/home/<USER>/connectionGroup.html'
                }"
                page-size="20"></guac-group-list>
        </div>

    </div>

</div>