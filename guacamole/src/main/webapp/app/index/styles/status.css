/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.status-outer {
    display: table;
    height: 100%;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.status-middle {
    width: 100%;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
}

.status-middle .notification {
    width: 75%;
    margin-left: auto;
    margin-right: auto;
    overflow: auto;

    text-align: left;
}

.status-middle .notification .body {
    margin: 32px;
    margin-top: 0px;
    margin-bottom: 0px;
    padding-bottom: 8px;
}

.status-middle .notification .buttons {
    margin: 1em;
}

/* Fade entire status area in/out based on shown status */

.status-outer {
    visibility: hidden;
    opacity: 0;
    transition: opacity, visibility;
    transition-duration: 0.25s;
}

.shown.status-outer {
    visibility: visible;
    opacity: 1;
}

/* Hide dialog immediately based on status */

.status-middle .notification {
    visibility: hidden;
}

.shown .status-middle .notification {
    visibility: visible;
}
