/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

.dialog-container {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    padding: 1em;
}

.dialog-outer {
    display: table;
    height: 100%;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
}

.dialog-outer-attendance {
    display: table;
    display: -webkit-flex;
    height: 100%;
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
}

.dialog-middle {
    width: 100%;
    text-align: center;
    display: table-cell;
    vertical-align: middle;
}

.dialog.edit {
    max-height: 100%;
}

.dialog {

    max-width: 100%;
    width: 8in;
    margin-left: auto;
    margin-right: auto;

    border: 0px solid rgba(0, 0, 0, 0.5);
    background: #E7E7E7;

    -moz-border-radius: 0.2em;
    -webkit-border-radius: 0.2em;
    -khtml-border-radius: 0.2em;
    border-radius: 0.2em;

    box-shadow: 0.1em 0.1em 0.2em rgba(0, 0, 0, 0.6);
    
}

.dialog > * {
    margin: 1em;
}

.dialog .header {
    margin: 0;
}

.dialog td {
    position: relative;
}

.dialog .overlay {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1;
}

.dialog .footer {
    text-align: right;
}
