<div class="text-field">
    <input type="text"
           ng-attr-id="{{ fieldId }}"
           ng-attr-list="{{ dataListId }}"
           ng-model="model"
           autocorrect="off"
           autocapitalize="off"/>
    <datalist ng-if="dataListId" ng-attr-id="{{ dataListId }}">
        <option ng-repeat="option in field.options | orderBy: option"
                value="{{ option }}">{{ getFieldOption(option) | translate }}</option>
    </datalist>
</div>
