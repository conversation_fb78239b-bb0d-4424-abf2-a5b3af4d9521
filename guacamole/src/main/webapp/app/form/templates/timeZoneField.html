<div class="time-zone-field">

    <!-- Available time zone regions -->
    <select class="time-zone-region"
            ng-attr-id="{{ fieldId }}"
            ng-model="region"
            ng-options="name for name in regions | orderBy: name"></select>

    <!-- Time zones within selected region -->
    <select class="time-zone"
            ng-disabled="!region"
            ng-model="model"
            ng-options="timeZone.value as timeZone.key for timeZone in timeZones[region] | toArray | orderBy: key"></select>

</div>
