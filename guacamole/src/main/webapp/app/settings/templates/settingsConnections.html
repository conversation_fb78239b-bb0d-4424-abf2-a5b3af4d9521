<div class="settings section connections" ng-class="{loading: !isLoaded()}">

    <!-- Connection management -->
    <p>{{'SETTINGS_CONNECTIONS.HELP_CONNECTIONS' | translate}}</p>

    <!-- Connection management toolbar -->
    <div class="toolbar">

        <!-- Form action buttons -->
        <div class="action-buttons">

            <a class="add-connection button"
               ng-show="canCreateConnections()"
               href="#/manage/{{dataSource}}/connections/">{{'SETTINGS_CONNECTIONS.ACTION_NEW_CONNECTION' | translate}}</a>

            <a class="add-connection-group button"
               ng-show="canCreateConnectionGroups()"
               href="#/manage/{{dataSource}}/connectionGroups/">{{'SETTINGS_CONNECTIONS.ACTION_NEW_CONNECTION_GROUP' | translate}}</a>

        </div>

        <!-- Connection filter -->
        <guac-group-list-filter connection-groups="rootGroups"
            filtered-connection-groups="filteredRootGroups"
            placeholder="'SETTINGS_CONNECTIONS.FIELD_PLACEHOLDER_FILTER' | translate"
            connection-properties="filteredConnectionProperties"
            connection-group-properties="filteredConnectionGroupProperties"></guac-group-list-filter>

    </div>

    <!-- List of accessible connections and groups -->
    <div class="connection-list">
        <guac-group-list
            page-size="25"
            connection-groups="filteredRootGroups"
            decorator="rootItemDecorator"
            templates="{

                'connection'       : 'app/settings/templates/connection.html',
                'sharing-profile'  : 'app/settings/templates/sharingProfile.html',
                'connection-group' : 'app/settings/templates/connectionGroup.html',

                'new-connection'       : 'app/settings/templates/newConnection.html',
                'new-sharing-profile'  : 'app/settings/templates/newSharingProfile.html',
                'new-connection-group' : 'app/settings/templates/newConnectionGroup.html'

            }"/>
    </div>
</div>
