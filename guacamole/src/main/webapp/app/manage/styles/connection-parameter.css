/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/* Do not stretch connection parameters to fit available area */
.connection-parameters input[type=text],
.connection-parameters input[type=email],
.connection-parameters input[type=password],
.connection-parameters input[type=number] {
    width: auto;
}

.connection-parameters .form .fields {
    display: table;
    padding-left: .5em;
    border-left: 3px solid rgba(0,0,0,0.125);
    width: 100%;
}

.connection-parameters .form .fields .labeled-field {
    display: table-row;
}

.connection-parameters .form .fields .field-header,
.connection-parameters .form .fields .form-field {
    display: table-cell;
    padding: 0.125em;
    vertical-align: top;
    width: 100%;
}

.connection-parameters .form .fields .field-header {
    padding-right: 1em;
    width: 0;
    white-space: nowrap;
}
