<div class="choice">

    <!-- Connection group icon -->
    <div class="icon type"></div>

    <!-- Permission checkbox -->
    <input type="checkbox" ng-model="context.getPermissionFlags().connectionGroupPermissions.READ[item.identifier]"
                           ng-change="context.connectionGroupPermissionChanged(item.identifier)"/>

    <!-- Connection group name -->
    <span class="name">{{item.name}}</span>

</div>
