<div class="manage-user-group view" ng-class="{loading: !isLoaded()}">

    <!-- User group header and data source tabs -->
    <div class="header tabbed">
        <h2>{{'MANAGE_USER_GROUP.SECTION_HEADER_EDIT_USER_GROUP' | translate}}</h2>
        <guac-user-menu></guac-user-menu>
    </div>
    <data-data-source-tabs ng-hide="cloneSourceIdentifier"
        permissions="managementPermissions"
        url="getUserGroupURL(dataSource)">
    </data-data-source-tabs>

    <!-- Warn if user group is read-only -->
    <div class="section" ng-hide="managementPermissions[dataSource].canSaveObject">
        <p class="notice read-only">{{'MANAGE_USER_GROUP.INFO_READ_ONLY' | translate}}</p>
    </div>

    <!-- Sections applicable to non-read-only user groups -->
    <div ng-show="managementPermissions[dataSource].canSaveObject">

        <!-- User group name -->
        <div class="section">
            <table class="properties">
                <tr>
                    <th>{{'MANAGE_USER_GROUP.FIELD_HEADER_USER_GROUP_NAME' | translate}}</th>
                    <td>
                        <input ng-show="canEditIdentifier()" ng-model="userGroup.identifier" type="text"/>
                        <span  ng-hide="canEditIdentifier()">{{userGroup.identifier}}</span>
                    </td>
                </tr>
            </table>
        </div>

        <!-- User group attributes section -->
        <div class="attributes" ng-show="managementPermissions[dataSource].canChangeAttributes">
            <guac-form namespace="'USER_GROUP_ATTRIBUTES'" content="attributes"
                       model="userGroup.attributes"
                       model-only="!managementPermissions[dataSource].canChangeAllAttributes"></guac-form>
        </div>

        <!-- System permissions section -->
        <system-permission-editor ng-show="managementPermissions[dataSource].canChangePermissions"
              data-data-source="dataSource"
              permission-flags="permissionFlags"
              permissions-added="permissionsAdded"
              permissions-removed="permissionsRemoved">
        </system-permission-editor>

        <!-- Parent group section -->
        <identifier-set-editor
            header="MANAGE_USER_GROUP.SECTION_HEADER_USER_GROUPS"
            empty-placeholder="MANAGE_USER_GROUP.HELP_NO_USER_GROUPS"
            unavailable-placeholder="MANAGE_USER_GROUP.INFO_NO_USER_GROUPS_AVAILABLE"
            identifiers-available="availableGroups"
            identifiers="parentGroups"
            identifiers-added="parentGroupsAdded"
            identifiers-removed="parentGroupsRemoved">
        </identifier-set-editor>

        <!-- Member group section -->
        <identifier-set-editor
            header="MANAGE_USER_GROUP.SECTION_HEADER_MEMBER_USER_GROUPS"
            empty-placeholder="MANAGE_USER_GROUP.HELP_NO_MEMBER_USER_GROUPS"
            unavailable-placeholder="MANAGE_USER_GROUP.INFO_NO_USER_GROUPS_AVAILABLE"
            identifiers-available="availableGroups"
            identifiers="memberGroups"
            identifiers-added="memberGroupsAdded"
            identifiers-removed="memberGroupsRemoved">
        </identifier-set-editor>

        <!-- Member user section -->
        <identifier-set-editor
            header="MANAGE_USER_GROUP.SECTION_HEADER_MEMBER_USERS"
            empty-placeholder="MANAGE_USER_GROUP.HELP_NO_MEMBER_USERS"
            unavailable-placeholder="MANAGE_USER_GROUP.INFO_NO_USERS_AVAILABLE"
            identifiers-available="availableUsers"
            identifiers="memberUsers"
            identifiers-added="memberUsersAdded"
            identifiers-removed="memberUsersRemoved">
        </identifier-set-editor>

        <!-- Connection permissions section -->
        <connection-permission-editor ng-show="managementPermissions[dataSource].canChangePermissions"
              data-data-source="dataSource"
              permission-flags="permissionFlags"
              permissions-added="permissionsAdded"
              permissions-removed="permissionsRemoved">
        </connection-permission-editor>

        <!-- Form action buttons -->
        <management-buttons namespace="MANAGE_USER_GROUP"
              permissions="managementPermissions[dataSource]"
              save="saveUserGroup()"
              delete="deleteUserGroup()"
              clone="cloneUserGroup()"
              return="returnToUserGroupList()">
        </management-buttons>

    </div>

</div>
