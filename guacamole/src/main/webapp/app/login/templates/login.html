<div class="login-ui" ng-class="{error: loginError, continuation: isContinuation(), initial: !isContinuation()}" >

    <!-- Login error message -->
    <p class="login-error" translate="{{loginError.key}}"
       translate-values="{{loginError.variables}}"></p>

    <div class="login-dialog-middle">

        <div class="login-dialog">

            <form class="login-form" ng-submit="login()">

                <!-- Guacamole version -->
                <div class="logo"></div>
                <div class="version">
                    <div class="app-name">{{'APP.NAME' | translate}}</div>
                    <div class="version-number">{{'APP.VERSION' | translate}}</div>
                </div>

                <!-- Login message/instructions -->
                <p ng-show="helpText" translate="{{helpText.key}}"
                   translate-values="{{helpText.variables}}"></p>

                <!-- Login fields -->
                <div class="login-fields">
                    <guac-form namespace="'LOGIN'" content="remainingFields" model="enteredValues"></guac-form>
                </div>

                <!-- Submit button -->
                <div class="buttons">
                    <input type="submit" name="login" class="login" value="{{'LOGIN.ACTION_LOGIN' | translate}}"/>
                    <input type="submit" name="login" class="continue-login" value="{{'LOGIN.ACTION_CONTINUE' | translate}}"/>
                </div>

            </form>

        </div>

    </div>

</div>
