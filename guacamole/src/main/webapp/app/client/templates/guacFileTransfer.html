<div class="transfer" ng-class="{'in-progress': isInProgress(), 'savable': isSavable(), 'error': hasError()}" ng-click="save()">

    <!-- Overall status of transfer -->
    <div class="transfer-status">
    <div><span class="abort-transfer" ng-if= "getPercentDone()<=100" ng-click="abortTransfer(transfer.filename)">x</span></div>
        <!-- Filename and progress bar -->
        <div class="filename">
            <div class="progress"><div ng-style="{'width': getPercentDone() + '%'}" class="bar"></div></div>
            {{transfer.filename}}
        </div>

        <!-- Error text -->
        <p class="error-text">{{getErrorText() | translate}}</p>

    </div>

</div>
