<table style="border-collapse: collapse;" class="file-browser" ng-class="{'multi-select':multiSelect}">

	<thead>
		<tr>
			<th ng-if="multiSelect" scope="col"></th>
			<th id="header-name" scope="col">Name</th>
			<th id="header-size" scope="col">Size</th>
			<th id="header-modified" scope="col">Last modified</th>
		</tr>
	</thead>
    <!-- Current directory contents --> 
    <tbody
	    ng-show="!ribbonService.skeletonLoad.assignmentManagementFile"
        class="current-directory-contents"
        ng-class="{'safari-browser': ribbonService.browser.isSafari}">
    </tbody>

	<tbody
        ng-show="ribbonService.skeletonLoad.assignmentManagementFile"
        class="skeleton-table"
        ng-class="{'safari-browser': ribbonService.browser.isSafari}">

		<tr ng-repeat="n in [].constructor(4) track by $index" class="normal-file">
		    <td ng-if="multiSelect">
				<div class="pulse"></div>
			</td>
		    <td>
				<div class="pulse"></div>
			</td>
		    <td>
				<div class="pulse"></div>
			</td>
		    <td>
				<div class="pulse"></div>
			</td>
		</tr>
    </tbody>

</table>
