/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

/* Hide directory contents by default */

.file-browser .directory > .children {
    padding-left: 1em;
    display: none;
}

th, td.data {
	text-align: start;
	overflow: hidden;
    color: #757575;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.assignment-management-dialog th{
    color: #000000dd;
}

.file-browser td {
    color: #1A1A1A;
    font-feature-settings: 'clig' off, 'liga' off;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.assignment-management-dialog .file-browser td {
    font-weight: 500;
}


.file-browser th:nth-child(1),
.file-browser td:nth-child(1) {
    width: 67%;
    padding-left: 8px;
    text-align: start;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.file-browser.multi-select th:nth-child(1),
.file-browser.multi-select td:nth-child(1) {
    width:30px;
    padding-left: 0px;
    vertical-align: middle;
}

.file-browser th:nth-child(2),
.file-browser td:nth-child(2) {
    width: 12%;
    padding-right: 4px;
    text-align: end;
}

.file-browser.multi-select th:nth-child(2),
.file-browser.multi-select td:nth-child(2) {
    width: 60%;
    padding-left: 8px;
    text-align: start;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}

.file-browser th:nth-child(3),
.file-browser td:nth-child(3) { 
    padding-right: 8px;
    text-align: end;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.file-browser.multi-select th:nth-child(3),
.file-browser.multi-select td:nth-child(3) {
    width: 12%;
    padding-right: 4px;
    text-align: end;
}

.file-browser.multi-select th:nth-child(4),
.file-browser.multi-select td:nth-child(4) {
    padding-right: 8px;
    text-align: end;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}
  
.file-browser tr {
    height: 32px;
}

.assignment-management-dialog .file-browser tr {
    height: 36px;
    border-bottom: 1px solid #75757533;
}

.file-browser tr:nth-child(even) {
    background-color: #f7fafd;
}

.assignment-management-dialog .file-browser tr {
    background: transparent;
}
  

td {
	overflow: hidden;
	width: 30%;
	text-align: left;
	cursor: pointer;
}

table {
	table-layout: fixed;
	width: 100%;
    border: transparent;
}

.file-browser .list-item .caption {
    white-space: nowrap;
    border: 1px solid transparent;
}

.file-browser .list-item.focused .caption {
    border: 1px dotted rgba(0, 0, 0, 0.5);
    background: rgba(204, 221, 170, 0.5);
    width: 3px;
}

table.file-browser tr .caption {
    white-space: nowrap;
    border: 1px solid transparent;
}

tr.normal-file:hover td{
    background: #dfe8f5;
 }
 
 tr.normal-file.focused:hover td{
    background: #156cd5;
 }

 .assignment-management-dialog tr.normal-file.focused:hover td{
    background: #dfe8f5;
 }

tr.normal-file.focused  {
    background: #156CD5;
    width: 3px;
}

.assignment-management-dialog tr.normal-file.focused{
    background: inherit;
 }

tr.normal-file.focused td{
    color: white !important;
}

.assignment-management-dialog tr.normal-file.focused td{
    color: black !important;
}

tr.directory.focused {
    border: 1px dotted rgba(0,0,0,0.5);
}

.assignment-management-dialog tr.directory.focused {
    border: 1px dotted rgba(0,0,0,0.5);
}

/* Directory / file icons */

.file-browser .normal-file > .caption .icon {
    background-image: url('images/file.png');
}

table.file-browser tbody.current-directory-contents tr.normal-file td> .caption .icon {
    background-image: url('images/file.png');
}

.file-browser .directory > .caption .icon {
    background-image: url('images/folder-closed.png');
}

table.file-browser tbody.current-directory-contents tr.directory td> .caption .icon {
    background-image: url('images/folder-closed.png');
}

table.file-browser tbody.current-directory-contents tr.directory td>.caption .icon.disabled {
    background-image: url(app/ext/ribbon/images/folder-grey.png);
}

.file-browser .directory.previous > .caption .icon {
    background-image: url('images/folder-up.png');
}

table.file-browser tbody.current-directory-contents tr.directory.previous td> .caption .icon {
    background-image: url('images/folder-up.png');
}

table.file-browser tbody.current-directory-contents tr.normal-file td:nth-child(1)> div {
    overflow-x: auto;
}

table.file-browser tbody.current-directory-contents.safari-browser tr.normal-file td:nth-child(1)> div {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.file-checkbox {
    margin-right: 8px;
    cursor: pointer;
    height: 16px;
    width: 16px;
}

.file-checkbox[disabled] {
    cursor: not-allowed;
    opacity: 0.5;
}