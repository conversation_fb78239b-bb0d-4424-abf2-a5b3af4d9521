<!DOCTYPE html>
<!--
    Licensed to the Apache Software Foundation (ASF) under one
    or more contributor license agreements.  See the NOTICE file
    distributed with this work for additional information
    regarding copyright ownership.  The ASF licenses this file
    to you under the Apache License, Version 2.0 (the
    "License"); you may not use this file except in compliance
    with the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing,
    software distributed under the License is distributed on an
    "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, either express or implied.  See the License for the
    specific language governing permissions and limitations
    under the License.
-->
<html ng-app="index" ng-controller="indexController">
    <head><!-- Google Tag Manager -->
        <script>
            let allResourcesLoaded = true;
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','GTM-MR9HTMR');</script>
        <!-- End Google Tag Manager -->
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <meta http-equiv="x-ua-compatible" content="IE=edge"/>
        <meta http-equiv="Cache-control" content="no-cache, no-store, must-revalidate"/>
        <meta http-equiv="Pragma" content="no-cache"/>
        <meta http-equiv="expires" content="0"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, target-densitydpi=medium-dpi"/>
        <meta name="mobile-web-app-capable" content="yes"/>
        <meta name="apple-mobile-web-app-capable" content="yes"/>
        <link rel="icon" type="image/png" href="images/logo-64.png"/>
        <link rel="icon" type="image/png" sizes="144x144" href="images/logo-144.png"/>
        <link rel="apple-touch-icon" type="image/png" href="images/logo-144.png"/>
        <link rel="stylesheet" type="text/css" href="webjars/simonwep__pickr/1.8.2/dist/themes/monolith.min.css"/>
        <link rel="stylesheet" type="text/css" href="app_${gitversion}.css">
        <link rel="stylesheet" type="text/css" href="https://unpkg.com/print-js@1.6.0/dist/print.css"
            integrity="sha256-FpdKS+ZModcod+vii6bOQTrWSaMwIRgLevlTU5o72n4=
                       sha384-deLeGaTra1nRmMTCPMPMFTEHJvO2J/U8dQGeHL5+lsI9gWLMku5g4z9cI5IzeOhI
                       sha512-tKGnmy6w6vpt8VyMNuWbQtk6D6vwU8VCxUi0kEMXmtgwW+6F70iONzukEUC3gvb+KTJTLzDKAGGWc1R7rmIgxQ=="
            crossorigin="anonymous"
            onerror="allResourcesLoaded = false;">
        <title ng-bind="page.title | translate"></title>
    </head>
    <body ng-class="page.bodyClassName"><!-- Google Tag Manager (noscript) -->
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MR9HTMR"
        height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->

        <div id="blockedContainer" style="text-align: center; padding: 16px; font-size: 16px; display: none;">
            <div class="fatal-page-error-outer shown">
                <div class="fatal-page-error-middle">
                    <div class="fatal-dogs"></div>
                    <div id="blockedMsgWrapper"></div>
                    <div class="logo"></div>
                </div>
            </div>
        </div>

        <div ng-if="!fatalError">

            <!-- Content for logged-in users -->
            <div ng-if="!expectedCredentials">
            
                <!-- Global status/error dialog -->
                <div ng-class="{shown: guacNotification.getStatus()}" class="status-outer">
                    <div class="status-middle">
                        <guac-notification notification="guacNotification.getStatus()"></guac-notification>
                    </div>
                </div>

                <main>
                    <div id="content" ng-view>
                    </div>
                </main>

            </div>

            <!-- Login screen for logged-out users -->
            <guac-login ng-show="expectedCredentials"
                        help-text="loginHelpText"
                        form="expectedCredentials"
                        values="acceptedCredentials"></guac-login>

        </div>

        <!-- Absolute fatal error -->
        <div ng-if="fatalError" ng-class="{shown: fatalError}" class="fatal-page-error-outer">
            <div class="fatal-page-error-middle">
                <div class="fatal-page-error">
                    <h1 translate="APP.DIALOG_HEADER_ERROR"></h1>
                    <p translate="APP.ERROR_PAGE_UNAVAILABLE"></p>
                </div>
            </div>
        </div>

        <!-- Reformat URL for AngularJS if query parameters are present -->
        <script type="text/javascript" src="relocateParameters.js"></script>

        <!-- Utility libraries -->
        <script type="text/javascript" src="webjars/jquery/3.3.1/dist/jquery.min.js"></script>
        <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"
            integrity="sha256-KM512VNnjElC30ehFwehXjx1YCHPiQkOPmqnrWtpccM=
                       sha384-PtTRqvDhycIBU6x1wwIqnbDo8adeWIWP3AHmnrvccafo35E7oIvW7HPXn2YimvWu
                       sha512-uto9mlQzrs59VwILcLiRYeLKPPbS/bT71da/OEBYEwcdNUk8jYIy+D176RYoop1Da+f9mvkYrmj5MCLZWEtQuA=="
            crossorigin="anonymous"
            onerror="allResourcesLoaded = false;"></script>
        <script type="text/javascript" src="webjars/lodash/4.17.21/dist/lodash.min.js"></script>

        <!-- AngularJS -->
        <script type="text/javascript" src="webjars/angular/1.8.2/angular.min.js"></script>
        <script type="text/javascript" src="webjars/angular-cookies/1.6.9/angular-cookies.min.js"></script>
        <script type="text/javascript" src="webjars/angular-route/1.8.2/angular-route.min.js"></script>
        <script type="text/javascript" src="webjars/angular-touch/1.8.2/angular-touch.min.js"></script>

        <!-- Internationalization -->
        <script type="text/javascript" src="webjars/messageformat/1.0.2/messageformat.min.js"></script>
        <script type="text/javascript" src="webjars/angular-translate/2.18.3/angular-translate.min.js"></script>
        <script type="text/javascript" src="webjars/angular-translate-interpolation-messageformat/2.18.3/angular-translate-interpolation-messageformat.min.js"></script>
        <script type="text/javascript" src="webjars/angular-translate-loader-static-files/2.18.3/angular-translate-loader-static-files.min.js"></script>

        <!-- JSTZ -->
        <script type="text/javascript" src="webjars/jstz/2.1.1/dist/jstz.min.js"></script>

        <!-- Pickr (color picker) -->
        <script type="text/javascript" src="webjars/simonwep__pickr/1.8.2/dist/pickr.es5.min.js"></script>

        <!-- Polyfills for the "datalist" element, Blob and the FileSaver API -->
        <script type="text/javascript" src="webjars/blob-polyfill/5.0.20210201/Blob.js"></script>
        <script type="text/javascript" src="webjars/datalist-polyfill/1.24.4/datalist-polyfill.min.js"></script>
        <script type="text/javascript" src="webjars/filesaver/1.3.3/FileSaver.min.js"></script>

        <!-- Allow arbitrary ordering of Angular module creation and retrieval -->
        <script type="text/javascript" src="webjars/angular-module-shim/0.0.4/angular-module-shim.js"></script>

        <!-- Web application -->
        <script type="text/javascript" src="app_${gitversion}.js"></script>

        <!-- Rollbar library -->
        <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/apportocorp/ng-rollbar@2.26.2/ng-rollbar.min.js"
            integrity="sha256-Z/xZga6SEgzPkFayEbwvGBwqeucJsGL3A+IVAOfCPn0=
                       sha384-xz3ySgNQb4xJ+jnGfOmyWjCUldJog+BL6IdW+myFuWE8jsBNVSQuHr9o4TuBx/Hb
                       sha512-hD8Yxemv1hwrz/AFerT1igk7vFppg/dUxG5CoVr5zucWbruXjpzia+ezh1YSIAxxSnfTQL9fGe+6vygS3qP4TA=="
            crossorigin="anonymous"
            onerror="allResourcesLoaded = false;"></script>

        <!-- Patternomaly library -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/patternomaly/1.3.2/patternomaly.js"
            integrity="sha256-C1Jlj6eY9slOfoa2zYk54bKc2tTAS1ow2QF5lvUlnM0=
                       sha384-kdgUFNEbslNOEAQ/zFH9wjDORAbvN0KUYOpfJIJYPdjtzLXkZsMJAXU9HPpi5B5c
                       sha512-gNM40ajr/bSi3Af8i6D4dV2CUWZrkm2zhgeWf46H91zOwWoH8Wwsyf6kQ4syfNyOrnjATrjKkP4ybWD7eKp2KA=="
            crossorigin="anonymous" referrerpolicy="no-referrer"
            onerror="allResourcesLoaded = false;"></script>

        <!-- Print JS -->
        <script type="text/javascript" src="print.js"></script>

        <script lang="javascript">
            // Make a controll variable that an admin can control
            const initValue = {
                enableLogging: false
            };
            const handler = {
                set(target, property, value) {
                    target[property] = value;
                    return true;
                }
            };
            adminController = new Proxy(initValue, handler);

            document.getElementById('blockedContainer').style.display = 'none';

            if (!allResourcesLoaded) {
                var blockedMsgWrapper = document.getElementById('blockedMsgWrapper');

                var msg = "<p style='width: 710px; text-align: justify;'>Unable to load web page. " +
                        "This may be due to security extensions in the browser or network filters." +
                        "<br/>Please make sure you don't have anything blocking Apporto traffic." +
                        "<br/><br/>If you continue to see this issue, please contact your administrator.</p>";

                // corresponding to Spanish
                var language = (navigator.languages && navigator.languages[0])
                    || navigator.language
                    || navigator.browserLanguage
                    || 'en';
                if (language.substring(0, 2) === "es") {
                    msg = "<p style='width: 710px; text-align: justify;'>No se puede cargar la página web. " +
                        "Esto puede deberse a extensiones de seguridad en el navegador o filtros de red." +
                        "<br/>Asegúrese de no tener nada que bloquee el tráfico de Apporto." +
                        "<br/><br/>Si continúa viendo este problema, comuníquese con su administrador.</p>";
                }

                blockedMsgWrapper.innerHTML = msg;
                document.getElementById('blockedContainer').style.display = 'block';
            }
        </script>

    </body>
</html>
