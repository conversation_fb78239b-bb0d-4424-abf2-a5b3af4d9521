{"language": "it_IT", "type": "qwerty", "width": 23, "keys": {"Esc": 65307, "F1": 65470, "F2": 65471, "F3": 65472, "F4": 65473, "F5": 65474, "F6": 65475, "F7": 65476, "F8": 65477, "F9": 65478, "F10": 65479, "F11": 65480, "F12": 65481, "Space": " ", "Back": [{"title": "⟵", "keysym": 65288}], "Tab": [{"title": "Tab ↹", "keysym": 65289}], "Enter": [{"title": "↵", "keysym": 65293}], "Home": [{"title": "Home", "keysym": 65360}], "PgUp": [{"title": "PgUp ↑", "keysym": 65365}], "PgDn": [{"title": "PgDn ↓", "keysym": 65366}], "End": [{"title": "End", "keysym": 65367}], "Ins": [{"title": "Ins", "keysym": 65379}], "Del": [{"title": "Del", "keysym": 65535}], "Left": [{"title": "←", "keysym": 65361}], "Up": [{"title": "↑", "keysym": 65362}], "Right": [{"title": "→", "keysym": 65363}], "Down": [{"title": "↓", "keysym": 65364}], "Menu": [{"title": "<PERSON><PERSON>", "keysym": 65383}], "LShift": [{"title": "Shift", "modifier": "shift", "keysym": 65505}], "RShift": [{"title": "Shift", "modifier": "shift", "keysym": 65506}], "LCtrl": [{"title": "Ctrl", "modifier": "control", "keysym": 65507}], "RCtrl": [{"title": "Ctrl", "modifier": "control", "keysym": 65508}], "Caps": [{"title": "Caps", "modifier": "caps", "keysym": 65509}], "LAlt": [{"title": "Alt", "modifier": "alt", "keysym": 65513}], "AltGr": [{"title": "AltGr", "modifier": "alt-gr", "keysym": 65027}], "Meta": [{"title": "Meta", "modifier": "meta", "keysym": 65511}], "\\": [{"title": "\\", "requires": []}, {"title": "|", "requires": ["shift"]}], "1": [{"title": "1", "requires": []}, {"title": "!", "requires": ["shift"]}], "2": [{"title": "2", "requires": []}, {"title": "\"", "requires": ["shift"]}], "3": [{"title": "3", "requires": []}, {"title": "£", "requires": ["shift"]}], "4": [{"title": "4", "requires": []}, {"title": "$", "requires": ["shift"]}], "5": [{"title": "5", "requires": []}, {"title": "%", "requires": ["shift"]}, {"title": "€", "requires": ["alt-gr"]}], "6": [{"title": "6", "requires": []}, {"title": "&", "requires": ["shift"]}], "7": [{"title": "7", "requires": []}, {"title": "/", "requires": ["shift"]}], "8": [{"title": "8", "requires": []}, {"title": "(", "requires": ["shift"]}], "9": [{"title": "9", "requires": []}, {"title": ")", "requires": ["shift"]}], "0": [{"title": "0", "requires": []}, {"title": "=", "requires": ["shift"]}], "'": [{"title": "'", "requires": []}, {"title": "?", "requires": ["shift"]}, {"title": "`", "requires": ["alt-gr", "shift"]}], "ì": [{"title": "ì", "requires": []}, {"title": "^", "requires": ["shift"]}, {"title": "~", "requires": ["alt-gr", "shift"]}], "q": [{"title": "q", "requires": []}, {"title": "Q", "requires": ["caps"]}, {"title": "Q", "requires": ["shift"]}, {"title": "q", "requires": ["caps", "shift"]}], "w": [{"title": "w", "requires": []}, {"title": "W", "requires": ["caps"]}, {"title": "W", "requires": ["shift"]}, {"title": "w", "requires": ["caps", "shift"]}], "e": [{"title": "e", "requires": []}, {"title": "E", "requires": ["caps"]}, {"title": "E", "requires": ["shift"]}, {"title": "e", "requires": ["caps", "shift"]}, {"title": "€", "requires": ["alt-gr"]}], "r": [{"title": "r", "requires": []}, {"title": "R", "requires": ["caps"]}, {"title": "R", "requires": ["shift"]}, {"title": "r", "requires": ["caps", "shift"]}], "t": [{"title": "t", "requires": []}, {"title": "T", "requires": ["caps"]}, {"title": "T", "requires": ["shift"]}, {"title": "t", "requires": ["caps", "shift"]}], "y": [{"title": "y", "requires": []}, {"title": "Y", "requires": ["caps"]}, {"title": "Y", "requires": ["shift"]}, {"title": "y", "requires": ["caps", "shift"]}], "u": [{"title": "u", "requires": []}, {"title": "U", "requires": ["caps"]}, {"title": "U", "requires": ["shift"]}, {"title": "u", "requires": ["caps", "shift"]}], "i": [{"title": "i", "requires": []}, {"title": "I", "requires": ["caps"]}, {"title": "I", "requires": ["shift"]}, {"title": "i", "requires": ["caps", "shift"]}], "o": [{"title": "o", "requires": []}, {"title": "O", "requires": ["caps"]}, {"title": "O", "requires": ["shift"]}, {"title": "o", "requires": ["caps", "shift"]}], "p": [{"title": "p", "requires": []}, {"title": "P", "requires": ["caps"]}, {"title": "P", "requires": ["shift"]}, {"title": "p", "requires": ["caps", "shift"]}], "è": [{"title": "è", "requires": []}, {"title": "è", "requires": ["caps"]}, {"title": "é", "requires": ["shift"]}, {"title": "é", "requires": ["caps", "shift"]}, {"title": "[", "requires": ["alt-gr"]}, {"title": "{", "requires": ["alt-gr", "shift"]}], "+": [{"title": "+", "requires": []}, {"title": "+", "requires": ["caps"]}, {"title": "*", "requires": ["shift"]}, {"title": "*", "requires": ["caps", "shift"]}, {"title": "]", "requires": ["alt-gr"]}, {"title": "}", "requires": ["alt-gr", "shift"]}], "a": [{"title": "a", "requires": []}, {"title": "A", "requires": ["caps"]}, {"title": "A", "requires": ["shift"]}, {"title": "a", "requires": ["caps", "shift"]}], "s": [{"title": "s", "requires": []}, {"title": "S", "requires": ["caps"]}, {"title": "S", "requires": ["shift"]}, {"title": "s", "requires": ["caps", "shift"]}], "d": [{"title": "d", "requires": []}, {"title": "D", "requires": ["caps"]}, {"title": "D", "requires": ["shift"]}, {"title": "d", "requires": ["caps", "shift"]}], "f": [{"title": "f", "requires": []}, {"title": "F", "requires": ["caps"]}, {"title": "F", "requires": ["shift"]}, {"title": "f", "requires": ["caps", "shift"]}], "g": [{"title": "g", "requires": []}, {"title": "G", "requires": ["caps"]}, {"title": "G", "requires": ["shift"]}, {"title": "g", "requires": ["caps", "shift"]}], "h": [{"title": "h", "requires": []}, {"title": "H", "requires": ["caps"]}, {"title": "H", "requires": ["shift"]}, {"title": "h", "requires": ["caps", "shift"]}], "j": [{"title": "j", "requires": []}, {"title": "J", "requires": ["caps"]}, {"title": "J", "requires": ["shift"]}, {"title": "j", "requires": ["caps", "shift"]}], "k": [{"title": "k", "requires": []}, {"title": "K", "requires": ["caps"]}, {"title": "K", "requires": ["shift"]}, {"title": "k", "requires": ["caps", "shift"]}], "l": [{"title": "l", "requires": []}, {"title": "L", "requires": ["caps"]}, {"title": "L", "requires": ["shift"]}, {"title": "l", "requires": ["caps", "shift"]}], "ò": [{"title": "ò", "requires": []}, {"title": "ò", "requires": ["caps"]}, {"title": "ç", "requires": ["shift"]}, {"title": "ç", "requires": ["caps", "shift"]}, {"title": "@", "requires": ["alt-gr"]}], "à": [{"title": "à", "requires": []}, {"title": "à", "requires": ["caps"]}, {"title": "°", "requires": ["shift"]}, {"title": "°", "requires": ["caps", "shift"]}, {"title": "#", "requires": ["alt-gr"]}], "ù": [{"title": "ù", "requires": []}, {"title": "ù", "requires": ["caps"]}, {"title": "§", "requires": ["shift"]}, {"title": "§", "requires": ["caps", "shift"]}], "<": [{"title": "<", "requires": []}, {"title": ">", "requires": ["shift"]}], "z": [{"title": "z", "requires": []}, {"title": "Z", "requires": ["caps"]}, {"title": "Z", "requires": ["shift"]}, {"title": "z", "requires": ["caps", "shift"]}], "x": [{"title": "x", "requires": []}, {"title": "X", "requires": ["caps"]}, {"title": "X", "requires": ["shift"]}, {"title": "x", "requires": ["caps", "shift"]}], "c": [{"title": "c", "requires": []}, {"title": "C", "requires": ["caps"]}, {"title": "C", "requires": ["shift"]}, {"title": "c", "requires": ["caps", "shift"]}], "v": [{"title": "v", "requires": []}, {"title": "V", "requires": ["caps"]}, {"title": "V", "requires": ["shift"]}, {"title": "v", "requires": ["caps", "shift"]}], "b": [{"title": "b", "requires": []}, {"title": "B", "requires": ["caps"]}, {"title": "B", "requires": ["shift"]}, {"title": "b", "requires": ["caps", "shift"]}], "n": [{"title": "n", "requires": []}, {"title": "N", "requires": ["caps"]}, {"title": "N", "requires": ["shift"]}, {"title": "n", "requires": ["caps", "shift"]}], "m": [{"title": "m", "requires": []}, {"title": "M", "requires": ["caps"]}, {"title": "M", "requires": ["shift"]}, {"title": "m", "requires": ["caps", "shift"]}, {"title": "µ", "requires": ["alt-gr"]}], ",": [{"title": ",", "requires": []}, {"title": ";", "requires": ["shift"]}], ".": [{"title": ".", "requires": []}, {"title": ":", "requires": ["shift"]}], "-": [{"title": "-", "requires": []}, {"title": "_", "requires": ["shift"]}]}, "layout": [["Esc", 0.8, "F1", "F2", "F3", "F4", 0.8, "F5", "F6", "F7", "F8", 0.8, "F9", "F10", "F11", "F12"], [0.1], {"main": {"alpha": [["\\", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "'", "ì", "Back"], ["Tab", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "è", "+", 1, 0.6], ["Caps", "a", "s", "d", "f", "g", "h", "j", "k", "l", "ò", "à", "ù", "Enter"], ["LShift", "<", "z", "x", "c", "v", "b", "n", "m", ",", ".", "-", "RShift"], ["LCtrl", "Meta", "LAlt", "Space", "AltGr", "<PERSON><PERSON>", "RCtrl"]], "movement": [["Ins", "Home", "PgUp"], ["Del", "End", "PgDn"], [1], ["Up"], ["Left", "Down", "Right"]]}}], "keyWidths": {"Back": 2, "Tab": 1.75, "\\": 1.25, "Caps": 1.75, "Enter": 1.5, "LShift": 2.2, "RShift": 2.2, "LCtrl": 1.6, "Meta": 1.6, "LAlt": 1.6, "Space": 6.4, "AltGr": 1.6, "Menu": 1.6, "RCtrl": 1.6, "Ins": 1.6, "Home": 1.6, "PgUp": 1.6, "Del": 1.6, "End": 1.6, "PgDn": 1.6}}